/* eslint-disable quotes */
import React from 'react';
import SearchInput from '../Input/SearchInput';
import '@testing-library/jest-dom';
import { createSnapshot, renderWithStyle, snapshotConfig } from 'utils/helpers/test.helpers';
import { toMatchImageSnapshot } from 'jest-image-snapshot';
import { fireEvent, screen } from '@testing-library/react';

expect.extend({ toMatchImageSnapshot });

describe('Search Input Component', () => {
  test('renders without crash', () => {
    renderWithStyle(<SearchInput placeholder="Search" />);
  });

  test('calls onChange correctly', async () => {
    const onChange = jest.fn();
    renderWithStyle(<SearchInput placeholder="Search" onChange={onChange} name="search-menu" />);
    expect(onChange).not.toBeCalled();
    const input = document.querySelector("input[name='search-menu']");
    fireEvent.change(input, { target: { value: 'porn' } });
    expect(onChange).toBeCalled();
  });

  test('search and clear icon change correctly', async () => {
    const onChange = jest.fn();
    renderWithStyle(<SearchInput placeholder="Search" onChange={onChange} name="search-menu" />);
    const searchButton = screen.getByAltText('search');
    expect(searchButton).toBeInTheDocument();
    const input = document.querySelector("input[name='search-menu']");
    fireEvent.change(input, { target: { value: 'porn' } });
    const clearButton = screen.getByAltText('clear');
    expect(clearButton).toBeInTheDocument();
  });

  test('displays value correctly', async () => {
    renderWithStyle(<SearchInput placeholder="Search" value="porn" name="search-menu" />);
    const input = document.querySelector("input[name='search-menu']");
    expect(input).toBeInTheDocument();
    expect(input.value).toBe('porn');
    const clearButton = screen.getByAltText('clear');
    fireEvent.click(clearButton);
    expect(input.value).toBe('');
  });

  test('matches snapshot', async () => {
    jest.setTimeout(30000);
    const image = await createSnapshot(<SearchInput placeholder="Search" />, { width: 200, heigth: 30 });
    expect(image).toMatchImageSnapshot(snapshotConfig);
  });

  test('matches snapshot when is small', async () => {
    jest.setTimeout(30000);
    const image = await createSnapshot(<SearchInput small placeholder="Search" />, { width: 200, heigth: 19 });
    expect(image).toMatchImageSnapshot(snapshotConfig);
  });

  test('matches snapshot when is full width', async () => {
    jest.setTimeout(30000);
    const image = await createSnapshot(<SearchInput placeholder="Search" fullWidth />, { width: 400, heigth: 30 });
    expect(image).toMatchImageSnapshot(snapshotConfig);
  });
});
