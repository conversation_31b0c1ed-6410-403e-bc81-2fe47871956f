import React, { InputHTMLAttributes } from 'react';
import styled from 'styled-components/macro';

const Div = styled.div<{ disabled?: boolean }>`
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  width: fit-content;
  cursor: ${({ disabled }) => (disabled ? 'default' : 'pointer')};
  & label {
    cursor: ${({ disabled }) => (disabled ? 'default' : 'pointer')};
  }
`;

const RadioButton = styled.input<Props>`
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
  width: 16px;
  height: 16px;
  border: 1px solid ${({ theme }) => theme.colorPalette.grey.grey4};
  border-radius: 50%;
  ::after {
    content: '';
    display: block;
    border-radius: 50%;
    width: 8px;
    height: 8px;
    margin: 3px;
  }
  :hover {
    ::after {
      ${({ disabled, theme }) => !disabled && `background-color: ${theme.colorPalette.turquoise.normal};`}
    }
  }
  :checked {
    border: 1px solid ${({ theme }) => theme.colorPalette.turquoise.normal};
    ::after {
      background-color: ${({ theme }) => theme.colorPalette.turquoise.normal};
    }
  }
`;

export interface Props extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  value?: string;
  disabled?: boolean;
}

const SingleRadioButton = ({ checked, onChange, label, value, disabled }: Props) => {
  return (
    <Div
      disabled={disabled}
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
      }}
    >
      <RadioButton
        id={value}
        type="radio"
        checked={checked}
        onChange={(e) => {
          if (disabled) return;
          onChange?.(e);
        }}
        disabled={disabled}
      />
      <div
        style={{ width: 'fit-content', cursor: 'pointer' }}
        onClick={(e) => {
          if (disabled) return;
          onChange?.(e as any);
        }}
      >
        <label htmlFor={value}>{label}</label>
      </div>
    </Div>
  );
};

export default SingleRadioButton;
