import Checkbox from 'components/Buttons/CheckboxButton';
import CommonButton from 'components/Buttons/CommonButton';
import Dropdown from 'components/Input/Dropdown';
import TextInput from 'components/Input/TextInput';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  ButtonsContainer,
  CheckboxContainer,
  CheckboxLabel,
  InputContainer,
  InputLabel,
  Right,
  RightTitle,
  RightWrapper,
} from 'routes/Configurator/styles';
import actions from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components';
import utils from 'utils';
import CronFrequencySelector from '../components/CronFrequencySelector';
import { FormValues } from './overdueUtils';

export const ExtendedInputLabel = styled(InputLabel)`
  width: 200px;
  white-space: normal;
  overflow: hidden;
`;
export const ExtendedCheckboxLabel = styled(CheckboxLabel)`
  width: 200px;
  white-space: normal;
  overflow: hidden;
`;

interface IProps {
  values: FormValues;
  setFieldValue: Function;
  dirty: boolean;
  isValid: boolean;
  isValidating: boolean;
  submitForm: (e: any) => void;
  userList: { idUser: number; name: string }[];
  templatesList: { value: number; label: string }[];
}

const FormOverdue = ({
  values,
  setFieldValue,
  dirty,
  isValid,
  isValidating,
  submitForm,
  userList,
  templatesList,
}: IProps) => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const formMode = useSelector(selectors.configurator.getFormModeOverdue);
  const selectedRow = useSelector(selectors.configurator.getSelectedOverdueSettings);

  return (
    <RightWrapper>
      <RightTitle> {t(`${formMode}_setting`)}</RightTitle>
      <Right>
        <InputContainer>
          <ExtendedInputLabel>{t('user')}</ExtendedInputLabel>
          {formMode === 'edit' ? (
            <TextInput disabled value={selectedRow?.name} />
          ) : (
            <Dropdown
              name="user"
              value={values['user']}
              onChange={(value) => {
                setFieldValue('user', value);
              }}
              options={utils.input.buildOptions(userList ?? [], 'name', 'idUser')}
              placeholder={t('select_user')}
              width="170px"
            />
          )}
        </InputContainer>
        <CheckboxContainer>
          <ExtendedCheckboxLabel>{t('overDueSendMail')}</ExtendedCheckboxLabel>
          <Checkbox
            name="overDueSendMail"
            isActive={values['overDueSendMail'] ? true : false}
            action={(e) => setFieldValue('overDueSendMail', e)}
          />
        </CheckboxContainer>
        <InputContainer>
          <ExtendedInputLabel>{t('overDueMaxDocuments')}</ExtendedInputLabel>
          <TextInput
            name="overDueMaxDocuments"
            value={values['overDueMaxDocuments']}
            onChange={(e: any) => {
              const value = Number(e.target.value);
              setFieldValue('overDueMaxDocuments', value);
            }}
            type="number"
            min={1}
          />
        </InputContainer>
        <InputContainer>
          <ExtendedInputLabel>{t('idTemplateScheduledOverdue')}</ExtendedInputLabel>
          <Dropdown
            name="idTemplateScheduledOverdue"
            value={values['idTemplateScheduledOverdue']}
            onChange={(value) => {
              setFieldValue('idTemplateScheduledOverdue', value);
            }}
            options={templatesList}
            placeholder={t('select_template_scheduled_open')}
            width="170px"
          />
        </InputContainer>
        <CronFrequencySelector
          hour={values['hour']}
          setHour={(value: number) => setFieldValue('hour', value)}
          minute={values['minute']}
          setMinute={(value: number) => setFieldValue('minute', value)}
          allDays={values['allDays']}
          setAllDays={(value: boolean) => setFieldValue('allDays', value)}
          daysOfWeek={values['daysOfWeek']}
          setDaysOfWeek={(value: { label: string; value: number }[]) => setFieldValue('daysOfWeek', value)}
        />
        <ButtonsContainer>
          <CommonButton
            id="saveButton"
            scope="primary"
            disabled={!dirty || !isValid || isValidating}
            type="submit"
            value={t(`${formMode}_settings`)}
            action={submitForm}
          />
          <CommonButton
            id="cancelButton"
            scope="secondary"
            type="button"
            value={t('cancel')}
            action={() => {
              dispatch(actions.configurator.setFormModeOverdue(null));
            }}
          />
        </ButtonsContainer>
      </Right>
    </RightWrapper>
  );
};
export default FormOverdue;
