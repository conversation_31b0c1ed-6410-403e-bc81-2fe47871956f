import React from 'react';
import { fireEvent, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import '@testing-library/jest-dom/extend-expect';
import Card from 'components/CTCard';
import { renderWithStyle } from 'utils/helpers/test.helpers';

const action = jest.fn();

const cardData = {
    title: 'Artificial Intelligence',
    icon: 'acquisition',
    ctaText: 'click me',
    action: action
  };

describe('CT Card Component', () => {
  let card = null;
  beforeEach(() => {
    card = <Card {...cardData} >main content</Card>;
    renderWithStyle(card);
  });

  afterEach(() => {
    card = null;
  });

  it('should render card with title', () => {
    const cardTitle = screen.getByText(cardData.title);
    expect(cardTitle).toBeInTheDocument();
  });

  it('should render card with content', () => {
    const content = screen.getByText('main content');
    expect(content).toBeInTheDocument();
  });

  it('should execute an action when click on CTA', () => {
    const cta = screen.getByText('click me');
    fireEvent.click(cta)
    expect(action).toHaveBeenCalledTimes(1);
  });
});
