import React, { CSSProperties, useEffect, useRef, useState } from 'react';
import styled from 'styled-components/macro';

import utils from 'utils';

import { MultiSelect, SelectProps } from 'react-multi-select-component';
import CheckboxButton from 'components/Buttons/CheckboxButton';

const CheckBoxContainer = styled.div`
  display: flex;
  align-items: center;
`;

const CheckBoxLabel = styled.div`
  padding-left: 10px;
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.grey.grey9};
`;

const FeedbackText = styled.p<{ feedbackColor: string; maxWidth?: string }>`
  position: absolute !important;
  right: 0;
  font-family: Roboto;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  color: ${({ feedbackColor }) => feedbackColor};
  margin: 3px 5px 0 3px;
  text-align: right;
  position: relative;
  ${({ maxWidth }) => maxWidth && `max-width: ${maxWidth}`};
`;

const MultiSelectWrapper = styled.div<{ feedbackColor?: string; feedbackMessage?: string; isOpen?: boolean }>`
  position: relative;
  max-width: fit-content !important;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XS};
  font-weight: ${({ theme }) => theme.fontWeightPalette.regular};
  > .multi-select-container {
    width: 165px;
    .dropdown-container {
      border-color: ${({ feedbackColor, feedbackMessage, theme, isOpen }) =>
        (feedbackMessage && feedbackColor) ||
        (isOpen && theme.colorPalette.turquoise.dark) ||
        theme.colorPalette.isDarkMode
          ? theme.colorPalette.grey.grey3
          : theme.colorPalette.grey.grey5};
      border-radius: 2px;
      .dropdown-heading {
        height: 23px;
        padding-right: 0;
        padding-left: 6px;
        font-size: ${({ theme }) => theme.fontSizePalette.body.XS};
        background-color: ${({ theme }) =>
          theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.white};
        .dropdown-heading-value {
          padding-top: 1px;
          color: ${({ theme }) =>
            theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.grey.grey9};
        }
      }
      .panel-content {
        background-color: ${({ theme }) =>
          theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.white};
        border: 1px solid
          ${({ theme }) =>
            theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.grey.grey3};

        .select-item {
          color: ${({ theme }) =>
            theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.grey.grey9};

          &.selected {
            background-color: ${({ theme }) =>
              theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.grey.grey1};
          }

          &:hover {
            background-color: ${({ theme }) =>
              theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.grey.grey1};
          }
        }

        .search {
          background-color: ${({ theme }) =>
            theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.white};
          border-bottom: 1px solid
            ${({ theme }) =>
              theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey4 : theme.colorPalette.grey.grey3};

          input {
            background-color: ${({ theme }) =>
              theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.white};
            color: ${({ theme }) =>
              theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.grey.grey9};

            &::placeholder {
              color: ${({ theme }) =>
                theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey7 : theme.colorPalette.grey.grey6};
            }

            &:focus {
              background-color: ${({ theme }) =>
                theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.white};
            }
          }
        }
      }
    }

    .dropdown-container:focus-within {
      border-color: ${({ theme }) => theme.colorPalette.turquoise.dark};
      box-shadow: ${({ theme, isOpen }) =>
        isOpen
          ? `${theme.colorPalette.turquoise.dark} 0 0 0 1px`
          : theme.colorPalette.isDarkMode
          ? `${theme.colorPalette.turquoise.light} 0 0 0 1px`
          : `${theme.colorPalette.turquoise.normal} 0 0 0 1px`};
    }
  }
`;

interface IDefaultItemRendererProps {
  checked: boolean;
  option: { label: string; value: string | number };
  disabled?: boolean;
  onClick: (isActive: boolean, e?: React.MouseEvent<HTMLInputElement, MouseEvent> | undefined) => void;
}

export interface SelectPropsExtender extends SelectProps {
  style?: CSSProperties;
  placeholder?: string;
  onClick?: (selected: { label: string; value: string | number }) => void;
  hasFeedback?: boolean;
  feedbackMessage?: string;
  feedbackColor?: string;
  minWidthLabel?: string;
}
const ItemRenderer = ({ checked, option, onClick, disabled }: IDefaultItemRendererProps) => (
  <CheckBoxContainer>
    <div>
      <CheckboxButton isActive={checked} isEditable={!disabled} action={onClick} />
    </div>
    <CheckBoxLabel>{option.label}</CheckBoxLabel>
  </CheckBoxContainer>
);

const customValueRenderer = (selected: IDefaultItemRendererProps['option'][]) => {
  return selected.length && selected.map(({ label }, i) => (i + 1 === selected.length ? label : `${label}, `));
};
const MultiSelectInput = (props: SelectPropsExtender) => {
  const t = utils.intl.useTranslator();
  const [isOpen, setIsOpen] = useState(false);
  const wrapperRef = useRef<HTMLDivElement>(null);

  const { hasFeedback, feedbackColor = 'red', minWidthLabel = '165px', feedbackMessage } = props;

  const handleClickOutside = (event: MouseEvent) => {
    if (wrapperRef.current && !wrapperRef.current.contains(event.target as Node)) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const texts = {
    allItemsAreSelected: t('All items are selected'),
    clearSearch: t('Clear Search'),
    clearSelected: t('Clear Selected'),
    noOptions: t('No options'),
    search: t('Search'),
    selectAll: t('Select All'),
    selectAllFiltered: t('Select All Filtered'),
    selectSomeItems: props.placeholder || ' ',
    create: t('Create'),
  };

  const handleClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    const nodeName = (e.target as any)?.nodeName;
    if (nodeName === 'svg' || nodeName === 'path') {
      setIsOpen(!isOpen);
      return;
    }
    !isOpen && setIsOpen(true);
  };
  return (
    <MultiSelectWrapper
      style={props.style}
      ref={wrapperRef}
      onClick={handleClick}
      feedbackMessage={feedbackMessage}
      feedbackColor={feedbackColor}
      isOpen={isOpen}
    >
      <MultiSelect
        overrideStrings={texts}
        ItemRenderer={ItemRenderer}
        valueRenderer={customValueRenderer}
        className="multi-select-container"
        isOpen={isOpen}
        {...props}
      />
      {hasFeedback && (
        <FeedbackText feedbackColor={feedbackColor} maxWidth={minWidthLabel}>
          {feedbackMessage}
        </FeedbackText>
      )}
    </MultiSelectWrapper>
  );
};

export default MultiSelectInput;
