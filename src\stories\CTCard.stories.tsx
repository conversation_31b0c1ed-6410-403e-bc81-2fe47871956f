import React from 'react';
import CTCard from 'components/CTCard';

import { Meta, Story } from '@storybook/react';
import { CTCardProps } from 'components/CTCard';

import KpiItem from 'components/KpiItem';

export default {
  title: 'CT Card',
  component: CTCard,
} as Meta;

const styleKpi = { paddingTop: '20px', minHeight: '130px' };
const Template: Story<CTCardProps> = (args) => (
  <CTCard {...args}>
    <div>some content</div>
  </CTCard>
);

const TemplateWithKpis: Story<CTCardProps> = (args) => (
  <CTCard {...args}>
    <div>
      <KpiItem
        style={{ ...styleKpi }}
        value={4.5}
        unit="days"
        description="Average document retention time in the validation queue"
      />
      <KpiItem
        style={{ ...styleKpi }}
        value={92}
        unit="%"
        description="Average percentage of mandatory field recognition"
      />
    </div>
  </CTCard>
);
export const Default = Template.bind({});
export const WithKPIs = TemplateWithKpis.bind({});

Default.args = {
  title: 'Artificial Intelligence',
  icon: 'acquisition',
};

WithKPIs.args = {
  title: 'Artificial Intelligence',
  icon: 'acquisition',
};
