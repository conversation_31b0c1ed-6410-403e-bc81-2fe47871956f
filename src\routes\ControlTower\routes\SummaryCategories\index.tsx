import React from 'react';
import { useHistory, useRouteMatch } from 'react-router';
import CTCard from 'components/CTCard';
import KpiItem from 'components/KpiItem';
import { SummaryCategory, Kpi } from 'models/response';
import styled from 'styled-components/macro';
import { URLS } from 'utils/constants';
import utils from 'utils';
import { CategoryDetailsFilters } from 'models/request';
import { SearchParams, ViewMode } from 'routes/ControlTower/interfaces';
import { createFiltersObject } from 'routes/ControlTower/utils';

const Wrapper = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 20px;
`;

interface SummaryCategories {
  loadCategoryDetails: (params: CategoryDetailsFilters) => Promise<void>;
  data: SummaryCategory[];
  searchParams?: SearchParams;
  viewMode: ViewMode;
  message: string;
}

const SummaryCategories = ({ data, loadCategoryDetails, searchParams, viewMode, message }: SummaryCategories) => {
  const { path } = useRouteMatch();
  const history = useHistory();
  const t = utils.intl.useTranslator();

  const openCategory = (id: number) => {
    history.push({
      pathname: `${path}/${URLS.controlTower.detailCategories}`,
      state: {
        categoryId: id,
      },
    });
  };

  if (data.length <= 0 && message) {
    return <Wrapper>{message}</Wrapper>;
  }

  return (
    <Wrapper>
      {data.map((category: SummaryCategory) => {
        const { idCategory, name, kpis, icon } = category || {};

        const params = createFiltersObject(searchParams);

        return (
          <CTCard
            action={() => {
              loadCategoryDetails({ ...params, idCategory, allInOneView: viewMode === 'table' });
              openCategory(idCategory);
            }}
            key={idCategory}
            title={name}
            icon={icon}
          >
            {kpis.map((kpi: Kpi) => {
              const { description, idMetric, formatType, result, timeframePeriod } = kpi || {};
              const translatedDescription = utils.app.textTranslated(description, t) || '';

              const units = {
                PERCENTAGE: '%',
                TIMEFRAME: utils.app.textTranslated(timeframePeriod, t) || '',
              };

              const unit = units[formatType as keyof typeof units];

              return (
                <KpiItem
                  key={idMetric}
                  description={translatedDescription}
                  unit={unit}
                  value={result}
                  style={{ paddingTop: '20px', minHeight: '130px' }}
                />
              );
            })}
          </CTCard>
        );
      })}
    </Wrapper>
  );
};

export default SummaryCategories;
