import Checkbox from 'components/Buttons/CheckboxButton';
import React, { useMemo, useCallback } from 'react';
import styled from 'styled-components/macro';
import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';

type Props = {
  options: { value: string; label: string }[];
  open: Boolean;
  setOpen?: (open: Boolean) => void;
  selected: string[];
  setSelected: React.Dispatch<React.SetStateAction<string[]>>;
  onChangeFilter: (filter: string) => void;
};

const MultiFieldDiv = styled.div<{ open?: Boolean }>`
  position: absolute;
  width: fit-content;
  border-radius: 15px;
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.white};
  z-index: 120000;
  padding: 7px;
  display: ${({ open }) => (open ? 'block' : 'none')};
  border: 1px solid ${({ theme }) => theme.colorPalette.turquoise.normal};
  > div.optionList {
    overflow-y: auto;
    scrollbar-width: auto;
    max-height: 200px;
    margin-bottom: 10px;
  }
  > div.searchButton button {
    width: 100%;
  }
`;

const List = styled.ul`
  white-space: nowrap;
  list-style: none;
  margin: 0;
  &::-webkit-scrollbar {
    width: 2px;
  }
  > .select-deselect-all-section > button {
    display: block;
  }
  max-width: 400px;
`;
const Element = styled.li`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding: 5px 15px 5px 5px;
  gap: 5px;
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-family: Roboto;
  font-size: ${({ theme }) => theme.fontSizePalette.small};
  letter-spacing: -0.38px;
  line-height: 14px;
  width: 100%;
  height: 100%;
`;

const Label = styled.label`
  color: inherit;
  text-decoration: underline 0.15em #4d4d4d00;
  transition: text-decoration-color 300ms ease-in-out;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  &:hover {
    text-decoration-color: ${({ theme }) => theme.colorPalette.grey.grey9};
  }
`;

function MultiField({ options, open, selected, setSelected, onChangeFilter }: Props) {
  const t = utils.intl.useTranslator();
  const [checkValues, setCheckValues] = React.useState<{
    [key: string]: boolean;
  }>(selected.reduce((acc, curr) => ({ ...acc, [curr]: true }), {}));

  const onChangeItem = useCallback(
    (i: string, checked: boolean) => {
      if (checked) {
        const items = [...selected, i];
        setCheckValues({ ...checkValues, [i]: true });
        setSelected(items);
        return;
      }
      const items = selected.filter((item) => item !== i);
      setCheckValues({ ...checkValues, [i]: false });
      setSelected(items);
    },
    [selected, setSelected, checkValues, setCheckValues],
  );

  const onChangeAll = useCallback(
    (setter: boolean) => {
      // mapping all the option and add void string to array if setter is true else empty array
      setSelected(setter ? [...options.map((option) => option.value)] : []);
      // setting the checkValues to setter
      setCheckValues(options.reduce((acc, curr) => ({ ...acc, [curr.value]: setter }), {}));
      if (!setter) {
        onChangeFilter('');
      }
    },
    [setSelected, options, onChangeFilter],
  );

  const listOfOptions = useMemo(
    () => (
      <List>
        <div className="select-deselect-all-section">
          <CommonButton
            id="all"
            action={() => onChangeAll(true)}
            scope="tertiary"
            disabled={selected.length === options.length}
            value={t('multifilter-select-all')}
          />
          <CommonButton
            id="all"
            action={() => onChangeAll(false)}
            scope="tertiary"
            disabled={selected.length === 0}
            value={t('multifilter-clear-all')}
          />
        </div>
        {options.map((option, i) => {
          return (
            <Element key={option.value} value={i + 1}>
              <Checkbox
                id={option.value}
                isActive={checkValues[option.value] ?? false}
                action={(_isActive, e) => {
                  const input = e?.target as HTMLInputElement;
                  onChangeItem(input.id, input.checked);
                }}
              />
              <Label title={option.label} htmlFor={option.value}>
                {option.label}
              </Label>
            </Element>
          );
        })}
      </List>
    ),
    [options, selected, onChangeAll, t, onChangeItem, checkValues],
  );

  return (
    <MultiFieldDiv open={open}>
      <div className="optionList">{listOfOptions}</div>
      <div className="searchButton">
        <CommonButton action={() => onChangeFilter(selected.join(','))} scope="tertiary" value={t('search')} />
      </div>
    </MultiFieldDiv>
  );
}

export default MultiField;
