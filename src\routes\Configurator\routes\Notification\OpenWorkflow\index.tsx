import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useAsync } from 'react-use';
import services from 'services';
import actions from 'state/actions';
import selectors from 'state/selectors';
import utils from 'utils';
import { Formik, FormikState } from 'formik';
import { CheckboxLabel, InputLabel } from 'routes/Configurator/styles';
import styled from 'styled-components';
import FormOpenWorkflow from './FormOpenWorkflow';
import { AddOpenWfNotificationSettings } from 'models/request';
import { generateCronExpression } from '../utils';
import TableOpenWorkflow from './TableOpenWorkflow';
import { FormValues, setInitialValues, validationLogic } from './openWorkflowFormUtils';

export const ExtendedInputLabel = styled(InputLabel)`
  width: 200px;
  white-space: normal;
  overflow: hidden;
`;
export const ExtendedCheckboxLabel = styled(CheckboxLabel)`
  width: 200px;
  white-space: normal;
  overflow: hidden;
`;

const OpenWorkflowNotifications = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const formMode = useSelector(selectors.configurator.getFormModeOpenWf);
  const selectedRow = useSelector(selectors.configurator.getSelectedOpenWfSettings);
  const openWorkflowList = useSelector(selectors.configurator.getListOfOpenWfSettings);
  const templatesList = useSelector(selectors.configurator.getTemplatesMailNotification);

  const [userList, setUserList] = useState<{ idUser: number; name: string }[]>([]);

  useAsync(async () => {
    try {
      const { data } = await services.getOpenWfNotificationSettings();
      dispatch(actions.configurator.setListOfOpenWfSettings(data));
    } catch (e) {
      console.error(e);
    }
  }, []);

  useAsync(async () => {
    if (formMode !== 'add') return;
    const { data } = await services.getUsersForOpenWfNotificationSettings();
    setUserList(data);
  }, [formMode]);

  const onSubmit = async (
    values: FormValues,
    resetForm: (nextState?: Partial<FormikState<FormValues>> | undefined) => void,
  ) => {
    const {
      user,
      openWfSendMail,
      openWfSendNotificationImmediately,
      openWfMaxDocuments,
      openWfMaxDocumentDetails,
      idTemplateImmediateOpenWorkflow,
      idTemplateScheduledOpenWorkflow,
      hour,
      minute,
      allDays,
      daysOfWeek,
    } = values;

    if (!user || !idTemplateImmediateOpenWorkflow || !idTemplateScheduledOpenWorkflow) return;

    const bodyRequest: AddOpenWfNotificationSettings = {
      idUser: user.value,
      openWfSendMail,
      openWfSendNotificationImmediately,
      openWfMaxDocuments,
      openWfMaxDocumentDetails,
      idTemplateImmediateOpenWorkflow: idTemplateImmediateOpenWorkflow?.value,
      idTemplateScheduledOpenWorkflow: idTemplateScheduledOpenWorkflow?.value,
      openWfFrequency: generateCronExpression(hour, minute, allDays, daysOfWeek),
    };
    try {
      if (formMode === 'add') {
        await services.addOpenWfNotificationSettings(bodyRequest);
        utils.app.notify('success', t('user-notification-created'));
        dispatch(
          actions.configurator.setListOfOpenWfSettings([
            {
              name: user.label,
              ...bodyRequest,
            },
            ...openWorkflowList,
          ]),
        );
      }
      if (formMode === 'edit') {
        await services.editOpenWfNotificationSettings(bodyRequest);
        const newList = openWorkflowList.map((e) =>
          e.idUser === bodyRequest.idUser ? { name: user.label, ...bodyRequest } : e,
        );
        dispatch(actions.configurator.setListOfOpenWfSettings(newList));
        dispatch(actions.configurator.setSelectedOpenWfSettings(null));
        utils.app.notify('success', t('user-notification-edited'));
      }
    } catch (e) {
      console.error(e);
    } finally {
      dispatch(actions.configurator.setFormModeOpenWf(null));
      resetForm();
    }
  };

  return (
    <>
      <h2>{t('Open-Workflow')}</h2>
      <TableOpenWorkflow />
      <Formik
        initialValues={setInitialValues(selectedRow, formMode, t, templatesList)}
        onSubmit={(values, { resetForm }) => {
          onSubmit(values, resetForm);
        }}
        validationSchema={validationLogic(t)}
        enableReinitialize
      >
        {({ values, isValid, isValidating, dirty, setFieldValue, submitForm }) => (
          <div>
            {formMode && (
              <FormOpenWorkflow
                values={values}
                setFieldValue={setFieldValue}
                dirty={dirty}
                isValid={isValid}
                isValidating={isValidating}
                submitForm={submitForm}
                userList={userList}
                templatesList={utils.input.buildOptions(templatesList, 'templateName', 'idMailTemplate', true)}
              />
            )}
          </div>
        )}
      </Formik>
    </>
  );
};
export default OpenWorkflowNotifications;
