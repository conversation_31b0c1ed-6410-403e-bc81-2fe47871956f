import Modal from 'components/Modal';
import React from 'react';
import utils from 'utils';
import selectors from 'state/selectors';
import services, { verifyErrorResponse } from 'services';
import { useDispatch, useSelector } from 'react-redux';
import RadioButton from 'components/Buttons/RadioButton';
import {
  checkMicrocategoryFunc,
  checkVendorListFunc,
  searchFunc,
} from 'routes/Configurator/routes/ActionTables/Po/components/utils';
import { ExpActionsTable } from 'models/request';
import actions from 'state/actions';
import { PoMicroCategoryList, PoVendorList } from 'models/response';

type Row = {
  companyName: string;
  workflowDefinition: string | number; // if it is not changed it will be a string
  wfCausal: number | string; // if it is not changed it will be a string
  fullOcr: boolean;
  id: number;
  microcategoryCode: string;
  microcategoryDescription: string;
};

function SelectCompanies() {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const allRows: any[] = useSelector(selectors.configurator.getLeftTableRows);
  const { row }: { row: Row } = useSelector(selectors.modal.getModalProps);
  const [allCompaniesSelected, setAllCompaniesSelected] = React.useState<string | null>(null);
  const selectedTable = useSelector(selectors.configurator.selectTableToSearch);
  const companiesSelected = useSelector(selectors.configurator.getSelectedCompanies);
  const selectedOptionValue = useSelector(selectors.configurator.getSelectedOptionValue);
  const notModifiedRow = useSelector(selectors.configurator.getNotModifiedRow);
  const wfTaskDefinitions = useSelector(selectors.configurator.getWfDefinitions);
  const wfCasuals = useSelector(selectors.configurator.getWfCasuals);
  const allAuthorizedCompanies = useSelector(selectors.configurator.getATcompanies);

  const checkMicroCategory = checkMicrocategoryFunc(selectedOptionValue);
  const checkVendorList = checkVendorListFunc(selectedOptionValue);
  const search = searchFunc(selectedTable, companiesSelected, checkMicroCategory, dispatch, checkVendorList);

  const onSaveAction = async () => {
    if (notModifiedRow === undefined) throw new Error('notModifiedRow is missing');
    try {
      let wfIdWorkflowDefinition: number | null;
      let wfIdCausal: number | null;
      if (typeof row.workflowDefinition === 'string') {
        const chosenDefinition = wfTaskDefinitions.find((definition) => definition.label === row.workflowDefinition);
        if (chosenDefinition === undefined) {
          wfIdWorkflowDefinition = notModifiedRow.wfIdWorkflowDefinition;
        } else {
          wfIdWorkflowDefinition = chosenDefinition.value;
        }
      } else {
        wfIdWorkflowDefinition = row.workflowDefinition;
      }
      if (typeof row.wfCausal === 'string') {
        const chosenCausal = wfCasuals.find((causal) => causal.label === row.wfCausal);
        if (chosenCausal === undefined) {
          wfIdCausal = notModifiedRow.wfIdCausal;
        } else {
          wfIdCausal = chosenCausal.value;
        }
      } else {
        wfIdCausal = row.wfCausal;
      }
      const idAction: number = allRows.find((originalRow) => originalRow.id === String(row.id))?.idAction;
      if (idAction === undefined) return;
      const expActionsTable: ExpActionsTable = {
        idAction: idAction,
        fullOcr: row.fullOcr,
        wfIdCausal: wfIdCausal,
        wfIdWorkflowDefinition: wfIdWorkflowDefinition,
        corporateApproval: false,
      };
      if (allCompaniesSelected === 'false') {
        await services.editExpActionTable({
          companyNames: [row.companyName],
          expActionsTable,
        });
      } else {
        const { data } = await services.editExpActionTable({
          companyNames: allAuthorizedCompanies.map((company) => company.companyName),
          expActionsTable: expActionsTable,
        });
        if (data.length) utils.app.notify('warning', t('error-not-edited-company') + ' ' + data.toString());
      }
      await search();
      utils.app.notify('success', t('editSuccess'));
    } catch (error) {
      // set the old row in the table
      const allRowsCopy = [...allRows] as PoMicroCategoryList[] | PoVendorList[];
      const index = allRowsCopy.findIndex((originalRow: any) => originalRow.id === String(row.id));
      if (index === -1 || notModifiedRow === undefined) return;
      allRowsCopy[index] = notModifiedRow;
      dispatch(actions.configurator.setLeftTableRows(allRowsCopy));
      verifyErrorResponse(error, 'editError', t);
    }
  };

  return (
    <div>
      <Modal.Header title={t('applyChangesTitle')} />
      <Modal.Content>
        <h4>{t('applyChanges')}</h4>
        <RadioButton
          options={[
            { value: 'false', label: t('single company') },
            { value: 'true', label: t('multiple companies') },
          ]}
          onChange={(e) => {
            setAllCompaniesSelected(e);
          }}
        ></RadioButton>
      </Modal.Content>
      <Modal.Footer
        confirmText={t('confirm')}
        confirmAction={onSaveAction}
        confirmDisabled={allCompaniesSelected === null}
        withCloseButton={false}
      />
    </div>
  );
}

export default SelectCompanies;
