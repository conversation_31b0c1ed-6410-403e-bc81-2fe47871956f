import React from 'react';
import CommonButton from '../Buttons/CommonButton';
import { act } from 'react-dom/test-utils';
import { unmountComponentAtNode } from 'react-dom';
import { colorPalette } from 'utils/styleConstants';
import { hex2Rgb, renderWithStyle } from 'utils/helpers/test.helpers';

let container = null;
beforeEach(() => {
  // setup a DOM element as a render target
  container = document.createElement('div');
  document.body.appendChild(container);
});

afterEach(() => {
  // cleanup on exiting
  unmountComponentAtNode(container);
  container.remove();
  container = null;
});

it('Common button render the inner text', () => {
  const { container } = renderWithStyle(<CommonButton value="test" />);
  expect(container.textContent).toBe('test');
});

it('Common button prevent the action trigger when disabled', () => {
  const action = jest.fn();
  renderWithStyle(<CommonButton value="test" disabled action={action} />);

  const button = document.querySelector('button');
  act(() => {
    button.dispatchEvent(new MouseEvent('click', { bubbles: true }));
  });

  expect(action).toHaveBeenCalledTimes(0);
});

it('Common button call the action when not disabled', () => {
  const action = jest.fn();
  renderWithStyle(<CommonButton value="test" action={action} />);

  const button = document.querySelector('button');
  act(() => {
    button.dispatchEvent(new MouseEvent('click', { bubbles: true }));
  });

  expect(action).toHaveBeenCalledTimes(1);
});

it('Common button primary has the rigth colors', () => {
  renderWithStyle(<CommonButton value="test" scope="primary" />);

  const button = document.querySelector('button');
  const styles = getComputedStyle(button);

  expect(styles.color).toBe(hex2Rgb(colorPalette.turquoise.dark));
  expect(styles.backgroundColor).toBe(hex2Rgb(colorPalette.turquoise.light));
});

it('Common button secondary has the rigth colors', () => {
  renderWithStyle(<CommonButton value="test" scope="secondary" />);

  const button = document.querySelector('button');
  const styles = getComputedStyle(button);

  expect(styles.color).toBe(hex2Rgb(colorPalette.turquoise.dark));
});

it('Common button tertiary has the rigth colors', () => {
  renderWithStyle(<CommonButton value="test" scope="tertiary" />);

  const button = document.querySelector('button');
  const styles = getComputedStyle(button);

  expect(styles.color).toBe(hex2Rgb(colorPalette.turquoise.normal));
  expect(styles.backgroundColor).toBe('transparent');
});

it('Common button render the inner tooltip', () => {
  renderWithStyle(<CommonButton value="test" tooltip="test-tooltip" scope="tertiary" />);

  const button = document.querySelector('button');
  expect(button.title).toBe('test-tooltip');
});

it('Common button secondary with icon has no paddings', () => {
  renderWithStyle(<CommonButton value="test" icon={true} noPadding={true} scope="secondary" />);

  const button = document.querySelector('button');
  const styles = getComputedStyle(button);

  expect(styles.padding).toBe('1px');
});

it('Common button custom fixed width', () => {
  renderWithStyle(<CommonButton value="test" scope="secondary" customFixedWidth="140px" />);

  const button = document.querySelector('button');
  const styles = getComputedStyle(button);

  expect(styles.width).toBe('140px');
});
