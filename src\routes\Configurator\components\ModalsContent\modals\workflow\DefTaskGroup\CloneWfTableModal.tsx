/* eslint-disable max-lines */
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components/macro';

import utils from 'utils';
import actions from 'state/actions';

import CommonButton from 'components/Buttons/CommonButton';
import Header from 'components/Modal/Header';
import { TextInput } from 'components/Input';
import services from 'services';
import selectors from 'state/selectors';
import axios from 'axios';
import { CompanyGroups, WorkflowCompaniesDefinitionList } from 'models/response';
import { definitionTaskGroupTabs } from 'utils/constants';
import { mutate } from 'swr';

export const ButtonContainer = styled.div`
  text-align: center;
  button {
    margin: 10px;
  }
  margin-top: 10px;
`;

const CloneWfTableModal = () => {
  const dispatch = useDispatch();

  const t = utils.intl.useTranslator();

  // wf definition
  const [wfName, setWfName] = useState<string>('');

  // wf group
  const [groupName, setGroupName] = useState<string>('');
  const [groupDesc, setGroupDesc] = useState<string>('');

  const selectedDefinitionFromCompanySelected = useSelector(
    selectors.configurator.getSelectedDefinitionFromCompanySelected,
  );
  const listOfDefinitions = useSelector(selectors.configurator.getListOfDefinitionsForCompanySelected);
  const selectedDefinitionTaskGroup = useSelector(selectors.configurator.getSelectedDefinitionTaskGroup);
  const selectedGroupFromCompanySelected = useSelector(selectors.configurator.getSelectedGroupFromCompanySelected);
  const selectedCompanyDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);

  const { listOfDefinitionsForAssociations } = services.useGetCompanyDefinitionsForAssociation(
    'getCompanyDefinitionForAssociationsKey',
    selectedCompanyDefinition?.idCompany,
  );
  const { listOfGroupsForAssociations } = services.useGetGroupsForTaskAssociations(
    'getCompanyGroupsForAssociationsKey',
    selectedCompanyDefinition?.idCompany,
  );

  const { groups } = services.useGetCompanyGroups('getCompanyGroupsKey', selectedCompanyDefinition?.idCompany);

  const cloneDefinition = async () => {
    const idDefinitionToClone = selectedDefinitionFromCompanySelected?.idDefinition;
    if (idDefinitionToClone) {
      const { data } = await services.cloneWfDefinition(idDefinitionToClone, wfName);
      const newRow: WorkflowCompaniesDefinitionList = {
        idDefinition: data.idDefinition,
        companyName: selectedDefinitionFromCompanySelected?.companyName || '',
        wfName: data.wfName,
        suspendType: data.suspendType,
        noteMandatory: data.noteMandatory,
        currencyExchange: data?.currencyExchange,
        takeNextSingleTask: data.takeNextSingleTask,
        taskOnMinCausalWeight: data.taskOnMinCausalWeight,
        taskList: selectedDefinitionFromCompanySelected?.taskList || [],
      };

      dispatch(actions.configurator.setListOfDefinitionsForCompanySelected([newRow, ...listOfDefinitions]));
      mutate(
        'getCompanyDefinitionForAssociationsKey',
        [
          {
            idDefinition: data.idDefinition,
            wfName: data.wfName,
            wfDesc: data.wfName,
            idCompany: selectedCompanyDefinition?.idCompany || undefined,
            suspendType: data.suspendType,
            noteMandatory: data.noteMandatory,
            currencyExchange: data?.currencyExchange,
            takeNextSingleTask: data.takeNextSingleTask,
            taskOnMinCausalWeight: data.taskOnMinCausalWeight,
          },
          ...(listOfDefinitionsForAssociations || []),
        ],
        {
          revalidate: false,
        },
      );

      utils.app.notify('success', t('wf-definition-cloned'));
    }
  };

  const cloneGroup = async () => {
    if (selectedGroupFromCompanySelected) {
      const idGroupToCopy = selectedGroupFromCompanySelected?.idGroup;
      if (idGroupToCopy) {
        const { data } = await services.cloneWfGroup(idGroupToCopy, groupName, groupDesc);
        const newRow: CompanyGroups = {
          idGroup: data,
          groupName,
          groupDesc,
        };

        const fromClone = listOfGroupsForAssociations?.find((el) => el.idGroup === idGroupToCopy);

        mutate(
          'getCompanyGroupsForAssociationsKey',
          [
            {
              idGroup: data,
              groupName: groupName,
              groupDesc: groupDesc,
              assigned: false,
              relation: fromClone?.relation,
              amountLimit: fromClone?.amountLimit,
              currency: fromClone?.currency,
              oUserList: fromClone?.oUserList,
            },
            ...(listOfGroupsForAssociations || []),
          ],
          {
            revalidate: false,
          },
        );

        mutate('getCompanyGroupsKey', [newRow, ...(groups || [])], {
          revalidate: false,
        });

        utils.app.notify('success', t('wf-group-cloned'));
      }
    }
  };

  const cloneConfirm = async () => {
    try {
      switch (selectedDefinitionTaskGroup) {
        case definitionTaskGroupTabs.WORKFLOW_DEFINITION:
          cloneDefinition();
          break;
        case definitionTaskGroupTabs.WORKFLOW_GROUP:
          cloneGroup();
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        utils.app.notify('fail', `${t('error')} ${error.response}`);
        return;
      }
      console.error(error);
    } finally {
      dispatch(actions.modal.closeModal());
    }
  };

  const wfDefinition = (
    <>
      <Header title={t('clone-wf-definition')} />
      <TextInput
        name="wfName"
        value={wfName}
        onChange={(e: React.FocusEvent<HTMLInputElement>) => {
          setWfName(e.target.value);
        }}
        label={t('wfName')}
        labelFontSize="16px"
        labelWidth="auto"
        borderRadius="4px"
      />
    </>
  );

  const wfGroup = (
    <>
      <Header title={t('clone-wf-group')} />
      <TextInput
        name="groupName"
        value={groupName}
        onChange={(e: React.FocusEvent<HTMLInputElement>) => {
          setGroupName(e.target.value);
        }}
        label={t('groupName')}
        labelFontSize="16px"
        labelWidth="auto"
        borderRadius="4px"
        margin="0px 0px 10px 0px"
        justifyContent="space-between"
      />
      <TextInput
        name="groupDesc"
        value={groupDesc}
        onChange={(e: React.FocusEvent<HTMLInputElement>) => {
          setGroupDesc(e.target.value);
        }}
        label={t('groupDesc')}
        labelFontSize="16px"
        labelWidth="auto"
        borderRadius="4px"
      />
    </>
  );

  const checkContent = () => {
    switch (selectedDefinitionTaskGroup) {
      case definitionTaskGroupTabs.WORKFLOW_DEFINITION:
        return wfDefinition;
      case definitionTaskGroupTabs.WORKFLOW_GROUP:
        return wfGroup;
      default:
        return wfDefinition;
    }
  };

  const checkDisabled = () => {
    switch (selectedDefinitionTaskGroup) {
      case definitionTaskGroupTabs.WORKFLOW_DEFINITION:
        if (wfName.length === 0) return true;
        break;
      case definitionTaskGroupTabs.WORKFLOW_GROUP:
        if (groupName.length === 0 || groupDesc.length === 0) return true;
        break;
    }
    return false;
  };

  return (
    <>
      {checkContent()}
      <ButtonContainer>
        <CommonButton value={t('continua')} action={() => cloneConfirm()} disabled={checkDisabled()} />
        <CommonButton
          scope="secondary"
          value={t('close')}
          action={() => {
            dispatch(actions.modal.closeModal());
          }}
        />
      </ButtonContainer>
    </>
  );
};

export default CloneWfTableModal;
