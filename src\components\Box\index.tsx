import { FontAwesomeIconProps } from '@fortawesome/react-fontawesome';
import MainButton from 'components/Buttons/CommonButton';
import React from 'react';
import styled from 'styled-components/macro';

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  flex-grow: 1;
  margin-bottom: 10px;
  align-items: center;
`;

const Content = styled.div`
  border: 1px solid ${({ theme }) => theme.colorPalette.grey.grey5};
  min-height: 120px;
  border-radius: 4px;
  padding: 25px 15px;
`;

const TitleSection = styled.h4`
  font-size: ${({ theme }) => theme.fontSizePalette.heading.H4};
  color: ${({ theme }) => theme.colorPalette.grey.grey10};
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
`;

export const WrapperBox = styled.div<{ visible?: boolean }>`
  ${(props) => !props.visible && 'display: none'};
`;

export interface ButtonProperty {
  buttonLabel?: string;
  isButtonActive?: boolean;
  buttonIcon?: FontAwesomeIconProps['icon'];
  buttonAction?: (e: React.MouseEvent<HTMLButtonElement>) => void;
}

export interface Props {
  title: string;
  hasButton?: boolean;
  children: React.ReactChild;
  visible?: boolean;
  buttonsProperty?: ButtonProperty[];
}

const Box = (props: Props) => {
  const { title, hasButton = false, visible = true, buttonsProperty } = props;
  return (
    <WrapperBox visible={visible}>
      <Header>
        <TitleSection>{title}</TitleSection>
        {hasButton && buttonsProperty?.length && (
          <div>
            {buttonsProperty?.map((button, index) => (
              <MainButton
                key={index}
                value={button.buttonLabel}
                scope="tertiary"
                noPadding={true}
                action={button.buttonAction}
                icon={button.buttonIcon}
                disabled={!button.isButtonActive}
              />
            ))}
          </div>
        )}
      </Header>
      <Content>{props.children}</Content>
    </WrapperBox>
  );
};

export default Box;
