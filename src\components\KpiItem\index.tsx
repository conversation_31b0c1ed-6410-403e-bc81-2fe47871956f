import React, { CSSProperties } from 'react';
import { ValueWrapper, Description, Dot, DataWrapper, Value, ValueUnit } from './style';
import utils from 'utils';

export interface KpiProps {
  value?: number | string | null;
  unit?: string;
  description?: string;
  dotColor?: string;
  variant?: 'dot';
  style?: CSSProperties;
}

const KpiItem = ({ description, value, dotColor, unit, variant, style }: KpiProps) => {
  const t = utils.intl.useTranslator();

  return (
    <div style={style}>
      <DataWrapper>
        {variant === 'dot' && <Dot className="dot" color={dotColor} />}
        <ValueWrapper variant={variant}>
          {value !== undefined && value !== null ? (
            <>
              <Value variant={variant}>{value}</Value>
              {unit && <ValueUnit variant={variant}>{unit}</ValueUnit>}
            </>
          ) : (
            <Value>{utils.app.textTranslated('n/a', t)}</Value>
          )}
        </ValueWrapper>
      </DataWrapper>
      {description && <Description variant={variant}>{description}</Description>}
    </div>
  );
};

export default KpiItem;
