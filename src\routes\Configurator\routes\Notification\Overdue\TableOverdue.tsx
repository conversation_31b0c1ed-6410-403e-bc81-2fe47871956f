import Checkbox from 'components/Buttons/CheckboxButton';
import Table, { TableColumn } from 'components/Table/Table';
import { IOverdueNotifications } from 'models/response';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import selectors from 'state/selectors';
import utils from 'utils';
import CronCell from '../components/CronCell';
const TableOverdue = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const overdueList = useSelector(selectors.configurator.getListOfOverdueSettings);
  const formMode = useSelector(selectors.configurator.getFormModeOverdue);

  const notificationColumns: TableColumn[] = [
    { accessor: 'idUser', Header: t('idUser'), filterType: 'free' },
    { accessor: 'name', Header: t('name'), filterType: 'free' },
    {
      accessor: 'overDueSendMail',
      Header: t('overDueSendMail'),
      sortType: 'boolean',
      Cell: ({ value }: any) => {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox isActive={value} isEditable={false} />
          </div>
        );
      },
    },
    {
      accessor: 'overDueFrequency',
      Header: t('overDueFrequency'),
      Cell: ({ value }: any) => {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            <CronCell value={value} />
          </div>
        );
      },
    },
    { accessor: 'overDueMaxDocuments', Header: t('overDueMaxDocuments'), filterType: 'free' },
    { accessor: 'idTemplateScheduledOverdue', Header: t('idTemplateScheduledOverdue'), filterType: 'free' },
  ];

  const onUserSelection = (rows: IOverdueNotifications[]) => {
    dispatch(actions.configurator.setSelectedOverdueSettings(rows[0]));
  };

  return (
    <Table
      rowId="idUser"
      hasToolbar
      columns={notificationColumns}
      rows={overdueList}
      hasSelection={!formMode}
      hasPagination
      hasResize
      hasSort
      hasFilter
      onSelection={(rows: IOverdueNotifications[]) => onUserSelection(rows)}
    />
  );
};
export default TableOverdue;
