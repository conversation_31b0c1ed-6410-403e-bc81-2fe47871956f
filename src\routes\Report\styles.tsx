import styled from 'styled-components/macro';

export const Container = styled.div`
  padding: 20px;
  overflow: auto;
`;

export const Title = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  > * {
    margin-right: 10px;
  }
`;

export const InputContainer = styled.div`
  display: flex;
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  padding-bottom: 15px;
  align-items: center;
  label {
    color: ${({ theme }) => theme.colorPalette.grey.grey9};
    padding-right: 15px;
    font-size: ${({ theme }) => theme.fontSizePalette.xSmall};
  }
`;

export const ColumnsWrapper = styled.div`
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  padding-bottom: 15px;
  align-items: center;
  label {
    padding-right: 15px;
    font-size: ${({ theme }) => theme.fontSizePalette.xSmall};
  }
`;
export const CheckBoxWrapper = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 20px;
`;
export const CheckText = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.XS};
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  margin-left: 10px;
`;
