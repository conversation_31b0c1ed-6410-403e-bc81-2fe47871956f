import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { actionType } from 'utils/constants';
import selectors from 'state/selectors';
import actions from 'state/actions';
import CommonButton from 'components/Buttons/CommonButton';
import utils from 'utils';
import { modalActionType } from 'utils/constants';
import services from 'services';

const BlockUsers = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const selectedUser = useSelector(selectors.configurator.getSelectedUser);

  const onConfirmBlockUser = async () => {
    dispatch(actions.configurator.setFormMode(null));
    if (selectedUser) {
      try {
        const { data } = await services.deleteUser({ idUser: selectedUser.idUser.toString() });
        utils.app.notify('success', data.message);
        dispatch(
          actions.configurator.updateTableRows({
            idUsers: [selectedUser].map((e) => e.idUser),
            property: 'blocked',
            value: 1,
          }),
        );
        dispatch(actions.modal.closeModal());
        dispatch(actions.configurator.setSelectedUser(null));
      } catch (e) {
        console.error(e);
      }
    }
  };

  return (
    <CommonButton
      action={() =>
        dispatch(
          actions.modal.setModal({
            actionType: modalActionType.configurator.DELETE_CONFIRMATION,
            props: {
              subtitle: `${t('Are you sure to block')} ${selectedUser?.username ? selectedUser.name : t('this user')}?`,
              func: () => onConfirmBlockUser(),
            },
          }),
        )
      }
      disabled={
        !(selectedUser && selectedUser.blocked === 0 && utils.user.isActionByCompanyActive(actionType.BLOCK_USER))
      }
      scope="tertiary"
      value={t('delete-user')}
      icon="circle"
    />
  );
};

export default BlockUsers;
