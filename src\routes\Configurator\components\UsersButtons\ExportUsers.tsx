import React from 'react';
import { useSelector } from 'react-redux';
import { actionType } from 'utils/constants';

import utils from 'utils';
import selectors from 'state/selectors';

import CommonButton from 'components/Buttons/CommonButton';

const ExportUsers = () => {
  const t = utils.intl.useTranslator();
  const users = useSelector(selectors.configurator.getUsers);

  const exportUsers = () => {
    const header: { accessor: string; label: string; converter?: (value: string) => string }[] = [
      {
        accessor: 'blocked',
        label: t('blocked'),
        converter: (value: string) => (value === '1' ? t('disabled user') : t('operative user')),
      },
      { accessor: 'username', label: t('username') },
      { accessor: 'name', label: t('name') },
      { accessor: 'email', label: t('email') },
    ];

    const body =
      users.map((row) => {
        const enhancedRow: { [key: string]: string } = {};
        const rowIDs = Object.keys(row);
        rowIDs.forEach((rowID) => {
          enhancedRow[rowID] = row[rowID] || '';
        });
        return enhancedRow;
      }) || [];

    const csv = utils.file.convertToCSV(header, body);
    utils.file.downloadCsv('users', csv);
  };

  return (
    <CommonButton
      action={exportUsers}
      scope="tertiary"
      value={t('export-users')}
      icon="circle"
      disabled={!utils.user.isActionByCompanyActive(actionType.EXPORT_USER)}
    />
  );
};

export default ExportUsers;
