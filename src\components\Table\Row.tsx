import React from 'react';
import { Row as IRow, Cell as ICell, TableInstance } from 'react-table';
import Cell from './Cell';

interface RowProps {
  row: IRow;
  prepareRow: TableInstance['prepareRow'];
  handleClick: (e: React.MouseEvent<HTMLTableRowElement, MouseEvent>, row: IRow) => void;
  fixedColumns: number;
  hasSelection?: boolean;
  hasMultiSelection?: boolean;
  style?: React.CSSProperties;
  editRowID?: string;
}

const Row: React.FC<RowProps> = (props) => {
  const { row, prepareRow, handleClick, fixedColumns, hasSelection, hasMultiSelection, style, editRowID } = props;
  row && prepareRow(row);
  return (
    <React.Fragment>
      <tr
        className={
          (row as IRow<{ disabled?: boolean }>).original?.disabled === true
            ? 'row-disabled'
            : row.isSelected && (hasSelection || hasMultiSelection)
            ? 'row-selected'
            : row.id === editRowID
            ? 'edited-row'
            : 'row-not-selected'
        }
        onClick={(e) => handleClick(e, row)}
        {...row.getRowProps()}
        style={style}
      >
        {row.cells.map((cell: ICell, index) => (
          <Cell
            key={`${row.id}-${index}-${cell?.column.id}`}
            cellProps={cell.getCellProps}
            index={index}
            width={cell.column.width}
            left={cell.column.totalLeft}
            title={String(cell?.value ?? '')}
            fixedColumns={hasMultiSelection ? fixedColumns + 1 : fixedColumns}
            hasLocalKeys={(cell.column as any)?.hasLocalKeys}
            header={cell.column?.Header as string}
          >
            {cell.render('Cell')}
          </Cell>
        ))}
      </tr>
    </React.Fragment>
  );
};

export default Row;
