import React from 'react';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';
import selectors from 'state/selectors';
import actions from 'state/actions';
import Dropdown from 'components/Input/Dropdown';
import { OTemplate } from 'models/configurator';

export const MapperDropdowns = () => {
  const activeTemplateId = useSelector(selectors.configurator.selectActiveTemplateId);
  const configActions: any = actions.configurator;
  const dispatch = useDispatch();

  const docTypesOption = useSelector(selectors.configurator.selectAllIdDocTypeNames('value', 'label'));
  const activeDocTypeId = useSelector(selectors.configurator.selectMapperIdDocType);
  const activeDocType = useSelector(selectors.configurator.selectActiveDocType);

  const { docName, oTemplateList } = activeDocType || { docName: '', oTemplateList: [] };
  const oTemplateListOptions = (oTemplateList as OTemplate[]).map((Template) => ({
    label: Template.templateName,
    value: Template.idTemplate,
  }));

  const activeDocTypeOption = activeDocType ? { label: docName, value: activeDocTypeId } : undefined;
  const activeOTemplate = useSelector(selectors.configurator.selectActiveOTemplate);
  const activeOTemplateOption = activeOTemplate
    ? { label: activeOTemplate.templateName, value: activeOTemplate.idTemplate }
    : undefined;

  // generic onChange for multiple dropdowns
  const genericOnChange = (value: number | null, action: Function) => {
    dispatch(action(value));
  };

  return (
    <>
      <Dropdown
        disabled={!activeTemplateId}
        placeholder={!activeTemplateId ? 'disabled' : undefined}
        onChange={({ value }) => {
          genericOnChange(value, configActions.setActiveDocTypeId);
          genericOnChange(null, configActions.setActiveOTemplateId);
        }}
        options={docTypesOption}
        value={activeDocTypeOption}
        margin="0px 15px"
      />
      <Dropdown
        disabled={!activeDocType}
        placeholder={!activeDocType ? 'disabled' : undefined}
        onChange={({ value }) => genericOnChange(value, configActions.setActiveOTemplateId)}
        options={oTemplateListOptions}
        value={activeOTemplateOption}
        margin="0px 15px"
      />
    </>
  );
};
