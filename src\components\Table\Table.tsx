/* eslint-disable */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import {
  StyledTable,
  TableContainer,
  BottomContainer,
  StyledCheckboxWrapper,
  StyledHeadCheckboxWrapper,
  ToolbarTableContainer,
  StyledArrow,
} from './styles';
import {
  UseFiltersColumnProps,
  UseTableColumnProps,
  useBlockLayout,
  useFilters,
  usePagination,
  useResizeColumns,
  useRowSelect,
  useSortBy,
  useTable,
  TableToggleAllRowsSelectedProps,
  useExpanded,
  Row as RowType,
} from 'react-table';
import { TableProps as Props, TableColumn as Column, OriginalRow as IRow } from './interfaces';
import Body from './Body';
import Header from './Header';
import Pagination from './Pagination';
import { Select as SelectFilter, Date as DateFilter } from './filters';
import Toolbar from './toolbar';
import Checkbox from 'components/Buttons/CheckboxButton';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { useUpdateEffect, useUnmount } from 'react-use';
import { FixedSizeList as List } from 'react-window';
import Row from './Row';
import ErrorBoundary from 'components/ErrorBoundary';
import EditableCell, { EditRow } from './editable';
import utils from 'utils';
import * as yup from 'yup';
import MultiText from './filters/Multi';
import { colorPalette } from 'utils/styleConstants';
import { cellWithCounter, DefaultCell } from './CellTypes';

// here we built a new row, when we are editing
let modifiedRow: OriginalRow = {};

const Table = (props: Props) => {
  const ctrlPressed = useRef(false);
  const handler = (event: KeyboardEvent, val: boolean) => (ctrlPressed.current = val);
  const [storedFilters, setStoredFilters] = useState<{ id: string; value: any }[]>([]);
  const t = utils.intl.useTranslator();

  useEffect(() => {
    const trueHandler = (e: KeyboardEvent) => handler(e, true);
    window.addEventListener('keydown', trueHandler);
    return () => window.removeEventListener('keydown', trueHandler);
  }, []);

  useEffect(() => {
    const falseHandler = (e: KeyboardEvent) => handler(e, false);
    window.addEventListener('keyup', falseHandler);
    return () => window.removeEventListener('keyup', falseHandler);
  }, []);

  const {
    hasPagination,
    hasFilter = false,
    hasResize,
    hasSort,
    hasSelection,
    hasMultiSelection,
    onSelection,
    onRowClick,
    onRowDoubleClick,
    onResizeEnd,
    initialSelection: selection,
    onSortingChange,
    onRowsChange,
    onChange,
    filters,
    sortBy,
    pageSize = 10,
    pageIndex = 0,
    id,
    rowId = 'id',
    fixedColumns = 0,
    maxHeight = 600,
    resetFiltersDependency,
    onUnmount,
    light = false,
    hasToolbar = false,
    onSave,
    useExpandibleRows = false,
    isEditable = false,
    editRowID,
    editRow,
    onEditRowChange,
    onSaveValidation,
    hasExport = true,
    externalPageIndex,
    setExternalPageIndex,
    openRowId,
    specialColumns,
    paginateExpandedRows,
    toolBarWidth,
    hideHeader = false,
  } = props;

  if (isEditable && !hasSelection) throw new Error('hasSelection must be true if hasEditableRows is active');

  const columns = useMemo(
    () =>
      props.columns.map((column) => {
        const filterType = Object.prototype.hasOwnProperty.call(column, 'filterType') ? column.filterType : null;
        const EditableCellVal = (props: any) => {
          const currentValue =
            String(editRow?.id) === editRowID && editRowID === String(props.row.id)
              ? editRow?.hasOwnProperty(column.accessor as string)
                ? editRow[column.accessor as string]
                : modifiedRow[column.accessor as string] || props.value
              : props.value;
          return (
            <EditableCell
              {...props}
              editRowID={editRowID}
              rowId={rowId}
              // this is used to change the value externally
              value={currentValue}
              accessor={column.accessor}
              onEditRowChange={onEditRowChange}
              modifiedRow={modifiedRow}
            />
          );
        };

        const filter = () => {
          if (column.filter) return column.filter;
          switch (filterType) {
            case 'select':
              return utils.table.selectValueFilterFunction(t);
            case 'free':
            default:
              return utils.table.multiValueFilterFunction(',', '_', t, column.hasLocalKeys, column.Header);
          }
        };

        const cell = () => {
          if (editRowID) return EditableCellVal;
          switch (column.Cell) {
            case 'withCounter':
              return cellWithCounter;
            default:
              return column.Cell ?? DefaultCell;
          }
        };

        const filters = ({ column }: { column: UseFiltersColumnProps<{}> & UseTableColumnProps<{}> & TableColumn }) => {
          switch (filterType) {
            case 'select':
              return <SelectFilter {...column} />;
            case 'date':
              return <DateFilter {...column} />;
            case 'free':
            default:
              return <MultiText {...column} />;
          }
        };
        const sortType = () => {
          switch (column.sortType) {
            case 'boolean':
              return utils.table.booleanSort;
            default:
              return column.sortType ?? 'alphanumeric';
          }
        };
        return {
          ...column,
          Filter: filters,
          Cell: cell(),
          sortType: sortType(),
          filter: filter(),
          // se la table ha hasFilter ma filterType per la column none
          // se la table non ha hasFilter ma per filterType per la comune 'select' or 'search'
          disableFilters: (() => {
            if (hasFilter && filterType === 'none') return true;
            if (!hasFilter && (filterType === 'none' || !filterType)) return true;
            return false;
          })(),
          disableSortBy: column.disableSortBy || false,
        };
      }),
    [props.columns, hasFilter, editRowID, editRow],
  );

  const makeData = useCallback(() => {
    // @todo this make data should add id to expandible rows too so we dont have to add it manually
    const { rows } = props;
    if (rows == null) return [];
    if (!rows?.every((row) => row?.hasOwnProperty(rowId))) {
      console.warn(`Some table's rows have missing ${rowId} property: using keys as unique ${rowId} property.`);
      // @note changing the rowId property to the index of the row have no sense
      return rows.map((row, index) => ({ ...row, [rowId]: index }));
    } else {
      const rowsLength = rows.length;
      const ids = rows.map((row) => String(row[rowId]));
      const uniqueIdsLength = new Set(ids).size;
      if (rowsLength === uniqueIdsLength) {
        return rows;
      } else {
        console.error(`Some table's rows have duplicate ${rowId} identifier property! Must be unique!`);
        throw new Error(`Some table's rows have duplicate ${rowId} identifier property! Must be unique!`);
      }
    }
  }, [props.rows]);

  const [data, setData] = useState(makeData());

  useEffect(() => {
    const newData = makeData();
    setData(newData);
  }, [makeData]);

  const hasCustomFilter = columns.some(
    (column) =>
      Object.prototype.hasOwnProperty.call(column, 'Filter') ||
      Object.prototype.hasOwnProperty.call(column, 'filterType'),
  );

  /*
  initialState.selectedRowIds: Object<rowId: Boolean>
  If a row's ID is set to true in this object, it will have a selected state.
  selectionKey = property's row to be used as id. Default = 'id'
  */
  const getSelectedRowIds = () => {
    const SelectedRowIds: { [key: string]: boolean; [key2: number]: boolean } = {};
    if (selection && selection.length > 0) {
      selection.forEach((id) => {
        let elem: IRow | null = null;
        data.find((row) => {
          if (row[rowId] === id) {
            elem = row;
          } else {
            if (row.subRows?.length) {
              const subElem = row.subRows.find((subRow: IRow) => subRow[rowId] === id);
              if (subElem) {
                elem = subElem;
              }
            }
          }
        });
        if (elem) {
          const idx = elem[rowId] as string | number;
          SelectedRowIds[idx] = true;
        }
      });
    }
    return SelectedRowIds;
  };

  const initialState = useMemo(() => {
    const numberOfPages = data.length / pageSize + 1;
    const pageIndexNumber = pageIndex < numberOfPages ? pageIndex : 0;
    return {
      hiddenColumns: [
        ...props.columns
          .filter((column) => Object.prototype.hasOwnProperty.call(column, 'isVisible') && !column.isVisible)
          .map(({ id }) => id || ''),
      ],
      selectedRowIds: getSelectedRowIds(),
      filters: filters || [],
      sortBy: sortBy || [],
      pageSize,
      pageIndex: pageIndexNumber,
    };
  }, [columns, pageIndex]);

  const defaultColumn: any = useMemo(
    () => {
      return {
        minWidth: 50,
        width: 200,
      };
    },
    [], // lasciare dipendenza vuota!
  );

  const tablePlugIn = [];
  if (hasFilter || hasCustomFilter) tablePlugIn.push(useFilters);
  if (hasSort) tablePlugIn.push(useSortBy);
  if (useExpandibleRows) tablePlugIn.push(useExpanded);
  if (hasPagination) tablePlugIn.push(usePagination);
  tablePlugIn.push(useRowSelect);
  if (hasResize) tablePlugIn.push(useResizeColumns);
  tablePlugIn.push(useBlockLayout);

  const expanded = (row: IRow, page: IRow[] | undefined) => {
    if (page == null) return;
    const rowExpanded = row.subRows.length;
    const indexOfRow = page.findIndex((el: any) => el.id === row.id);
    const pageSizes = pageSize - 1;
    const expandedInPage = pageSizes - indexOfRow;

    if (rowExpanded > expandedInPage) {
      utils.app.notify('warning', t('expanded-items-next-page'));
    }
  };

  const {
    getTableProps,
    getTableBodyProps,
    rows,
    page,
    prepareRow,
    headerGroups,
    selectedFlatRows,
    // pagination
    canPreviousPage,
    canNextPage,
    pageOptions,
    pageCount,
    gotoPage,
    nextPage,
    previousPage,
    state,
    dispatch,
    toggleAllRowsSelected,
    getToggleAllPageRowsSelectedProps,
    setAllFilters,
  } = useTable(
    {
      // @ts-ignore
      columns,
      data,
      initialState,
      defaultColumn,
      autoResetFilters: false,
      autoResetSortBy: false,
      autoResetPage: false,
      paginateExpandedRows,
      // how row's id is obtained
      getRowId: (row: OriginalRow) => row[rowId],
      stateReducer: (newState, action, prevState) => {
        try {
          switch (action.type) {
            case 'onDoubleClick':
              onRowDoubleClick && onRowDoubleClick(action.allRows, action.row);
              return newState;
            case 'columnDoneResizing':
              if (onResizeEnd) {
                const [columnId, width] = Object.entries(
                  newState.columnResizing.columnWidths as Record<string, number>,
                )[0];
                const prevWidth = newState.columnResizing.columnWidth;
                if (prevWidth !== width) onResizeEnd(columnId, width);
              }
              return newState;
            case 'updatePageIndexByFilters':
              return {
                ...newState,
                pageIndex: action.pageIndex,
              };
            case 'onChange':
              onChange && onChange(action.rows);
              return newState;
            case 'setFilter':
            case 'setAllFilters':
              return {
                ...newState,
                pageIndex: 0,
              };
            case 'resetPage':
              return {
                ...newState,
                pageIndex:
                  prevState.pageIndex < Math.ceil(props.rows.length / newState.pageSize) ? prevState.pageIndex : 0,
              };
            case 'update_page_size':
              return {
                ...newState,
                pageSize: action.payload,
              };
            case 'resetSortBy':
              return {
                ...newState,
                sortBy: [],
              };
            case 'update_row_selection':
              return {
                ...newState,
                selectedRowIds: action.payload,
              };
            default:
              return newState;
          }
        } catch (e) {
          console.error(e);
          return prevState;
        }
      },
    },
    (hooks) => {
      hasMultiSelection &&
        hooks.visibleColumns.push((columns) => [
          {
            id: 'selection',
            disableResizing: true,
            width: 40,
            minWidth: 40,
            Header: ({ isAllPageRowsSelected = false }) => {
              return (
                <StyledHeadCheckboxWrapper
                  onClick={(e) => {
                    getToggleAllPageRowsSelectedProps?.().onChange?.(
                      e as unknown as React.ChangeEvent<HTMLInputElement>,
                    );
                    e.stopPropagation();
                  }}
                >
                  <Checkbox
                    name="select-all-records"
                    isActive={isAllPageRowsSelected}
                    isEditable={!(isEditable && editRowID !== '')}
                  />
                </StyledHeadCheckboxWrapper>
              );
            },
            Cell: ({ row }: { row: RowType<{ disabled?: boolean }> }) => {
              const isDisabled = row.original?.disabled || (isEditable && editRowID !== '');
              return (
                <StyledCheckboxWrapper
                  onClick={(e) => {
                    if (isDisabled) return;
                    e.stopPropagation();
                    row.toggleRowSelected();
                  }}
                >
                  <div>
                    <Checkbox
                      name="select-row"
                      isActive={row.getToggleRowSelectedProps().checked}
                      isEditable={!isDisabled}
                    />
                  </div>
                </StyledCheckboxWrapper>
              );
            },
          },
          ...columns,
        ]);
      useExpandibleRows &&
        hooks.visibleColumns.push((columns) => [
          {
            width: 30,
            minWidth: 30,
            id: 'expander',
            Header: ({ getToggleAllRowsExpandedProps, isAllRowsExpanded }) =>
              hideHeader ? (
                <></>
              ) : (
                <StyledArrow
                  onClick={(e) => {
                    if (isAllRowsExpanded) gotoPage(0);
                    getToggleAllRowsExpandedProps?.()?.onClick?.(e);
                  }}
                >
                  {isAllRowsExpanded ? <FontAwesomeIcon icon="angle-up" /> : <FontAwesomeIcon icon="angle-down" />}
                </StyledArrow>
              ),
            Cell: ({ row, page }) =>
              row.canExpand ? (
                <StyledArrow
                  height="30px"
                  width="30px"
                  top="0px"
                  onClick={(e) => {
                    e.stopPropagation();
                    row.toggleRowExpanded();
                  }}
                >
                  {row.isExpanded ? (
                    <FontAwesomeIcon
                      icon="angle-up"
                      onClick={() => {
                        if (row.isSelected) {
                          setTimeout(() => {
                            row.toggleRowSelected(true);
                          }, 10);
                        }
                      }}
                    />
                  ) : (
                    <FontAwesomeIcon
                      icon="angle-down"
                      onClick={() => {
                        expanded(row, page);
                        if (row.isSelected) {
                          setTimeout(() => {
                            row.toggleRowSelected(true);
                          }, 10);
                        }
                      }}
                    />
                  )}
                </StyledArrow>
              ) : null,
          },
          ...columns,
        ]);
    },
    ...tablePlugIn,
  );

  const toggleRow = useCallback(() => {
    const row: OriginalRow | undefined = rows.find((row) => {
      // @ts-ignore
      return Number(row[rowId]) === openRowId;
    });
    if (row) {
      expanded(row, page);
      row.toggleRowExpanded && row?.toggleRowExpanded();
    }
  }, [openRowId]);

  useEffect(() => {
    if (openRowId || openRowId === 0) {
      toggleRow();
    }
  }, [openRowId]);

  const dependencyArray = () => {
    if ((hasMultiSelection || hasSelection) && selectedFlatRows.length === 0) {
      return [`-1`];
    }
    if ((hasMultiSelection || hasSelection) && selectedFlatRows.length === 1) {
      return [`${selectedFlatRows[0].index} - ${selectedFlatRows.length} - ${selectedFlatRows[0].id}`];
    }
    if ((hasMultiSelection || hasSelection) && selectedFlatRows.length > 1) {
      return [`${selectedFlatRows.length}`];
    }
    return [`-1`];
  };

  const updateRowSelection = () => {
    dispatch({
      type: 'update_row_selection',
      payload: getSelectedRowIds(),
    });
  };

  useEffect(() => {
    // @note we could directly change the page index
    // by finding the current position of edited row
    externalPageIndex != null && gotoPage(externalPageIndex);
    setExternalPageIndex?.(undefined);
  }, [externalPageIndex]);

  const stableUpdateRowSelection = useCallback(updateRowSelection, [selection, rows.length]);
  useEffect(() => {
    stableUpdateRowSelection();
  }, [stableUpdateRowSelection]);

  useUpdateEffect(() => {
    if ((hasSelection || hasMultiSelection) && onSelection && selectedFlatRows) {
      onSelection(selectedFlatRows.map((d) => d.original));
    }
  }, dependencyArray());

  useUnmount(() => {
    onUnmount &&
      onUnmount(
        state,
        rows.map((el) => el.original),
      );
  });

  useEffect(() => {
    dispatch({
      type: 'onChange',
      rows,
    });
  }, [rows, dispatch]);

  useUpdateEffect(() => {
    dispatch({
      type: 'update_page_size',
      payload: pageSize,
    });
  }, [pageSize]);

  useUpdateEffect(() => {
    if (!filters || filters.length === 0) {
      setAllFilters?.([]);
    }
  }, [filters, resetFiltersDependency]);

  // NB: when we ADD a new ROW to the table, the row's ID must be ALWAYS -1
  // NB: when we ADD a new SUBROW to the table, the row's ID must be ALWAYS -2
  // This way we will be able to understand in which "edit" mode we are (ADD row / ADD subrow / EDIT)
  useUpdateEffect(() => {
    if (
      (state.filters.length &&
        state.filters.filter((e) => e.value !== '').length &&
        (editRowID === '-1' || editRowID === '-2')) ||
      storedFilters.length
    ) {
      if (editRowID) {
        setStoredFilters(state.filters);
        setAllFilters([]);
        if (editRowID === '-2') {
          const editedRowWithSubRows = props.rows.find((row) =>
            row.subRows.find((subRow: any) => String(subRow.id) === '-2'),
          );
          if (editedRowWithSubRows) {
            const indexEditedRowWithSubRows = props.rows.map((row) => row[rowId]).indexOf(editedRowWithSubRows[rowId]);
            if (indexEditedRowWithSubRows > 0) {
              const newData = [...props.rows];
              newData.unshift(newData.splice(indexEditedRowWithSubRows, 1)[0]);
              setData(newData);
            }
          }
        }
      } else {
        setAllFilters(storedFilters);
        setStoredFilters([]);
        const newData = makeData();
        setData(newData);
      }
    }
    modifiedRow = {} as IRow;
    if (editRowID === '-1') gotoPage(0);
  }, [editRowID]);

  useUpdateEffect(() => {
    onRowsChange && onRowsChange(rows.map(({ original }) => (original as { id: any; [x: string]: any }).id));
  }, [rows]);

  useUpdateEffect(() => {
    if (sortBy && sortBy.length === 0) {
      dispatch({ type: 'resetSortBy' });
    }
  }, [sortBy]);

  const onDoubleClick = (row: OriginalRow) => {
    onRowDoubleClick &&
      dispatch({
        type: 'onDoubleClick',
        allRows: rows,
        row,
      });
  };
  const previousId = useRef(-1);
  const handleClick = (
    e: React.MouseEvent<HTMLTableRowElement, MouseEvent>,
    row: RowType<{ disabled?: boolean; id?: string }>,
  ) => {
    if (editRowID || row.original.disabled) return;
    const selectAllPageProps: TableToggleAllRowsSelectedProps | null = hasPagination
      ? { ...getToggleAllPageRowsSelectedProps() }
      : null;
    const selectionItems = () => {
      if (hasMultiSelection || hasSelection) {
        if (row.values.id === previousId.current && row.isSelected) return toggleAllRowsSelected(false);
        if (hasSelection) {
          if (isEditable) {
            if (!editRowID) {
              if (!hasMultiSelection) {
                toggleAllRowsSelected(false);
                setTimeout(() => {
                  row.toggleRowSelected();
                }, 5);
              }
              if (selectAllPageProps?.checked) {
                selectAllPageProps?.onChange &&
                  selectAllPageProps.onChange(e as unknown as React.ChangeEvent<HTMLInputElement>);
              }
            }
          } else {
            toggleAllRowsSelected(false);
            row.toggleRowSelected();
            if (selectAllPageProps?.checked) {
              selectAllPageProps?.onChange && selectAllPageProps.onChange(e as unknown as React.ChangeEvent<Element>);
            }
          }
        }
        if (hasMultiSelection) {
          if (selectAllPageProps?.checked) {
            selectAllPageProps?.onChange && selectAllPageProps.onChange(e as unknown as React.ChangeEvent<Element>);
          }
          if (ctrlPressed?.current) {
            row.toggleRowSelected();
          } else {
            toggleAllRowsSelected(false);
            row.toggleRowSelected();
          }
        }
        previousId.current = row.values.id;
      }
    };
    // single click
    if ((e as any).detail === 1 && onRowClick) onRowClick(row.original);
    // double click
    if ((e as any).detail > 1) {
      onDoubleClick(row.original);
      onRowClick && onRowClick(row.original);
      return;
    }
    if ((e as any).detail <= 1) selectionItems();
  };

  const TableBodyRenderer = React.memo((props: { style: React.CSSProperties; children: React.ReactChildren }) => {
    return (
      <StyledTable {...getTableProps()} id={id || ''} style={{ height: props.style.height }}>
        <Header
          headerGroups={headerGroups}
          hasSort={hasSort}
          hasResize={hasResize}
          fixedColumns={hasMultiSelection ? fixedColumns + 1 : fixedColumns}
          onSortingChange={onSortingChange}
          light={light}
          hideHeader={hideHeader}
        />
        <Body
          light={light}
          hideHeader={hideHeader}
          headerHeight={
            hasFilter || columns.findIndex((column) => column.filterType) >= 0
              ? '61px'
              : hasMultiSelection
              ? '40px'
              : '33px'
          }
          getTableBodyProps={getTableBodyProps}
        >
          {props.children}
        </Body>
      </StyledTable>
    );
  });

  const getStatusValues = React.useCallback(
    (array: {
      [key: string]: any;
      docStatus?: number | null;
      duplicatedProtocol?: number | null;
      duplicationLevel?: number | null;
      idSubject?: number | null;
    }): string => {
      const status = [];
      if (array === undefined) return '';
      if (array.duplicatedProtocol) {
        status.push(t('duplicated'));
      }
      if (array?.idSubject === null) {
        status.push(t('no-subject'));
      }
      switch (array?.docStatus) {
        case 1:
          status.push(t('doc-status-red'));
          break;
        case 2:
          status.push(t('doc-status-yellow'));
          break;
        case 3:
          status.push(t('doc-status-green'));
          break;
      }
      return status.join('-');
    },
    [t],
  );

  const getBlockedValueCsv = (value: any) => {
    switch (value.blocked) {
      case 0:
        return t('operative user');
      case 1:
        return t('disabled user');
    }
    return '';
  };

  const defaultSpecialColumns = {
    status: getStatusValues,
    blocked: getBlockedValueCsv,
  };

  const onExportClick = () => {
    utils.file.downloadCsv('data.csv', utils.file.arrayToCsv(rows, columns, specialColumns ?? defaultSpecialColumns));
  };
  // calculate the number of rows and subrows from an array of rows
  const getRowsCount = (rows: IRow[]): number => {
    const numRows = rows.reduce((count, row) => {
      if (row?.canExpand) {
        return count + (row?.originalSubRows?.length as number);
      }
      if (row?.depth !== 0) return count;
      return count + 1;
    }, 0);
    return numRows;
  };

  const countRows = useMemo(() => {
    return getRowsCount(rows);
  }, [rows]);

  const dynamicHeight = useCallback(() => {
    const max = maxHeight > 600 ? 600 : maxHeight;
    const header = hideHeader ? 18 : hasFilter ? 61 + 18 : 33 + 18;
    const total = max + header;
    if (hasPagination && page) {
      return page.length * 30 + header < total ? page.length * 30 + header : total;
    }
    return rows.length * 30 + header < max ? rows.length * 30 + header : max;
    // eslint-disable-next-line
  }, [page?.length, rows.length, hasFilter, hasPagination, maxHeight]);

  return (
    <ErrorBoundary>
      <ToolbarTableContainer>
        {hasToolbar ? (
          <Toolbar
            hasFilters={state.filters.map((filter) => filter.value).filter((value) => value !== '').length}
            results={useExpandibleRows ? countRows : rows.length}
            onRemoveFilters={() => setAllFilters([])}
            hasExport={hasExport}
            onExportClick={onExportClick}
            width={toolBarWidth}
          />
        ) : (
          hasExport && (
            <div style={{ display: 'flex', justifyContent: 'flex-end', margin: '0 8px 8px 0' }}>
              <FontAwesomeIcon icon="file-csv" onClick={onExportClick} style={{ color: colorPalette.grey.grey6 }} />
            </div>
          )
        )}
        <TableContainer id="TableContainer">
          <List
            overscanCount={2}
            itemCount={hasPagination ? page.length : rows.length}
            itemSize={30}
            height={dynamicHeight()}
            width={'auto'}
            itemKey={(index) => (hasPagination ? page[index].id : rows[index].id)}
            innerElementType={TableBodyRenderer}
          >
            {(props) => {
              const { index, style } = props;
              return (
                <Row
                  style={{ ...style }}
                  row={hasPagination ? page[index] : rows[index]}
                  prepareRow={prepareRow}
                  fixedColumns={fixedColumns}
                  handleClick={handleClick}
                  hasMultiSelection={hasMultiSelection}
                  hasSelection={hasSelection}
                  editRowID={editRowID}
                />
              );
            }}
          </List>
          <BottomContainer>
            {hasPagination && (
              <Pagination
                pageIndex={state.pageIndex}
                canPreviousPage={canPreviousPage}
                canNextPage={canNextPage}
                pageOptions={pageOptions}
                pageCount={pageCount}
                gotoPage={gotoPage}
                nextPage={nextPage}
                previousPage={previousPage}
              />
            )}
            {props.editRowID && (
              <EditRow
                onCancel={() => {
                  props.onCancel?.(modifiedRow);
                  onEditRowChange?.(modifiedRow);
                }}
                onSave={async () => {
                  try {
                    await onSaveValidation?.validate(modifiedRow);
                    onSave?.(modifiedRow);
                  } catch (error) {
                    console.error(error);
                    utils.app.notify('fail', (error as yup.ValidationError).errors.join(','));
                  }
                }}
              />
            )}
          </BottomContainer>
        </TableContainer>
      </ToolbarTableContainer>
    </ErrorBoundary>
  );
};

export default Table;
export type TableProps = Props;
export type TableColumn = Column;
export type OriginalRow = IRow;
