const hslToHex = (h: number, s: number, l: number) => {
  l /= 100;
  const a = (s * Math.min(l, 1 - l)) / 100;
  const f = (n: number) => {
    const k = (n + h / 30) % 12;
    const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1);
    return Math.round(255 * color)
      .toString(16)
      .padStart(2, '0'); // convert to Hex and prefix "0" if needed
  };
  return `#${f(0)}${f(8)}${f(4)}`;
};

const generateHexColors = ({
  numColors,
  hueRangeStart = 0,
  hueRangeEnd = 360,
  saturation = 80,
  lightness = 70,
}: {
  numColors: number;
  hueRangeStart?: number;
  hueRangeEnd?: number;
  saturation?: number;
  lightness?: number;
}) => {
  const colors = [];

  for (let i = 0; i < numColors; i++) {
    const hue = hueRangeStart + (i * (hueRangeEnd - hueRangeStart)) / numColors;
    const hexColor = hslToHex(hue, saturation, lightness);
    colors.push(hexColor);
  }
  return colors;
};

export default {
  generateHexColors,
};
