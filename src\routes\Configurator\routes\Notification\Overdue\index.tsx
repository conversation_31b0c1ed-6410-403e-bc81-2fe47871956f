import { Formik, FormikState } from 'formik';
import { AddOverDueNotificationSettings } from 'models/request';
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useAsync } from 'react-use';
import services from 'services';
import actions from 'state/actions';
import selectors from 'state/selectors';
import utils from 'utils';
import { generateCronExpression } from '../utils';
import FormOverdue from './FormOverdue';
import { FormValues, setInitialValues, validationLogic } from './overdueUtils';
import TableOverdue from './TableOverdue';

const OverdueNotifications = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const formMode = useSelector(selectors.configurator.getFormModeOverdue);
  const selectedRow = useSelector(selectors.configurator.getSelectedOverdueSettings);
  const overdueList = useSelector(selectors.configurator.getListOfOverdueSettings);
  const templatesList = useSelector(selectors.configurator.getTemplatesMailNotification);

  const [userList, setUserList] = useState<{ idUser: number; name: string }[]>([]);

  useAsync(async () => {
    try {
      const { data } = await services.getOverdueNotificationSettings();
      dispatch(actions.configurator.setListOfOverdueSettings(data));
    } catch (e) {
      console.error(e);
    }
  }, []);

  useAsync(async () => {
    if (formMode !== 'add') return;
    const { data } = await services.getUsersForOverdueNotificationSettings();
    setUserList(data);
  }, [formMode]);

  const onSubmit = async (
    values: FormValues,
    resetForm: (nextState?: Partial<FormikState<FormValues>> | undefined) => void,
  ) => {
    const {
      user,
      overDueSendMail,
      overDueMaxDocuments,
      idTemplateScheduledOverdue,
      hour,
      minute,
      allDays,
      daysOfWeek,
    } = values;

    if (!user || !idTemplateScheduledOverdue) return;

    const bodyRequest: AddOverDueNotificationSettings = {
      idUser: user.value,
      overDueSendMail,
      overDueMaxDocuments,
      idTemplateScheduledOverdue: idTemplateScheduledOverdue?.value,
      overDueFrequency: generateCronExpression(hour, minute, allDays, daysOfWeek),
    };
    try {
      if (formMode === 'add') {
        await services.addOverdueNotificationSettings(bodyRequest);
        utils.app.notify('success', t('user-notification-created'));
        dispatch(
          actions.configurator.setListOfOverdueSettings([
            {
              name: user.label,
              ...bodyRequest,
            },
            ...overdueList,
          ]),
        );
      }
      if (formMode === 'edit') {
        await services.editOverdueNotificationSettings(bodyRequest);
        const newList = overdueList.map((e) =>
          e.idUser === bodyRequest.idUser ? { name: user.label, ...bodyRequest } : e,
        );
        dispatch(actions.configurator.setListOfOverdueSettings(newList));
        dispatch(actions.configurator.setSelectedOverdueSettings(null));
        utils.app.notify('success', t('user-notification-edited'));
      }
    } catch (e) {
      console.error(e);
    } finally {
      dispatch(actions.configurator.setFormModeOverdue(null));
      resetForm();
    }
  };

  return (
    <div>
      <h2>{t('Overdue')}</h2>
      <TableOverdue />
      <Formik
        initialValues={setInitialValues(selectedRow, formMode, t, templatesList)}
        onSubmit={(values, { resetForm }) => {
          onSubmit(values, resetForm);
        }}
        validationSchema={validationLogic(t)}
        enableReinitialize
      >
        {({ values, isValid, isValidating, dirty, setFieldValue, submitForm }) => (
          <div>
            {formMode && (
              <FormOverdue
                values={values}
                setFieldValue={setFieldValue}
                dirty={dirty}
                isValid={isValid}
                isValidating={isValidating}
                submitForm={submitForm}
                userList={userList}
                templatesList={utils.input.buildOptions(templatesList, 'templateName', 'idMailTemplate', true)}
              />
            )}
          </div>
        )}
      </Formik>
    </div>
  );
};
export default OverdueNotifications;
