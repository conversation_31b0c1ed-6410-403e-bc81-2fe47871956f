import { Config, Configs } from 'models/app';
import { actionType } from './constants';

export const lucyModules = {
  CLAIMS: 109,
  INTERACTIVE: 3,
  WORKFLOW: 50,
  CONTRACTS: 108,
  MONITOR: 112,
  RX: 110,
  CONFIGURATOR: 111,
  REPORT: 10,
  CONTROL_TOWER: 113,
};

export const lucyModuleKey: { [key: number]: keyof typeof lucyModules } = {
  109: 'CLAIMS',
  3: 'INTERACTIVE',
  50: 'WORKFLOW',
  108: 'CONTRACTS',
  112: 'MONITOR',
  110: 'RX',
  111: 'CONFIGURATOR',
  10: 'REPORT',
  113: 'CONTROL_TOWER',
};

export type ModuleKeys = keyof typeof lucyModules;
export const BASE_MODULE_KEY = 'baseApplyRules_';

const createConfig = (
  item: string,
  val: number | string = 1,
  CompanyConf: { [key: string]: 0 | 1 | string } = {},
  isForCompany = false,
): Config => ({
  isForCompany,
  val,
  item,
  CompanyConf,
});
const LocalConfig: { [key: number]: Configs } = {
  [lucyModules.CLAIMS]: {
    //* Focusing a Q1/Q3 Field the related area on the pdf is highlighted automatically */
    [actionType.AUTO_VIEW_IN_PAGE]: createConfig(actionType.AUTO_VIEW_IN_PAGE),
    //* default translation key used in q1 type select field, if missing 'plese-select' is used */
    [actionType.DEFAULT_SELECT_Q1_PLACEHOLDER]: createConfig(
      actionType.DEFAULT_SELECT_Q1_PLACEHOLDER,
      'review-required',
    ),
    //* Clearing the 0 values onBlurring Q1 currency inputs*/
    [actionType.CLEAR_ZERO_VALUE_Q1_CURRENCY_FIELD]: createConfig(actionType.CLEAR_ZERO_VALUE_Q1_CURRENCY_FIELD),
    //* Clearing the 0 values onBlurring Q3 currency inputs*/
    [actionType.CLEAR_ZERO_VALUE_Q3_CURRENCY_FIELD]: createConfig(actionType.CLEAR_ZERO_VALUE_Q3_CURRENCY_FIELD, 0),
    //* Autosave functionality for the Document Module leaving the document detail view */
    [actionType.AUTOSAVE_DOCUMENT]: createConfig(actionType.AUTOSAVE_DOCUMENT),
    [actionType.Q3_LEFT]: createConfig(actionType.Q3_LEFT),
  },
  [lucyModules.INTERACTIVE]: {
    //* show duplicated icon on the main table in the status column */
    [actionType.DUPLICATED_PROTOCOL]: createConfig(actionType.DUPLICATED_PROTOCOL),
    //* show stoplight icon on the main table in the status column */
    [actionType.DOC_STATUS_STOPLIGHT]: createConfig(actionType.DOC_STATUS_STOPLIGHT),
    //* show priority icon on the main table in the status column */
    [actionType.DOC_PRIORITY]: createConfig(actionType.DOC_PRIORITY),
    //* show doc no subject icon on the main table in the status column */
    [actionType.NO_SUBJECT]: createConfig(actionType.NO_SUBJECT),
    //* show subject details input on the open document header section */
    [actionType.SHOW_SUBJECT_DETAILS]: createConfig(actionType.SHOW_SUBJECT_DETAILS),
    //* hide info flow from sendToWf modal */
    [actionType.NO_INFO_SENDTOWF]: createConfig(actionType.NO_INFO_SENDTOWF),
    [actionType.PRIORITY_WARNING]: createConfig(actionType.PRIORITY_WARNING),
    [actionType.HIDE_ADD_STORE_BUTTON]: createConfig(actionType.HIDE_ADD_STORE_BUTTON),
    [actionType.SHOW_FILTER_FOR_DOCUMENTS_LIST]: createConfig(actionType.SHOW_FILTER_FOR_DOCUMENTS_LIST, 0),
    [actionType.SET_ALL_DOCS_AS_DEFAULT]: createConfig(actionType.SET_ALL_DOCS_AS_DEFAULT),
    [actionType.DISABLE_LITIGATION_WITH_REGISTRATION]: createConfig(actionType.DISABLE_LITIGATION_WITH_REGISTRATION),
    [actionType.CHECK_VENDOR_ON_FORCE_VALIDATE]: createConfig(actionType.CHECK_VENDOR_ON_FORCE_VALIDATE, 0),
  },
  [lucyModules.MONITOR]: {
    [actionType.SHOW_FILTER_FOR_DOCUMENTS_LIST]: createConfig(actionType.SHOW_FILTER_FOR_DOCUMENTS_LIST),
    //* show doc status stoplihght icon on the main table in the status column */
    [actionType.DOC_STATUS_STOPLIGHT]: createConfig(actionType.DOC_STATUS_STOPLIGHT, 0),
    [actionType.DOC_PRIORITY]: createConfig(actionType.DOC_PRIORITY, 0),
    //* show duplicated icon on the main table in the status column */
    [actionType.DUPLICATED_PROTOCOL]: createConfig(actionType.DUPLICATED_PROTOCOL, 0),
    //* show doc no subject icon on the main table in the status column */
    [actionType.NO_SUBJECT]: createConfig(actionType.NO_SUBJECT, 0),
  },
  [lucyModules.WORKFLOW]: {
    [actionType.DISABLE_LITIGATION_WITH_REGISTRATION]: createConfig(actionType.DISABLE_LITIGATION_WITH_REGISTRATION, 0),
  },
  [lucyModules.CONTRACTS]: {
    [actionType.HIDE_MOVE_TYPE]: createConfig(actionType.HIDE_MOVE_TYPE),
  },
};

export const defaultDelay = 500;
export default LocalConfig;
