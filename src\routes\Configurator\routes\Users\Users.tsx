/* eslint-disable max-lines */
import React, { useEffect, useCallback } from 'react';
import { Formik, FormikState } from 'formik';
import * as Yup from 'yup';
import { TableColumn } from 'components/Table';
import { UsersResponse } from 'models/response';
import { useSelector, useDispatch } from 'react-redux';
import { notDraggable } from 'utils/constants';
import utils from 'utils';
import services from 'services';
import selectors from 'state/selectors';
import actions from 'state/actions';

import {
  Wrapper,
  Container,
  Left,
  Right,
  InputContainer,
  InputLabel,
  RightTitle,
  RightWrapper,
  CheckboxLabel,
  CheckboxContainer,
  ButtonsContainer,
  IconsStatusContainer,
} from '../../styles';

import Table from 'components/Table';
import TextInput from 'components/Input/TextInput';
import Checkbox from 'components/Buttons/CheckboxButton';
import Dropdown from 'components/Input/Dropdown';
import DatePicker from 'components/Input/Datepicker';
import CommonButton from 'components/Buttons/CommonButton';
import Icon from 'components/Input/Icon';
import Modal from 'components/Modal';
import ModalsContent from '../../components/ModalsContent/ModalSwitcher';
import Legenda from 'components/Legenda';
import RolesTable from './RolesTable';

interface FormValues {
  name: string;
  username: string;
  email: string;
  sendMail: boolean;
  groupDistribution: boolean;
  loginType: { label: string; value: string } | null;
  changePassword: boolean;
  blocked: boolean;
  expirationDate: Date | null;
}

const Users = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();

  const idUser = useSelector(selectors.user.getIdUser) || -1;
  const dateFormat = useSelector(selectors.user.getUserPreference).dateFormat;
  const closeModal = () => dispatch(actions.modal.closeModal());
  const isModalVisible = useSelector(selectors.modal.getVisibility);
  const currentModal = useSelector(selectors.modal.getActionType);
  const isModalDraggable = notDraggable.includes(currentModal || '');

  // Redux selectors users
  const users = useSelector(selectors.configurator.getUsers);
  const selectedUser = useSelector(selectors.configurator.getSelectedUser);
  const formMode = useSelector(selectors.configurator.getFormMode);

  const loginTypes = [
    { label: t('lucyLdapAuth'), value: 'lucyLdapAuth' },
    { label: t('lucyOAuth'), value: 'lucyOAuth' },
    { label: t('lucyJdbcAuth'), value: 'lucyJdbcAuth' },
  ];

  const defaultValues: FormValues = {
    name: '',
    username: '',
    email: '',
    sendMail: false,
    groupDistribution: false,
    loginType: loginTypes[2],
    changePassword: false,
    blocked: false,
    expirationDate: null,
  };

  const userColumns: TableColumn[] = [
    {
      accessor: 'blocked',
      Header: t('status'),
      filterType: 'select',
      selectFilterOptions: [
        {
          value: 0,
          label: (
            <span>
              <Icon custom icon="green-circle" style={{ width: 11 }} />
            </span>
          ),
        },
        {
          value: 1,
          label: (
            <span>
              <Icon custom icon="block-circle" style={{ width: 10 }} />
            </span>
          ),
        },
      ],
      width: 100,
      disableResizing: true,
      Cell: ({ value }: { value: number }) => {
        try {
          const icons: JSX.Element[] = [];
          switch (value) {
            case 0:
              icons.push(
                <span>
                  <Icon custom icon="green-circle" style={{ width: 11, margin: 10 }} />
                </span>,
              );
              break;
            case 1:
            case 2:
            case 3:
            case 4:
            case 5:
            case 6:
            case 7:
              icons.push(
                <span>
                  <Icon custom icon="block-circle" style={{ width: 10, margin: 10 }} />
                </span>,
              );
              break;
          }
          return <IconsStatusContainer>{icons}</IconsStatusContainer>;
        } catch (e) {
          console.error(e);
          return null;
        }
      },
    },
    { accessor: 'username', Header: t('username'), filterType: 'free' },
    { accessor: 'name', Header: t('firstName'), filterType: 'free' },
    { accessor: 'email', Header: t('email'), filterType: 'free' },
  ];

  const setAllUsersFunction = async () => {
    try {
      const { data } = await services.getDocuments({ idProgram: 111, idUser, idTemplate: 13, allAuthorized: false });
      dispatch(actions.configurator.setUsers(data));
    } catch (e) {
      console.error(e);
    }
  };

  const stableSetAllUsersFunction = useCallback(setAllUsersFunction, []);

  useEffect(() => {
    stableSetAllUsersFunction();
  }, [stableSetAllUsersFunction]);

  const validationLogic = () => {
    return Yup.object({
      name: Yup.string().required(t('required')),
      username: Yup.string().required(t('required')),
      email: Yup.string().required(t('required')),
    });
  };

  const setInitialValues = (): FormValues => {
    if (selectedUser && formMode === 'edit') {
      const { name, username, email, sendMail, groupDistribution, loginType, changePassword, blocked, expirationDate } =
        selectedUser;

      return {
        name,
        username,
        email,
        sendMail: Boolean(sendMail),
        groupDistribution: groupDistribution ? true : false,
        loginType: loginTypes.find((e) => e.value === loginType) || loginTypes[2],
        changePassword: changePassword ? true : false,
        blocked: blocked ? true : false,
        expirationDate: expirationDate ? new Date(expirationDate) : null,
      };
    } else {
      return defaultValues;
    }
  };

  const onSubmit = async (
    values: FormValues,
    resetForm: (nextState?: Partial<FormikState<FormValues>> | undefined) => void,
  ) => {
    const { name, username, email, sendMail, groupDistribution, loginType, changePassword, blocked, expirationDate } =
      values;

    const body = {
      name,
      username,
      email,
      sendMail: sendMail ? 1 : 0,
      groupDistribution: groupDistribution ? 1 : 0,
      loginType: loginType?.value || loginTypes[2].value,
      blocked: blocked ? 1 : 0,
      expirationDate: expirationDate || null,
    };

    try {
      if (formMode === 'new') {
        await services.createNewUser(body);
        utils.app.notify('success', t('user-created'));
        dispatch(actions.configurator.setFormMode(null));
        setAllUsersFunction();
      } else if (formMode === 'edit') {
        if (selectedUser) {
          const { data } = await services.saveUser({
            ...body,
            idUser: selectedUser.idUser,
            changePassword: changePassword ? 1 : 0,
          });
          utils.app.notify('success', t('user-saved'));
          const newUsers = users.map((e) => (e.idUser === data.idUser ? data : e));
          dispatch(actions.configurator.setUsers(newUsers));
          dispatch(actions.configurator.setSelectedUser(data));
        }
      } else if (formMode === 'clone') {
        if (selectedUser) {
          await services.cloneUser({
            idUserToCopy: selectedUser.idUser,
            emailNewUser: email,
            usernameNewUser: username,
            nameNewUser: name,
          });
          resetForm();
        }
        utils.app.notify('success', t('user-created'));
        setAllUsersFunction();
      }
    } catch (e) {
      console.error(e);
    }
  };

  const onUserSelection = (rows: UsersResponse[]) => {
    dispatch(actions.configurator.setSelectedUser(rows[0]));
    dispatch(actions.configurator.setFormMode(null));
  };

  const legendaProps = [
    [
      {
        icon: 'green-circle',
        label: t('operative user'),
      },
      {
        icon: 'block-circle',
        label: t('disabled user'),
      },
    ],
  ];

  return (
    <>
      <Modal onClose={closeModal} open={isModalVisible} isDraggable={!isModalDraggable}>
        <ModalsContent />
      </Modal>
      <Wrapper>
        <h2>{t('users')}</h2>
        <Legenda items={legendaProps} />
        <Formik
          initialValues={setInitialValues()}
          onSubmit={(values, { resetForm }) => {
            onSubmit(values, resetForm);
          }}
          validationSchema={validationLogic()}
          enableReinitialize
        >
          {({
            values,
            errors,
            touched,
            isValid,
            isValidating,
            dirty,
            handleChange,
            handleBlur,
            setFieldValue,
            submitForm,
          }) => (
            <Container>
              <Left>
                {users.length > 0 ? (
                  <Table
                    rowId="idUser"
                    onSelection={(rows: UsersResponse[]) => onUserSelection(rows)}
                    hasToolbar
                    columns={userColumns}
                    rows={users}
                    hasSelection
                    hasPagination
                    hasResize
                    hasSort
                    hasFilter
                    initialSelection={selectedUser ? [selectedUser.idUser] : []}
                  />
                ) : null}
              </Left>
              {formMode ? (
                formMode === 'allRoles' ? (
                  <RightWrapper noBasis>
                    <RightTitle>{t(`${formMode}_user_Roles`)}</RightTitle>
                    <RolesTable />
                  </RightWrapper>
                ) : (
                  <RightWrapper>
                    <RightTitle>{t(`${formMode}_user_details`)}</RightTitle>
                    <Right>
                      <InputContainer>
                        <InputLabel>{t('name')}</InputLabel>
                        <TextInput
                          name="name"
                          hasFeedback={errors.name && touched.name ? true : false}
                          feedbackMessage={errors.name ? errors.name : ''}
                          value={values['name']}
                          onChange={handleChange}
                          onBlur={handleBlur}
                        />
                      </InputContainer>
                      <InputContainer>
                        <InputLabel>{t('username')}</InputLabel>
                        <TextInput
                          name="username"
                          hasFeedback={errors.username && touched.username ? true : false}
                          feedbackMessage={errors.username ? errors.username : ''}
                          value={
                            values['loginType']?.value === 'lucyOAuth'
                              ? values['username'].toLowerCase()
                              : values['username']
                          }
                          onChange={handleChange}
                          onBlur={handleBlur}
                        />
                      </InputContainer>
                      <InputContainer>
                        <InputLabel>{t('email')}</InputLabel>
                        <TextInput
                          name="email"
                          hasFeedback={errors.email && touched.email ? true : false}
                          feedbackMessage={errors.email ? errors.email : ''}
                          value={values['email']}
                          onChange={handleChange}
                          onBlur={handleBlur}
                        />
                      </InputContainer>
                      {formMode === 'new' || formMode === 'edit' ? (
                        <>
                          <CheckboxContainer>
                            <CheckboxLabel>{t('sendMail')}</CheckboxLabel>
                            <Checkbox
                              name="sendMail"
                              isActive={values['sendMail'] ? true : false}
                              action={(e) => setFieldValue('sendMail', e)}
                            />
                          </CheckboxContainer>
                          <CheckboxContainer>
                            <CheckboxLabel>{t('groupDistribution')}</CheckboxLabel>
                            <Checkbox
                              name="groupDistribution"
                              isActive={values['groupDistribution'] ? true : false}
                              action={(e) => setFieldValue('groupDistribution', e)}
                            />
                          </CheckboxContainer>
                          <InputContainer>
                            <InputLabel>{t('loginType')}</InputLabel>
                            <Dropdown
                              name="loginType"
                              value={values['loginType']}
                              onChange={(option) => {
                                setFieldValue('loginType', option);
                              }}
                              options={loginTypes}
                            />
                          </InputContainer>
                        </>
                      ) : null}
                      {formMode === 'edit' ? (
                        <CheckboxContainer>
                          <CheckboxLabel>{t('changePassword')}</CheckboxLabel>
                          <Checkbox
                            name="changePassword"
                            isActive={values['changePassword'] ? true : false}
                            action={(e) => setFieldValue('changePassword', e)}
                          />
                        </CheckboxContainer>
                      ) : null}
                      {formMode === 'new' || formMode === 'edit' ? (
                        <>
                          <CheckboxContainer>
                            <CheckboxLabel>{t('blocked')}</CheckboxLabel>
                            <Checkbox
                              name="blocked"
                              isActive={values['blocked'] ? true : false}
                              action={(e) => setFieldValue('blocked', e)}
                            />
                          </CheckboxContainer>
                          <InputContainer>
                            <InputLabel>{t('expirationDate')}</InputLabel>
                            <DatePicker
                              selected={values['expirationDate'] || null}
                              onChange={(date) => setFieldValue('expirationDate', date || null)}
                              dateFormat={dateFormat}
                              placeholderText={dateFormat?.toUpperCase()}
                            />
                          </InputContainer>
                        </>
                      ) : null}
                      <ButtonsContainer>
                        <CommonButton
                          id="saveButton"
                          scope="primary"
                          disabled={!dirty || !isValid || isValidating}
                          type="submit"
                          value={t(`${formMode}_user`)}
                          action={submitForm}
                        />
                        <CommonButton
                          id="cancelButton"
                          scope="secondary"
                          type="button"
                          value={t('cancel')}
                          action={() => {
                            dispatch(actions.configurator.setFormMode(null));
                          }}
                        />
                      </ButtonsContainer>
                    </Right>
                  </RightWrapper>
                )
              ) : null}
            </Container>
          )}
        </Formik>
      </Wrapper>
    </>
  );
};

export default Users;
