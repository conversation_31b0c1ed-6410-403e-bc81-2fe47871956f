import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actionType } from 'utils/constants';

import utils from 'utils';

import CommonButton from 'components/Buttons/CommonButton';
import actions from 'state/actions';
import selectors from 'state/selectors';

const BackViewModeVendor = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const editedRowId = useSelector(selectors.configurator.getEditedRowIdVendorDetail);

  const selectedCompany = useSelector(selectors.configurator.getSelectedCompanyVendorConfig);
  const companyName = selectedCompany?.label;
  const hasPermission = utils.user.isActionByCompanyActive(actionType.BACK_VIEW_MODE_VENDOR, companyName);

  return (
    <CommonButton
      action={() => {
        dispatch(actions.configurator.setIsEditedVendorDetail(false));
        dispatch(actions.configurator.setSelectedRowIdSubjectDetails(''));
      }}
      scope="tertiary"
      value={t(actionType.BACK_VIEW_MODE_VENDOR)}
      icon="circle"
      disabled={!hasPermission || !!editedRowId}
    />
  );
};

export default BackViewModeVendor;
