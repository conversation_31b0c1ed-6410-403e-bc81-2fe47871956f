import axios from 'axios';
import { ApplyRulesResponse } from 'models/response';
import { trackPromise } from 'react-promise-tracker';
import { ModuleKeys, BASE_MODULE_KEY } from 'utils/configs';

const VALIDATION = 'validation';

type CheckMandatoryFieldsResponse = {
  protocol: number;
  rulesGroup: ModuleKeys;
};

export const checkMandatoryFields = async ({ protocol, rulesGroup }: CheckMandatoryFieldsResponse) => {
  return await trackPromise(
    axios.post<ApplyRulesResponse>(`${VALIDATION}/applyRules`, {
      protocol,
      rulesGroup: BASE_MODULE_KEY + rulesGroup,
    }),
    'module',
  );
};

export default {
  checkMandatoryFields,
};
