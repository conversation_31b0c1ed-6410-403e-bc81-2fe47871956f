import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import utils from 'utils';
import selectors from 'state/selectors';

import { UserCompany } from 'models/response';
import Dropdown from 'components/Input/Dropdown';
import actions from 'state/actions';

export const VendorDropDowns = () => {
  const selectedCompany = useSelector(selectors.configurator.getSelectedCompanyVendorConfig);
  const companies = useSelector(selectors.app.getCompanies);
  const editedVendorId = useSelector(selectors.configurator.getIdEditedVendorFromList);

  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const companyOptions = (data: UserCompany[]) => {
    const values = utils.input.removeDefaultValue(data, 'idCompany');
    const options = utils.input.buildOptions(values, 'name', 'idCompany');

    return options;
  };

  return (
    <Dropdown
      placeholder={t('select_company')}
      onChange={(value) => dispatch(actions.configurator.setSelectedCompanyVendorConfig(value))}
      options={companyOptions(companies)}
      value={selectedCompany}
      margin="0px 15px"
      disabled={!!editedVendorId}
    />
  );
};
