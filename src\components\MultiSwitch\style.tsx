import styled, { css } from 'styled-components/macro';
import { fontSizePalette } from 'utils/styleConstants';

const Wrapper = styled.div<{ disabled: boolean }>`
  display: flex;
  border-radius: 20px;
  padding: 2px;
  color: ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.white : theme.colorPalette.grey.grey9)};
  align-items: center;
  background-color: ${(props) =>
    props.disabled
      ? props.theme.colorPalette.isDarkMode
        ? props.theme.colorPalette.grey.grey3
        : props.theme.colorPalette.grey.grey4
      : props.theme.colorPalette.isDarkMode
      ? props.theme.colorPalette.turquoise.dark
      : props.theme.colorPalette.turquoise.normal};
  cursor: ${(props) => (props.disabled ? 'not-allowed' : 'pointer')};
  width: fit-content;
  height: 24px;
`;

const SelectedChip = css`
  border: 1px solid
    ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey9 : theme.colorPalette.white)};
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey9 : theme.colorPalette.white};
  border-radius: 20px;
  height: 21px;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const Chip = styled.div<{ disabled: boolean; selected: boolean }>`
  padding: 3px 6px;
  font-size: ${fontSizePalette.xSmall};
  ${(props) => props.selected && SelectedChip};
  color: ${(props) =>
    props.selected
      ? props.disabled
        ? props.theme.colorPalette.isDarkMode
          ? props.theme.colorPalette.grey.grey5
          : props.theme.colorPalette.grey.grey4
        : props.theme.colorPalette.isDarkMode
        ? props.theme.colorPalette.turquoise.light
        : props.theme.colorPalette.turquoise.normal
      : props.theme.colorPalette.isDarkMode
      ? props.theme.colorPalette.grey.grey9
      : props.theme.colorPalette.white};
`;

export { Wrapper, Chip };
