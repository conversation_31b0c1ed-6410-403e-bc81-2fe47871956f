import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actionType } from 'utils/constants';

import utils from 'utils';
import actions from 'state/actions';
import selectors from 'state/selectors';

import CommonButton from 'components/Buttons/CommonButton';

const CloneUser = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const selectedUser = useSelector(selectors.configurator.getSelectedUser);

  return (
    <CommonButton
      action={() => dispatch(actions.configurator.setFormMode('clone'))}
      disabled={!(selectedUser && utils.user.isActionByCompanyActive(actionType.CLONE_USER))}
      scope="tertiary"
      value={t('clone-user')}
      icon="circle"
    />
  );
};

export default CloneUser;
