import { statusColors } from 'utils/styleConstants';

// This function is used to create the options (label & value) for the dropdowns
const buildOptionsConcatLabel = (values: any[], labels: string[], value: string) => {
  if (values === null || !values.length) return [];
  return values.map((ele) => {
    return {
      label: labels
        .map((label) => ele[label])
        .filter((label) => label)
        .join(' - '),
      value: ele[value],
    };
  });
};
// This function is used to create the options (label & value) for the dropdowns
const buildOptions = (values: any[], label: string, value: string, concatLabelValue?: boolean) => {
  if (values === null || !values.length) return [];
  return values.map((ele) => {
    return {
      label:
        concatLabelValue || (label === 'description' && value === 'code')
          ? `${ele[value]} - ${ele[label]}`
          : ele[label],
      value: ele[value],
    };
  });
};
// this functions is used to create the options (label & value) for the dropdowns
const buildOptionsMaps = (
  values: any[],
  label: string,
  value: string,
  concatLabelValue?: boolean,
): Map<string, string> => {
  const map = new Map();
  values.forEach((ele) => {
    map.set(
      String(ele[value]),
      concatLabelValue || (label === 'description' && value === 'code') ? `${ele[value]} - ${ele[label]}` : ele[label],
    );
  });
  return map;
};
/**
 * This function returns a boolean to handle the visibility of an Input
 * valued by comparing the confidece and the threshold (confidence < threshold)
 * except when the filter is not active or the field is mandatory
 * @param { boolean } globalSetting filter status
 * @param { number } confidence input's value accuracy assigned by the ML
 * @param { number } threshold
 * @param { number } obbl status number value declaring the if the field is required
 * @param { string } value text value of the field
 * @return { boolean }
 */
const isFieldVisible = (
  globalSetting: boolean,
  confidence: number,
  threshold: number,
  obbl: number,
  value?: string,
) => {
  if (!globalSetting) return true;

  // obbligatorio
  if (obbl > 0) return true;

  // normal field
  if (obbl === 0) {
    return confidence < threshold;
  }

  // warning
  return obbl < 0 && value ? true : false;
};

const getInputStatusColor = (id: number, status: any) => {
  if (status?.length) {
    const found = status.find(({ idField }: any) => idField === id);
    const statusValue = found.status;

    switch (statusValue) {
      case 1:
        return statusColors.error;
      case 2:
        return statusColors.warning;
      case 3:
        return statusColors.success;
      default:
        return undefined;
    }
  }
};

const getInputStatusMessage = (id: number, status: any) => {
  if (status?.length) {
    const found = status.find(({ idField }: any) => idField === id);
    return found?.messageField;
  }
};

const removeDefaultValue = (values: any[], label: string) => {
  return values.filter((value) => value[label] !== 0);
};

export default {
  buildOptionsConcatLabel,
  buildOptionsMaps,
  buildOptions,
  isFieldVisible,
  getInputStatusMessage,
  getInputStatusColor,
  removeDefaultValue,
};
