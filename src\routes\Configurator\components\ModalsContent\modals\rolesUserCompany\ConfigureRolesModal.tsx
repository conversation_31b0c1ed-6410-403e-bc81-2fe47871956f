/* eslint-disable max-lines */
import React, { useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components/macro';
import { useAsync } from 'react-use';

import actions from 'state/actions';
import utils from 'utils';

import CommonButton from 'components/Buttons/CommonButton';
import Header from 'components/Modal/Header';
import Table from 'components/Table';
import Dropdown from 'components/Input/Dropdown';
import RadioButton from 'components/Buttons/RadioButton';

import { FlexContainer, VerticalCenterContainer, TableContainerOne, TableContainerTwo } from './style';
import { TextInput } from 'components/Input';
import service from 'services';
import { Checkbox, SingleRadioButton } from 'components/Buttons';
import { PrivilegeConfigureRole, ProgramConfigureRole, Role } from 'models/configurator';

import selectors from 'state/selectors';
import { EditRoles } from 'models/response';

export const ButtonContainer = styled.div`
  text-align: center;
  button {
    margin: 10px;
  }
`;

const TableEmpty = styled.div`
  display: flex;
  align-items: center;
  border: solid 1px #c5c5c5;
  height: 362px;
  margin-top: 31px;
  border-radius: 4px;
  width: 400px;
  justify-content: center;
`;

const Wrapper = styled.div`
  display: flex;
  padding-bottom: 15px;
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  overflow-x: auto;
  margin-top: 26px;
`;

const CustomTitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.M};
  font-weight: bold;
`;

interface TypesOfAction {
  new: string;
  edit: string;
}

const typesOfAction: TypesOfAction = {
  new: 'new',
  edit: 'edit',
};

const ConfigureRolesModal = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();

  const [actionSelected, setActionSelected] = useState<string>(typesOfAction.new);
  const [roleLists, setRoleLists] = useState<EditRoles[]>([]);
  const [roleSelected, setRoleSelected] = useState<Role | null>(null);
  const [programs, setPrograms] = useState<ProgramConfigureRole[]>([]);
  const [newPrograms, setNewPrograms] = useState<ProgramConfigureRole[]>([]);
  const [newRole, setNewRole] = useState<string>('');

  // redux selectors
  const selectedProgram = useSelector(selectors.configurator.getSelectedProgram);

  useAsync(async () => {
    const { data } = await service.getConfigureRoles();

    const { availablePrivileges } = data?.newRoles;

    /** Set default privilege for each programs*/
    const copyOfAvailablePrivileges = [...availablePrivileges];
    if (!availablePrivileges.find((el: PrivilegeConfigureRole) => el.selected)) {
      copyOfAvailablePrivileges[0].selected = true;
    }

    const programsList = data.newRoles.availablePrograms.map((available: ProgramConfigureRole) => ({
      ...available,
      privilegeResponseList: data?.newRoles.availablePrivileges,
    }));

    setPrograms(programsList);
    setRoleLists(data?.editRoles);
    setNewPrograms(programsList);
  }, []);

  const onProgramSelection = (row: ProgramConfigureRole[] | null) => {
    dispatch(actions.configurator.setSelectedProgram(row ? row[0] : null));
  };

  const changeRole = (role: Role) => {
    setRoleSelected(role);
    const listprogram = role.programs;
    setPrograms(listprogram);
    onProgramSelection(null);
  };

  const checkPrivilege = (indexRow: number) => {
    const programsList = [...programs];

    if (selectedProgram) {
      const index = selectedProgram.id;
      const newPrivileges = programsList[index].privilegeResponseList?.map((el: PrivilegeConfigureRole) => {
        return {
          ...el,
          selected: false,
        };
      });
      newPrivileges[indexRow].selected = true;
      programsList[index].privilegeResponseList = [...newPrivileges];

      setPrograms(programsList);
    }
  };

  const checkProgram = (id: number) => {
    const programsCopy = [...programs];
    programsCopy[id].active = !programsCopy[id].active;
    setPrograms(programsCopy);
  };

  const closeModal = () => {
    dispatch(actions.configurator.setSelectedProgram(null));
    dispatch(actions.modal.closeModal());
  };

  const confirm = async () => {
    const activeProgram = programs.filter((program: ProgramConfigureRole) => program.active);

    if (actionSelected === 'new') {
      const programPrivilegeComb = activeProgram.map((el) => {
        const selectedPrivilege = el.privilegeResponseList.find((e: PrivilegeConfigureRole) => e.selected);
        return {
          idProgram: el.idProgram,
          idPrivilege: selectedPrivilege ? selectedPrivilege.idPrivilege : null,
        };
      });

      const body = {
        roleName: newRole,
        programPrivilegeRequests: programPrivilegeComb,
      };
      try {
        await service.configureNewRole(body);
        closeModal();
        utils.app.notify('success', t('new_role_configured'));
      } catch (e) {
        console.error(e);
      }
    } else {
      const programPrivilegeComb = activeProgram.map((el) => {
        const selectedPrivilege = el.privilegeResponseList.find((e: PrivilegeConfigureRole) => e.selected);
        return {
          idProgram: el.idProgram,
          idPrivilege: selectedPrivilege ? selectedPrivilege.idPrivilege : null,
          idRole: roleSelected ? roleSelected.value : null,
          invisible: false,
        };
      });
      try {
        await service.editConfiguredRole(programPrivilegeComb);
        closeModal();
        utils.app.notify('success', t('configured_role_edited'));
      } catch (e) {
        console.error(e);
      }
    }
  };

  const changeActionType = (value: string) => {
    if (value === 'edit' && actionSelected !== 'edit') setPrograms([]);
    if (value === 'new') setRoleSelected(null);
    setActionSelected(value);
    dispatch(actions.configurator.setSelectedProgram(null));
  };

  const isDisabled =
    actionSelected === 'new'
      ? newRole.length === 0 || programs.filter((el) => el.active).length === 0
      : !roleSelected || programs.filter((el) => el.active).length === 0;

  useEffect(() => {
    if (actionSelected === 'new') {
      setPrograms(newPrograms);
    }
  }, [actionSelected, newPrograms]);

  const tableOneColumns = [
    { accessor: 'programName', Header: t('module') },
    {
      accessor: 'active',
      Header: t('association'),
      Cell: ({ row }: any) => {
        return (
          <div style={{ display: 'flex', gap: '8px' }}>
            <Checkbox
              isEditable={!row?.original?.disabled}
              isActive={row?.original.active}
              action={(isActive, e) => {
                checkProgram(row?.id);
                // isActive should change the value of the row
                e != null && e.stopPropagation();
              }}
            ></Checkbox>
            <span>Active</span>
          </div>
        );
      },
    },
  ];

  const tableTwoColumns = [
    { accessor: 'description', Header: t('privilege') },
    {
      accessor: 'selected',
      Header: t('association'),
      Cell: ({ ...rest }: any) => {
        return (
          <div style={{ display: 'flex', gap: '8px', height: '100%' }}>
            <SingleRadioButton
              onChange={() => {
                checkPrivilege(rest?.row?.index);
              }}
              checked={rest.row.original.selected}
            />
          </div>
        );
      },
    },
  ];

  return (
    <>
      <Header title={t('configure_roles')} />
      <FlexContainer>
        <VerticalCenterContainer>{t('select_what_to_do')}</VerticalCenterContainer>
        <VerticalCenterContainer>
          <RadioButton
            inline
            value={actionSelected}
            onChange={(value: string) => {
              changeActionType(value);
            }}
            options={[
              { label: t('new role'), value: typesOfAction.new },
              { label: t('edit role'), value: typesOfAction.edit },
            ]}
          />
        </VerticalCenterContainer>
      </FlexContainer>
      <FlexContainer>
        <VerticalCenterContainer>{t('select_role')}</VerticalCenterContainer>
        <VerticalCenterContainer>
          {actionSelected === 'edit' && (
            <Dropdown
              onChange={(option) => changeRole(option)}
              options={roleLists.map((option: EditRoles) => ({
                label: option.roleName,
                value: option.idRole,
                programs: option.roleProgramPrivilegeResponseList,
              }))}
              value={roleSelected}
            />
          )}
          {actionSelected === 'new' && (
            <TextInput
              placeholder={t('new_role')}
              onChange={(e: React.FocusEvent<HTMLInputElement>) => setNewRole(e.target.value)}
              id="newRole"
            />
          )}
        </VerticalCenterContainer>
      </FlexContainer>
      <Wrapper>
        <TableContainerOne>
          <CustomTitle>Module table</CustomTitle>
          {actionSelected === 'new' || roleSelected ? (
            <>
              <Table
                onSelection={(row: ProgramConfigureRole[]) => onProgramSelection(row)}
                hasToolbar
                columns={tableOneColumns}
                rows={programs}
                hasSelection
                hasPagination
                hasResize
                hasSort
                hasFilter
                hasExport={false}
                initialSelection={selectedProgram ? [selectedProgram.id] : []}
              />
            </>
          ) : (
            <TableEmpty>{t('select_role')}</TableEmpty>
          )}
        </TableContainerOne>
        <TableContainerTwo>
          <CustomTitle>Privilege table</CustomTitle>
          {selectedProgram ? (
            <>
              <Table
                hasToolbar
                columns={tableTwoColumns}
                rows={
                  selectedProgram && programs.length > 0 ? programs[selectedProgram?.id]?.privilegeResponseList : []
                }
                hasSelection
                hasPagination
                hasResize
                hasSort
                hasFilter
                hasExport={false}
              />{' '}
            </>
          ) : (
            <TableEmpty>{t('select_row_from_module')}</TableEmpty>
          )}
        </TableContainerTwo>
      </Wrapper>
      <ButtonContainer>
        <CommonButton value={t('continua')} action={() => confirm()} disabled={isDisabled} />
        <CommonButton scope="secondary" value={t('annulla')} action={() => closeModal()} />
      </ButtonContainer>
    </>
  );
};

export default ConfigureRolesModal;
