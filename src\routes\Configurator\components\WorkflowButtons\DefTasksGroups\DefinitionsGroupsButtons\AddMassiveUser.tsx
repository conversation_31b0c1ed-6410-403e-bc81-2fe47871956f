import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actionType, modalActionType } from 'utils/constants';

import utils from 'utils';
import actions from 'state/actions';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';

const AddMassiveUser = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const oUserSelected = useSelector(selectors.configurator.getSelectedOUser);

  const onAction = () => {
    dispatch(
      actions.modal.openModal(modalActionType.configurator.ADD_REPLACE_MASSIVE_USER,{
        type: 'add',
      }),
    );
  };

  return (
    <CommonButton
      action={onAction}
      disabled={oUserSelected === null || !utils.user.isActionByCompanyActive(actionType.ADD_MASSIVE_USER)}
      scope="tertiary"
      value={t('add-massive-user')}
      icon="circle"
    />
  );
};
export default AddMassiveUser;
