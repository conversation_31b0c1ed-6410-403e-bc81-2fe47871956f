import React from 'react';
import styled from 'styled-components';

export interface Props {
  imageUrl: string;
  bannerTitle: string;
  linkTo: string;
}

const BannerContainer = styled.div`
  width: 256px;
  border: ${({ theme }) => (theme.colorPalette.isDarkMode ? 'none' : `1px solid ${theme.colorPalette.grey.grey5}`)};
  border-radius: 8px;
`;

const ImageContainer = styled.div<{ imageUrl: string }>`
  background-image: ${({ imageUrl }) => imageUrl && `url(${imageUrl})`};
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  height: 106px;
  width: 100%;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
`;

const BannerLinkContainer = styled.div`
  height: 50px;
  width: 100%;
  display: flex;
  align-items: center;
  border-top: ${({ theme }) => (theme.colorPalette.isDarkMode ? 'none' : `1px solid ${theme.colorPalette.grey.grey5}`)};
  padding: 10px;
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.white};
  color: ${({ theme }) => (theme.colorPalette.isDarkMode ? 'white' : theme.colorPalette.black)};
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  justify-content: center;
  cursor: pointer;
`;

const BannerComponent = (props: Props) => {
  const { imageUrl, bannerTitle, linkTo } = props;

  const handleClick = () => {
    if (linkTo.length) window.open(linkTo, '_blank');
  };

  return (
    <BannerContainer>
      <ImageContainer imageUrl={imageUrl} data-testid="image-container" />
      <BannerLinkContainer data-testid="linkToWebSite" onClick={handleClick}>
        {bannerTitle}
      </BannerLinkContainer>
    </BannerContainer>
  );
};
export default BannerComponent;
