import React from 'react';
import { TD } from './styles';
import { Cell as ICell } from 'react-table';
import { isEqual } from 'lodash';
import utils from 'utils';
import { LocalKeys } from './interfaces';

interface CellProp {
  cellProps: ICell['getCellProps'];
  fixedColumns: number;
  index: number;
  title?: string;
  width: ICell['column']['width'];
  left: ICell['column']['totalLeft'];
  children: ReturnType<ICell['column']['render']>;
  hasLocalKeys?: LocalKeys;
  header?: string;
}
const Cell: React.FunctionComponent<CellProp> = (props) => {
  const { children, width, left, fixedColumns, index, cellProps, title, hasLocalKeys, header } = props;
  const t = utils.intl.useTranslator();
  const { dateConverter } = utils.date.useConvertDate();
  const handleCopy = (e: React.MouseEvent<HTMLTableCellElement, MouseEvent>) => {
    if (e.ctrlKey) {
      e.stopPropagation();
      if ((title ?? '') === '') {
        utils.app.notify('warning', t('Nothing to copy'), 5000, 'nothing-to-copy');
        return;
      }
      utils.app.notify('success', t('Copied to clipboard'));

      const [localKeys, prefixName] = hasLocalKeys ?? [undefined, undefined];
      switch (localKeys) {
        case 'LocalizableString':
          navigator.clipboard.writeText(utils.app.textTranslated(title, t) ?? '');
          break;
        case 'LocalizableStringFe':
          navigator.clipboard.writeText(utils.app.setLocalizableString(title, header, prefixName, t) ?? '');
          break;
        case 'date':
          navigator.clipboard.writeText(dateConverter(Number(title) ?? ''));
          break;
        case 'stringDate':
          navigator.clipboard.writeText(dateConverter(title ?? ''));
          break;
        default:
          navigator.clipboard.writeText(title ?? '');
          break;
      }
    }
  };

  return (
    <TD
      className={`${fixedColumns > index ? 'fixed' : ''} ${fixedColumns - 1 === index ? 'last-fixed' : ''}`}
      width={fixedColumns >= index ? width : 'auto'}
      left={fixedColumns >= index ? left : 'unset'}
      title={title}
      {...cellProps()}
      onClick={(e) => {
        handleCopy(e);
      }}
    >
      {children}
    </TD>
  );
};

export default React.memo(Cell, (prev, prop) => !isEqual(prev.children, prop.children));
