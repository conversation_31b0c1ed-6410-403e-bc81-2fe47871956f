/* eslint-disable max-lines */
import sortDown from 'images/sort-down.svg';
import sortUp from 'images/sort-up.svg';
import React from 'react';
import styled, { css } from 'styled-components/macro';
import { fontSizePalette, fontWeightPalette } from 'utils/styleConstants';

const TableContainer = styled.div`
  padding-bottom: 8px;
  overflow-y: auto;
`;

const StyledTable = styled.table`
  border-collapse: separate;
  border-spacing: 0;
  color: ${({ theme }) => theme.colorPalette.isDarkMode
    ? theme.colorPalette.black
    : theme.colorPalette.grey.grey9};
  font-size: ${fontSizePalette.body.XS};
  text-align: left;
  user-select: none;
  white-space: nowrap;
`;

const THead = styled.thead<{ light: boolean; hideHeader: boolean }>`
  position: sticky;
  top: 0;
  font-weight: ${fontWeightPalette.medium};
  z-index: ${({ theme }) => theme.zIndexPalette.medium};

  th {
    margin: 0;

    padding: ${({ hideHeader }) => (hideHeader ? '0' : '10px')};
    height: ${({ hideHeader }) => (hideHeader ? '0' : null)};

    ${({ light, hideHeader, theme }) =>
      light &&
      !hideHeader &&
      `
      border-bottom: solid 1px ${theme.colorPalette.grey.grey5};
      background-color: ${theme.colorPalette.white};
    `}

    ${({ light, theme, hideHeader }) =>
      !light &&
      `
      border-top: ${hideHeader ? 'none' : `solid 1px ${theme.colorPalette.grey.grey5}`};
      background-color: ${theme.colorPalette.grey.grey2};
      &:first-child {
        border-top-left-radius: 4px;
        border-left: solid 1px ${theme.colorPalette.grey.grey5};
      }

      &:last-child {
        border-top-right-radius: 4px;
        border-right: solid 1px ${theme.colorPalette.grey.grey5};
      }
    `}

    text-overflow: ellipsis;

    .thLabel {
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: flex-end;
    }

    &.fixed {
      position: sticky;
      /* background-color: ${({ theme }) => theme.colorPalette.grey.grey4}; */

      &.last-fixed {
        border-right: solid 1px ${({ theme }) => theme.colorPalette.grey.grey5};
      }
    }
  }
`;

const TBody = styled.tbody<{ light: boolean; hideHeader: boolean; headerHeight?: string }>`
  position: relative;
  line-height: 29px;
  font-weight: ${fontWeightPalette.light};
  @-moz-document url-prefix() {
    z-index: ${({ theme }) => theme.zIndexPalette.high};
    position: absolute;
    top: ${({ headerHeight }) => headerHeight};
    left: 0;
  }
  tr {
    background-color: ${({ theme }) => theme.colorPalette.white};
    user-select: none;
    cursor: pointer;
    display: table;

    &.row-selected td {
      background-color: ${({ theme }) => theme.colorPalette.isDarkMode
        ? theme.colorPalette.turquoise.normal
        : theme.colorPalette.turquoise.light};
    }
    &.row-disabled td {
      color: ${({ theme }) => theme.colorPalette.grey.grey4};
      cursor: default;
    }
    &:not(.row-disabled) {
      &:hover:not(.row-selected):not(.edited-row) td {
        background-color: ${({ theme }) =>
          theme.colorPalette.isDarkMode
            ? theme.colorPalette.turquoise.light
            : theme.colorPalette.turquoise.background} !important;
      }
    }

    ${({ hideHeader, light, theme }) =>
      hideHeader &&
      !light &&
      `
        &:first-child {
          td {
            border-top: solid 1px ${theme.colorPalette.grey.grey5};
          }
          td:first-child {
            border-top-left-radius: 4px;
          }
          td:last-child {
            border-top-right-radius: 4px;
          }
        }

    `}

    &:last-child {
      td:first-child {
        border-bottom-left-radius: 4px;
      }

      td:last-child {
        border-bottom-right-radius: 4px;
      }
    }

    &:not(.row-selected) td.fixed {
      background-color: ${({ theme }) => theme.colorPalette.white};
    }

    td {
      height: 30px;
      margin: 0;
      padding: 0 10px;
      overflow: hidden;
      vertical-align: middle;

      ${({ light, theme }) =>
        !light &&
        `
        border-bottom: solid 1px ${theme.colorPalette.grey.grey5};
        &:first-child {
          border-left: solid 1px ${theme.colorPalette.grey.grey5};
        }

        &:last-child {
          border-right: solid 1px ${theme.colorPalette.grey.grey5};
        }
      `}

      &.fixed {
        position: sticky;

        &.last-fixed {
          border-right: solid 1px ${({ theme }) => theme.colorPalette.grey.grey5};
        }
      }
    }
  }
`;

const Resizer = styled.div<{ lastItem?: boolean }>`
  width: 15px;
  border-right: solid 1px ${({ lastItem, theme }) => (lastItem ? 'transparent' : theme.colorPalette.grey.grey5)};
  height: 100%;
  position: absolute;
  top: 0;
  right: 0px;
  :hover {
    cursor: col-resize;
  }
`;

const Img = styled.img`
  width: 7px;
  margin-right: 5px;

`;

const Sorter = ({ isSorted, isSortedDesc }: { isSorted: boolean; isSortedDesc?: boolean }) => {
  return isSorted ? isSortedDesc ? <Img src={sortDown} /> : <Img src={sortUp} /> : null;
};

const Filter = styled.div`
  padding-bottom: 10px;
`;

const TH = styled.th<{ width?: number | 'auto' | string; left?: number }>`
  ${({ width }) =>
    width &&
    css`
      width: ${width}px;
    `}
  ${({ left }) =>
    (left || left === 0) &&
    css`
      left: ${left}px;
    `}
  span {
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }
`;

const TD = styled.td<{ width?: string | number; left?: string | number }>`
  ${({ width }) =>
    width &&
    css`
      width: ${width}px;
    `}
  ${({ left }) =>
    (left || left === 0) &&
    css`
      left: ${left}px;
    `}
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
`;
const BottomContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 4px;
`;

const StyledCheckboxWrapper = styled.div`
  margin-top: 5px;
`;
const StyledHeadCheckboxWrapper = styled.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
`;
const Circle = styled.span`
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.turquoise.light : theme.colorPalette.turquoise.dark};
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-size: clamp(6px, ${fontSizePalette.body.XXS}, 18px);
  margin-left: 4px;
  padding: 4px;
  color: ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.black : theme.colorPalette.white)};
  align-content: center;
`;

const ToolbarTableContainer = styled.div`
  max-width: fit-content;
`;

const StyledArrow = styled.div<{ width?: string; height?: string; top?: string }>`
  cursor: pointer;
  width: ${({ width }) => width || '28px'};
  height: ${({ height }) => height || '24px'};
  position: absolute;
  top: ${({ top }) => top || '36px'};
  left: 0px;
  text-align: center;
`;
export {
  TableContainer,
  StyledTable,
  THead,
  TH,
  TD,
  TBody,
  Resizer,
  Sorter,
  Filter,
  BottomContainer,
  StyledCheckboxWrapper,
  StyledHeadCheckboxWrapper,
  Circle,
  ToolbarTableContainer,
  StyledArrow,
};
