/* eslint-disable max-lines */
import Modal from 'components/Modal';
import Table, { OriginalRow, TableColumn } from 'components/Table';
import { AP2OptionsList } from 'models/configurator';
import { AP2Template, GetNopoVendorResponse } from 'models/response';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useAsync } from 'react-use';
import ToolBarDetail from 'routes/Configurator/components/ToolbarDetail';
import services from 'services';
import actions from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import utils from 'utils';
import * as yup from 'yup';

export const ButtonContainer = styled.div`
  button {
    margin: 10px;
  }
`;

const SelectAP2Template = () => {
  const t = utils.intl.useTranslator();
  const optionsList = useSelector(selectors.configurator.getAP2OptionList);
  const editRowID = useSelector(selectors.configurator.getEditedRowAP2ID);
  const isEdit = useSelector(selectors.configurator.getIsEditAP2Template);
  const selectedNOPORow = useSelector(selectors.configurator.getNOPOSelectedRow);
  const templateList = useSelector(selectors.configurator.getTemplateAP2List);
  const vendorList = useSelector(selectors.configurator.getNOPOLeftTableRows);
  const dispatch = useDispatch();
  const { costCenter, glAccount, sign, taxCode } = optionsList;

  const templateListColumns: TableColumn[] = [
    {
      id: '1',
      accessor: 'id',
      Header: 'id',
      inputType: 'number',
      isVisible: false,
    },
    {
      id: '2',
      accessor: 'companyName',
      Header: t('companyName'),
      filterType: 'free',
      readOnly: true,
      Cell: ({ value, row }: { value: any; row: any }) => {
        return <>{row.depth > 0 ? '-' : value}</>;
      },
    },
    {
      id: '3',
      accessor: 'vendorCode',
      Header: t('vendorCode'),
      filterType: 'free',
      readOnly: true,
      Cell: ({ value, row }: { value: any; row: any }) => {
        return <>{row.depth > 0 ? '-' : value}</>;
      },
    },
    {
      id: '4',
      accessor: 'sign',
      Header: t('sign'),
      filterType: 'free',
      inputType: 'select',
      selectOptionsTypes: utils.input.buildOptions(sign, 'code', 'code'),
    },
    {
      id: '5',
      accessor: 'taxPercent',
      Header: t('taxPercent'),
      filterType: 'free',
      inputType: 'text',
      Cell: ({ value, row }: { value: any; row: any }) => {
        return <>{row.depth > 0 ? '-' : value}</>;
      },
    },
    {
      id: '6',
      accessor: 'taxCode',
      Header: t('taxCode'),
      filterType: 'free',
      inputType: 'select',
      selectOptionsTypes: utils.input.buildOptions(taxCode, 'code', 'code'),
    },
    {
      id: '7',
      accessor: 'glAccount',
      Header: t('glAccount'),
      filterType: 'free',
      inputType: 'select',
      selectOptionsTypes: utils.input.buildOptions(glAccount, 'code', 'code'),
    },
    {
      id: '8',
      accessor: 'costCenter',
      Header: t('costCenter'),
      filterType: 'free',
      inputType: 'select',
      selectOptionsTypes: utils.input.buildOptions(costCenter, 'code', 'code'),
    },
  ];

  useAsync(async () => {
    try {
      if (!selectedNOPORow) return;
      const { data } = await services.getAP2HTables();

      const selectedRow = selectedNOPORow as GetNopoVendorResponse;
      const { supplierCode: vendorCode, companyName } = selectedRow;
      if (vendorCode && companyName) {
        const { data } = await services.getAP2Template(companyName, vendorCode);
        const list = data.map((el, index) => ({
          ...el,
          id: index,
        }));
        dispatch(actions.configurator.setTemplateAP2List(list));
      }
      const { idCompany } = selectedNOPORow;
      const { costCenter, sign, glAccount, taxCode } = data;
      if (costCenter && sign && glAccount && taxCode) {
        const ccResp = await services.getAP2HTable(costCenter, idCompany);
        const signResp = await services.getAP2HTable(sign, idCompany);
        const taxCodeResp = await services.getAP2HTable(taxCode, idCompany);

        const optionsList: AP2OptionsList = {
          costCenter: ccResp.data,
          sign: signResp.data,
          glAccount: signResp.data,
          taxCode: taxCodeResp.data,
        };
        dispatch(actions.configurator.setAP2OptionList(optionsList));
      }
    } catch (error) {
      utils.app.notify('fail', error as Error);
    }
  }, [selectedNOPORow]);

  useEffect(() => {
    dispatch(actions.configurator.setSelectedAP2row(null));
  }, [dispatch]);

  const onCancel = () => {
    const newRows = templateList.filter((el) => !el.newRow);
    dispatch(actions.configurator.setTemplateAP2List(newRows));
    dispatch(actions.configurator.setEditedRowAP2ID(null));
    dispatch(actions.configurator.setIsEditAP2Template(false));
    dispatch(actions.configurator.setIsAddP2Template(false));
    dispatch(actions.configurator.setSelectedAP2row(null));
  };

  const confirmCreate = async (newRow: OriginalRow) => {
    try {
      const obj = {
        companyName: newRow.companyName,
        costCenter: newRow.costCenter,
        glAccount: newRow.glAccount,
        sign: newRow.sign,
        taxCode: newRow.taxCode,
        taxPercent: newRow.taxPercent,
        vendorCode: newRow.vendorCode,
      };

      const { data } = await services.createAP2Template(obj);

      const newList = templateList.map((el) => {
        if (el.id === newRow.id) {
          return { ...newRow, ...obj, newRow: false, idAp2Template: data };
        }
        return el;
      });

      // update ap2template column in main table
      if (selectedNOPORow && !selectedNOPORow.ap2Template) {
        const cpVendor = [...vendorList] as GetNopoVendorResponse[];
        if (selectedNOPORow) {
          const findIndex = cpVendor.findIndex((el) => el.idAction === selectedNOPORow.idAction);
          cpVendor[findIndex] = {
            ...cpVendor[findIndex],
            ap2Template: true,
          };
          dispatch(actions.configurator.setNoPoLeftTableRows(cpVendor));
        }
      }

      dispatch(actions.configurator.setTemplateAP2List(newList));
      dispatch(actions.configurator.setEditedRowAP2ID(null));
      dispatch(actions.configurator.setIsAddP2Template(false));
      dispatch(actions.configurator.setSelectedAP2row(null));

      utils.app.notify('success', t('ap2template-created'));
    } catch (error) {
      utils.app.notify('fail', error as Error);
    }
  };

  const confirmEdit = async (newRow: OriginalRow) => {
    try {
      let idAp2 = null;
      if (typeof newRow.id === 'string') {
        idAp2 = templateList[parseInt(newRow.id)]?.idAp2Template;
      } else if (typeof newRow.id === 'number') {
        idAp2 = templateList[newRow.id]?.idAp2Template;
      }

      const obj = {
        idAp2Template: idAp2,
        companyName: newRow.companyName,
        costCenter: newRow.costCenter,
        glAccount: newRow.glAccount,
        sign: newRow.sign,
        taxCode: newRow.taxCode,
        taxPercent: newRow.taxPercent,
        vendorCode: newRow.vendorCode,
      };

      await services.editAP2Template(obj);

      dispatch(actions.configurator.setIsEditAP2Template(false));

      const newList = templateList.map((el) => {
        if (el.id === newRow.id) {
          return { ...newRow, ...obj };
        }
        return el;
      });

      dispatch(actions.configurator.setTemplateAP2List(newList));
      dispatch(actions.configurator.setEditedRowAP2ID(null));
      dispatch(actions.configurator.setSelectedAP2row(null));

      utils.app.notify('success', t('ap2template-changed'));
    } catch (error) {
      utils.app.notify('fail', error as Error);
    }
  };

  const formValidation = yup.object().shape({
    companyName: yup.string().required(),
    vendorCode: yup.string().required(),
    sign: yup.string().required(),
    taxCode: yup.string().required(),
    glAccount: yup.string().required(),
    taxPercent: yup.string().required().max(5).min(1),
  });

  const onSelection = (rows: AP2Template[]) => {
    if (rows.length <= 0) {
      dispatch(actions.configurator.setSelectedAP2row(null));
    } else {
      dispatch(actions.configurator.setSelectedAP2row(rows[0]));
    }
  };

  return (
    <>
      <Modal.Header title={t('select_ap2_template')} />
      <Modal.Content>
        <ToolBarDetail />
        <Table
          isEditable
          hasSelection
          columns={templateListColumns}
          rows={templateList}
          hasPagination
          hasResize
          hasSort
          hasFilter
          editRowID={editRowID !== null ? editRowID.toString() : ''}
          onSelection={onSelection}
          onCancel={onCancel}
          onSave={(row) => (isEdit ? confirmEdit(row) : confirmCreate(row))}
          onSaveValidation={formValidation}
          initialSelection={editRowID !== null ? [editRowID] : undefined}
        />
      </Modal.Content>
    </>
  );
};
export default SelectAP2Template;
