import styled from 'styled-components/macro';

export const InputWrapper = styled.div``;

export const InputsContainer = styled.div<{ multi?: boolean; flexDirection?: string; marginRight?: string }>`
  & > * {
    margin: 0 5px;
  }
  margin-bottom: 8px;
  padding-bottom: 6px;
  margin-right: 160px;
  display: flex;
  flex-direction: ${({ flexDirection }) => flexDirection ?? 'row'};
  align-items: center;
  margin-right: ${({ multi, marginRight }) => marginRight ?? (!multi && '224px')};
  justify-content: ${({ multi }) => (multi ? 'flex-start' : 'flex-end')};
`;

export const InputQ1Wrapper = styled.div<{ multi?: boolean; margin?: string }>`
  display: flex;
  align-items: center;
  justify-content: flex-end;
  ${({ multi }) => multi && 'width: 140px;'}
  margin: ${({ margin }) => margin ?? '0 155px'};
`;

export const InputQ1TextAreaWrapper = styled.div<{ multi?: boolean; margin?: string }>`
  display: flex;
  justify-content: flex-end;
  ${({ multi }) => multi && 'width: 140px;'}
  margin: ${({ margin }) => margin ?? '0 155px'};
  align-items: flex-start;
`;

export const InputLabel = styled.div`
  margin-right: 15px;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  width: 100px;
  text-align: right;
  overflow: hidden;
  letter-spacing: -0.31px;
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
`;

export const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  margin-right: 168px;
`;

export const CheckboxLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  width: 100px;
  margin-right: 15px;
  text-align: right;
  letter-spacing: -0.31px;
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
`;

export const IconTextAreaWrapper = styled.div`
  align-self: stretch;
  padding-top: 4px;
  width: 14px;
  height: 14px;
`;
