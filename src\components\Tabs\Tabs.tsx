import * as React from 'react';
import { Tab, ITabProps } from './Tab';
import { Panel, IPanelProps } from './Panel';
import { TabList, TabPanels } from './style';

interface ITabsContext {
  activeTab: number;
  setActiveTab: (index: number) => void;
}

interface ITabsComposition {
  Tab: React.FC<ITabProps>;
  Panel: React.FC<IPanelProps>;
  TabList?: any;
  TabPanels?: any;
}

export interface ITabsProps {
  children?: React.ReactNode;
  nTabs?: number;
  defaultTab?: number;
  setActiveTabExternal?: (index: number) => void;
}

const TabsContext = React.createContext<ITabsContext | undefined>(undefined);

const Tabs: React.FC<ITabsProps> & ITabsComposition = (props: ITabsProps) => {
  const { children, defaultTab, setActiveTabExternal } = props;
  const [activeTab, setActiveTab] = React.useState(defaultTab || 0);

  /**
   * Memoize the context to prevent unnecessary renders.
   */
  const memoizedContextValue = React.useMemo(
    () => ({
      activeTab,
      setActiveTab,
    }),
    [activeTab],
  );

  React.useEffect(() => {
    setActiveTabExternal && setActiveTabExternal(activeTab);
  }, [activeTab, setActiveTabExternal]);

  return <TabsContext.Provider value={memoizedContextValue}>{children}</TabsContext.Provider>;
};

export const useTabs = (): ITabsContext => {
  const context = React.useContext(TabsContext);
  if (!context) {
    throw new Error('This component must be used within a <Tabs> component.');
  }
  return context;
};

Tabs.Tab = Tab;
Tabs.Panel = Panel;
Tabs.TabList = TabList;
Tabs.TabPanels = TabPanels;

export { Tabs };
