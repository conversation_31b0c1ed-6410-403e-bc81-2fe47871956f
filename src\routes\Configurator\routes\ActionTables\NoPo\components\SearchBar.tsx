import RadioButton from 'components/Buttons/RadioButton';
import MultiSelectInput from 'components/Input/MultiSelect';
import React, { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import selectors from 'state/selectors';
import utils from 'utils';
import { MicroCategoryList, WSubjectsList } from 'models/response';
import CommonButton from 'components/Buttons/CommonButton';
import { modalActionType } from 'utils/constants';
import styled from 'styled-components/macro';
import { OptionAT } from 'models/configurator';
import { checkMicrocategoryFunc, checkVendorListFunc, searchFunc } from './utils';

const Bar = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 50px;
  gap: 40px;
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
`;

const Left = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  gap: 12px;
`;

const Right = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
`;

const SearchBar = () => {
  const t = utils.intl.useTranslator();

  const selectedTable = useSelector(selectors.configurator.selectTableToSearch);
  const selectedOptionValue = useSelector(selectors.configurator.getSelectedOptionValue);
  const selectedCompanies = useSelector(selectors.configurator.getSelectedCompanies);
  const companies = useSelector(selectors.configurator.getATcompanies);

  const optionsValue = useSelector(selectors.configurator.getOptionValueDropdown);

  const dispatch = useDispatch();

  const checkMicroCategory = checkMicrocategoryFunc(selectedOptionValue);
  const checkVendorList = checkVendorListFunc(selectedOptionValue);
  const search = searchFunc(selectedTable, selectedCompanies, checkMicroCategory, dispatch, checkVendorList);
  const rows = useSelector(selectors.configurator.getNOPOLeftTableRows);
  const editedRowId = useSelector(selectors.configurator.getNOPOEditedRowId);

  const editMode = editedRowId != null && editedRowId !== '';

  const checkSelectedOptions = () => {
    const valueOption = [];

    if (selectedOptionValue.length) {
      for (let i = 0; i < selectedCompanies.length; i++) {
        for (let j = 0; j < selectedOptionValue.length; j++) {
          switch (selectedTable) {
            case 'microcategory':
              if (
                (selectedCompanies[i].microCategoryList ?? []).find((el) => el.code === selectedOptionValue[j].value)
              ) {
                valueOption.push(selectedOptionValue[j]);
              }
              break;
            case 'vendor':
              if (
                (selectedCompanies[i].wSubjectsList ?? []).find((el) => el.idWSubject === selectedOptionValue[j].value)
              ) {
                valueOption.push(selectedOptionValue[j]);
              }
          }
        }
      }
    }

    dispatch(actions.configurator.setSelectedOptionValue(valueOption));
  };

  const stableFunction = useCallback(checkSelectedOptions, [selectedCompanies]);

  useEffect(() => {
    if (selectedCompanies) {
      let listOptionsValue: OptionAT[] = [];
      if (selectedTable === 'microcategory') {
        let microCategory: MicroCategoryList[] = [];
        for (let i = 0; i < selectedCompanies.length; i++) {
          microCategory = microCategory.concat(selectedCompanies[i].microCategoryList ?? []);
        }
        listOptionsValue = microCategory.map((option) => ({
          value: option?.code ?? 'null',
          label: option?.code ? `${option?.code} - ${option?.description}` : 'null',
        }));
      }
      if (selectedTable === 'vendor') {
        let subjectList: WSubjectsList[] = [];
        for (let i = 0; i < selectedCompanies.length; i++) {
          subjectList = subjectList.concat(selectedCompanies[i].wSubjectsList ?? []);
        }
        listOptionsValue = subjectList.map((option) => ({
          value: option.supplierCode,
          label: `${option.supplierCode.toString()} - ${option.subject}`,
        }));
      }
      const arrUniqValue = listOptionsValue.filter((v, i, a) => a.findIndex((v2) => v.value === v2.value) === i);
      dispatch(actions.configurator.setOptionValueDropdown(arrUniqValue));
    }
  }, [companies, selectedCompanies, selectedTable, dispatch]);

  useEffect(() => {
    stableFunction();
  }, [stableFunction]);

  const clearAll = () => {
    dispatch(actions.configurator.setSelectedOptionValue([]));
    dispatch(actions.configurator.setNoPoLeftTableRows([]));
    dispatch(actions.configurator.setNoPoSelectedRow(null));
  };

  const switchTableToSearch = (value: string) => {
    clearAll();
    dispatch(actions.configurator.setSelectedTableToSearch(value));
  };

  return (
    <Bar>
      <Left>
        <span>{t('select_table_to_search')}</span>
        <RadioButton
          inline
          value={selectedTable}
          onChange={(value: string) => {
            switchTableToSearch(value);
          }}
          options={[
            { label: t('microcategory'), value: 'microcategory', disabled: editMode },
            { label: t('vendor'), value: 'vendor', disabled: editMode },
          ]}
        />
        <MultiSelectInput
          options={optionsValue}
          onChange={(value: OptionAT[]) => {
            dispatch(actions.configurator.setSelectedOptionValue(value));
          }}
          value={selectedOptionValue}
          labelledBy="firstSelect"
          hasSelectAll={selectedCompanies.length === 1}
          disabled={editMode}
          disableSearch={editMode}
        />
      </Left>
      <Right>
        <CommonButton
          action={() => {
            search();
          }}
          scope="tertiary"
          value={t('search')}
          disabled={!selectedOptionValue?.length || editMode}
        />
        <CommonButton
          action={() =>
            dispatch(
              actions.modal.setModal({
                actionType: modalActionType.configurator.CLEAR_CONFIRMATION,
                props: {
                  func: () => clearAll(),
                },
              }),
            )
          }
          scope="tertiary"
          value={t('clear')}
          disabled={(!selectedOptionValue?.length && !rows.length) || editMode}
        />
      </Right>
    </Bar>
  );
};
export default SearchBar;
