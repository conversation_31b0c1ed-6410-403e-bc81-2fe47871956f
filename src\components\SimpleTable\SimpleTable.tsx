import React from 'react';
import {
  TableBorder,
  RowBorder,
  StyledTableTHead,
  StyledTableTitle,
 Td,
  RowTr,
  Tbody,
} from './StyleSimpleTable';

type TableProps = {
  data: Array<{ title: string; info: string | number }>;
  title: string;
};

const SimpleTable = ({ data, title }: TableProps) => {
  return (
    <TableBorder data-testid="table">
      <StyledTableTHead>
        <RowBorder>
          <StyledTableTitle colSpan={2}> {title} </StyledTableTitle>
        </RowBorder>
      </StyledTableTHead>
      <Tbody data-testid="body">
        {data.map(({ title, info }) => (
          <RowTr key={`${title}${info}`}>
            <Td fontWeight="medium">{title}</Td>
            <Td fontWeight="light">{info}</Td>
          </RowTr>
        ))}
      </Tbody>
    </TableBorder>
  );
};

export default SimpleTable;
