import React from 'react';
import Checkbox from '../Buttons/CheckboxButton';
import '@testing-library/jest-dom';
import '@testing-library/jest-dom/extend-expect';
import { fireEvent, cleanup } from '@testing-library/react';
import { renderWithStyle } from 'utils/helpers/test.helpers';

afterEach(cleanup);

describe('Checkbox Component', () => {
  test('renders without crash', () => {
    renderWithStyle(<Checkbox isActive />);
  });

  test('manages active status correctly', () => {
    renderWithStyle(<Checkbox isActive name="test" />);
    const button = document.querySelector('input');
    expect(button?.checked).toBe(true);
  });

  test('manages inactive status correctly', () => {
    renderWithStyle(<Checkbox name="test" />);
    const button = document.querySelector('input');
    expect(button?.checked).toBe(false);
  });

  test('manages action function correctly', () => {
    const action = jest.fn();
    renderWithStyle(<Checkbox name="test" action={action} />);
    const button = document.querySelector('input');
    fireEvent.click(button);
    expect(action).toHaveBeenCalledTimes(1);
  });

  test('manages action function correctly when disabled', () => {
    const action = jest.fn();
    renderWithStyle(<Checkbox name="test" action={action} isEditable={false} />);
    const button = document.querySelector('input');
    fireEvent.click(button, { bubbles: false }); // disabled prevent bubblig I think
    expect(action).toHaveBeenCalledTimes(0);
  });

  test('manages status change correctly ', () => {
    renderWithStyle(<Checkbox name="test" isActive />);
    const button = document.querySelector('input');
    expect(button?.checked).toBe(true);
    fireEvent.click(button);
    expect(button?.checked).toBe(false);
  });
});
