import React from 'react';
import { fireEvent, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import '@testing-library/jest-dom/extend-expect';
import Accordion from 'components/Accordion';
import { renderWithStyle } from 'utils/helpers/test.helpers';

const accordionData = {
  title: 'Process efficiency',
  mainContent: <div>main content</div>,
  hiddenContent: <div>hidden content</div>
};

describe('Accordion Component', () => {
  describe('when not disabled', () => {
    let accordion = null;
    beforeEach(() => {
      accordion = <Accordion {...accordionData} />;
      renderWithStyle(accordion);
    });

    afterEach(() => {
      accordion = null;
    });

    it('should render accordion with title', () => {
      const accordionTitle = screen.getByText(accordionData.title);
      expect(accordionTitle).toBeInTheDocument();
    });

    it('should show only main content on load', () => {
      const mainContent = screen.getByText('main content');
      const hiddenContent = screen.queryAllByText('hidden content');

      expect(mainContent).toBeInTheDocument();
      expect(hiddenContent).toHaveLength(0);
    });

    it('should open accordion when click on chevron button and show hidden content', () => {
      const button = screen.getByRole('button');
      fireEvent.click(button);

      const mainContent = screen.getByText('main content');
      const hiddenContent = screen.getByText('hidden content');

      expect(mainContent).toBeInTheDocument();
      expect(hiddenContent).toBeInTheDocument();
    });
  });

  describe('when disabled', () => {
    it('should not open accordion when button is disabled', () => {
      const accordion = <Accordion {...accordionData} disabled={true} />;
      renderWithStyle(accordion);
    
      const button = screen.getByRole('button');
      fireEvent.click(button);
    
      const hiddenContent = screen.queryAllByText('hidden content');
    
      expect(hiddenContent).toHaveLength(0);
    });
  });
});

