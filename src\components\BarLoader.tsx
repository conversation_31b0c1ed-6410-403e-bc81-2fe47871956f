import React, { useEffect, useState } from 'react';
import styled from 'styled-components/macro';

const Container = styled.div`
  width: 400px;
  height: 10px;
  border-radius: 10px;
  overflow: hidden;
  background-color: ${({ theme }) => theme.colorPalette.grey.grey2};
  max-width: 100%;
`;

const LoadingBar = styled.div<{ width: number }>`
  height: 100%;
  width: ${(props) => props.width}%;
  max-width: 100%;
  min-width: 0;
  background-color: ${({ theme }) => theme.colorPalette.turquoise.dark};
  box-shadow: ${({ theme }) =>
    `0 3px 3px -5px ${theme.colorPalette.turquoise.dark}, 0 2px 5px ${theme.colorPalette.turquoise.dark}`};
  transition: width 500ms ease-in-out;
`;

export interface Props {
  id?: string;
  currentPercentage: number;
}

const LoadingBarComponent = ({ id, currentPercentage }: Props) => {
  const [width, setWidth] = useState(0);

  useEffect(() => {
    setTimeout(() => {
      setWidth(currentPercentage < 0 ? 0 : currentPercentage > 100 ? 100 : currentPercentage);
    }, 250);
  }, [currentPercentage]);

  return (
    <Container id={id}>
      <LoadingBar width={width} />
    </Container>
  );
};
export default LoadingBarComponent;
