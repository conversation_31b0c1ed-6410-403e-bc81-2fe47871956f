import PasswordModal from 'components/Modal/PasswordModal';
import UserModal from 'components/Modal/UserModal';
import ChangelogModal from 'components/Modal/ChangelogModal';
import Preloader from 'components/Preloader';
import React, { ReactNode } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import { headerHeight, toogleHeight } from 'utils/styleConstants';

import Menu from '../Menu';
import Header from '../PageHeader/LayoutHeader';
import ArrowLeft from 'images/arrow';

const Main = styled.main`
  position: relative;
  overflow: auto;
  background-color: ${({ theme }) => theme.colorPalette.white};
  @media screen and (max-width: ${({ theme }) => theme.breakpointsPalette.m}) {
    position: fixed;
    top: 70px;
    width: calc(100% - 37px);
    left: 37px;
  }
`;

const Sidebar = styled.aside<{ isMenuOpen: boolean }>`
  background-color: ${({ theme }) => theme.colorPalette.white};
  border-right: solid 1px ${({ theme }) => theme.colorPalette.grey.grey5};
  position: relative;
  width: ${({ isMenuOpen }) => (isMenuOpen ? 230 : 37)}px;
  transition: all 0.1s ease;
  @media screen and (max-width: ${({ theme }) => theme.breakpointsPalette.m}) {
    position: fixed;
    top: 70px;
    left: 0;
    background-color: ${({ theme }) => theme.colorPalette.white};
    z-index: ${({ theme }) => theme.zIndexPalette.medium};
  }
`;

const ToggleBar = styled.div`
  height: ${toogleHeight}px;
  line-height: ${toogleHeight}px;
  background-color: ${({ theme: { colorPalette } }) =>
    colorPalette.isDarkMode ? colorPalette.grey.grey2 : colorPalette.turquoise.background};
  color: ${({ theme: { colorPalette } }) => colorPalette.turquoise.dark};
  padding-right: 10px;
  text-align: right;
  cursor: pointer;
  font-family: 'Roboto';
`;

const Grid = styled.div<{ isMenuOpen: boolean }>`
  display: grid;
  grid-template-columns: ${({ isMenuOpen }) => (isMenuOpen ? 230 : 37)}px auto;
  grid-template-rows: ${headerHeight}px calc(100vh - ${headerHeight}px);
  grid-template-areas:
    'header header'
    'menu main';
  > Header {
    grid-area: header;
  }
  Menu {
    visibility: ${({ isMenuOpen }) => (isMenuOpen ? 'visible' : 'hidden')};
  }
`;

interface Props {
  children: ReactNode;
}

const Layout = (props: Props) => {
  const isMenuOpen = useSelector(selectors.app.getMenu);
  const dispatch = useDispatch();

  return (
    <Grid id="layout" isMenuOpen={isMenuOpen}>
      <Preloader area="app" />
      <Preloader area="leftBottom" />
      <Header />
      <Sidebar isMenuOpen={isMenuOpen}>
        <Preloader area="menu" />
        <ToggleBar onClick={() => dispatch(actions.app.toggleMenu())}>
          {isMenuOpen ? <ArrowLeft /> : <ArrowLeft rotation={180} />}
        </ToggleBar>
        <Menu />
      </Sidebar>
      <Main>
        <Preloader area="module" />
        {props.children}
      </Main>
      <UserModal />
      <PasswordModal />
      <ChangelogModal />
    </Grid>
  );
};

export default Layout;
