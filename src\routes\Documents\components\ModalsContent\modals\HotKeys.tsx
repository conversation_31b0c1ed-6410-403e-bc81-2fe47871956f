import React, { useState, useEffect } from 'react';
import utils from 'utils';
import Header from 'components/Modal/Header';
import { hotActions, HotActionDocumentList } from '../../../hotActions';
import styled from 'styled-components/macro';
import selectors from 'state/selectors';
import { useSelector } from 'react-redux';
import { actionType } from 'utils/constants';
import { RootState } from 'models';

const KeyboardKey = styled.span`
  border: solid 1px ${({ theme }) => theme.colorPalette.black};
  font-size: small;
  background-color: ${({ theme }) => theme.colorPalette.white};
  padding: 5px;
  margin: 0 5px;
  min-width: 22px;
  border-radius: 3px;
  display: inline-block;
  text-align: center;
  margin-bottom: 10px;
`;

const Hotkeys = () => {
  const t = utils.intl.useTranslator();
  const isEditView = utils.documents.isOpenDocumentView();
  const activeActions = isEditView ? hotActions : HotActionDocumentList;
  const activeTemplate = useSelector(selectors.documents.selectActiveTemplateId);
  const templates = useSelector(selectors.documents.selectTemplates);

  const rows = useSelector(selectors.documents.selectTableRows);
  const protocol = useSelector(selectors.documents.selectActiveDocumentID);

  const autosave =
    useSelector((store: RootState) => selectors.app.getConfig(store, actionType.AUTOSAVE_DOCUMENT))?.val === 1;
  const [hasRegProtocol, setHasRegProtocol] = useState<boolean>(false);
  const isReadOnly = useSelector(selectors.documents.selectIsTemplateReadOnly);
  const isDocReadOnly = useSelector(selectors.documents.selectIsDocReadOnly);

  useEffect(() => {
    if (protocol) {
      const registrationProtocol = rows.find((ele) => ele.protocol === protocol)?.registrationProtocol;
      const barcodeLast = rows.find((ele) => ele.protocol === protocol)?.protocolIn.slice(-1);
      setHasRegProtocol(registrationProtocol || barcodeLast === 'S' ? true : false);
    }
  }, [protocol, rows]);

  return (
    <>
      <Header title={t('hotkeys')} />
      {activeActions.map(({ code, id, alwaysShow = false, isAction = false }) => {
        const hotKey = () => {
          return (
            <p key={code}>
              {code.split('+').map((el, index) => (
                <>
                  {' '}
                  {index ? '+' : ''}
                  <KeyboardKey key={el}>{el}</KeyboardKey>
                </>
              ))}
              : {t(id)}
            </p>
          );
        };
        if (document.getElementById(id) || alwaysShow) {
          return hotKey();
        } else if (isAction) {
          const actualTemplate = templates.find((e) => e.idTemplate === activeTemplate);
          if (actualTemplate) {
            if (
              (isEditView && actualTemplate.actions_document_open.find((e) => e === id)) ||
              actualTemplate.actions_document_list.find((e) => e === id)
            ) {
              return hotKey();
            }
          }
        } else if (id === actionType.SAVE_DOCUMENT && !autosave && !(isReadOnly || hasRegProtocol || isDocReadOnly)) {
          return hotKey();
        }
        return null;
      })}
    </>
  );
};

export default Hotkeys;
