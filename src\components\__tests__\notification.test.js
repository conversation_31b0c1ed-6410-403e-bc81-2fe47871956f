import React from 'react';
import Notification from '../Notification';
import '@testing-library/jest-dom';
import { screen, cleanup } from '@testing-library/react';
import { renderWithStyle } from 'utils/helpers/test.helpers';
import { colorPalette } from 'utils/styleConstants';

afterEach(cleanup);

describe('Notification Component', () => {
  test('renders without crash', () => {
    renderWithStyle(<Notification type="success" message="It works!" />);
    const element = screen.getByTestId('notification');
    expect(element).toBeInTheDocument();
  });

  test('has the right fail color background and text', () => {
    renderWithStyle(<Notification type="fail" message="Oh noooo!" />);
    const element = screen.getByTestId('notification');
    expect(element).toHaveStyle(`background-color: ${colorPalette.red.medium}`);
    expect(screen.getByText('Fail')).toBeInTheDocument();
    expect(screen.getByText('Oh noooo!')).toBeInTheDocument();
  });

  test('has the right warning color background and text', () => {
    renderWithStyle(<Notification type="warning" message="Ops! Something went wrong!" />);
    const element = screen.getByTestId('notification');
    expect(element).toHaveStyle(`background-color: ${colorPalette.orange.medium}`);
    expect(screen.getByText('Warning')).toBeInTheDocument();
    expect(screen.getByText('Ops! Something went wrong!')).toBeInTheDocument();
  });

  test('has the right succes color background and text', () => {
    renderWithStyle(<Notification type="success" message="All rigth!" />);
    const element = screen.getByTestId('notification');
    expect(element).toHaveStyle(`background-color: ${colorPalette.green.medium}`);
    expect(screen.getByText('Success')).toBeInTheDocument();
    expect(screen.getByText('All rigth!')).toBeInTheDocument();
  });
});
