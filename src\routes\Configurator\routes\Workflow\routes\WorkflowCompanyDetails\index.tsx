import MultiTabs from 'components/Tabs/MultiTabs';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useAsync } from 'react-use';
import { Wrapper, Title } from 'routes/Configurator/styles';
import services from 'services';
import actions from 'state/actions';
import selectors from 'state/selectors';
import utils from 'utils';
import Associations from './tabs/Associations';
import DefTasksGroupTab from './tabs/DefinitionTasksGroups';

const WorkflowCompanyDetails = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const selectedCompanyDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  const isWfDefinitionInEdit = useSelector(selectors.configurator.getIsWfTableEdit);
  const isIsWFAssociationTableEdit = useSelector(selectors.configurator.getEditedRowIdWfAssociations) !== null;
  const getSelectedTab = useSelector(selectors.configurator.getSelectedTab);

  useEffect(() => {
    const button = document.getElementById('view-mode-workflow');
    if (button) {
      button.style.display = 'none';
    }
  }, []);

  useAsync(async () => {
    if (selectedCompanyDefinition?.idCompany) {
      const { data } = await services.getCompanyDefinitions(selectedCompanyDefinition?.idCompany);
      dispatch(actions.configurator.setListOfDefinitionsForCompanySelected(data));
    }
  });

  const tabs = [
    {
      name: t('definition_tasks_groups'),
      content: <DefTasksGroupTab />,
      id: 0,
      disabled: isWfDefinitionInEdit || isIsWFAssociationTableEdit,
    },
    {
      name: t('associations'),
      content: <Associations />,
      id: 1,
      disabled: isWfDefinitionInEdit || isIsWFAssociationTableEdit,
    },
  ];

  const setSelectedTab = (id: number) => {
    let subRow = '';

    switch (id) {
      case 0:
        subRow = 'WORKFLOW_DEFINITION_TASK_GROUP';
        break;
      case 1:
        subRow = 'WORKFLOW_ASSOCIATIONS';
        break;

      default:
    }
    dispatch(actions.configurator.setSelectedTab(subRow));
    if (subRow === 'WORKFLOW_DEFINITION_TASK_GROUP' && getSelectedTab === 'WORKFLOW_ASSOCIATIONS')
      dispatch(actions.configurator.setSelectedDefinitionTaskGroup('WORKFLOW_DEFINITION'));
  };

  return (
    <Wrapper>
      <Title>
        <h2>{t('workflow_configurator_for') + ' ' + selectedCompanyDefinition?.companyName}</h2>
      </Title>
      <MultiTabs tabs={tabs} tabWidth="50" setActiveTabExternal={(id: any) => setSelectedTab(id)} />
    </Wrapper>
  );
};

export default WorkflowCompanyDetails;
