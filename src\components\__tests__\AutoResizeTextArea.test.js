import AutoResizeTextArea from '../Input/AutoResizeTextArea';
import React from 'react';
import '@testing-library/jest-dom';
import { fireEvent, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom/extend-expect';
import { renderWithStyle } from 'utils/helpers/test.helpers';

describe('AutoResizeTextArea', () => {
  it('gets rendered in DOM and has a starting date', async () => {
    renderWithStyle(<AutoResizeTextArea maxHeight={165} />);
    const textArea = screen.getByDisplayValue('');
    expect(textArea).toBeInTheDocument();
  });
  it('has a max height', async () => {
    renderWithStyle(<AutoResizeTextArea maxHeight={201} />);
    const textArea = screen.getByDisplayValue('');
    expect(textArea).toBeInTheDocument();
    expect(textArea).toHaveStyle({ maxHeight: '201px' });
  });
  it('has a min height', async () => {
    renderWithStyle(<AutoResizeTextArea minHeight={30} value={''} />);
    const textArea = screen.getByDisplayValue('');
    expect(textArea).toHaveStyle({ minHeight: '30px' });
  });
  it('has a value', async () => {
    renderWithStyle(<AutoResizeTextArea value={'test'} />);
    const textArea = screen.getByDisplayValue('test');
    expect(textArea).toBeInTheDocument();
  });
  it('has a feedback message', async () => {
    renderWithStyle(<AutoResizeTextArea hasFeedBack={true} feedBackMessage={'Error message'} />);
    const paragraph = screen.getByText('Error message');
    expect(paragraph).toBeInTheDocument();
  });
  it('has a feedback color', async () => {
    renderWithStyle(<AutoResizeTextArea hasFeedBack={true} feedbackColor={'blue'} />);
    const textArea = screen.getByDisplayValue('');
    expect(textArea).toHaveStyle({ border: '1px solid blue' });
  });
  it('should change value', async () => {
    renderWithStyle(<AutoResizeTextArea value={''} />);
    const textArea = screen.getByDisplayValue('');
    fireEvent.change(textArea, { target: { value: 'test2' } });
    expect(textArea).toHaveValue('test2');
  });
  it('should resize when value is changed', async () => {
    renderWithStyle(<AutoResizeTextArea maxHeight={165} />);
    const textArea = screen.getByDisplayValue('');
    userEvent.type(textArea, 'lorem ipsum olor'.repeat(20));
    expect(textArea).toBeInTheDocument();
    expect(textArea).toHaveStyle({ maxHeight: '165px' });
  });
  it('should change font size', () => {
    renderWithStyle(<AutoResizeTextArea value="test" fontSize={14} />);
    const textArea = screen.getByDisplayValue('test');

    const styles = getComputedStyle(textArea);
    expect(styles.fontSize).toBe('14px');
  });
});
