import React from 'react';
import styled from 'styled-components/macro';
import CommonButton from '../Buttons/CommonButton';
import utils from '../../utils';
import { EditStates } from 'models/configurator';

const ModificationDiv = styled.div`
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  padding-bottom: 20px;
  display: 'inline-block';
  padding-top: 16px;
`;
type EditRowProps = {
  cancelAction?: (idx: number) => void;
  editAction?: () => void;
  onSaveAction?: (savedRow: any) => void;
  addAction?: () => void;
  saveConstraints?: boolean;
  row?: any;
  isEdit: EditStates;
  gotoPage: (page: number) => void;
  pageCount: number;
  // paging: boolean;
  hasPagination: boolean | undefined;
};
const EditRow = (props: EditRowProps) => {
  const {
    cancelAction,
    onSaveAction,
    row,
    isEdit,
    gotoPage,
    pageCount,
    // paging,
    hasPagination,
    saveConstraints,
  } = props;
  const t = utils.intl.useTranslator();
  return (
    <ModificationDiv>
      {isEdit !== 'inactive' ? (
        <>
          <CommonButton
            value={t('cancel')}
            scope={'tertiary'}
            icon="times"
            action={() => {
              cancelAction?.(row?.index);
              if (hasPagination) gotoPage(pageCount - 1);
            }}
            noPadding
          />
          <CommonButton
            value={t('save-row')}
            icon="circle"
            scope={'tertiary'}
            action={() => {
              onSaveAction?.(row);
            }}
            disabled={saveConstraints !== undefined && !saveConstraints}
            noPadding
          />
        </>
      ) : (
        <></>
      )}
    </ModificationDiv>
  );
};
export default EditRow;
