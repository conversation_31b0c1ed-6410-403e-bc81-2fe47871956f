/* eslint-disable max-lines */
import Checkbox from 'components/Buttons/CheckboxButton';
import Table, { TableColumn } from 'components/Table';
import {
  WorkflowCompaniesDefinition,
  WorkflowCompaniesDefinitionListExtended,
  WorkflowTaskList,
} from 'models/response';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Wrapper, Title } from 'routes/Configurator/styles';
import actions from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import utils from 'utils';
import { modalActionType } from 'utils/constants';
import { suspendTypes } from '../utils';

const CustomTitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.M};
  font-weight: bold;
`;
const SubTitle = styled.div`
  color: ${({ theme }) => theme.colorPalette.grey.grey7};
  margin: 25px 0px;
`;
const WrapperTables = styled.div`
  display: flex;
  flex-direction: row;
`;
const FirstTable = styled.div`
  padding-right: 15px;
`;
const UnderLine = styled.div`
  text-decoration: underline;
  color: ${({ theme }) => theme.colorPalette.turquoise.dark};
`;

const WorkflowMainView = () => {
  const t = utils.intl.useTranslator();

  const dispatch = useDispatch();

  const selectedCompanyDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  const companiesDefinition = useSelector(selectors.configurator.getCompaniesDefinition);

  const suspendTypesList = suspendTypes('workflowMain');

  const [transformedCompaniesDef, setTransformedCompaniesDef] = useState<
    {
      companyName: string;
      idCompany: number;
      subRows: WorkflowCompaniesDefinitionListExtended[];
    }[]
  >([]);

  const definitionColumns: TableColumn[] = [
    {
      accessor: 'companyName',
      Header: t('companyName'),
      Cell: 'withCounter',
    },
    {
      accessor: 'wfName',
      Header: t('wfName'),
      Cell: ({ value, row }: { value: any; row: any }) => {
        return <>{row.original.idDefinition ? value : '-'}</>;
      },
    },
    {
      accessor: 'suspendType',
      Header: t('suspendType'),
      selectOptionsTypes: suspendTypesList,
      hasLocalKeys: ['LocalizableStringFe', 'workflowMain'],
    },
    {
      accessor: 'noteMandatory',
      Header: t('noteMandatory'),
      Cell: ({ value, row }: { value: any; row: any }) => {
        return row.original.idDefinition ? (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox isActive={value} isEditable={false}></Checkbox>
          </div>
        ) : (
          '-'
        );
      },
      sortType: 'boolean',
    },
    {
      accessor: 'currencyExchange',
      Header: t('currencyExchange'),
      Cell: ({ value, row }: { value: any; row: any }) => {
        return row.original.idDefinition ? (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox isActive={value} isEditable={false}></Checkbox>
          </div>
        ) : (
          '-'
        );
      },
      sortType: 'boolean',
    },
    {
      accessor: 'takeNextSingleTask',
      Header: t('takeNextSingleTask'),
      Cell: ({ value, row }: { value: any; row: any }) => {
        return row.original.idDefinition ? (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox isActive={value} isEditable={false}></Checkbox>
          </div>
        ) : (
          '-'
        );
      },
      sortType: 'boolean',
    },
    {
      accessor: 'taskOnMinCausalWeight',
      Header: t('taskOnMinCausalWeight'),
      Cell: ({ value, row }: { value: any; row: any }) => {
        return row.original.idDefinition ? (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox isActive={value} isEditable={false}></Checkbox>
          </div>
        ) : (
          '-'
        );
      },
      sortType: 'boolean',
    },
  ];

  const taskColumns: TableColumn[] = [
    { accessor: 'idTask', Header: t('idTask') },
    { accessor: 'taskName', Header: t('taskName') },
    {
      accessor: 'groupList.length',
      Header: t('groups_length'),
      Cell: ({ value, row }: { value: number; row: any }) => {
        return (
          <UnderLine
            onClick={() => {
              dispatch(actions.configurator.setSelectedTask(row.original));
              if (value > 0) {
                dispatch(
                  actions.modal.setModal({
                    actionType: modalActionType.configurator.GROUPS_USERS_MODAL,
                  }),
                );
              }
            }}
          >
            {value}
          </UnderLine>
        );
      },
    },
  ];

  useEffect(() => {
    const button = document.getElementById('view-mode-workflow');
    if (button) {
      button.style.display = 'block';
    }
  }, []);

  const transformedRows = (rows: WorkflowCompaniesDefinition[]) => {
    let id = 0;
    const newRows: {
      id: number;
      companyName: string;
      idCompany: number;
      subRows: WorkflowCompaniesDefinitionListExtended[];
      idDefinition?: number;
      wfName?: string;
      suspendType?: number;
      noteMandatory?: boolean;
      currencyExchange?: boolean;
      takeNextSingleTask?: boolean;
      taskOnMinCausalWeight?: boolean;
      taskList?: WorkflowTaskList[];
    }[] = [];
    rows.forEach((row) => {
      const { companyName, idCompany, companyDefinitions } = row;
      if (row.companyDefinitions.length === 1) {
        const companyDefinition = row.companyDefinitions[0];
        const newList = {
          id,
          companyName,
          idCompany,
          subRows: [],
          currencyExchange: companyDefinition.currencyExchange,
          idDefinition: companyDefinition.idDefinition,
          noteMandatory: companyDefinition.noteMandatory,
          suspendType: companyDefinition.suspendType || undefined,
          takeNextSingleTask: companyDefinition.takeNextSingleTask,
          taskOnMinCausalWeight: companyDefinition.taskOnMinCausalWeight,
          taskList: companyDefinition.taskList,
          wfName: companyDefinition.wfName,
        };
        newRows.push(newList);
      } else {
        const newList = {
          id,
          companyName,
          idCompany,
          subRows:
            companyDefinitions.length > 1
              ? companyDefinitions.map((el) => ({
                  id: ++id,
                  isSubRow: true,
                  ...el,
                }))
              : [],
        };
        newRows.push(newList);
      }
      id++;
    });
    return newRows;
  };

  useEffect(() => {
    const transformedList = transformedRows(companiesDefinition);
    setTransformedCompaniesDef(transformedList);
  }, [companiesDefinition]);

  const onSelectionCompanyDefinition = (rows: WorkflowCompaniesDefinitionListExtended[]) => {
    dispatch(actions.configurator.setSelectedCompanyDefinition(rows?.length ? rows[0] : null));
  };

  useEffect(() => {
    dispatch(actions.configurator.setSelectedCompanyDefinition(null));
  }, [dispatch]);

  return (
    <Wrapper>
      <Title>
        <h2>{t('workflow_configurator')}</h2>
      </Title>
      {companiesDefinition.length === 0 ? <SubTitle>{t('please_select_company')}</SubTitle> : null}
      {companiesDefinition.length > 0 ? (
        <>
          <SubTitle>{t('please_select_row')}</SubTitle>
          <WrapperTables>
            <FirstTable>
              <CustomTitle>{t('definition')}</CustomTitle>
              <Table
                rowId="id"
                hasToolbar
                onSelection={(rows: WorkflowCompaniesDefinitionListExtended[]) => {
                  onSelectionCompanyDefinition(rows);
                }}
                columns={definitionColumns}
                rows={transformedCompaniesDef}
                hasSelection
                hasPagination
                hasResize
                hasSort
                hasFilter
                useExpandibleRows
              />
            </FirstTable>
            {selectedCompanyDefinition && selectedCompanyDefinition.taskList?.length ? (
              <div>
                <CustomTitle>{t('task')}</CustomTitle>
                <Table
                  hasToolbar
                  columns={taskColumns}
                  rows={selectedCompanyDefinition.taskList}
                  hasPagination
                  hasResize
                  hasSort
                  hasFilter
                />
              </div>
            ) : null}
          </WrapperTables>
        </>
      ) : null}
    </Wrapper>
  );
};

export default WorkflowMainView;
