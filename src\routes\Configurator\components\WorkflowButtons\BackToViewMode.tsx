import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actionType } from 'utils/constants';

import utils from 'utils';
import actions from 'state/actions';

import CommonButton from 'components/Buttons/CommonButton';
import { useHistory, useRouteMatch } from 'react-router';
import selectors from 'state/selectors';

const BackToViewMode = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const history = useHistory();
  const { path } = useRouteMatch();

  const isIsWFAssociationTableEdit = useSelector(selectors.configurator.getEditedRowIdWfAssociations) !== null;
  // use getIsWfTableEdit or isIsWFAssociationTableEdit to disable the button when is in edit mode
  const isDisabled = useSelector(selectors.configurator.getIsWfTableEdit) || isIsWFAssociationTableEdit;

  return (
    <CommonButton
      action={() => {
        dispatch(actions.configurator.setViewMode(true));
        dispatch(actions.configurator.setSelectedTab('WORKFLOW_DEFINITION_TASK_GROUP'));
        dispatch(actions.configurator.setSelectedDefinitionTaskGroup('WORKFLOW_DEFINITION'));
        history.push(`${path}/WORKFLOW`);
      }}
      scope="tertiary"
      value={t(actionType.BACK_TO_VIEW_MODE)}
      icon="circle"
      disabled={isDisabled}
    />
  );
};

export default BackToViewMode;
