import React from 'react';
import helper from 'utils';
import DatePicker from 'components/Input/Datepicker';
import { Props } from '.';
import 'react-datepicker/dist/react-datepicker.css';

const date = (props: Props) => {
  const column = props;
  const { filterValue, setFilter, dateFormat } = column;

  const handleChange = (date: Date | null) => {
    if (date !== null) {
      const formatDate = helper.date.formatDateForDb(date);
      setFilter(formatDate);
    } else {
      setFilter(null);
    }
  };
  const dateValue = filterValue ? helper.date.convertToObjectDate(filterValue) : null;

  return (
    <DatePicker
      inputWidth={'100%'}
      selected={dateValue}
      isClearable
      fontSize={10}
      small
      portal
      noBorder
      dateFormat={dateFormat}
      disabled={false}
      onChange={handleChange}
    />
  );
};

export default date;
