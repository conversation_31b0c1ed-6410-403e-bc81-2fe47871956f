import first from 'images/pagination-first.svg';
import last from 'images/pagination-last.svg';
import next from 'images/pagination-next.svg';
import previous from 'images/pagination-previous.svg';
import React from 'react';
import styled from 'styled-components/macro';
import { fontSizePalette, opacity } from 'utils/styleConstants';

const Img = styled.img<{ disabled: boolean }>`
  width: 10px;
  cursor: pointer;
  margin: 5px;
  ${({ disabled }) => disabled && `opacity: ${opacity.medium}`}
`;

const StyledPagination = styled.div<{ marginTop: number }>`
  margin-top: ${({ marginTop }) => marginTop}px;
  padding: 16px 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: ${fontSizePalette.body.XXS};
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
`;

interface PaginationProps {
  marginTop?: number;
  pageIndex: number;
  pageCount: number;
  pageOptions: number[];
  nextPage: () => void;
  canPreviousPage: boolean;
  canNextPage: boolean;
  gotoPage: (page: number) => void;
  previousPage: () => void;
}

const Pagination = (props: PaginationProps) => {
  const {
    gotoPage,
    canPreviousPage,
    canNextPage,
    nextPage,
    pageCount,
    pageOptions,
    pageIndex,
    marginTop = 0,
    previousPage,
  } = props;
  return (
    <StyledPagination className="table-pagination" marginTop={marginTop}>
      <Img src={first} onClick={() => gotoPage(0)} disabled={!canPreviousPage || pageCount === 1} />
      <Img src={previous} onClick={() => previousPage()} disabled={!canPreviousPage || pageCount === 1} />
      {pageCount === 0 ? 0 : pageIndex + 1} of {pageOptions.length}
      <Img src={next} onClick={() => nextPage()} disabled={!canNextPage || pageCount === 1} />
      <Img src={last} onClick={() => gotoPage(pageCount - 1)} disabled={!canNextPage || pageCount === 1} />
    </StyledPagination>
  );
};

export default Pagination;
