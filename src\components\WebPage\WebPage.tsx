import React from 'react';
import styled from 'styled-components/macro';

type Props = {
  url: string;
};

const Container = styled.div`
  height: 92vh;
`;

const WebPage = ({ url }: Props) => {
  const jwt = localStorage.getItem('jwt');
  return (
    <Container className="web-page-container">
      <iframe src={url ? `${url}?bearer=${jwt}` : 'about:blank'} title="Web Page" width="100%" height="100%" />
    </Container>
  );
};

export default WebPage;
