import React from 'react';

import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';
import { useDispatch, useSelector } from 'react-redux';
import selectors from 'state/selectors';
import { definitionTaskGroupTabs } from 'utils/constants';
import actions from 'state/actions';
import { actionType } from 'utils/constants';

const Edit = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);

  const isWfDefinitionInEdit = useSelector(selectors.configurator.getIsWfTableEdit);
  const selectedDefinitionTaskGroup = useSelector(selectors.configurator.getSelectedDefinitionTaskGroup);
  const selectedDefinitionFromCompanySelected = useSelector(
    selectors.configurator.getSelectedDefinitionFromCompanySelected,
  );
  const selectedGroupFromCompanySelected = useSelector(selectors.configurator.getSelectedGroupFromCompanySelected);

  // WF DEFINITION
  const editWfDefinition = () => {
    if (selectedDefinitionFromCompanySelected) {
      dispatch(actions.configurator.setEditedRowIdWfDefinition(selectedDefinitionFromCompanySelected?.idDefinition));
      dispatch(actions.configurator.setIsWfTableEdit(true));
    }
  };

  // WF GROUPS
  const editWfGroup = () => {
    if (selectedGroupFromCompanySelected) {
      dispatch(actions.configurator.setEditedRowIdWfGroup(selectedGroupFromCompanySelected?.idGroup));
      dispatch(actions.configurator.setIsWfTableEdit(true));
    }
  };

  const editRow = () => {
    switch (selectedDefinitionTaskGroup) {
      case definitionTaskGroupTabs.WORKFLOW_DEFINITION:
        editWfDefinition();
        break;
      case definitionTaskGroupTabs.WORKFLOW_GROUP:
        editWfGroup();
    }
  };

  const checkDisabledButton = () => {
    switch (selectedDefinitionTaskGroup) {
      case definitionTaskGroupTabs.WORKFLOW_DEFINITION:
        if (
          isWfDefinitionInEdit ||
          selectedDefinitionFromCompanySelected === null ||
          !utils.user.isActionByCompanyActive(actionType.EDIT_WF_DEFINITION, companiesDefinition?.companyName)
        )
          return true;
        break;
      case definitionTaskGroupTabs.WORKFLOW_GROUP:
        if (
          isWfDefinitionInEdit ||
          selectedGroupFromCompanySelected === null ||
          !utils.user.isActionByCompanyActive(actionType.EDIT_WF_GROUP, companiesDefinition?.companyName)
        )
          return true;
        break;

      default:
        return false;
    }
  };

  return (
    <CommonButton
      action={() => {
        editRow();
      }}
      scope="tertiary"
      value={t('edit')}
      icon="circle"
      disabled={checkDisabledButton()}
    />
  );
};

export default Edit;
