import styled from 'styled-components/macro';

interface VariantProps {
  variant?: 'dot';
}

export const DataWrapper = styled.div`
  display: flex;
  align-items: center;
`;

export const Dot = styled.div`
  height: 10px;
  width: 10px;
  background-color: ${({ color, theme }) => color || theme.colorPalette.grey.grey7};
  border-radius: 50%;
`;

export const ValueWrapper = styled.div<VariantProps>`
  padding-left: ${({ variant }) => (variant === 'dot' ? '10px' : '0')};
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.turquoise.light : theme.colorPalette.turquoise.dark};
  > div {
    display: inline-block;
  }
`;

export const Value = styled.div<VariantProps>`
  font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  font-size: ${({ variant }) => (variant === 'dot' ? '44px' : '40px')};
  padding-right: 6px;
`;

export const ValueUnit = styled.div<VariantProps>`
  font-size: ${({ variant }) => (variant === 'dot' ? '24px' : '40px')};
  font-weight: ${({ variant, theme }) =>
    variant === 'dot' ? theme.fontWeightPalette.regular : theme.fontWeightPalette.medium};
`;

export const Description = styled.div<VariantProps>`
  padding-left: ${({ variant }) => (variant === 'dot' ? '18px' : '0')};
  padding-top: 5px;
  line-height: 1.2;
  font-size: ${({ variant }) => (variant === 'dot' ? '14px' : '18px')};
  color: ${({ variant, theme }) => (variant === 'dot' ? theme.colorPalette.grey.grey7 : theme.colorPalette.grey.grey9)};
`;
