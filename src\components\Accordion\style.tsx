import styled from 'styled-components/macro';

export const AccordionWrapper = styled.div`
  width: 100%;
  display: flex;
  justify-content: space-between;
  border-radius: 8px;
  margin-bottom: 10px;
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.white};
  box-shadow: ${({ theme }) => theme.boxShadowPalette.medium};
`;

export const Line = styled.div`
  border-top-left-radius: 8px;
  border-bottom-left-radius: 8px;
  width: 10px;
  background-color: ${({ theme }) => theme.colorPalette.turquoise.dark};
`;

export const ContentGrid = styled.div`
  display: grid;
  grid-template-areas: 'title content';
  grid-template-columns: 30% auto;
  width: 100%;
  align-items: center;
  gap: 30px;
  margin: 20px;
`;

export const HiddenContentGrid = styled(ContentGrid)`
  align-items: flex-start;
`;

export const Content = styled.div`
  flex: 1;
`;

export const MainContent = styled.div`
  min-height: 95px;
  align-items: center;
  display: flex;
  padding-right: 20px;
  color: ${({ theme }) => theme.colorPalette.black};
`;

export const Heading = styled.div`
  font-size: ${({ theme }) => theme.fontSizePalette.body.XL};
  text-transform: uppercase;
  color: ${({ theme }) => theme.colorPalette.grey.grey13};
  font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  flex-basis: 33.33%;
`;

export const Button = styled.button`
  background: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.turquoise.normal : theme.colorPalette.turquoise.light};
  border: none;
  cursor: pointer;
  margin: 37px 20px;
  width: 62px;
  height: 62px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 5px;
  opacity: ${({ disabled }) => (disabled ? 0.5 : 1)};

  div {
    display: flex;
    justify-content: center;
    align-items: center;
  }
`;
