import React from 'react';
import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';
import actions from 'state/actions';
import { useSelector, useDispatch } from 'react-redux';
import { modalActionType } from 'utils/constants';
import { actionType } from 'utils/constants';
import services from 'services';
import {
  flattenArray,
  rowWithMultipleSubRowColumn,
  rowWithMultipleSubRowLinear,
  SubRowType,
  SubRowTypeColumn,
  voidParam,
} from 'routes/Configurator/routes/Mapper/utils';
import { DeleteTemplateFields } from 'models/request';

type deleteRowType = rowWithMultipleSubRowLinear | rowWithMultipleSubRowColumn | SubRowType | SubRowTypeColumn;

function isLinear(row: deleteRowType): row is rowWithMultipleSubRowLinear | SubRowType {
  return (row as any).idField !== undefined;
}
function isSubRow(row: deleteRowType): row is SubRowType | SubRowTypeColumn {
  return (
    (row as rowWithMultipleSubRowColumn).subRows === undefined ||
    (row as rowWithMultipleSubRowColumn).subRows.length === 0
  );
}
function isVoidParam(val: any): val is voidParam {
  return val === '-';
}
const DeleteRowButton = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const templateSelectedRowId = useSelector(selectors.configurator.selectMapperSelectedRowId);
  const templateEditRowID = useSelector(selectors.configurator.selectMapperEditRowId);

  const templateID = useSelector(selectors.configurator.selectActiveOTemplateId);
  const rowToDelete = useSelector(selectors.configurator.selectMapperSingleRowOrSubRow);

  const onCancel = () => {
    dispatch(actions.configurator.setMapperEditRowId(''));
    dispatch(actions.configurator.setMapperSelectedRowId(''));
  };

  const convertToDeleteRows = (row: deleteRowType) => {
    if (templateID !== null) {
      if (isLinear(row)) {
        if (isSubRow(row)) {
          if (isVoidParam(row.type) || isVoidParam(row.inputType)) {
            return [];
          }
          const rowToDelete: DeleteTemplateFields = [
            {
              idTemplate: Number(templateID),
              idField: row.idField,
              idColumn: null,
              content: row.content,
              page: row.page,
              x1: row.x1,
              y1: row.y1,
              x2: row.x2,
              y2: row.y2,
              rowNumber: row.rowNumber,
            },
          ];
          return rowToDelete;
        }
        return row.subRows.map((subRow) => {
          return {
            idTemplate: Number(templateID),
            idField: subRow.idField,
            idColumn: null,
            content: subRow.content,
            page: subRow.page,
            x1: subRow.x1,
            y1: subRow.y1,
            x2: subRow.x2,
            y2: subRow.y2,
            rowNumber: subRow.rowNumber,
          };
        });
      }
      if (isSubRow(row)) {
        if (isVoidParam(row.type) || isVoidParam(row.inputType)) {
          return [];
        }
        const rowToDelete: DeleteTemplateFields = [
          {
            idTemplate: Number(templateID),
            idField: null,
            idColumn: row.idColumn,
            content: row.content,
            page: row.page,
            x1: row.x1,
            y1: row.y1,
            x2: row.x2,
            y2: row.y2,
            rowNumber: row.rowNumber,
          },
        ];
        return rowToDelete;
      }
      return row.subRows.map((subRow) => {
        return {
          idTemplate: Number(templateID),
          idField: null,
          idColumn: subRow.idColumn,
          content: subRow.content,
          page: subRow.page,
          x1: subRow.x1,
          y1: subRow.y1,
          x2: subRow.x2,
          y2: subRow.y2,
          rowNumber: subRow.rowNumber,
        };
      });
    }
    return [];
  };
  const fetchFieldsData = async () => {
    try {
      if (templateID === null) return;
      const { data } = await services.getTemplateFields(Number(templateID));
      dispatch(actions.configurator.setMapperFields(data));
      dispatch(actions.configurator.setMapperRows(flattenArray(data)));
      dispatch(actions.configurator.setMapperEditRowId(''));
      dispatch(actions.configurator.setMapperSelectedRowId(''));
    } catch (error) {
      console.error(error);
    }
  };

  const onDeleteRow = async () => {
    try {
      if (rowToDelete !== undefined && templateID !== null) {
        dispatch(actions.configurator.deleteRowOrSingleSubRow({ id: Number(rowToDelete.id) }));
        const response = await services.deleteTemplateFields(convertToDeleteRows(rowToDelete));
        if (response.data?.statusCode !== undefined && response.data?.statusCode <= 0) {
          throw new Error(response.data?.statusMessage);
        }
        utils.app.notify('success', t('Row deleted successfully'));
        dispatch(actions.modal.closeModal());
        fetchFieldsData();
        return;
      }
      throw new Error('rowToDelete is null or templateID is null');
    } catch (error) {
      console.error(error);
      utils.app.notify('fail', t(error as string));
      onCancel();
    }
  };

  return (
    <CommonButton
      action={() =>
        dispatch(
          actions.modal.setModal({
            actionType: modalActionType.configurator.DELETE_CONFIRMATION,
            props: {
              subtitle: t('Are you sure to remove this rows?'),
              func: () => onDeleteRow(),
            },
          }),
        )
      }
      scope="tertiary"
      value={t('DeleteRow')}
      disabled={
        !(templateEditRowID === '' && templateSelectedRowId !== '') ||
        !utils.user.isActionByCompanyActive(actionType.DELETE_ROWS)
      }
    />
  );
};
export default DeleteRowButton;
