/* eslint max-lines: 0 */
import axios from 'axios';
import {
  ChangePriority,
  DeleteDocumentAttach,
  DeleteDocuments,
  GetAllowedUsers,
  GetDocumentAttach,
  ChangeCompanyDocument,
  GetDocumentFieldsArchive,
  GetArchivedAttachImage,
  GetDocumentAttachments,
  GetDocumentFields,
  GetDocumentHeader,
  InsertDocumentAttach,
  Reassign,
  ValidateDocument,
  SetMemo,
  ChangeDocumentType,
  SetSubject,
  SaveDocument,
  GetZoneText,
  LockOrUnlockDocument,
  SetScanDateBody,
  SplitAndFix,
  ApplyRules,
  ReverseRepost,
  GetDocument,
  ReassignDocumentLatency,
  GetDocumentsDynamic,
} from 'models/request';
import {
  AIsuggestion,
  DocumentFields,
  DocumentHeaderResponse,
  FatherContracts,
  Attachment,
  DocumentLockedResponse,
  GetZoneTextResponse,
  BulkActions,
  SuccessScanDate,
  FailScanDate,
  SelfValidate,
  ApplyRulesResponse,
  CompanyDocTypeUserResponse,
  ReverseRepostReturnType,
  AuthorizedSplitAndFixDocumentsResponse,
  RolesByProgram,
  ErrorResponse,
  IExtractDocsResponse,
} from 'models/response';
import { trackPromise } from 'react-promise-tracker';
import qs from 'query-string';
import { WDOCBATCHSERVICE } from '../utils/constants';
import appHelpers from '../utils/helpers/app.helpers';

/**
 * This API returns the document list
 * @param {number} idProgram
 * @param {number} idUser
 * @param {number} idTemplate
 * @param {boolean} allAuthorized
 * @param {number} companyEquals
 * @param {string} socialReasonIn
 * @param {string} socialReasonLike
 * @param {string} socialReasonEquals
 * @param {string} vatNumberIn
 * @param {string} vatNumberLike
 * @param {string} vatNumberEquals
 * @param {string} protocolInIn
 * @param {string} protocolInLike
 * @param {string} protocolInEquals
 * @param {string} registrationProtocolIn
 * @param {string} registrationProtocolLike
 * @param {string} registrationProtocolEquals
 * @param { [string | undefined, string | undefined]} protocolInRange
 * @param {string} supplierCodeIn
 * @param {string} supplierCodeLike
 * @param {string} supplierCodeEquals
 * @param {string} documentTypeEquals
 * @param {string} documentTypeLike
 * @param {string} userNameLike
 * @param {string} userNameEquals
 * @param {string} fromDateEquals
 * @param {string} fromDateLess
 * @param {string} fromDateGreater
 * @param {string} toDateEquals
 * @param {string} toDateLess
 * @param {string} toDateGreater
 * @param {number} idGroupEquals
 * @param {string | undefined} customFilter
 * @return {Promise<AxiosResponse>}
 */

const getDocuments = (body: GetDocument, customParam?: string) => {
  const url = `${WDOCBATCHSERVICE}/getDocuments`;
  if (customParam) {
    const customObject = JSON.parse(customParam);
    return trackPromise(axios.post(url, { ...body, ...customObject }), 'module');
  }
  return trackPromise(axios.post(url, body), 'module');
};

/**
 * This API returns the Pdf doc image in base64
 * @param {number} protocol
 * @return {Promise<AxiosResponse<string>>}
 */
const getDocumentImage = (protocol: number) => {
  return trackPromise(axios.get<string>(`${WDOCBATCHSERVICE}/getDocumentImage?protocol=${protocol || 0}`), 'pdf');
};
/**
 * This API returns the Pdf doc image of an archived document in base64
 * @param {number} protocol
 * @return {Promise<AxiosResponse<string>>}
 */
const getArchivedDocumentImage = (protocol: number) => {
  return trackPromise(axios.get<string>(`${WDOCBATCHSERVICE}/getArchDocumentImage?protocol=${protocol || 0}`), 'pdf');
};

/**
 * This API deletes the pdf record
 * @param {Object} body
 * @param {number} body.protocols
 * @param {number} body.idCompany
 * @param {number} body.idUser
 * @param {number} body.idProgram
 * @return {Promise<AxiosResponse>}
 */
const deleteDocuments = (body: DeleteDocuments) =>
  trackPromise(axios.post<BulkActions>(`${WDOCBATCHSERVICE}/deleteFromWorkingQueue`, body), 'modal');

/**
 * Changes priority of the document
 * @param {Object} body
 * @param {number} body.protocols
 * @param {number} body.idCompany
 * @param {number} body.idProgram
 * @param {number} body.idUser
 * @param {number} body.idProgram
 * @return {Promise<AxiosResponse>}
 */
const changePriority = (body: ChangePriority) => axios.post(`${WDOCBATCHSERVICE}/changePriority`, body);

/**
 * Update memo text
 * @param {Object} body
 * @param {string | number} body.protocols
 * @param {number} body.idUser
 * @param {string} body.memo
 * @return {Promise<AxiosResponse>}
 */
const setMemo = (body: SetMemo) => trackPromise(axios.post(`${WDOCBATCHSERVICE}/setMemo`, body), 'modal');

/**
 * Allowed users for reassign
 * @param {Object} body
 * @param {number} body.idUser
 * @param {number} body.idProgram
 * @param {number} body.idCompany
 * @return {Promise<AxiosResponse>}
 */
const getAllowedUsers = (body: GetAllowedUsers) =>
  trackPromise(axios.post(`${WDOCBATCHSERVICE}/getAllowedUsers`, body), 'modal');

/**
 * Reassign user
 * @param {Object} body
 * @param {number} body.protocols
 * @param {number} body.idUser
 * @param {number} body.idUserTo
 * @param {number} body.idProgram
 * @return {Promise<AxiosResponse>}
 */
const reassign = (body: Reassign) => trackPromise(axios.post(`${WDOCBATCHSERVICE}/reassign`, body), 'modal');

/**
 *  Insert document attachment
 * @param {Object} body
 * @param {number} body.idUser
 * @param {number} body.protocol
 * @param {string} body.base64Attach
 * @param {string} body.extension
 * @param {string} body.fileName
 * @return {Promise<AxiosResponse>}
 */
const insertDocumentAttach = (body: InsertDocumentAttach) =>
  trackPromise(axios.post(`${WDOCBATCHSERVICE}/insertDocumentAttach`, body), 'modal');

/**
 *  Delete document attachment
 * @param {Object} body
 * @param {number} body.idDocAttach
 * @param {number} body.idUserDisable
 * @return {Promise<AxiosResponse>}
 */
const deleteDocumentAttach = (body: DeleteDocumentAttach) =>
  axios.post(`${WDOCBATCHSERVICE}/deleteDocumentAttach`, body);

/**
 *  Get single document attachment
 * @param {Object} body
 * @param {number} body.idDocAttach
 * @return {Promise<AxiosResponse>}
 */
const getDocumentAttach = (body: GetDocumentAttach) =>
  trackPromise(axios.post(`${WDOCBATCHSERVICE}/getDocumentAttach`, body), 'modal');

/**
 *  Get single archived document image / attachment
 * @param {Object} body
 * @param {number} body.protocol
 * @param {number} body.idDocAttach
 * @return {Promise<AxiosResponse>}
 */
const getArchivedAttachImage = (body: GetArchivedAttachImage) =>
  trackPromise(
    axios.post(
      `${WDOCBATCHSERVICE}/getArchivedAttachImage?protocol=${body.protocol || 0}&idDocAttach=${body.idDocAttach || 0}`,
      body,
    ),
    'modal',
  );

/**
 *  Get list of all attachments
 * @param {Object} body
 * @param {number} body.protocol
 * @return {Promise<AxiosResponse<Attachment[]>>}
 */
const getDocumentAttachments = (body: GetDocumentAttachments) =>
  trackPromise(axios.post<Attachment[]>(`${WDOCBATCHSERVICE}/getDocumentAttachments`, body), 'modal');

/**
 *  Get list of attachments of an archived document
 * @param {Object} body
 * @param {number} body.protocol
 * @return {Promise<AxiosResponse<Attachment[]>>}
 */
const getArchivedAttachments = (body: GetDocumentAttachments) =>
  trackPromise(
    axios.get<Attachment[]>(`${WDOCBATCHSERVICE}/getArchivedAttach?protocol=${body.protocol || 0}`),
    'modal',
  );

/**
 *  Get list of document fields
 * @param {Object} body
 * @param {number} body.protocol
 * @param {number} body.idProgram
 * @return {Promise<AxiosResponse>}
 */
const getDocumentFields = (body: GetDocumentFields) =>
  trackPromise(axios.post<DocumentFields>(`${WDOCBATCHSERVICE}/getDocumentFields`, body), 'module');

/**
 *  Get list of document fields archived
 * @param {Object} body
 * @param {number} body.protocol
 * @param {number} body.idProgram
 * @param {number} body.idUser
 * @return {Promise<AxiosResponse>}
 */
const getDocumentFieldsArchive = (body: GetDocumentFieldsArchive) =>
  trackPromise(axios.post<DocumentFields>(`${WDOCBATCHSERVICE}/getDocumentFieldsArchive`, body), 'module');

/**
 *  Get document header
 * @param {Object} body
 * @param {number} body.protocol
 * @param {number} body.idProgram
 * @param {number} body.idTemplate
 * @return {Promise<AxiosResponse<DocumentHeaderResponse>>}
 */
const getDocumentHeader = (body: GetDocumentHeader) =>
  trackPromise(axios.post<DocumentHeaderResponse>(`${WDOCBATCHSERVICE}/getDocumentHeader`, body), 'module');

/**
 *  Get Archived document header
 * @param {Object} body
 * @param {number} body.protocol
 * @param {number} body.idProgram
 * @param {number} body.idTemplate
 * @return {Promise<AxiosResponse<DocumentHeaderResponse>>}
 */
const getArchivedDocumentHeader = (body: GetDocumentHeader) =>
  trackPromise(axios.post<DocumentHeaderResponse>(`${WDOCBATCHSERVICE}/getDocumentArchivedHeader`, body), 'module');

/**
 * This API return a list of possible values
 * for the Q1 input
 * @param {number} protocol document ID
 * @param {number} idField field ID, from /getDocumentList
 * @return {Promise<AxiosResponse<DocumentHeaderResponse>>}
 */
const getAIsuggestions = (protocol: number, idField: number) =>
  axios.post<AIsuggestion[]>(`${WDOCBATCHSERVICE}/getAiSuggestion`, { idField, protocol });

/**
 *  Change document type
 * @param {Object} body
 * @param {number} body.idDocType
 * @param {number} body.idCompany
 * @param {number} body.idProgram
 * @param {number[]} body.protocols
 * @return {Promise<AxiosResponse<BulkActions>>}
 */
const changeDocumentType = (body: ChangeDocumentType) =>
  trackPromise(axios.post<BulkActions>(`${WDOCBATCHSERVICE}/changeDocumentType`, body), 'module');

/**
 *  Change document company
 * @param {Object} body
 * @param {number} body.idDocType
 * @param {number} body.idCompany
 * @param {number} body.idProgram
 * @param {number[]} body.protocols
 * @return {Promise<AxiosResponse<BulkActions>>}
 */
const changeCompanyDocument = (body: ChangeCompanyDocument) =>
  trackPromise(axios.post<BulkActions>(`${WDOCBATCHSERVICE}/changeCompanyDocument`, body), 'module');

/**
 *  Change document subject
 * @param {Object} body
 * @param {number} body.idSubject
 * @param {number} body.protocol
 * @return {Promise<AxiosResponse>}
 */
const setSubject = (body: SetSubject) => trackPromise(axios.post(`${WDOCBATCHSERVICE}/setSubject`, body), 'modal');

/**
 * Get father contracts
 * @param {number} idCompany
 * @param {number} idSubject
 * @return {Promise<AxiosResponse<FatherContracts[]>>}
 */

const getFatherContracts = (idCompany: number, idSubject: number) =>
  trackPromise(
    axios.get<FatherContracts[]>(`${WDOCBATCHSERVICE}/getArchiveFatherContracts`, {
      params: { idCompany, idSubject },
    }),
    'modal',
  );

/**
 * Set father contract
 * @param {number} protocolFather
 * @param {number} protocolChild
 * @return {Promise<AxiosResponse>}
 */
const mergeFather = (protocolFather: number, protocolChild: number) =>
  trackPromise(
    axios.post(`${WDOCBATCHSERVICE}/mergeFatherWithAmendment`, qs.stringify({ protocolFather, protocolChild })),
    'modal',
  );

/**
 * This API saves Q1 and Q3/body sections
 * @param {Object} body document ID
 * @param {number} idField field ID, from /getDocumentList
 * @return {Promise<AxiosResponse<DocumentHeaderResponse>>}
 */
const saveDocument = (body: SaveDocument) =>
  trackPromise(axios.post(`${WDOCBATCHSERVICE}/saveDocument`, { ...body }), 'module');

/**
 * This API validate the document
 * @param {Object} body request
 * @param {number | number[]} Object.protocol document protocol ID
 * @param {number} Object.idProgram current program ID
 * @param {number} Object.idUserQualityCheck logged user ID
 * @return {Promise<AxiosResponse<BulkActions>>}
 */
const validateDocument = (body: ValidateDocument) =>
  trackPromise(axios.post<BulkActions>(`${WDOCBATCHSERVICE}/validateDocument`, { ...body }), 'module');

/**
 * This API gets the text read value from rectangle or click coordinates
 * @param {Object} body
 * @param {number} protocol
 * @param {number} pageNumber
 * @param {number} fieldType
 * @param {string} x1
 * @param {string} y1
 * @param {string} x2?
 * @param {string} y2?
 * @return {Promise<AxiosResponse<GetZoneTextResponse>>}
 */
const getZoneText = (body: GetZoneText) =>
  trackPromise(axios.post<GetZoneTextResponse>(`${WDOCBATCHSERVICE}/getZoneText`, { ...body }), 'module');

/**
 * This API lock a documnet by its protocol
 * @param {Object} body request
 * @param {number} Object.protocol document protocol ID
 * @param {number} Object.idProgram current program ID
 * @param {number} Object.idUser logged user ID
 * @return {Promise<AxiosResponse<DocumentLockedResponse>>}
 */
const lockDocument = (body: LockOrUnlockDocument) =>
  trackPromise(axios.post<DocumentLockedResponse>(`${WDOCBATCHSERVICE}/lockDocument`, { ...body }), 'module');

/**
 * This API unlock a document by its protocol
 * @param {Object} body request
 * @param {number} Object.protocol document protocol ID
 * @param {number} Object.idProgram current program ID
 * @param {number} Object.idUser logged user ID
 * @return {Promise<AxiosResponse>}
 */
const unlockDocument = (body: LockOrUnlockDocument) =>
  trackPromise(axios.post(`${WDOCBATCHSERVICE}/unlockDocument`, { ...body }), 'module');
/**
 *
 * @param {string} protocol
 * @return {Promise<AxiosResponse>}
 */
const unlockDocumentByUser = (protocol: number) =>
  trackPromise(axios.post(`${WDOCBATCHSERVICE}/unlockDocumentByProtocol`, { protocol }), 'module');
/**
 * This API, add new documents to document list queue
 * return true if new documents exists, otherwise false
 * @return {boolean}
 */
const manualReassignDocuments = () =>
  trackPromise(axios.post<boolean>(`${WDOCBATCHSERVICE}/manualReassignDocuments`), 'module');

/**
 * This API, reassign ocr documents
 * * return true if new documents exists, otherwise false
 * @return {boolean}
 */
const manualReassignOcrDocuments = () =>
  trackPromise(axios.post<boolean>(`${WDOCBATCHSERVICE}/manualReassignOcrDocuments`), 'module');

/**
 * This API, select all non ocr documents
 * return true if new documents exists, otherwise false
 * @return {boolean}
 */
const manualReassignNonOcrDocuments = () =>
  trackPromise(axios.post<boolean>(`${WDOCBATCHSERVICE}/manualReassignNonOcrDocuments`), 'module');

/**
 * This API returns the Pdf split and fix document protocols
 * @return {Promise<AxiosResponse<AuthorizedSplitAndFixDocumentsResponse[]>>}
 */
const getAuthorizedSplitDocuments = () =>
  trackPromise(
    axios.get<AuthorizedSplitAndFixDocumentsResponse[]>(`${WDOCBATCHSERVICE}/getAuthorizedSplitAndFixDocuments`),
    'module',
  );

/**
 * This API update the selected document with the chosen Date
 * @param {Object} body requestSetScanDateBody
 * @return {Promise<SuccessScanDate | FailScanDate>}
 */
const changeScanData = (body: SetScanDateBody) =>
  trackPromise(
    axios.post<SuccessScanDate | FailScanDate>(`${WDOCBATCHSERVICE}/changeDataScanAndRestampProtocol`, body),
    'modal',
  );

/**
 * This API sends the documet to split and fix
 * @param {Object} body
 * @param {number[]} body.documentToDistributeList
 * @return {Promise<AxiosResponse>}
 */
const splitAndFix = (body: SplitAndFix) =>
  trackPromise(axios.post<BulkActions>(`${WDOCBATCHSERVICE}/setDocToSplitAndFix`, body), 'modal');

/**
 * This API sends document to rebatch
 * @return {Promise<AxiosResponse>}
 */

const rebatchDocument = (protocols: number[], ignoreReject: boolean) =>
  trackPromise(
    axios.post(`${WDOCBATCHSERVICE}/rebatch`, {
      protocols,
      ignoreReject,
    }),
    'modal',
  );

/** This API does the self validate to the selected user from workflow
 * @param {Object} body request
 * @param {number[]} Object.protocols document protocols ID
 * @param {number} Object.idUser logged user ID
 * @param {number} Object.idUserTo target user ID
 * @param {number} Object.idProgram current program ID
 * @return {Promise<AxiosResponse>}
 */
const selfValidate = (body: SelfValidate) =>
  trackPromise(axios.post<string>(`${WDOCBATCHSERVICE}/selfValidate`, { ...body }), 'modal');

/** This API validates the q1 fields and return their status
 * @param {Object} body request
 * @param {number} Object.protocol document protocol ID
 * @param {number} Object.idDocType doctype
 * @param {Object} Object.fields fieldsData
 * @return {Promise<AxiosResponse<ApplyRulesResponse>>}
 */
const applyRules = (body: ApplyRules) =>
  trackPromise(axios.post<ApplyRulesResponse>(`${WDOCBATCHSERVICE}/applyRules`, { ...body }), 'module');

/* This API gets companies and doctype info */
const getCompaniesWithDocTypeAndUser = () =>
  trackPromise(
    axios.post<CompanyDocTypeUserResponse[]>(`${WDOCBATCHSERVICE}/getCompaniesWithDocTypeAndUser`),
    'module',
  );

const reverseRepost = (body: ReverseRepost) =>
  trackPromise(axios.post<ReverseRepostReturnType>(`${WDOCBATCHSERVICE}/reverseRepostArchDocument`, body), 'modal');

const translateDocument = (idProtocol: string, targetLanguage: string) =>
  trackPromise(
    axios.post('translate/translateDocument', {
      idProtocol,
      targetLanguage,
    }),
    'module',
  );

/**  This API returns a list of id roles with all associated companies or only the company you specify in input
 * @param {number} idCompany
 * @return {Promise<AxiosResponse<RolesByProgram[]>>}
 */
const getRolesByIdProgram = (idCompany?: number) =>
  trackPromise(
    axios.get<RolesByProgram[]>(`${WDOCBATCHSERVICE}/getRolesByIdProgram${idCompany ? '?idCompany=' + idCompany : ''}`),
    'module',
  );

/**
 * This API sends document to checkDuplicateDocument
 * @param {number} protocol document protocol ID
 * @return {Promise<AxiosResponse>}
 */
const checkDuplicateDocument = (protocol: number) =>
  trackPromise(axios.post<ErrorResponse>(`${WDOCBATCHSERVICE}/checkDuplicateDocument`, { protocol }), 'modal');

/**
 * Reassign document latency
 * @param {Object} body
 * @param {number} body.protocols
 * @param {number} body.idProgram
 * @return {Promise<AxiosResponse>}
 */
const reassignDocumentLatency = (body: ReassignDocumentLatency) =>
  trackPromise(axios.post(`${WDOCBATCHSERVICE}/reassignLatencyDocument`, body), 'module');

/**
 * This API returns the document list
 * @param {Object} body request
 * @param {number} Object.idProgram
 * @param {number} Object.idUser
 * @param {number} Object.idTemplate
 * @param {boolean} Object.allAuthorized
 * @param {IFilterValue[]} Object.filters
 * @param {string | undefined} customFilter
 * @return {Promise<AxiosResponse>}
 */

const getDocumentsDynamic = (body: GetDocumentsDynamic, customParam?: string) => {
  const url = `${WDOCBATCHSERVICE}/getDocumentsDynamic`;
  if (customParam) {
    const customObject = JSON.parse(customParam);
    return trackPromise(axios.post(url, { ...body, ...customObject }), 'module');
  }
  return trackPromise(axios.post(url, body), 'module');
};

const checkDocumentAvailability = (protocol: number[] | number, username: string, documentOpen: boolean) => {
  return axios.post<{
    documents: {
      protocol: number;
      message: string;
      messageKey: string;
      available: boolean;
      docAvailabilityStatus: boolean;
    }[];
  }>(`${WDOCBATCHSERVICE}/getDocumentsAvailability`, { protocol, username, documentOpen });
};

const extractDocsFromDispatcher = (protocols: number[]) =>
  trackPromise(axios.post<IExtractDocsResponse>(`${WDOCBATCHSERVICE}/extractDocsFromDispatcher`, protocols), 'module');

/**
 * Get document types for a company
 * @param {number} idCompany - Company ID
 * @return {Promise<AxiosResponse>}
 */
const getDocTypes = (idCompany: number) =>
  trackPromise(
    axios.get<
      {
        batchDistribute: number;
        creditDebit: number;
        docName: string;
        docType: number;
        fixedName: string;
        genericOption: boolean;
        hide: boolean;
        idDocType: number;
        ocr: boolean;
      }[]
    >(`${WDOCBATCHSERVICE}/getDocTypes`, { params: { idCompany } }),
    'module',
  );

/**
 * Generate DEM document for the specified protocol
 * @param {number} protocol document protocol ID
 * @return {Promise<AxiosResponse<string>>} Base64 encoded PDF
 */
const generateDEM = (protocol: number) =>
  trackPromise(axios.post<string>(`${WDOCBATCHSERVICE}/generateDEM`, { protocol }), 'module');

export default {
  getDocuments,
  getDocumentImage: appHelpers.withAbortController(getDocumentImage),
  getArchivedDocumentImage: appHelpers.withAbortController(getArchivedDocumentImage),
  deleteDocuments,
  changePriority,
  getAllowedUsers,
  reassign,
  setMemo,
  insertDocumentAttach,
  deleteDocumentAttach,
  getDocumentAttach,
  getArchivedAttachImage,
  getDocumentAttachments,
  getArchivedAttachments,
  getDocumentFields,
  getDocumentFieldsArchive,
  getDocumentHeader,
  getAIsuggestions,
  changeDocumentType,
  changeCompanyDocument,
  setSubject,
  getFatherContracts,
  mergeFather,
  saveDocument,
  validateDocument,
  getArchivedDocumentHeader,
  getZoneText,
  lockDocument,
  unlockDocument,
  manualReassignDocuments,
  manualReassignOcrDocuments,
  manualReassignNonOcrDocuments,
  getAuthorizedSplitDocuments,
  changeScanData,
  splitAndFix,
  rebatchDocument,
  selfValidate,
  applyRules,
  getCompaniesWithDocTypeAndUser,
  reverseRepost,
  translateDocument,
  getRolesByIdProgram,
  checkDuplicateDocument,
  unlockDocumentByUser,
  reassignDocumentLatency,
  getDocumentsDynamic,
  checkDocumentAvailability,
  extractDocsFromDispatcher,
  getDocTypes,
  generateDEM,
};
