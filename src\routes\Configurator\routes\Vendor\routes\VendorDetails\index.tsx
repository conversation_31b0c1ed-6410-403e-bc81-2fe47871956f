import { ButtonsContainer, Title, Wrapper } from 'routes/Configurator/styles';
import React, { useState } from 'react';
import { useAsync } from 'react-use';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components/macro';

import selectors from 'state/selectors';
import actions from 'state/actions';

import utils from 'utils';
import service from 'services';

import Table, { OriginalRow, TableColumn } from 'components/Table';
import { CommonButton } from 'components/Buttons';
import DetailsTable from './components/DetailsTable';
import DetailsForm from './components/DetailsForm';
import { SearchSubject, SubjectDetailRows } from 'models/response';
import { getDetailInputType, transformDetailsToRows, transformRowsToDetails } from '../utils';

const SectionHeading = styled.div`
  font-size: 15px;
  font-weight: 500;
  color: ${({ theme }) => theme.colorPalette.grey.dark};
  padding-bottom: 20px;
`;

const TableHeading = styled.div`
  font-size: 15px;
  font-weight: 500;
  color: ${({ theme }) => theme.colorPalette.grey.dark};
`;

const DetailsSection = styled.div`
  border: 1px solid ${({ theme }) => theme.colorPalette.grey.mediumDark};
  border-radius: 4px;
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 40px;
`;

const VendorDetail = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const selectedCompany = useSelector(selectors.configurator.getSelectedCompanyVendorConfig);
  const selectedVendorId = useSelector(selectors.configurator.getSelectedVendorIdFromList);
  const isEdited = useSelector(selectors.configurator.getIsEditedVendorDetail);
  const subjectTablesColumns = useSelector(selectors.configurator.getSubjectTableColumns);
  const subjectList = useSelector(selectors.configurator.getSubjectList);

  const defaultDetailsTableColumns = [
    {
      Header: t('supplierCode'),
      accessor: 'supplierCode',
      id: 'supplierCode',
      width: 100,
      readOnly: true,
    },
  ];

  const selectedVendor = subjectList.find(({ idWSubject }) => idWSubject === selectedVendorId) || ({} as SearchSubject);

  const createNewDataObject = (vendor: SearchSubject, tablesColumns: TableColumn[]) => {
    const selectedValues = tablesColumns.map(({ accessor }) => accessor);
    const filteredObject = Object.keys(vendor)
      .filter((key) => selectedValues.includes(key))
      .reduce((obj, key) => {
        obj[key] = vendor[key as keyof typeof vendor];
        return obj;
      }, {} as { [key: string]: any });

    return filteredObject;
  };

  const [newData, setNewData] = useState(createNewDataObject(selectedVendor, subjectTablesColumns || []));
  const [details, setDetails] = useState<SubjectDetailRows>();
  const [detailsTableColumns, setDetailsTableColumns] = useState<TableColumn[]>([]);

  const saveDetails = (rows: OriginalRow[]) => {
    if (selectedVendorId) {
      const newDetails = transformRowsToDetails(rows, detailsTableColumns, selectedVendorId);
      setNewData({ ...newData, details: newDetails });
    }
  };

  const updateData = (data: { [key: string]: string }) => {
    setNewData({ ...newData, ...data });
  };

  const subjectDetailsTableRows = useSelector(selectors.configurator.getSubjectDetailsTableRows);
  const editedRowId = useSelector(selectors.configurator.getEditedRowIdVendorDetail);

  useAsync(async () => {
    try {
      if (selectedVendorId) {
        const { data: details } = await service.getSubjectDetail(selectedVendorId);
        const { data: columns } = await service.getSubjectDetailAllColumns(selectedVendorId);

        setDetails(details);

        const columnsSorted = columns.sort((a, b) => a.position - b.position);
        const columnsMapped = columnsSorted.map(
          ({ allowedValues, type, title, readOnly, position, width, idCompanyDetail, link }) => {
            const inputType = getDetailInputType(link, type);
            const selectOptionsTypes =
              inputType === 'select'
                ? allowedValues?.map(({ code, description }) => ({ value: code, label: description }))
                : undefined;

            return {
              Header: t(title),
              accessor: `${position}`,
              id: `${idCompanyDetail}`,
              width,
              readOnly,
              selectOptionsTypes,
              inputType,
              Cell: ({ value = '' }: { value: number | string }) => {
                return allowedValues?.find(({ code }) => code === value)?.description ?? value;
              },
            };
          },
        );

        const objectRows = transformDetailsToRows(details);

        setDetailsTableColumns([...defaultDetailsTableColumns, ...columnsMapped]);
        dispatch(actions.configurator.setSubjectDetailsTableRows([...(objectRows as OriginalRow[])]));
      }
    } catch (err) {
      console.error(err);
    }
  }, []);

  return (
    <Wrapper>
      <Title>
        <h2>
          {selectedCompany && selectedCompany.label ? selectedCompany.label + ' ' : ''}
          {t('master_data_configurator')} - {selectedVendor?.subject}
        </h2>
      </Title>
      <div>
        <TableHeading>{t('selectedVendorHeading')}</TableHeading>
        {selectedVendor?.idWSubject && (
          <Table
            rowId="idWSubject"
            columns={subjectTablesColumns?.map((column) => ({ ...column, filterType: undefined })) || []}
            rows={selectedVendor ? [selectedVendor] : []}
            hasResize
            isEditable={false}
          />
        )}
      </div>
      <DetailsSection>
        <div>
          <SectionHeading>
            {selectedVendor?.subject} - {t('detailedInformation')}
          </SectionHeading>
          <p>{t('pressEditButtonToMakeChanges')}</p>
        </div>
        <div>
          <SectionHeading>{t('anagrafic')}</SectionHeading>
          <DetailsForm updateData={updateData} newData={newData} />
        </div>
        <div>
          <TableHeading>{t('vendorDetailsHeading')}</TableHeading>

          <DetailsTable saveDetails={saveDetails} columns={detailsTableColumns} />
        </div>
      </DetailsSection>

      {isEdited && (
        <ButtonsContainer>
          <CommonButton
            id="saveButton"
            scope="primary"
            type="submit"
            disabled={Boolean(editedRowId)}
            value={t('save')}
            action={async () => {
              if (!selectedVendorId) return;

              try {
                const newDetails = transformRowsToDetails(
                  subjectDetailsTableRows,
                  detailsTableColumns,
                  selectedVendorId,
                );
                const dataToSave = { ...newData, details: newDetails };

                await service.saveSubjectAndDetails(dataToSave);

                dispatch(
                  actions.configurator.updateVendorTableRow({
                    newRow: { ...selectedVendor, ...newData },
                    idWSubject: selectedVendorId,
                  }),
                );
                dispatch(actions.configurator.setIsEditedVendorDetail(false));
                utils.app.notify('success', t('subject-detail-saved'));
              } catch (err) {
                console.error(err);
              }
            }}
          />
          <CommonButton
            id="cancelButton"
            scope="secondary"
            type="button"
            value={t('cancel')}
            disabled={Boolean(editedRowId)}
            action={() => {
              if (selectedVendor) {
                setNewData(createNewDataObject(selectedVendor, subjectTablesColumns || []));

                const rowsOrig = transformDetailsToRows(details);
                dispatch(actions.configurator.setSubjectDetailsTableRows(rowsOrig));
              }

              dispatch(actions.configurator.setIsEditedVendorDetail(false));
            }}
          />
        </ButtonsContainer>
      )}
    </Wrapper>
  );
};

export default VendorDetail;
