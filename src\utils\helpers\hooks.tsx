/* eslint-disable max-lines */
import Header from 'components/Modal/Header';
import { useSelector, useDispatch } from 'react-redux';
import utils from 'utils';
import selectors from 'state/selectors';
import React, { useState, Dispatch, SetStateAction, useMemo, useCallback, useEffect } from 'react';
import { FormValues, MovTypeValue, Q1Value } from 'models/documents';
import services from 'services';
import actions from 'state/actions';
import styled from 'styled-components/macro';
import { useFormikContext } from 'formik';
import { useLocation } from 'react-router-dom';
import Pdf from 'components/Pdf/Pdf';
import intlHelper from './intl.helper';
const Wrapper = styled.div<{ open: boolean }>`
  display: ${({ open }) => (open ? 'content' : 'none')};
`;

function useBulkActionErrorModal(
  protocols: number[],
  visibility: boolean,
): [() => JSX.Element, Dispatch<SetStateAction<boolean>>] {
  const [isVisible, setIsVisible] = useState(visibility);

  const BulkActionErrorModal = () => {
    const t = utils.intl.useTranslator();
    const activeActionType = useSelector(selectors.modal.getActionType) || '';
    const [protocolsList, setProtocolsList] = useState<number[]>([]);

    useEffect(() => {
      setIsVisible(visibility);
      const protocolsNotDuplicated = protocols.filter((v, i) => protocols.indexOf(v) === i);
      setProtocolsList(protocolsNotDuplicated);
    }, []);

    return (
      <Wrapper open={isVisible}>
        <Header title={t(activeActionType)} subtitle={t('subtitle-error-protocols')} />
        <ul>
          {protocolsList?.map((protocol) => (
            <li key={protocol}> {protocol}</li>
          ))}
        </ul>
      </Wrapper>
    );
  };

  return [BulkActionErrorModal, setIsVisible];
}

function useInitialValues() {
  const documentFlatFields = useSelector(selectors.documents.selectDocumentFlatFields);
  const documentHeader = useSelector(selectors.documents.selectDocumentHeader);

  const { hTableValues, values } = documentFlatFields.find(({ fieldName }) => fieldName === 'Valuta') || {};
  const valutaCode = values?.[0]?.content;
  const initialDecimal = hTableValues?.find(({ code }) => code === valutaCode)?.decimals;

  return useMemo(
    () =>
      documentFlatFields.reduce(
        (acc, { fieldName, fieldInputType, values, fieldGroup, fieldType }) => {
          // update value if is currency field, need this in case the value coming from db is having wrong decimals
          const newValues =
            fieldInputType === 3
              ? values.map((value) => {
                  return {
                    ...value,
                    content: value.content
                      ? intlHelper.formatNumber(intlHelper.convertToNumber(value.content), initialDecimal, ',')
                      : '',
                  };
                })
              : values;

          return {
            ...acc,
            [fieldName]: {
              content:
                values && values.length
                  ? newValues
                  : [{ boundingBox: { x1: -1, x2: -1, y1: -1, y2: -1 }, pageNumber: -1, confidence: -1, content: '' }],
              body: fieldGroup
                ? utils.documents.setInitialBodyData(fieldGroup.tableValues, fieldGroup.fieldColumn, initialDecimal)
                : null,
              status: values && values.length ? values[0]?.status : '',
              fieldType: fieldType,
            },
          };
        },
        {
          documentTypeHeader: documentHeader?.actualDocumentType
            ? {
                label: documentHeader.actualDocumentType.docName,
                value: documentHeader.actualDocumentType.idDocType,
                option: documentHeader.actualDocumentType.genericOption,
              }
            : null,
          companyHeader: documentHeader?.actualCompany
            ? {
                label: documentHeader.actualCompany.name,
                value: documentHeader.actualCompany.idCompany,
              }
            : null,
          movTypeHeader: documentHeader?.actualHMoveType
            ? {
                label: `${documentHeader.actualHMoveType.code} - ${documentHeader.actualHMoveType.description}`,
                value: documentHeader.actualHMoveType.code,
              }
            : null,
          subjectHeader: documentHeader?.subject
            ? {
                name: documentHeader.subject.subject,
                id: documentHeader.subject.idWSubject,
              }
            : null,
          supplierCode: documentHeader?.supplierCode ? documentHeader.supplierCode : null,
          isChildOf: documentHeader?.fatherProtocolIn || '',
          vatNumber: documentHeader?.subject?.vatNumber ? documentHeader?.subject?.vatNumber : null,
        },
      ),
    [documentHeader, documentFlatFields, initialDecimal],
  );
}

const useSaveAction = () => {
  const idUser = useSelector(selectors.user.getIdUser);
  const idProgram = useSelector(selectors.app.getProgramCode);
  const protocol = useSelector(selectors.documents.selectActiveDocumentID);
  const documentFlatFields = useSelector(selectors.documents.selectDocumentFlatFields);
  const documentHeader = useSelector(selectors.documents.selectDocumentHeader);
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const idTemplate = useSelector(selectors.documents.selectActiveTemplateId);

  const assigneQ1ValueToDocList = useCallback(
    (values: FormValues) => {
      const filteredFieldArchive = documentFlatFields.filter((el) => el.archive);

      filteredFieldArchive.forEach((field) => {
        const fieldName = field.fieldName;
        if (values[fieldName]) {
          dispatch(
            actions.documents.updateTableRows({
              protocols: [protocol],
              property: `${field.archive.toString().toLowerCase()}`,
              value: (values[fieldName] as Q1Value).content?.[0]?.content,
            }),
          );
        }
      });
    },
    [dispatch, documentFlatFields, protocol],
  );

  return useCallback(
    async (values: FormValues) => {
      try {
        const documentFields = utils.documents.prepareQ1Fields(values, documentFlatFields);
        const accountFields = utils.documents.prepareQ3Fields(values, documentFlatFields);
        if (protocol && idUser && idTemplate) {
          const { data } = await services.saveDocument({
            idProgram,
            protocol,
            idUser,
            export2Code: null,
            export1Code: null,
            movementTypeCode: values.movTypeHeader ? values.movTypeHeader.value : null,
            docStatus: null,
            supplierCode: values.supplierCode,
            subject: null,
            documentFields,
            accountFields,
          });
          utils.app.notify('success', data);
          const { data: docFields } = await services.getDocumentFields({ protocol, idProgram });
          dispatch(actions.documents.setDocumentFieldsPassed(protocol, idProgram, docFields));
          assigneQ1ValueToDocList(values);

          // Update MovType
          const movTypeHeader = values.movTypeHeader?.value;
          movTypeHeader &&
            dispatch(
              actions.documents.updateTableRows({
                protocols: [protocol],
                property: 'movementTypeCode',
                value: movTypeHeader,
              }),
            );

          // Update Supplier Code
          dispatch(
            actions.documents.updateTableRows({
              protocols: [protocol],
              property: 'supplierCode',
              value: values.supplierCode,
            }),
          );

          const value = documentHeader?.actualDocumentType?.docName;
          value && dispatch(actions.documents.setDocumentHeader(protocol, idProgram, idTemplate));
          return docFields;
        }
        return null;
      } catch (e) {
        console.error(e);
        utils.app.notify('fail', t('something-went-wrong'));
        return null;
      }
    },
    [idUser, idProgram, protocol, dispatch, documentFlatFields, documentHeader, t, assigneQ1ValueToDocList, idTemplate],
  );
};

const useResetManualDirty = () => {
  const dispatch = useDispatch();
  const availableMovTypes = useSelector(selectors.documents.selectAvailableHMoveTypes);

  return useCallback(
    (values: FormValues) => {
      if (availableMovTypes != null) {
        const movType = availableMovTypes.find(
          (mov) => mov.description === (values?.movTypeHeader as MovTypeValue)?.label,
        );
        if (movType != null) {
          dispatch(actions.documents.setActualHMoveType(movType));
        }
      }
    },
    [availableMovTypes, dispatch],
  );
};

/**
 * Hook to save a document
 * @param {boolean} resetFilters if true, the filters are reset after the action is executed
 * @return {Function} saveDocumentAction
 */
const useSaveDocument = (resetFilters = false) => {
  const formik = useFormikContext<FormValues>();
  const values: FormValues | undefined = formik?.values;
  const saveAction = useSaveAction();
  const resetManualDirty = useResetManualDirty();
  const documentSelected = useSelector(selectors.documents.selectActiveDocumentID) != null;
  const { pathname } = useLocation();
  const isInEditDocument = pathname.includes('/edit');
  const saveDocumentAction = async () => {
    try {
      if (!isInEditDocument) return undefined;
      if (!documentSelected && !values) return null;
      const val = await saveAction(values);
      resetFilters && resetManualDirty(values);
      return val;
    } catch (e) {
      return null;
    }
  };
  return saveDocumentAction;
};

const CenteringWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  justify-items: center;
  height: 75vh;
`;
const usePDFImage = (idProtocol?: number | null) => {
  const dispatch = useDispatch();
  const [pdfComponent, setPdfComponent] = useState<JSX.Element | null>(null);

  useEffect(() => {
    if (!idProtocol) {
      setPdfComponent(null);
      dispatch(actions.documents.setDocumentImage(''));
      return;
    }

    const fetchPDFImage = async () => {
      try {
        const { data } = await services.getMonitorDocumentImage(idProtocol);
        dispatch(actions.documents.setDocumentImage(data));
        setPdfComponent(
          <CenteringWrapper>
            <Pdf fixedHeight={'70vh'} width="60%" docImage={data} />
          </CenteringWrapper>,
        );
      } catch (error) {
        console.error('Error fetching PDF image:', error);
        setPdfComponent(null);
      }
    };

    fetchPDFImage();
  }, [idProtocol, dispatch]);

  return pdfComponent;
};

export default {
  useInitialValues,
  useSaveAction,
  useBulkActionErrorModal,
  useResetManualDirty,
  useSaveDocument,
  usePDFImage,
};
