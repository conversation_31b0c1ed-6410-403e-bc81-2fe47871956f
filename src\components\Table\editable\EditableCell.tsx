import React from 'react';
import { useEffectOnce } from 'react-use';
import { Row } from 'react-table';
import { TableColumn, OriginalRow } from '../interfaces';
import { Text, Number, Date, Select } from './inputs';
import { Checkbox } from 'components/Buttons';
import utils from 'utils';
import { UserDate } from 'models/user';
import { useSelector } from 'react-redux';
import selectors from 'state/selectors';

const renderValueOnEdit = (
  inputType?: string,
  value?: any,
  dateFormat?: UserDate,
  selectOptionsTypes?: any[],
  timeZone?: string,
) => {
  switch (inputType) {
    case 'date':
      return utils.date.convertDate(value, timeZone ?? 'UTC');
    case 'select':
      return selectOptionsTypes?.find((option) => option.value === value)?.label ?? value;
    default:
      return value;
  }
};
interface Props {
  column: TableColumn;
  accessor: TableColumn['accessor'];
  value: any;
  row: Row;
  rowId: string;
  editRowID: string;
  modifiedRow: OriginalRow;
  onEditRowChange?: (editRow: any, accessor?: any) => void;
  onEditCellChange?: (editCell: any) => void;
}

const EditableCell = ({
  value: initialValue = '',
  row: { id, original },
  rowId,
  column: { inputType, dateFormat, selectOptionsTypes, readOnly },
  editRowID,
  onEditRowChange,
  accessor,
  modifiedRow,
}: Props): JSX.Element => {
  // We need to keep and update the state of the cell normally
  const [value, setValue] = React.useState(initialValue);
  const userPreference = useSelector(selectors.user.getUserPreference);

  useEffectOnce(() => {
    if (`${editRowID}` === `${id}`) {
      modifiedRow[accessor as string] = initialValue;
      modifiedRow[rowId] = parseInt(id);
      if ((original as OriginalRow).newRow === true) modifiedRow.newRow = true;
    }
  });

  const onChange = (newValue: string | number | boolean) => {
    modifiedRow[accessor as string] = newValue;
    onEditRowChange?.(modifiedRow, accessor);
    setValue(newValue);
  };

  // If the initialValue is changed externally, sync it up with our state
  React.useEffect(() => {
    if (`${editRowID}` === `${id}`) {
      setValue(initialValue);
      modifiedRow[accessor as string] = initialValue;
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialValue]);

  let EditCellComponent: JSX.Element | string | number | null = null;
  switch (inputType) {
    case 'text':
      EditCellComponent = <Text value={value} onChange={onChange} />;
      break;
    case 'number':
      EditCellComponent = <Number value={value} onChange={onChange} />;
      break;
    case 'date':
      EditCellComponent = <Date dateFormat={dateFormat} value={value} onChange={onChange} />;
      break;
    case 'select':
      EditCellComponent = <Select value={value} onChange={onChange} options={selectOptionsTypes} />;
      break;
    case 'checkbox':
      EditCellComponent = (
        <div style={{ display: 'flex', height: '100%' }}>
          <Checkbox
            isActive={value}
            action={(val) => {
              onChange(val);
            }}
          ></Checkbox>
        </div>
      );
      break;
    default:
      EditCellComponent = value;
      break;
  }
  return `${editRowID}` === `${id}` && !readOnly
    ? EditCellComponent
    : renderValueOnEdit(inputType, value, dateFormat, selectOptionsTypes, userPreference.timeZone);
};

export default EditableCell;
