import { FormTypesNotifications } from 'models/configurator';
import { GetTemplatesMailNotification, IOpenWorkflowNotifications } from 'models/response';
import * as Yup from 'yup';
import { parserfrequency } from '../utils';

export interface FormValues {
  user: { label: string; value: number } | null;
  openWfSendMail: boolean;
  openWfSendNotificationImmediately: boolean;
  openWfMaxDocuments: number;
  openWfMaxDocumentDetails: number;
  idTemplateImmediateOpenWorkflow: { label: string; value: number } | null;
  idTemplateScheduledOpenWorkflow: { label: string; value: number } | null;
  hour: { label: string; value: string } | null;
  minute: { label: string; value: number } | null;
  allDays: boolean;
  daysOfWeek: { label: string; value: number }[];
}

export const defaultValues: FormValues = {
  user: null,
  openWfSendMail: false,
  openWfSendNotificationImmediately: false,
  openWfMaxDocuments: 1,
  openWfMaxDocumentDetails: 1,
  idTemplateImmediateOpenWorkflow: null,
  idTemplateScheduledOpenWorkflow: null,
  hour: null,
  minute: null,
  allDays: false,
  daysOfWeek: [],
};

export const validationLogic = (t: (key: string) => string) => {
  return Yup.object({
    user: Yup.object()
      .shape({
        label: Yup.string().required(t('required')),
        value: Yup.number().required(t('required')),
      })
      .required(t('required')),
    idTemplateImmediateOpenWorkflow: Yup.object()
      .shape({
        label: Yup.string().required(t('required')),
        value: Yup.number().required(t('required')),
      })
      .required(t('required')),
    idTemplateScheduledOpenWorkflow: Yup.object()
      .shape({
        label: Yup.string().required(t('required')),
        value: Yup.number().required(t('required')),
      })
      .required(t('required')),
    hour: Yup.object()
      .shape({
        label: Yup.string().required(t('required')),
        value: Yup.number().required(t('required')),
      })
      .required(t('required')),
    minute: Yup.object()
      .shape({
        label: Yup.string().required(t('required')),
        value: Yup.number().required(t('required')),
      })
      .required(t('required')),
    allDays: Yup.boolean(),
    daysOfWeek: Yup.array()
      .of(
        Yup.object().shape({
          label: Yup.string().required(),
          value: Yup.number().required(),
        }),
      )
      .test('valid-days', t('required_days_of_week'), function (daysOfWeek) {
        const { allDays } = this.parent;
        if (!allDays && (!daysOfWeek || daysOfWeek.length === 0)) {
          return this.createError({ path: 'daysOfWeek', message: t('required_days_of_week') });
        }
        return true;
      }),
  });
};
export const setInitialValues = (
  selectedRow: IOpenWorkflowNotifications | null,
  formMode: FormTypesNotifications,
  t: (key: string) => string,
  templatesList: GetTemplatesMailNotification[],
): FormValues => {
  if (selectedRow && formMode === 'edit') {
    const {
      idUser,
      name,
      openWfSendMail,
      openWfSendNotificationImmediately,
      openWfMaxDocuments,
      openWfMaxDocumentDetails,
      idTemplateImmediateOpenWorkflow,
      idTemplateScheduledOpenWorkflow,
      openWfFrequency,
    } = selectedRow;

    const { validHour, validMinute, allDays, daysOfWeek } = parserfrequency(openWfFrequency, t);

    const templateImmediateOpenWorkflow =
      templatesList.find((el) => el.idMailTemplate === idTemplateImmediateOpenWorkflow) ?? null;
    const templateScheduledOpenWorkflow =
      templatesList.find((el) => el.idMailTemplate === idTemplateScheduledOpenWorkflow) ?? null;

    return {
      user: { value: idUser, label: name },
      openWfSendMail,
      openWfSendNotificationImmediately,
      openWfMaxDocuments,
      openWfMaxDocumentDetails,
      idTemplateImmediateOpenWorkflow: templateImmediateOpenWorkflow
        ? {
            value: templateImmediateOpenWorkflow.idMailTemplate,
            label: `${templateImmediateOpenWorkflow.idMailTemplate} - ${templateImmediateOpenWorkflow.templateName}`,
          }
        : null,
      idTemplateScheduledOpenWorkflow: templateScheduledOpenWorkflow
        ? {
            value: templateScheduledOpenWorkflow.idMailTemplate,
            label: `${templateScheduledOpenWorkflow.idMailTemplate} - ${templateScheduledOpenWorkflow.templateName}`,
          }
        : null,
      hour: validHour,
      minute: validMinute,
      allDays,
      daysOfWeek,
    };
  }

  return defaultValues;
};
