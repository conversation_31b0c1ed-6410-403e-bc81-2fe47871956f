/* eslint-disable max-lines */
import { OriginalRow } from 'components/Table';
import { TableRowsType } from 'routes/Configurator/routes/Mapper/utils';
import { TableProps } from 'components/Table';
import { MicroCategory, RoleList, WfCasual, WfDefinition } from './request';
import {
  BoundingBox,
  Template,
  TableValue,
  UsersResponse,
  TablesDataColumns,
  Company,
  ProgramResponseList,
  RoleProgramResponseList,
  GetUserCompanyRoles,
  CompanyActionTable,
  PoVendorList,
  PoMicroCategoryList,
  CorporateApproval,
  Subject,
  HTableValues,
  AP2Template,
  VendorNoPoList,
  GetNopoMicrocategoriesResponse,
  GetNopoVendorResponse,
  CompanyWorkflow,
  WorkflowCompaniesDefinitionList,
  WorkflowCompaniesDefinition,
  WorkflowTaskList,
  WorkflowGroupList,
  WorkflowCompaniesDefinitionListExtended,
  CompanyGroups,
  OUserList,
  TaskGroupAssociations,
  CompanyGroupsForTaskAssociation,
  WorkflowDefinitionTaskGroupsAssociationsExtended,
  SearchSubject,
  IOpenWorkflowNotifications,
  IWfReminderNotifications,
  IOverdueNotifications,
  GetTemplatesMailNotification,
} from './response';

export type ConfiguratorState = {
  mapper: ConfiguratorMapper;
  tables: ConfiguratorTables;
  users: ConfiguratorUsers;
  rolesUserCompany: ConfiguratorRoles;
  activeTemplateId: number | null;
  views: Template[];
  companies: Companies;
  actionTables: ActionTables;
  workflow: ConfiguratorWorkflow;
  vendor: ConfiguratorVendor;
  notifications: ConfiguratorNotifications;
};

export interface ConfiguratorMapper {
  docTypes: GetTemplateWithDocType[];
  activeDocTypeId: null | number;
  activeOTemplate: OTemplate | null;
  activeOTemplateId: number | null;
  fields: GetTemplatesFields | null;
  templateEditRowID: string;
  templateSelectedRowID: string;
  rows: TableRowsType[];
  areas: Area[];
  pdfCurrentPage: number;
}

export interface ConfiguratorTables {
  tablesSelectValues: TableValue[];
  activeTable: { value: string; label: string } | null;
  tableColumns: TablesDataColumns[];
  tableRows: any[];
  externalTablesRowSelected: number[];
  companyPriv: boolean;
  selectedRow: any[];
  editRowID: string;
  tableMode: 'edit' | 'add' | null;
}

export type FormTypes = 'edit' | 'new' | 'clone' | 'allRoles' | null;

export interface ConfiguratorUsers {
  users: UsersResponse[];
  selectedUser: UsersResponse | null;
  formMode: FormTypes;
  importUserKey: number;
  userRoles: RoleList[];
}

export interface ConfiguratorRoles {
  companiesForRoles: any[];
  selectedCompanyIdForRoles: number | null;
  usersForCompanyRoles: GetUserCompanyRoles | null;
  selectedUserForRoles: UserForRoleRow[];
  editRowId: string | null;
  openRowId: number | null;
  userAvailableRoles: RoleProgramResponseList[];
  transformedUserRows: UserForRoleRow[];
  isEditingUserRows: UserForRoleRow[];
  externalSelectedUserId: number[];
  selectedProgram: ProgramConfigureRole | null;
}

export interface ActionTables {
  // -------------- SearchBar --------------
  selectedView: { value: string; label: string } | null;
  selectedTableToSearch: string;
  companies: CompanyActionTable[];
  selectedCompanies: CompanyActionTable[];
  optionValueDropdown: OptionAT[];
  optionDescriptionDropdown: OptionAT[];
  selectedOptionValue: OptionAT[];
  // -------------- PO --------------
  editedRowId?: string;
  notModifiedRow?: PoMicroCategoryList | PoVendorList;
  leftTableRows: PoMicroCategoryList[] | PoVendorList[];
  selectedRow: PoMicroCategoryList | PoVendorList | null;

  // ---------------NOPO-------------------
  NOPO: {
    selectedRow: GetNopoVendorResponse | GetNopoMicrocategoriesResponse | null;
    editedRowId?: string;
    notModifiedRow?: GetNopoVendorResponse | GetNopoMicrocategoriesResponse | undefined;
    leftTableRows: GetNopoVendorResponse[] | GetNopoMicrocategoriesResponse[];
  };
  // --------------NOPO and PO --------------
  wfCasuals: WfCasual[];
  wfDefinitions: WfDefinition[];
  allMicroCategories: MicroCategory[];
  allSubjects: Subject[];
  // -------------- Corporate Approval --------------
  corporateApproval: {
    editRowId?: string;
    selectedRow: CorporateApproval | null;
    corporateApprovalList: CorporateApproval[];
  };
  // ---------------------NOPO--------------------------
  vendorListAp2Template: VendorNoPoList[]; // da cambiare
  AP2OptionsList: AP2OptionsList;
  selectedAP2row: AP2Template | null;
  editedRowAP2ID: number | null;
  isEditAP2Template: boolean;
  isAddAP2Template: boolean;
  templateAP2List: OriginalRow[];
}

export interface UpdateTableRowsUsers {
  idUsers: number[];
  property: string;
  value: any;
}

// ----------- mapper types -----------
export type Area = BoundingBox & {
  id: number;
  [x: string]: any;
  pageNumber: number;
  name?: string;
};

export type EditStates = 'newRow' | 'editRow' | 'inactive';

export type TemplateFieldList = BoundingBox & {
  id?: string;
  idTemplate: number;
  rowNumber: number;
  page: number;
  content: string;
};

export type TemplateFieldListScalar = TemplateFieldList & {
  idField: number;
};

export type TemplateFieldListColumn = TemplateFieldList & {
  idColumn: number;
};

export type TemplateFields = {
  id?: number;
  fixedName: string;
  type: number;
  inputType: number;
};

export type TemplateLinearFields = TemplateFields & {
  idField: number;
  name: string;
  otemplateFieldList: TemplateFieldListScalar[];
};

export type TemplateColumnFields = TemplateFields & {
  idColumn: number;
  title: string;
  otemplateColumnFieldList: TemplateFieldListColumn[];
};

export type GetTemplatesFields = {
  fields: TemplateLinearFields[];
  columnFields: TemplateColumnFields[];
};

export type GetTemplateIds = {
  fields: {
    idColumn?: number;
    idField?: number;
    fixedName: string;
    type: number;
    typology: number;
  }[];
};

export type OTemplate = {
  idTemplate: number;
  idDocType: number;
  templateName: string;
  pdfNrPages: number;
  pdfTemplatePath: string;
  metadataJsonPath: string | null;
};

export type DeleteTemplate = {
  idTemplate: number;
};

export type GetTemplateWithDocType = {
  idDocType: number;
  docName: string;
  oTemplateList: OTemplate[];
};

// ----------------------------------------------

// ----------- Roles interfaces -----------

export interface UserForRoleRow {
  id: number;
  idUser: number;
  idRole: number | null;
  username: string;
  name: string;
  email: string;
  role: string;
  programResponseList: ProgramResponseList[];
  subRows: UserForRoleRow[];
  isSubRow?: boolean;
}

export interface ProgramConfigureRole {
  id: number;
  idProgram: number;
  programName: string;
  active: boolean;
  privilegeResponseList: PrivilegeConfigureRole[];
}

export interface PrivilegeConfigureRole {
  idPrivilege: number;
  description: string;
  selected: boolean;
}

export interface Role {
  label: string;
  programs: ProgramConfigureRole[];
  value: number;
}

// ----------------------------------------------
// COMPANIES INTERFACE
interface DocumentType {
  idDocType: number;
  docName: string;
  batchDistribute: number;
  docType: number;
  creditDebit: number;
  oct: boolean;
  hide: boolean;
}

interface Companies {
  companies: Company[];
  selectedCompany: Company | null;
  formModeCompany: 'edit' | 'new' | 'clone' | null;
}

export interface Option {
  value: number;
  label: string;
}

export interface FormValuesCompany {
  name: string;
  label: string;
  vatNumber: string;
  socialReason: string;
  fiscalCode: string;
  phone: string;
  fax: string;
  email: string;
  url: string;
  onlineCode: string;
  referenceUser: number | undefined;
  counterPrefix: string;
  country: string;
  ocrDoc: boolean;
  documentTypeList: Option[];
  currency: string;
  countryCurrency: string;
}

export interface OptionAT {
  value: string | number;
  label: string;
}

export type ActionTableViews = {
  PO: 'PO';
  NO_PO: 'NO PO';
  CORPORATE_APPROVAL: 'CORPORATE APPROVAL';
};

export const actionTableViews: ActionTableViews = {
  PO: 'PO',
  NO_PO: 'NO PO',
  CORPORATE_APPROVAL: 'CORPORATE APPROVAL',
};

export interface AP2OptionsList {
  costCenter: HTableValues[];
  sign: HTableValues[];
  glAccount: HTableValues[];
  taxCode: HTableValues[];
}

export interface ConfiguratorWorkflow {
  viewMode: boolean;
  selectedTabColeCorp: string;
  selectedWorkflowCompanies: CompanyWorkflow[];
  selectedDefinitionTaskGroup: string;
  selectedCompanyDefinition: WorkflowCompaniesDefinitionListExtended | null;
  companiesDefinition: WorkflowCompaniesDefinition[];
  selectedTask: WorkflowTaskList | null;
  selectedGroup: WorkflowGroupList | null;
  listOfDefinitionsForCompanySelected: WorkflowCompaniesDefinitionList[];
  selectedDefinitionFromCompanySelected: WorkflowCompaniesDefinitionList | null;
  editedRowIdWfDefinition: number | null;
  isWfDefinitionTableEdit: boolean;
  selectedDefinitionTaskGroupsAssociations: WorkflowDefinitionTaskGroupsAssociationsExtended | null;
  selectedTaskWf: number | null;
  editedTaskWfRow: number | null;
  isWfTableEdit: boolean;
  editedRowIdWfGroup: number | null;
  listOfUserForGroup: OUserList[];
  selectedOUserList: OUserList | null;
  selectedGroupFromCompanySelected: CompanyGroups | null;
  selectedGroupUserAssociation: TaskGroupAssociations | null;
  listOfAssociations: WorkflowDefinitionTaskGroupsAssociationsExtended[];
  editedRowIdWfAssociation: number | null;
  listOfGroupsForTaskAssociations: CompanyGroupsForTaskAssociation[];
  isActiveCreateNewAssociation: boolean;
  isActiveEditAssociation: boolean;
  isActiveAddTaskToAssociation: boolean;
  openRowIdAssociationTable: number | null;
  selectedIdDefFromAssociations: number | undefined;
  selectedIdTaskDefFromAssociations: number | undefined;
}

export interface AppliedFilters {
  column: { label: string; value: string } | null;
  operator: { label: string; value: number } | null;
  value: string;
}

export interface ConfiguratorVendor {
  selectedCompanyVendorConfig: { value: number; label: string } | null;
  appliedFilters: AppliedFilters;
  subjectList: SearchSubject[];
  selectedVendorIdFromList: number | null;
  idEditedVendorFromList: number | null;
  isEditedVendorDetail: boolean;
  subjectTableColumns: TableProps['columns'] | null;
  subjectDetails: {
    editedRowId: string | undefined;
    selectedRowId: string | undefined;
    tableRows: OriginalRow[];
  };
}

export interface UpdateTableRowsVendor {
  idWSubject: number;
  newRow: SearchSubject;
}

// NOTIFICATIONS

export const configuratorNotificationsViews = {
  OPEN_WORKFLOW: 'OPEN WORKFLOW',
  WORKFLOW_REMINDER: 'WORKFLOW REMINDER',
  OVERDUE: 'OVERDUE',
} as const;

export type ActiveViewNotifications =
  (typeof configuratorNotificationsViews)[keyof typeof configuratorNotificationsViews];

export type FormTypesNotifications = 'edit' | 'add' | null;

export interface OpenWfNotification {
  formModeOpenWF: FormTypesNotifications;
  selectedOpenWfSettings: IOpenWorkflowNotifications | null;
  listOfOpenWfSettings: IOpenWorkflowNotifications[];
}

export interface WfReminderNotification {
  listOfWfReminderSettings: IWfReminderNotifications[];
  selectedWfReminderSettings: IWfReminderNotifications | null;
  formModeWfReminder: FormTypesNotifications;
}

export interface OverdueNotification {
  listOfOverdueSettings: IOverdueNotifications[];
  selectedOverdueSettings: IOverdueNotifications | null;
  formModeOverdue: FormTypesNotifications;
}

export interface ConfiguratorNotifications {
  activeView: ActiveViewNotifications;
  templatesList: GetTemplatesMailNotification[];
  openWf: OpenWfNotification;
  wfReminder: WfReminderNotification;
  overdue: OverdueNotification;
}
