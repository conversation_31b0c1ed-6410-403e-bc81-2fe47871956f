import React from 'react';
import { Route, Switch, useRouteMatch } from 'react-router-dom';
import styled from 'styled-components/macro';
import SummaryCategories from './SummaryCategories';
import DetailCategories from './DetailsCategories';
import { URLS } from 'utils/constants';
import Chart from './Chart';
import { Data, SearchParams, ViewMode } from '../interfaces';
import { CategoryDetailsFilters, ChartFilters, SummaryKpiFilters } from 'models/request';

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  padding-top: 0;
  position: relative;
  height: calc(100vh - 130px);
  overflow-x: hidden;
`;

interface RoutesProps {
  data: Data;
  loadCharts: (params: ChartFilters) => Promise<void>;
  loadCategoryDetails: (params: CategoryDetailsFilters) => Promise<void>;
  exportReport: (reportId: number) => Promise<void>;
  searchParams?: SearchParams;
  loadSummaryData: (params: SummaryKpiFilters) => void;
  drillDownProtocols: (idMetric: number, idGeoList: any) => Promise<void>;
  setViewMode: (value: ViewMode) => void;
  viewMode: ViewMode;
  message: string;
  setMessage: (value: string) => void;
}

const Routes = ({
  data,
  loadCharts,
  exportReport,
  searchParams,
  loadCategoryDetails,
  loadSummaryData,
  setViewMode,
  viewMode,
  drillDownProtocols,
  message,
  setMessage,
}: RoutesProps) => {
  const { path } = useRouteMatch();
  const { summaryCategories, detailedCategories, hierarchicalCategories, chartDataArray } = data;

  return (
    <Switch>
      <Wrapper>
        <Route
          exact
          path={path}
          render={() => (
            <SummaryCategories
              message={message}
              data={summaryCategories}
              loadCategoryDetails={loadCategoryDetails}
              searchParams={searchParams}
              viewMode={viewMode}
            />
          )}
        />
        <Route
          exact
          path={`${path}/${URLS.controlTower.detailCategories}`}
          render={() => (
            <DetailCategories
              path={path}
              exportReport={exportReport}
              detailedCategories={detailedCategories}
              hierarchicalCategories={hierarchicalCategories}
              searchParams={searchParams}
              loadSummaryData={loadSummaryData}
              loadCategoryDetails={loadCategoryDetails}
              setViewMode={setViewMode}
              viewMode={viewMode}
              drillDownProtocols={drillDownProtocols}
              message={message}
              setMessage={setMessage}
            />
          )}
        />
        <Route
          exact
          path={`${path}/${URLS.controlTower.detailCategories}/${URLS.controlTower.chart}`}
          render={() => (
            <Chart
              path={path}
              loadCharts={loadCharts}
              data={chartDataArray}
              searchParams={searchParams}
              message={message}
              setMessage={setMessage}
            />
          )}
        />
      </Wrapper>
    </Switch>
  );
};

export default Routes;
