import axios from 'axios';
import { ApiError } from 'utils/helpers/errors.helpers';

import cIniService from './cIni.service';
import cIniDiskService from './cIniDisk.service';
import monitorService from './monitor.service';
import gridColumnService from './gridColumn.service';
import localService from './local.service';
import loginService from './login.service';
import mailService from './mail.service';
import programService from './program.service';
import userService from './user.service';
import wDocBatchService from './wDocBatch.service';
import wHubPackageMailService from './wHubPackageMail.service';
import mailStatusService from './mailStatus.service';
import workflowService from './workflow.service';
import interactiveService from './interactive.service';
import configuratorService from './configurator.service';
import tableFields from './tableFields.service';
import kpiService from './kpi.service';
import gbsService from './gbs.service';
import chartsService from './charts.service';
import oCausalService from './oCausal.service';
import insightService from './insight.service';
import utils from 'utils';
import configurator from './configurator.service';
import notificationService from './notification.service';

import subject from './subject.service';
import file from './file.service';
import store from 'state/store';
import reportsService from './reports.service';
import { ErrorResponse } from 'models/response';
import userReportSettingsService from './userReportSettings.service';
import validationService from './validation.sevice';

const { REACT_APP_API_BASE_URL } = process.env;

const isErrorResponse = (response: any): response is ErrorResponse =>
  response.hasOwnProperty('statusCode') &&
  response.hasOwnProperty('status') &&
  response.hasOwnProperty('statusMessage') &&
  response.hasOwnProperty('actionsToDo');

export const verifyErrorResponse = (error: any, message: string, t: (value: string) => string) => {
  console.error(error);
  if (axios.isAxiosError(error)) {
    utils.app.notify('fail', `${t(message)} (${error.response?.data?.message})`);
    return;
  }
  if (isErrorResponse(error)) {
    utils.app.notify('fail', `${t(message)} (${error?.statusMessage}`);
    return;
  }
  utils.app.notify('fail', `${t(message)} (${(error as Error).message})`);
};

const configureApiClient = () => {
  axios.defaults.baseURL = REACT_APP_API_BASE_URL;

  axios.interceptors.request.use((config) => {
    const token = localStorage.getItem('jwt');
    const idProgram = store.getState().app.programCode;
    if (token) config.headers = { ...config.headers, Authorization: `Bearer ${token}`, idProgram };
    // http status code che risolvono la promise
    // ho aggiunto al al default (200-300) il 401, 400
    config.validateStatus = (status) => (status >= 200 && status < 300) || status === 401;
    // to be used with the HOC withAbortController
    if (globalThis.abortController) {
      config.signal = globalThis.abortController.signal;
    }
    return config;
  });
  axios.interceptors.response.use(
    (response) => {
      const { status } = response;
      if (status === 401) {
        localStorage.removeItem('jwt');
        // 401 http code is returned also from /login API when credentials are wrong
        if (window.location.pathname !== '/login') {
          utils.app.notify('warning', 'Session expired or unauthorized! Please login in again');
          window.location.href = '/login';
        }
      }
      if (status >= 200 && status <= 300 && isErrorResponse(response.data)) {
        const statusText = utils.app.textTranslated(response.data.statusMessage, utils.intl.translate);
        if (response.data.statusCode < 0) {
          utils.app.notify('warning', statusText);
        } else {
          utils.app.notify('success', statusText);
        }

        return Promise.reject(response.data);
      }
      // same as return response directly
      return Promise.resolve(response);
    },
    (error) => {
      if (error.message === 'canceled') return Promise.reject(error);
      try {
        console.error(error);
        const errorResponse = error?.response;
        const message = errorResponse?.data?.message || error?.message || 'something went wrong!';
        const customError = new ApiError(`${errorResponse?.status} ${errorResponse?.config?.url}: ${message}`);
        utils.app.notify('fail', customError, 7000);
        return Promise.reject(customError);
      } catch (catchedError) {
        utils.app.notify('fail', catchedError as Error, 7000);
        return Promise.reject(catchedError);
      }
    },
  );
};

export default {
  configureApiClient,
  ...cIniService,
  ...cIniDiskService,
  ...monitorService,
  ...localService,
  ...loginService,
  ...programService,
  ...gridColumnService,
  ...wDocBatchService,
  ...userService,
  ...mailService,
  ...subject,
  ...file,
  ...wHubPackageMailService,
  ...mailStatusService,
  ...monitorService,
  ...reportsService,
  ...workflowService,
  ...interactiveService,
  ...configuratorService,
  ...configurator,
  ...tableFields,
  ...kpiService,
  ...gbsService,
  ...chartsService,
  ...userReportSettingsService,
  ...oCausalService,
  ...notificationService,
  ...insightService,
  ...validationService,
};
