/* eslint-disable max-lines */
import { TableProps } from 'components/Table';
import {
  Area,
  GetTemplatesFields,
  TemplateColumnFields,
  TemplateFieldListScalar,
  TemplateFieldListColumn,
  TemplateLinearFields,
} from 'models/configurator';
import { AddTemplateFields, UpdateTemplateFields } from 'models/request';
import * as yup from 'yup';
import services from 'services';
import utils from 'utils';

export type voidParam = '-';

type Coordinates = {
  x1: number | voidParam;
  y1: number | voidParam;
  x2: number | voidParam;
  y2: number | voidParam;
};
// 4 type of rows and 2 type of subRows
type rowType = { id: string; fixedname: string; type: number | voidParam; inputType: number | voidParam };

export type SubRowType = TemplateFieldListScalar & rowType & Coordinates;
export type SubRowTypeColumn = TemplateFieldListColumn & rowType & Coordinates;

type RowTypeLinear = rowType & { name: string; idField: number; subRows: SubRowType[]; content: string };
type RowTypeColumn = rowType & { name: string; idColumn: number; subRows: SubRowTypeColumn[]; content: string };

export type rowWithMultipleSubRowLinear = RowTypeLinear & Coordinates;
export type rowWithMultipleSubRowColumn = RowTypeColumn & Coordinates;

export type rowWithSingleSubRowLinear = rowWithMultipleSubRowLinear & SubRowType;
export type rowWithSingleSubRowColumn = rowWithMultipleSubRowColumn & SubRowTypeColumn;

export type TableRowsType =
  | rowWithMultipleSubRowLinear
  | rowWithMultipleSubRowColumn
  | rowWithSingleSubRowLinear
  | rowWithSingleSubRowColumn;
// rowWithMultipleSubRowLinear | rowWithMultipleSubRowColumn | tableSubRowTypeColumn | tableSubRowType

// predicates to distinguish
export function isRowWitSingleSubRow(row: TableRowsType): row is rowWithSingleSubRowLinear | rowWithSingleSubRowColumn {
  return (row as rowWithSingleSubRowLinear).page !== undefined;
}
export function isLinearRow(row: TableRowsType): row is rowWithMultipleSubRowLinear | rowWithSingleSubRowLinear {
  return (row as rowWithMultipleSubRowLinear).idField !== undefined;
}

// predicate to distinguish if a template is TemplateField or TemplateColumnFields
export function isLinear(template: TemplateColumnFields | TemplateLinearFields): template is TemplateLinearFields {
  return (template as TemplateLinearFields).idField !== undefined;
}

export const flattenArray = (template: GetTemplatesFields): TableRowsType[] => {
  // filtering template fields and template columnsFields with more than 1 subRow
  const filteredTemplate = template.fields.filter((field) => field.otemplateFieldList.length > 0);
  const filteredTemplateColumn = template.columnFields.filter((field) => field.otemplateColumnFieldList.length > 0);
  // mapping template fields and template columnsFields with more than 1 subRow
  const filteredTemplateFields = {
    fields: filteredTemplate,
    columnFields: filteredTemplateColumn,
  };
  try {
    // take 2 array from the template and transform it in an unique array with shared properties
    const rowsArray = [...filteredTemplateFields.fields, ...filteredTemplateFields.columnFields];
    let counter = 0;
    const tableRows: TableRowsType[] = rowsArray.map((row) => {
      const tableRow: rowType = {
        id: String(counter++),
        fixedname: row.fixedName,
        type: row.type,
        inputType: row.inputType,
      };
      if (isLinear(row)) {
        if (row.otemplateFieldList.length > 1) {
          const rowWithMultipleSubRowLinear: rowWithMultipleSubRowLinear = {
            ...tableRow,
            name: row.name,
            idField: row.idField,
            x1: '-',
            x2: '-',
            y1: '-',
            y2: '-',
            content: '',
            subRows: row.otemplateFieldList.map((subRow) => {
              const tableSubRow = {
                ...tableRow,
                ...subRow,
                name: row.name,
                id: String(counter++),
              };
              return tableSubRow;
            }),
          };
          return rowWithMultipleSubRowLinear;
        }
        const NewRow: rowWithSingleSubRowLinear = {
          ...tableRow,
          ...row.otemplateFieldList[0],
          id: String(counter++),
          name: row.name,
          subRows: [],
          idField: row.idField,
        };
        return NewRow;
      } else {
        if (row.otemplateColumnFieldList.length > 1) {
          const rowWithMultipleSubRow: rowWithMultipleSubRowColumn = {
            ...tableRow,
            name: row.title,
            idColumn: row.idColumn,
            x1: '-',
            x2: '-',
            y1: '-',
            y2: '-',
            content: '',
            subRows: row.otemplateColumnFieldList.map((subRow) => {
              const tableSubRow = {
                ...tableRow,
                ...subRow,
                name: row.title,
                id: String(counter++),
              };
              return tableSubRow;
            }),
          };
          return rowWithMultipleSubRow;
        } else {
          const NewRow: rowWithSingleSubRowColumn = {
            ...tableRow,
            name: row.title,
            subRows: [],
            ...row.otemplateColumnFieldList[0],
          };
          return NewRow;
        }
      }
    });
    return tableRows;
  } catch (error) {
    console.error(error);
    return [];
  }
};

export const flattenArrayAreas = (template: GetTemplatesFields): Area[] => {
  const fields = template.fields;
  const columnFields = template.columnFields;
  const rowsArray: Area[] = [];
  let counter = 0;
  fields.forEach((field) => {
    field.otemplateFieldList.forEach((subRow) => {
      rowsArray.push({
        id: counter++,
        x1: subRow.x1 / 100,
        x2: subRow.x2 / 100,
        y1: subRow.y1 / 100,
        y2: subRow.y2 / 100,
        pageNumber: subRow.page,
        name: field.fixedName + '-' + subRow.rowNumber,
      });
    });
  });
  columnFields.forEach((field) => {
    field.otemplateColumnFieldList.forEach((subRow) => {
      // const { idColumn, title } = field
      rowsArray.push({
        id: counter++,
        x1: subRow.x1 / 100,
        x2: subRow.x2 / 100,
        y1: subRow.y1 / 100,
        y2: subRow.y2 / 100,
        pageNumber: subRow.page,
        name: field.fixedName + '-' + subRow.rowNumber,
      });
    });
  });
  return rowsArray;
};

export const rowSchema = (newRow: boolean): yup.ObjectSchema => {
  return newRow
    ? yup
        .object()
        .shape({
          name: yup.string().required().min(1),
          content: yup.string(),
          x1: yup.number().required().max(100).min(0),
          x2: yup.number().required().max(100).min(0),
          y1: yup.number().required().max(100).min(0),
          y2: yup.number().required().max(100).min(0),
        })
        .test('x1 < x2', 'x1 must be less than x2', (value) => {
          if (value) {
            const { x1, x2 } = value;
            return x1 < x2;
          }
          return true;
        })
        .test('y1 < y2', 'y1 must be less than y2', (value) => {
          if (value) {
            const { y1, y2 } = value;
            return y1 < y2;
          }
          return true;
        })
    : yup
        .object()
        .shape({
          content: yup.string(),
          x1: yup.number().required().max(100).min(0),
          x2: yup.number().required().max(100).min(0),
          y1: yup.number().required().max(100).min(0),
          y2: yup.number().required().max(100).min(0),
        })
        .test('x1 < x2', 'x1 must be less than x2', (value) => {
          if (value) {
            const { x1, x2 } = value;
            return x1 < x2;
          }
          return true;
        })
        .test('y1 < y2', 'y1 must be less than y2', (value) => {
          if (value) {
            const { y1, y2 } = value;
            return y1 < y2;
          }
          return true;
        });
};


export const columns = (
  types: { value: string | number; label: string; [x: string]: any }[],
  newRow: boolean,
): TableProps['columns'] => [
  {
    id: '0',
    accessor: 'name',
    Header: 'Name',
    inputType: 'select',
    selectOptionsTypes: types,
    filterType: 'select',
    selectFilterOptions: types,
    width: 150,
    isVisible: true,
    Cell: 'withCounter',
    readOnly: newRow ? false : true,
  },
  {
    id: '1',
    accessor: 'type',
    Header: 'Type',
    Cell: ({ value, row: { depth } }: { value: any; row: any }) => (depth > 0 ? '-' : value),
    inputType: 'text',
    filterType: 'free',
    width: 70,
    readOnly: true,
    isVisible: true,
  },
  {
    id: '2',
    accessor: 'rowNumber',
    Header: 'Row Number',
    width: 60,
    inputType: 'number',
    isVisible: false,
  },
  {
    id: '3',
    accessor: 'page',
    Header: 'Page Number',
    width: 76,
    inputType: 'number',
    isVisible: false,
  },
  {
    id: '4',
    accessor: 'idColumn',
    Header: 'ID Column',
    width: 76,
    readOnly: true,
    isVisible: false,
  },
  {
    id: '5',
    accessor: 'idField',
    Header: 'ID Field',
    width: 76,
    readOnly: true,
    isVisible: false,
  },
  {
    id: '6',
    accessor: 'inputType',
    Header: 'Typology',
    Cell: ({ value, row: { depth } }: { value: any; row: any }) => (depth > 0 ? '-' : value),
    inputType: 'text',
    filterType: 'free',
    width: 70,
    readOnly: true,
  },
  {
    id: '7',
    accessor: 'content',
    Header: 'Content',
    inputType: 'text',
    filterType: 'free',
    width: 76,
    Cell: ({ value, row: { depth, subRows } }: { value: any; row: any }) =>
      depth === 0 && subRows.length > 0 ? '-' : value,
    filter: utils.table.contentFilter,
    isVisible: true,
  },
  {
    id: '8',
    accessor: 'x1',
    Header: 'Area x1',
    inputType: 'number',
    width: 62,
    filterType: 'none',
    disableSortBy: true,
    Cell: ({ value, row: { depth, subRows } }: { value: any; row: any }) =>
      depth === 0 && subRows.length ? '-' : value,
    isVisible: true,
  },
  {
    id: '9',
    accessor: 'y1',
    Header: 'Area y1',
    inputType: 'number',
    width: 62,
    filterType: 'none',
    disableSortBy: true,
    Cell: ({ value, row: { depth, subRows } }: { value: any; row: any }) =>
      depth === 0 && subRows.length ? '-' : value,
    isVisible: true,
  },
  {
    id: '10',
    accessor: 'x2',
    Header: 'Area x2',
    inputType: 'number',
    width: 62,
    filterType: 'none',
    disableSortBy: true,
    Cell: ({ value, row: { depth, subRows } }: { value: any; row: any }) =>
      depth === 0 && subRows.length ? '-' : value,
    isVisible: true,
  },
  {
    id: '11',
    accessor: 'y2',
    Header: 'Area y2',
    inputType: 'number',
    width: 62,
    filterType: 'none',
    disableSortBy: true,
    Cell: ({ value, row: { depth, subRows } }: { value: any; row: any }) =>
      depth === 0 && subRows.length ? '-' : value,
    isVisible: true,
  },
];

export const addNewField = async (
  row: AddTemplateFields,
  t: (label: string) => string,
  fetchFieldsData: () => Promise<void>,
  onCancel: () => void,
) => {
  try {
    const response = await services.addTemplateFields(row);
    if (response.data?.statusCode !== undefined && response.data?.statusCode <= 0) {
      throw new Error(response.data?.statusMessage);
    }
    await fetchFieldsData();
    utils.app.notify('success', t('Field added successfully'));
  } catch (error) {
    utils.app.notify('fail', t(error as string));
    onCancel();
  }
};

export const editField = async (
  row: UpdateTemplateFields,
  t: (label: string) => string,
  fetchFieldsData: () => Promise<void>,
  onCancel: () => void,
) => {
  try {
    const response = await services.updateTemplateFields(row);
    if (response.data?.statusCode !== undefined && response.data?.statusCode <= 0) {
      throw new Error(response.data?.statusMessage);
    }
    await fetchFieldsData();
    utils.app.notify('success', t('Field updated successfully'));
  } catch (error) {
    utils.app.notify('fail', t(error as string));
    console.error('editField ~ error', t(error as string));
    onCancel();
  }
};
