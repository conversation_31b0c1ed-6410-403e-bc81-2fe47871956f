import React from 'react';
import Widget from '../Cake/WidgetContainer';
import { toMatchImageSnapshot } from 'jest-image-snapshot';
import { createSnapshot, renderWithStyle, snapshotConfig } from 'utils/helpers/test.helpers';

expect.extend({ toMatchImageSnapshot });

const properties = [
  {
    name: 'b2c',
    value: 30,
    color: '#ff0000',
  },
  {
    name: 'b2c',
    value: 300,
    color: '#fffb00',
  },
  {
    name: 'c2c',
    value: 30,
    color: '#36196b',
  },
  {
    name: 'c2b',
    value: 50,
    color: '#583838',
  },
];

describe('Widget Cake', () => {
  test('Widget Cake total check', () => {
    renderWithStyle(<Widget id="test" title="test-title" properties={properties} />);

    const cakeTotal = document.querySelector('#test > div > div');

    expect(cakeTotal.textContent).toBe('410');
  });

  test('Widget Cake title check', () => {
    renderWithStyle(<Widget id="test" title="test-title" properties={properties} />);

    const cakeTitle = document.querySelector('#test > div:nth-child(2) h1');

    expect(cakeTitle.textContent).toBe('test-title');
  });

  test('Widget Cake values check', () => {
    renderWithStyle(<Widget id="test" title="test-title" properties={properties} />);

    const cakeValueOne = document.querySelector('#test > div:nth-child(2) > div:nth-child(2) h2');
    expect(cakeValueOne.textContent).toBe('30');

    const cakeValueTwo = document.querySelector('#test > div:nth-child(2) > div:nth-child(3) h2');
    expect(cakeValueTwo.textContent).toBe('300');

    const cakeValueThree = document.querySelector('#test > div:nth-child(2) > div:nth-child(4) h2');
    expect(cakeValueThree.textContent).toBe('30');

    const cakeValueFour = document.querySelector('#test > div:nth-child(2) > div:nth-child(5) h2');
    expect(cakeValueFour.textContent).toBe('50');
  });

  test('matches snapshot when is full width', async () => {
    jest.setTimeout(30000);
    const image = await createSnapshot(<Widget id="test" title="test-title" properties={properties} />, {
      width: 400,
      heigth: 400,
    });
    expect(image).toMatchImageSnapshot(snapshotConfig);
  });
});
