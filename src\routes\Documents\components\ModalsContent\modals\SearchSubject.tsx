/* eslint max-lines: 0 */
import React, { useState } from 'react';
import { useFormikContext } from 'formik';
import Header from 'components/Modal/Header';
import Table from 'components/Table';
import Dropdown from 'components/Input/Dropdown';
import TextInput from 'components/Input/TextInput';
import CommonButton from 'components/Buttons/CommonButton';
import { Formik } from 'formik';
import styled from 'styled-components/macro';
import services from 'services';
import actions from 'state/actions';
import selectors from 'state/selectors';
import { DocumentHeaderResponse, SearchSubject } from 'models/response';
import { useDispatch, useSelector } from 'react-redux';
import utils from 'utils';
import { FormValues } from 'models/documents';
import { useExecuteToolbarActionDocHeader } from 'utils/helpers/toolbar.helpers';
import { FilePosition, FilePositionTypes } from '../../DocumentSearch/index.types';
import { slice } from 'state/documents/slice';

const SearchContainer = styled.div`
  display: flex;
  padding-bottom: 25px;
  > div {
    margin-right: 15px;
  }
`;

const TableContainer = styled.div`
  width: 1200px;
  .table-pagination {
    justify-content: center;
  }
`;

const ButtonsContainer = styled.div`
  display: flex;
  justify-content: center;
  button {
    margin: 0 5px;
  }
`;

const SearchSubjectModal = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const [subjectResult, setSubjectResult] = useState<SearchSubject[]>([]);
  const [selectedRow, setSelectedRow] = useState<SearchSubject | null>(null);
  const idProgram = useSelector(selectors.app.getProgramCode);
  const selectedDocuments = useSelector(selectors.documents.selectAllActiveDocuments);
  const isArchived = useSelector(selectors.documents.selectIsTemplateArchived);
  const tableRows = useSelector(selectors.configurator.selectTableRows);
  const idTemplate = useSelector(selectors.documents.selectActiveTemplateId);

  const protocols = selectedDocuments.map(({ protocol }) => protocol);
  // assuming that all documents have the same company and docType
  const { idCompany } = selectedDocuments[0];
  const { idDocType } = selectedDocuments[0];

  const { values } = useFormikContext<FormValues>();
  const saveAction = utils.hooks.useSaveAction();
  const resetManualDirty = utils.hooks.useResetManualDirty();
  const executeToolbarAction = useExecuteToolbarActionDocHeader();

  const searchTypes = [
    {
      label: 'EQUAL',
      value: 0,
    },
    {
      label: 'LIKE',
      value: 1,
    },
    {
      label: 'DISTANCE',
      value: 2,
    },
  ];

  const keys = [
    { label: t('soggetto'), value: 'SOGGETTO' },
    { label: t('codforn'), value: 'CODFORN' },
    { label: t('piva'), value: 'PIVA' },
    { label: t('codfisc'), value: 'CODFISC' },
    { label: t('tel'), value: 'TEL' },
    { label: t('email'), value: 'EMAIL' },
    { label: t('address'), value: 'ADDRESS' },
  ];

  const columns = [
    {
      Header: t('subject'),
      accessor: 'subject',
    },
    {
      Header: t('codForn'),
      accessor: 'supplierCodesString',
    },
    {
      Header: t('vatNumber'),
      accessor: 'vatNumber',
    },
    {
      Header: t('taxCode'),
      accessor: 'fiscalCode',
    },
    {
      Header: t('tel'),
      accessor: 'tel',
    },
    {
      Header: t('fax'),
      accessor: 'fax',
    },
    {
      Header: t('email'),
      accessor: 'email',
    },
    {
      Header: t('address'),
      accessor: 'address',
    },
  ];

  const onSubmit = async (values: {
    key: { label: string; value: string };
    searchType: { label: string; value: number };
    value: string;
    distance: number;
  }) => {
    const { data } = await services.getSubjects(
      {
        key: values.key.value,
        value: values.value,
        searchType: values.searchType.value,
        distance: values.distance,
        idCompany: idCompany || -1,
        idDocType: idDocType || -1,
        fromClient: false,
      },
      'modal',
    );
    setSubjectResult(data);
  };

  const onSelection = (rows: any) => {
    setSelectedRow(rows[0]);
  };

  const setDocumentHeader = async (): Promise<DocumentHeaderResponse | undefined> => {
    const protocol = protocols[0];
    const filePosition = tableRows.find((el) => el.protocol === protocol)?.position as FilePosition;
    if (!idTemplate) return;
    try {
      const { data } =
        isArchived || filePosition === FilePositionTypes.archived
          ? await services.getArchivedDocumentHeader({ protocol, idProgram, idTemplate })
          : await services.getDocumentHeader({ protocol, idProgram, idTemplate });
      dispatch(slice.actions.setDocumentHeader(data));
      return data;
    } catch (e) {
      console.error(e);
    }
  };

  const reloadPage = async () => {
    const previousSupplierCode = values.supplierCode;
    if (protocols && selectedRow) {
      // open document view
      const data = await setDocumentHeader();
      dispatch(actions.documents.setDocumentFields(protocols[0], idProgram));
      // documents list view
      dispatch(
        actions.documents.updateTableRows({
          protocols,
          property: 'idSubject',
          value: selectedRow.idWSubject,
        }),
      );
      dispatch(
        actions.documents.updateTableRows({
          protocols,
          property: 'subject',
          value: selectedRow.subject,
        }),
      );
      dispatch(
        actions.documents.updateTableRows({
          protocols,
          property: 'supplierCode',
          value: selectedRow.supplierCode,
        }),
      );
      if (data?.numberOfDetails === 1 && previousSupplierCode !== null) executeToolbarAction('supplierCode');
    }
  };

  const setSubject = async (val: number) => {
    try {
      if (protocols) {
        await saveAction(values);
        resetManualDirty(values);
        await services.setSubject({
          idSubject: val,
          protocols,
        });
        dispatch(actions.modal.closeModal());
        reloadPage();
        utils.app.notify('success', t('success'));
      }
    } catch (e) {
      console.error(e);
    }
  };

  const saveRecord = () => {
    if (selectedRow) {
      setSubject(selectedRow.idWSubject);
    }
  };

  return (
    <>
      <Header title={t('searchSubjectTitle')} subtitle={t('searchSubjectSubTitle')} />
      <Formik
        initialValues={{
          key: keys[0],
          searchType: searchTypes[1],
          value: '',
          distance: 0,
        }}
        onSubmit={onSubmit}
      >
        {({ values, handleBlur, handleChange, handleSubmit, setFieldValue }) => (
          <form onSubmit={handleSubmit}>
            <SearchContainer>
              <Dropdown
                name="key"
                value={values['key']}
                onChange={(value) => {
                  setFieldValue('key', value);
                }}
                options={keys}
              />
              <Dropdown
                name="searchType"
                placeholder={t('searchType')}
                value={values['searchType']}
                onChange={(value) => {
                  setFieldValue('searchType', value);
                }}
                options={searchTypes}
              />
              <TextInput
                id="distance"
                value={values['distance']}
                placeholder={t('distance')}
                type="number"
                onChange={handleChange}
                onBlur={handleBlur}
                disabled={values.searchType.value !== 2}
              />
              <TextInput
                id="value"
                value={values['value']}
                placeholder={t('valoreRicerca')}
                onChange={handleChange}
                onBlur={handleBlur}
              />
              <CommonButton scope="primary" value={t('search')} type="submit" disabled={!values['value']} />
            </SearchContainer>
          </form>
        )}
      </Formik>
      <TableContainer>
        {subjectResult ? (
          <Table
            rowId="idWSubject"
            hasSort
            columns={columns}
            rows={subjectResult}
            onSelection={onSelection}
            onRowDoubleClick={saveRecord}
            hasSelection
            hasPagination
            hasResize
          />
        ) : null}
      </TableContainer>
      <ButtonsContainer>
        <CommonButton
          scope="primary"
          value={t('confirm')}
          type="button"
          disabled={!selectedRow}
          action={() => saveRecord()}
        />
        <CommonButton
          scope="secondary"
          value={t('cancel')}
          type="button"
          action={() => dispatch(actions.modal.closeModal())}
        />
      </ButtonsContainer>
    </>
  );
};

export default SearchSubjectModal;
