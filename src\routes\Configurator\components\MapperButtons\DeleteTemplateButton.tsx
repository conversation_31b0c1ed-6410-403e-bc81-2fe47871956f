import React from 'react';
import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';
import actions from 'state/actions';
import { useSelector, useDispatch } from 'react-redux';
import { actionType } from 'utils/constants';

const DeleteTemplateButton = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const idDocType = useSelector(selectors.configurator.selectMapperIdDocType);
  const idTemplate = useSelector(selectors.configurator.selectActiveOTemplateId);
  const onRemoveTemplate = async () => {
    dispatch(actions.modal.setModal({ actionType: actionType.DELETE_TEMPLATE, props: { idTemplate, idDocType } }));
  };
  return (
    <CommonButton
      action={onRemoveTemplate}
      scope="tertiary"
      value={t('RemoveTemplate')}
      disabled={idTemplate === null || !utils.user.isActionByCompanyActive(actionType.DELETE_TEMPLATE)}
      icon="circle"
    />
  );
};
export default DeleteTemplateButton;
