/* eslint-disable max-lines */
import React, { useCallback, useState } from 'react';
import Header from '../../components/Header';
import { useDispatch, useSelector } from 'react-redux';
import { useAsync } from 'react-use';
import { Wrapper, Title, Buttons } from '../../styles';

import utils from 'utils';
import services from 'services';
import selectors from 'state/selectors';

import { UserCompany, PendingResponse } from 'models/response';

import Table, { TableProps } from 'components/Table';
import CommonButton from 'components/Buttons/CommonButton';
import actions from 'state/actions';
import { modalActionType } from 'utils/constants';

export interface CompanyOption {
  label: string;
  value: string;
}

const Pending = () => {
  const t = utils.intl.useTranslator();
  const companies = useSelector(selectors.app.getCompanies);
  const idUser = useSelector(selectors.user.getIdUser);
  const programCode = useSelector(selectors.app.getProgramCode);
  const [selectedCompany, setSelectedCompany] = useState<{ label: string; value: string } | null>(null);
  const [pending, setPending] = useState<PendingResponse[]>([]);
  const [selectedPending, setSelectedPending] = useState<number[]>([]);
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const { dateFormat } = useSelector(selectors.user.getUserPreference);
  const [chosenCompany, setChosenCompany] = useState<string[]>([]);
  const { isActionByCompanyActive } = utils.user;
  const dispatch = useDispatch();
  const userPreference = useSelector(selectors.user.getUserPreference);
  const columns: TableProps['columns'] = [
    {
      Header: t('protocol'),
      accessor: 'protocol',
      filterType: 'free',
    },
    {
      Header: t('fileName'),
      accessor: 'fileName',
      filterType: 'free',
    },
    {
      Header: t('companyName'),
      accessor: 'companyName',
      filterType: 'free',
    },
    {
      Header: t('documentTypeName'),
      accessor: 'documentTypeName',
      filterType: 'free',
    },
    {
      Header: t('dateIn'),
      accessor: 'dateIn',
      filterType: 'date',
      dateFormat,
      filter: utils.date.epochFilterFunction(userPreference.timeZone ?? 'UTC'),
      Cell: ({ value }: { value: number }) => utils.date.convertDate(value, userPreference.timeZone ?? 'UTC'),
    },
    {
      Header: t('acquisitionDate'),
      accessor: 'acquisitionDate',
      filterType: 'date',
      dateFormat,
      filter: utils.date.epochFilterFunction(userPreference.timeZone ?? 'UTC'),
      Cell: ({ value }: { value: number }) => utils.date.convertDate(value, userPreference.timeZone ?? 'UTC'),
    },
    {
      Header: t('sourceChannel'),
      accessor: 'sourceChannel',
      filterType: 'free',
    },
    {
      Header: t('dateTry'),
      accessor: 'dateTry',
      filterType: 'date',
      dateFormat,
      filter: utils.date.epochFilterFunction(userPreference.timeZone ?? 'UTC'),
      Cell: ({ value }: { value: number }) => utils.date.convertDate(value, userPreference.timeZone ?? 'UTC'),
    },
    {
      Header: t('barcode'),
      accessor: 'protocolIn',
      filterType: 'free',
    },
    {
      Header: t('status'),
      accessor: 'exportStatusName',
      filterType: 'free',
    },
  ];

  useAsync(async () => {
    try {
      const { data } = await services.getPending({ idCompany: Number(selectedCompany?.value) || null });
      setPending(data);
    } catch (e) {
      console.error(e);
    }
  }, [selectedCompany]);

  const companyOptions = (data: UserCompany[]) => {
    const companies = utils.input.removeDefaultValue(data, 'idCompany');
    const options = utils.input.buildOptions(companies, 'name', 'idCompany');

    return options;
  };

  const isForceRetryActive = useCallback(
    () =>
      chosenCompany.map((companyName) => isActionByCompanyActive('ForceRetry', companyName)).every((e) => e === true) &&
      selectedRows.map((e) => e.dateTry).every((e) => e !== null && e !== undefined && e !== ''),
    [chosenCompany, isActionByCompanyActive, selectedRows],
  );

  const onSelection = (selection: PendingResponse[]) => {
    setSelectedPending(selection.map(({ protocol }) => protocol));
    setSelectedRows(selection);
    setChosenCompany(Array.from(new Set(selection.map((e) => e.companyName))));
  };

  const deletePending = async () => {
    if (idUser) {
      try {
        await services.deleteDocuments({
          protocols: selectedPending,
          idCompany: selectedRows[0].idCompany,
          idProgram: programCode,
          idUser,
        });
        const { data } = await services.getPending({ idCompany: Number(selectedCompany?.value) || null });
        setPending(data);
        dispatch(actions.modal.closeModal());
        setSelectedRows([]);
        utils.app.notify('success', t('deleted'));
      } catch (e) {
        utils.app.notify('fail', t('not_deleted'));
        console.error(e);
      }
    }
  };

  const retryPending = async () => {
    try {
      await services.retryPending({
        protocols: selectedPending,
      });
      const { data } = await services.getPending({ idCompany: Number(selectedCompany?.value) || null });
      setPending(data);
      utils.app.notify('success', t('success_retry'));
    } catch (e) {
      utils.app.notify('fail', t('not_success_retry'));
      console.error(e);
    }
  };

  const forceRetryPending = async () => {
    try {
      await services.forceRetriedDocuments({
        protocols: selectedPending,
      });
      const { data } = await services.getPending({ idCompany: Number(selectedCompany?.value) || null });
      setPending(data);
      utils.app.notify('success', t('success_retry_forced'));
    } catch (e) {
      utils.app.notify('fail', t('not_success_retry_forced'));
      console.error(e);
    }
  };

  const checkDeleteButton = () => {
    if (selectedRows.length) {
      const idCompanyCheck = selectedRows[0].idCompany;
      const checkArray = selectedRows.map((e) => {
        return idCompanyCheck === e.idCompany;
      });
      return !checkArray.every((e) => e === true);
    } else {
      return true;
    }
  };

  return companies ? (
    <>
      <Header
        companies={companyOptions(companies)}
        companySelection={true}
        setSelectedCompany={setSelectedCompany}
        selectedCompany={selectedCompany}
      />
      <Wrapper>
        <Title>
          <h2>{t('pending_title')}</h2>
        </Title>
        <Buttons>
          <CommonButton
            scope="secondary"
            value={t('retry')}
            type="button"
            action={() => retryPending()}
            disabled={!selectedPending.length}
          />
          <CommonButton
            scope="secondary"
            value={t('force_retry')}
            type="button"
            action={() => forceRetryPending()}
            disabled={!selectedPending.length || !isForceRetryActive()}
          />
          <CommonButton
            scope="secondary"
            value={t('delete')}
            type="button"
            action={() =>
              dispatch(
                actions.modal.setModal({
                  actionType: modalActionType.normalizer.DELETE_CONFIRMATION,
                  props: {
                    subtitle: t(`Are you sure to remove this ${selectedRows.length > 1 ? 'rows' : 'row'}?`),
                    func: () => deletePending(),
                  },
                }),
              )
            }
            disabled={checkDeleteButton()}
          />
        </Buttons>
        {pending.length ? (
          <Table
            rowId="protocolIn"
            hasToolbar
            columns={columns}
            rows={pending}
            onSelection={onSelection}
            hasMultiSelection
            hasPagination
            hasResize
            hasSort
            hasFilter
          />
        ) : (
          <div>{t('no_data')}</div>
        )}
      </Wrapper>
    </>
  ) : null;
};

export default Pending;
