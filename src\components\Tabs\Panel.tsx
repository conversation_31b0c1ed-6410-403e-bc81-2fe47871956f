import * as React from 'react';
import { useTabs } from './Tabs';

export interface IPanelProps {
  /**
   * Unique identifier for this tab.
   */
  label: string;
  children?: React.ReactNode;
  id: number;
}

export const Panel: React.FC<IPanelProps> = (props: IPanelProps) => {
  /**
   * Individual panel component.
   */
  const { activeTab } = useTabs();
  return activeTab === props.id ? <div className="active">{props.children}</div> : null;
};
