import Modal from 'components/Modal';
import React from 'react';
import utils from 'utils';
import selectors from 'state/selectors';
import actions from 'state/actions';
import services from 'services';
import { useDispatch, useSelector } from 'react-redux';

function RemoveTemplateModal() {
  const dispatch = useDispatch();
  const { idDocType, idTemplate }: { idDocType: number | null; idTemplate: number | null } = useSelector(
    selectors.modal.getModalProps,
  );
  const t = utils.intl.useTranslator();

  const onSaveAction = async () => {
    try {
      if (!(idDocType && idTemplate)) {
        throw new Error('idDocType or idTemplate is null');
      }
      await services.deleteTemplate(idTemplate);
      dispatch(actions.configurator.setActiveOTemplateId(null));
      dispatch(actions.configurator.setActiveDocTypeId(null));
      dispatch(actions.configurator.removeOtemplate({ idDocType, idTemplate }));
      utils.app.notify('success', t('template deleted'));
    } catch (error) {
      console.error('EditTemplateModal ~ error ', error);
      utils.app.notify('fail', t('template not deleted'));
    }
  };

  return (
    <div>
      <Modal.Header title={t('Remove Template')} />
      <Modal.Content>
        <p>{t('Are you sure?')}</p>
      </Modal.Content>
      <Modal.Footer confirmText={t('Remove')} confirmAction={onSaveAction} withCloseButton />
    </div>
  );
}

export default RemoveTemplateModal;
