import { useTheme } from 'providers/ThemeProvider';
import React from 'react';
import { Link } from 'react-router-dom';
import styled from 'styled-components/macro';

const StyledLink = styled(Link)`
  text-decoration: none;
`;

// Gestisci css submenu in modo ricorsivo
const StyledMenuButton = styled.div<{ isActive: boolean; darkMode: boolean }>`
  display: flex;
  align-items: center;
  width: 100%;
  margin: 5px 0 5px 0;
  padding-left: 2px;
  font-size: ${({ theme }) => theme.fontSizePalette.body.M};
  text-align: left;
  height: 24px;
  border: 1px solid transparent;
  border-radius: 5px;
  background-color: ${({ isActive, theme }) =>
    isActive &&
    `${theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.turquoise.background}`};
  p {
    color: ${({ isActive, theme, darkMode }) =>
      isActive
        ? darkMode
          ? theme.colorPalette.turquoise.light
          : theme.colorPalette.turquoise.dark
        : theme.colorPalette.grey.grey9};
    font-weight: ${({ isActive, theme }) =>
      isActive ? theme.fontWeightPalette.medium : theme.fontWeightPalette.light};
    padding: 5px 0 4px 6px;
    line-height: 14px;
    font-size: ${({ theme }) => theme.fontSizePalette.body.S};
    letter-spacing: -0.38px;
  }

  &:hover {
    cursor: pointer;
  }
`;

const MenuContainer = styled.div<{ isActive: boolean }>`
  border-bottom: 1px solid ${({ theme }) => theme.colorPalette.grey.grey5};
`;

export interface Props {
  isActive: boolean;
  text: string;
  link: string;
}

const MenuOption = (props: Props) => {
  const { isActive, text, link } = props;
  const { isDarkMode } = useTheme();

  return (
    <StyledLink to={link}>
      <MenuContainer isActive={isActive}>
        <StyledMenuButton isActive={isActive} darkMode={isDarkMode}>
          <p>{text}</p>
        </StyledMenuButton>
      </MenuContainer>
    </StyledLink>
  );
};

export default MenuOption;
