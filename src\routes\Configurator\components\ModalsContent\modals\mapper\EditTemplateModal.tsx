import Modal from 'components/Modal';
import { useFormik } from 'formik';
import React from 'react';
import utils from 'utils';
import selectors from 'state/selectors';
import actions from 'state/actions';
import { colorPalette } from 'utils/styleConstants';
import { TextInput } from 'components/Input';
import services from 'services';
import { useDispatch, useSelector } from 'react-redux';

function EditTemplateModal() {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const { templateId, activeDocTypeId }: { templateId: number; activeDocTypeId: number } = useSelector(
    selectors.modal.getModalProps,
  );
  const formikInitialValue: {
    name: string;
  } = {
    name: '',
  };

  const formikValidation = (values: typeof formikInitialValue) => {
    const errors: any = {};
    if (!values.name) {
      errors.name = t('validation template name');
    }
    return errors;
  };
  const onSaveAction = async (values: { name: string }) => {
    try {
      if (!(typeof templateId == 'number' && typeof activeDocTypeId === 'number')) {
        throw new Error('some params are not valid');
      }
      await services.editTemplate(templateId, values.name);
      utils.app.notify('success', t('template edited'));
      dispatch(actions.configurator.setTemplateNewName({ docTypeId: activeDocTypeId, templateId, name: values.name }));
      dispatch(actions.modal.closeModal());
    } catch (e) {
      console.error(e);
      utils.app.notify('fail', e as string);
    }
  };
  const formik = useFormik({
    initialValues: formikInitialValue,
    onSubmit: (values) => onSaveAction(values),
    validate: formikValidation,
  });
  const {
    errors,
    values,
    // setFieldValue,
    handleSubmit,
    handleChange,
    dirty,
    isValid,
  } = formik;

  return (
    <div>
      <Modal.Header title={t('New Template')} subtitle={t('SelectNewNameTemplate')} />
      <form onSubmit={handleSubmit}>
        <Modal.Content>
          <TextInput
            name="name"
            value={values.name}
            onChange={handleChange}
            label={t('Template-name')}
            width="168px"
            labelMarginRight="auto"
            labelFontSize="16px"
            labelWidth="auto"
            labelColor={colorPalette.grey.grey9}
            hasFeedback={errors && errors.name ? true : false}
            feedbackMessage={errors.name}
            borderRadius="3px"
          />
        </Modal.Content>
      </form>
      <Modal.Footer
        confirmText={t('Save')}
        confirmAction={formik.submitForm}
        withCloseButton
        confirmDisabled={!dirty || !isValid}
      />
    </div>
  );
}

export default EditTemplateModal;
