/* eslint max-lines: 0 */
import React, { useState, useRef, useEffect } from 'react';
import { useAsync } from 'react-use';
import { useDispatch, useSelector } from 'react-redux';
import CommonButton from 'components/Buttons/CommonButton';
import ToolBar from 'components/Toolbar';
import { PdfRotation, modalActionType } from 'utils/constants';
import {
  PdfUploaderWrapper,
  PdfWrapper,
  PdfHeader,
  MassiveWrapper,
  MassivePdf,
  DeleteMassivePdf,
  NameMassivePdf,
} from './styles';
import PdfCarousel from './pdf/corousel';
import Bottom from './pdf/bottom';

import Dialog from '@material-ui/core/Dialog';
import DialogContent from '@material-ui/core/DialogContent';
import DialogContentText from '@material-ui/core/DialogContentText';
import DialogTitle from '@material-ui/core/DialogTitle';

import utils from 'utils';
import selectors from 'state/selectors';
import actions from 'state/actions';
import services from 'services';

import { PageAction } from 'models/request';
import { CompanyWithDocAndMeta, AuthorizedSplitAndFixDocumentsResponse } from 'models/response';
import { FormData } from './pdf/bottom';
import Modal from 'components/Modal';
import Table, { TableColumn } from 'components/Table';

interface PdfUploaderProps {
  idProgram: number;
}

export interface PageOptions {
  page: number;
  rotation: PdfRotation;
  deltaRotation: number;
  delete: boolean;
}

interface Pdf {
  base64: string;
  name: string;
}

const defaultPageOptions: { rotation: PdfRotation; delete: boolean; deltaRotation: number } = {
  rotation: null,
  deltaRotation: 0,
  delete: false,
};

const PdfUploader = (props: PdfUploaderProps) => {
  const { idProgram } = props;
  const idUser = useSelector(selectors.user.getIdUser);
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const [remember, setRemember] = useState<boolean>(false);
  const [rememberData, setRememberData] = useState<FormData | null>(null);
  const [mode, setMode] = useState<string>('');
  const [pdf, setPdf] = useState<Pdf[]>([]);
  const [split, setSplit] = useState<number[]>([]);
  const [splitString, setSplitString] = useState<string>('');
  const [splitStringError, setSplitStringError] = useState<boolean>(false);
  const [pageOptions, setPageOptions] = useState<PageOptions[]>([]);
  const inputFile = useRef<HTMLInputElement>(null);
  const [inputKey, setInputKey] = useState<number>(0);
  const [activePdf, setActivePdf] = useState<string>('');
  const [activePdfPageLength, setActivePdfPageLength] = useState<number>(0);
  const [thumbnailZoom, setThumbnailZoom] = useState<number>(0.25);
  const [pageNumber, setPageNumber] = useState<number>(0);
  const [percentage, trackPercentage] = useState(0);
  const [companiesWithDocsAndMetadataByProgram, setCompaniesWithDocsAndMetadataByProgram] = useState<
    CompanyWithDocAndMeta[]
  >([]);
  const [currentFile, setCurrentFile] = useState<HTMLInputElement | null>(null);

  const [open, setOpen] = useState(false);

  const [openQueueModal, setOpenQueueModal] = useState(false);
  const [queueProtocols, setQueueProtocols] = useState<AuthorizedSplitAndFixDocumentsResponse[]>([]);
  const [selectedQueueProtocol, setSelectedQueueProtocol] = useState<AuthorizedSplitAndFixDocumentsResponse[]>([]);

  const { dateFormat, timeZone } = useSelector(selectors.user.getUserPreference);

  const handleClose = () => setOpen(false);
  // getting templates and setting first as default
  useAsync(async () => {
    if (idUser && idProgram) {
      const { data } = await services.getCompanyWithDocsAndMetadataByProgram({ idUser, idProgram });
      setCompaniesWithDocsAndMetadataByProgram(utils.input.removeDefaultValue(data, 'idCompany'));
    }
  }, [idUser, idProgram]);

  const selectFile = () => {
    inputFile?.current?.click();
    setCurrentFile(inputFile?.current);
  };

  const activeFile = pdf.find(({ name }) => name === activePdf)?.base64;

  const HandleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.length) {
      const { files } = e.target;
      const base64Array = [];
      for (let i = 0; i < files.length; i++) {
        const { name } = files[i];
        const base64 = await utils.file.toBase64(files[i]);
        typeof base64 === 'string' && base64Array.push({ base64, name });
      }
      if (base64Array) {
        setPdf(base64Array);
        setActivePdf(base64Array[0].name);
      }
    }
  };

  const queueUpload = async () => {
    if (selectedQueueProtocol.length) {
      try {
        const { data } = await services.getDocumentImage(Number(selectedQueueProtocol[0]?.protocol));
        setPdf([{ base64: data, name: selectedQueueProtocol[0]?.protocol.toString() }]);
        setActivePdf(selectedQueueProtocol[0]?.protocol.toString());
        setOpenQueueModal(false);
      } catch (e) {
        console.error(e);
      }
    }
  };

  const reset = (fileName?: string) => {
    setSplitString('');
    setSplit([]);
    setPageOptions([]);
    setSplitStringError(false);
    setThumbnailZoom(0.25);
    setInputKey((prev) => prev + 1);
    if (fileName) {
      const newPdfArray = pdf.filter(({ name }) => name !== fileName);
      setPdf(newPdfArray);
      if (newPdfArray.length) {
        const { name } = newPdfArray[0];
        setActivePdf(name);
      }
    }
  };

  const annulla = () => {
    setSelectedQueueProtocol([]);
    setPdf([]);
    if (!remember) {
      setInputKey((prev) => prev + 1);
    }
    setActivePdfPageLength(0);
    reset();
    const ref = inputFile.current;
    if (ref) {
      ref.value = '';
    }
  };

  const setRememberdata = (data: FormData | null) => {
    setRememberData(data);
  };

  useEffect(() => {
    if (activePdf) {
      const pageOptions: PageOptions[] = [];
      let i;
      for (i = 1; i <= activePdfPageLength; i++) {
        pageOptions.push({ ...defaultPageOptions, page: i });
      }
      setPageOptions(pageOptions);
      setSplit([activePdfPageLength]);
      setSplitString(`1-${activePdfPageLength}`);
    }
  }, [activePdf, activePdfPageLength, currentFile]);

  const setPreview = (page: number) => {
    dispatch(actions.modal.openModal(modalActionType.pdfUploader.PDF_PREVIEW));
    setPageNumber(page);
  };

  const rotateAll = (direction: 'left' | 'rigth') =>
    document
      .querySelectorAll<HTMLButtonElement>(`.filefixer-container .filefixer-${direction}`)
      .forEach((button) => button.click());

  const rotatePdf = (direction: 'left' | 'rigth', pageNumber: number, currentRotation: PdfRotation) => {
    if (currentRotation !== null) {
      const index = pageOptions.findIndex((elem) => elem.page === pageNumber);
      const arrayRotationValue: PdfRotation[] = [0, 90, 180, 270];
      const currentRotationIndex = arrayRotationValue.indexOf(currentRotation);
      if (index !== -1) {
        const copy = [...pageOptions];
        let currentDeltaRotation = copy[index].deltaRotation;
        let newRotationINdex: number;
        if (direction === 'left') {
          newRotationINdex = currentRotationIndex - 1;
          newRotationINdex = newRotationINdex < 0 ? 3 : newRotationINdex;
          currentDeltaRotation--;
          currentDeltaRotation = currentDeltaRotation < 0 ? 3 : currentDeltaRotation;
          copy[index].deltaRotation = currentDeltaRotation;
        } else {
          newRotationINdex = currentRotationIndex + 1;
          newRotationINdex = newRotationINdex > 3 ? 0 : newRotationINdex;
          currentDeltaRotation++;
          currentDeltaRotation = currentDeltaRotation > 3 ? 0 : currentDeltaRotation;
          copy[index].deltaRotation = currentDeltaRotation;
        }
        copy[index].rotation = arrayRotationValue[newRotationINdex];

        setPageOptions(copy);
      }
    }
  };

  const getRotation = (nPage: number): PdfRotation => {
    const rotation = pageOptions?.find(({ page }) => page === nPage)?.rotation;
    return rotation === undefined ? null : rotation;
  };

  const toggleDelete = (pageNumber: number) => {
    const index = pageOptions.findIndex((elem) => elem.page === pageNumber);
    if (index !== -1) {
      const copy = [...pageOptions];
      copy[index].delete = !copy[index].delete;
      setPageOptions(copy);
    }
  };

  const setSplitFn = (val: number[]) => {
    setSplit(val);
  };

  const setSplitStringFn = (val: string) => {
    setSplitString(val);
  };

  const deleteMassiveFile = (i: number) => {
    const newPdf = pdf;
    newPdf.splice(i, 1);
    setPdf([...newPdf]);
    if (newPdf.length === 0) {
      const ref = inputFile.current;
      if (ref) {
        ref.value = '';
      }
    }
    if (newPdf.length) {
      setActivePdf(pdf[0].name);
    }
  };

  const handleSplitStringChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setSplitString(value);
    if (!value) {
      setSplit([]);
      return setSplitStringError(false);
    }
    if (value === 'all') {
      let splitArray: number[] = [];
      for (let i = 1; i <= activePdfPageLength; i++) {
        splitArray = [...splitArray, i];
      }
      setSplit(splitArray);
      setSplitStringError(false);
      return;
    }
    if (/^all:[1-9]$/.test(value)) {
      const interval = parseInt(value.slice(-1), 10);
      if (interval > activePdfPageLength) {
        setSplitStringError(true);
        setSplit([]);
      } else {
        const rest = activePdfPageLength % interval;
        let splitArray: number[] = [];
        let i;
        for (i = 0; i < activePdfPageLength; ) {
          i = i + interval;
          if (i > activePdfPageLength) break;
          splitArray = [...splitArray, i];
        }
        if (rest) splitArray = [...splitArray, activePdfPageLength];
        setSplit(splitArray);
        setSplitStringError(false);
        return;
      }
    }
    // eslint-disable-next-line max-len
    const rangeRegex =
      /^(([1-9][0-9]?-[1-9][0-9]?,(?=.))|([1-9][0-9]?),(?=.)){0,}(([1-9][0-9]?-[1-9][0-9]?)|([1-9][0-9]?)|(all(:[1-9])?))?$/;
    if (rangeRegex.test(value)) {
      const splitValue = value.split(/,|-/);
      const splitValueNumber = splitValue.map((e) => parseInt(e, 10));
      const isSorted = (arr: number[]) => arr.every((v, i, a) => !i || a[i - 1] <= v);
      const hasUniqueElements = (arr: number[]) => new Set(arr).size !== arr.length;
      if (
        splitValueNumber.some((e) => e > activePdfPageLength) ||
        !isSorted(splitValueNumber) ||
        hasUniqueElements(splitValueNumber)
      ) {
        setSplitStringError(true);
      } else {
        const splitValue = value.split(',').map((e) => e.split('-')?.pop() || '0');
        if (splitValue) {
          const splitValueNumber = splitValue.map((e) => parseInt(e, 10));
          setSplit(splitValueNumber);
        }
        return setSplitStringError(false);
      }
    }
    setSplitStringError(true);
    setSplit([]);
  };

  const splitDocument = async (e?: React.SyntheticEvent, force?: boolean) => {
    if (mode === 'splitter') {
      if (!split.includes(activePdfPageLength) && !force) {
        setOpen(true);
        return;
      }
      try {
        if (activeFile && rememberData?.company?.value && idUser && rememberData?.docType?.value && activePdf) {
          let pageAction: PageAction = {};
          for (let i = 1; i < activePdfPageLength + 1; i++) {
            const index = pageOptions.findIndex((elem) => elem.page === i);
            if (index < 0) break;
            const page = pageOptions[index];
            // @ts-ignore
            pageAction = {
              ...pageAction,
              [i]: {
                delete: page.delete,
                rotation: [null, 90, 180, 270][page.deltaRotation],
              },
            };
          }
          dispatch(actions.modal.openModal(modalActionType.pdfUploader.UPLOAD_PROGRESS));
          const { data: result } = await services.uploadFile(
            {
              originalProtocol: selectedQueueProtocol ? selectedQueueProtocol[0]?.protocol : undefined,
              idUser,
              idCompany: rememberData?.company?.value,
              idDocType: rememberData?.docType?.value,
              pdfBase64: activeFile,
              splitString,
              filename: activePdf,
              uploaderFields: {
                // eslint-disable-next-line @typescript-eslint/camelcase
                id_subject: rememberData?.subject?.value,
                // eslint-disable-next-line @typescript-eslint/camelcase
                id_father: rememberData?.idParent || undefined,
              },
              pageAction,
            },
            trackPercentage,
          );
          if (result === 'ok') {
            utils.app.notify('success', '*pdf ok');
            reset(activePdf);
          } else {
            utils.app.notify('warning', '*something went wrong');
          }
        }
      } catch (e) {
        utils.app.notify('fail', `an error has occurred: ${e}`);
      } finally {
        dispatch(actions.modal.closeModal());
        trackPercentage(0);
      }
    } else if (mode === 'all') {
      try {
        if (rememberData?.company?.value && idUser && rememberData?.docType?.value) {
          dispatch(actions.modal.openModal(modalActionType.pdfUploader.UPLOAD_PROGRESS));
          let cumulativePercentage = 0;
          for (let i = 0; i < pdf.length; ) {
            setActivePdf(pdf[i].name);
            const { data: result } = await services.uploadFile(
              {
                originalProtocol: selectedQueueProtocol ? selectedQueueProtocol[0]?.protocol : undefined,
                idUser,
                idCompany: rememberData?.company?.value,
                idDocType: rememberData?.docType?.value,
                pdfBase64: pdf[i].base64,
                splitString: '',
                filename: pdf[i].name,
                uploaderFields: {
                  // eslint-disable-next-line @typescript-eslint/camelcase
                  id_subject: rememberData?.subject?.value,
                  // eslint-disable-next-line @typescript-eslint/camelcase
                  id_father: rememberData?.idParent || undefined,
                },
                pageAction: {},
              },
              // eslint-disable-next-line
              (progressPercentage) => {
                cumulativePercentage += progressPercentage / pdf.length;
                trackPercentage(cumulativePercentage);
              },
            );
            if (result === 'ok') {
              utils.app.notify('success', '*pdf ok');
            } else {
              utils.app.notify('warning', '*something went wrong');
            }
            i++;
          }
          setMode('');
          annulla();
          utils.app.notify('success', 'operation completed');
        }
      } catch (e) {
        utils.app.notify('fail', `*an error has occurred: ${e}`);
      } finally {
        dispatch(actions.modal.closeModal());
        trackPercentage(0);
      }
    }
  };

  const queueUploadColumns: TableColumn[] = [
    { accessor: 'protocol', Header: t('protocol'), filterType: 'free' },
    { accessor: 'protocolIn', Header: t('protocolIn'), filterType: 'free' },
    {
      accessor: 'acquisitionDate',
      Header: t('acquisitionDate'),
      filterType: 'date',
      dateFormat,
      filter: utils.date.epochFilterFunction(timeZone ?? 'UTC'),
      Cell: ({ value }: { value: number }) => {
        return (
          <div title={utils.date.convertDate(value, timeZone ?? 'UTC')}>
            {utils.date.convertDate(value, timeZone ?? 'UTC')}
          </div>
        );
      },
    },
    { accessor: 'companyName', Header: t('companyName'), filterType: 'free' },
    { accessor: 'idSubject', Header: t('idSubject'), filterType: 'free' },
  ];

  const onSelection = (rows: AuthorizedSplitAndFixDocumentsResponse[]) => {
    setSelectedQueueProtocol(rows);
  };

  return (
    <PdfUploaderWrapper>
      <ToolBar.BlueBar>
        <ToolBar.Left>
          {pdf.length ? (
            <CommonButton scope="primary" value={t('cancel')} action={annulla} />
          ) : (
            <>
              <CommonButton
                scope="primary"
                value={t('process-new-documents')}
                action={() => {
                  selectFile();
                  setMode('splitter');
                }}
              />
              <CommonButton
                scope="primary"
                value={t('massive-upload')}
                action={() => {
                  selectFile();
                  setMode('all');
                }}
              />
              <CommonButton
                scope="primary"
                value={t('queue-upload')}
                action={async () => {
                  try {
                    const { data } = await services.getAuthorizedSplitDocuments();
                    if (data.length > 0) {
                      setQueueProtocols(data);
                      setOpenQueueModal(true);
                      setMode('splitter');
                    } else {
                      utils.app.notify('warning', t('queue-spilt-and-fix-empty'));
                    }
                  } catch (e) {
                    console.error(e);
                  }
                }}
              />
            </>
          )}
          <input
            key={inputKey}
            multiple
            ref={inputFile}
            style={{ display: 'none' }}
            accept={'.pdf'}
            id="PdfFileFixer"
            type="file"
            onChange={HandleFileChange}
          />
        </ToolBar.Left>
        <ToolBar.Right>
          <ToolBar.ModuleName>{t('uploader')}</ToolBar.ModuleName>
        </ToolBar.Right>
      </ToolBar.BlueBar>
      {activeFile ? (
        <>
          {mode === 'splitter' ? (
            <PdfWrapper>
              <PdfHeader>
                <CommonButton
                  scope="tertiary"
                  value={t('zoom-plus')}
                  action={() => setThumbnailZoom((prev) => prev + 0.25)}
                  icon="plus"
                  disabled={thumbnailZoom >= 0.75}
                />
                <CommonButton
                  scope="tertiary"
                  value={t('zoom-minus')}
                  action={() => setThumbnailZoom((prev) => prev - 0.25)}
                  icon="minus"
                  disabled={thumbnailZoom <= 0.25}
                />
                <CommonButton scope="tertiary" value={t('rotate-all')} action={() => rotateAll('rigth')} />
              </PdfHeader>
              <PdfCarousel
                docImage={activeFile}
                thumbnailZoom={thumbnailZoom}
                zoom={setPreview}
                fileName={pdf[0].name}
                rotatePdf={rotatePdf}
                pageOptions={pageOptions}
                activePdfPageLength={activePdfPageLength}
                setActivePdfPageLength={setActivePdfPageLength}
                toggleDelete={toggleDelete}
                split={split}
                setSplit={setSplitFn}
                setSplitString={setSplitStringFn}
              />
              <Bottom
                mode={mode}
                splitString={splitString}
                splitStringError={splitStringError}
                handleSplitStringChange={handleSplitStringChange}
                companiesWithDoc={companiesWithDocsAndMetadataByProgram}
                activeFile={activeFile}
                pageRotation={getRotation(pageNumber) || 0}
                pageNumber={pageNumber}
                setRemember={setRemember}
                remember={remember}
                setRememberdata={setRememberdata}
                rememberData={rememberData}
                splitDocument={splitDocument}
                percentage={percentage}
                filename={activePdf}
              />
            </PdfWrapper>
          ) : mode === 'all' && pdf.length ? (
            <MassiveWrapper>
              <div>
                {pdf.map((ele, i) => {
                  return (
                    <MassivePdf key={i}>
                      <NameMassivePdf>{ele.name}</NameMassivePdf>
                      <DeleteMassivePdf onClick={() => deleteMassiveFile(i)}>{t('delete')}</DeleteMassivePdf>
                    </MassivePdf>
                  );
                })}
              </div>
              <Bottom
                mode={mode}
                splitString={splitString}
                splitStringError={splitStringError}
                handleSplitStringChange={handleSplitStringChange}
                companiesWithDoc={companiesWithDocsAndMetadataByProgram}
                activeFile={activeFile}
                pageRotation={getRotation(pageNumber) || 0}
                pageNumber={pageNumber}
                setRemember={setRemember}
                remember={remember}
                setRememberdata={setRememberdata}
                rememberData={rememberData}
                splitDocument={splitDocument}
                percentage={percentage}
                filename={activePdf}
              />
            </MassiveWrapper>
          ) : null}
        </>
      ) : null}
      <Dialog fullWidth maxWidth="md" open={open} onClose={handleClose}>
        <DialogTitle>{t('split-are-you-sure')}</DialogTitle>
        <DialogContent>
          <DialogContentText>{t('split-missing-pages-text')}</DialogContentText>
        </DialogContent>
        <div style={{ display: 'flex', justifyContent: 'space-around', margin: '20px' }}>
          <CommonButton action={handleClose} value={t('undo-split')} />
          <CommonButton
            action={(e) => {
              splitDocument(e, true);
              handleClose();
            }}
            value={t('force-split')}
          />
        </div>
      </Dialog>
      <Modal onClose={() => setOpenQueueModal(false)} open={openQueueModal}>
        <>
          <Modal.Header title={t('Queue upload header')} subtitle={t('subtitle')} />
          <Modal.Content>
            <Table
              hasToolbar
              columns={queueUploadColumns}
              rows={queueProtocols}
              hasPagination
              hasResize
              hasSort
              hasFilter
              hasSelection
              onSelection={onSelection}
            />
          </Modal.Content>
          <Modal.Footer confirmAction={queueUpload} cancelAction={() => setOpenQueueModal(false)} />
        </>
      </Modal>
    </PdfUploaderWrapper>
  );
};

export default PdfUploader;
