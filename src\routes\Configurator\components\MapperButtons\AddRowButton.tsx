import React from 'react';
import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';
import { useDispatch, useSelector } from 'react-redux';
import selectors from 'state/selectors';
import actions from 'state/actions';
import { rowWithSingleSubRowColumn } from 'routes/Configurator/routes/Mapper/utils';
import { actionType } from 'utils/constants';

const AddRowButton = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const rows = useSelector(selectors.configurator.selectMapperRows);
  const templateEditRowId = useSelector(selectors.configurator.selectMapperEditRowId);
  const selectPdfCurrentPage = useSelector(selectors.configurator.selectCurrentPdfPage);
  const activeOTemplate = useSelector(selectors.configurator.selectActiveOTemplate);

  const setAddRow = () => {
    const newRow: rowWithSingleSubRowColumn = {
      id: '-1',
      content: '',
      subRows: [],
      fixedname: '',
      idTemplate: -1,
      idColumn: -1,
      inputType: '-',
      name: '',
      page: selectPdfCurrentPage,
      rowNumber: -1,
      type: '-',
      x1: 40,
      x2: 60,
      y1: 40,
      y2: 60,
    };
    dispatch(actions.configurator.setMapperRows([newRow, ...rows]));
    dispatch(actions.configurator.setMapperEditRowId('-1'));
    dispatch(actions.configurator.setMapperSelectedRowId('-1'));
    const { x1, x2, y1, y2 } = newRow;
    // divide by 100 because the area is in percentage
    dispatch(
      actions.configurator.setMapperAreas([
        { x1: x1 / 100, x2: x2 / 100, y1: y1 / 100, y2: y2 / 100, id: -1, pageNumber: selectPdfCurrentPage },
      ]),
    );
  };
  return (
    <CommonButton
      action={setAddRow}
      scope="tertiary"
      value={t('AddRow')}
      disabled={
        activeOTemplate === null ||
        activeOTemplate === undefined ||
        templateEditRowId === '-1' ||
        !utils.user.isActionByCompanyActive(actionType.MAPPER_ADD_ROW)
      }
    />
  );
};
export default AddRowButton;
