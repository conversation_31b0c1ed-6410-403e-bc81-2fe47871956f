import React from 'react';
import Dropdown from 'components/Input/Dropdown';
import { useDispatch, useSelector } from 'react-redux';
import selectors from 'state/selectors';
import utils from 'utils';
import actions from 'state/actions';
import { ActiveViewNotifications } from 'models/configurator';

const NotificationsDropdowns = () => {
  const dispatch = useDispatch();

  const templates = useSelector(selectors.configurator.selectTemplates);
  const activeTemplateId = useSelector(selectors.configurator.selectActiveTemplateId);
  const activeView = useSelector(selectors.configurator.getNotificationActiveView);
  const openWfForm = useSelector(selectors.configurator.getFormModeOpenWf);
  const reminderWfForm = useSelector(selectors.configurator.getFormModeWfReminder);
  const overdueForm = useSelector(selectors.configurator.getFormModeOverdue);
  const formModes = [openWfForm, reminderWfForm, overdueForm];
  const isActiveEditView = formModes.includes('edit');

  const subviews = templates.find((el) => el.idTemplate === activeTemplateId)?.subViews;

  return (
    <Dropdown
      options={utils.input.buildOptions(subviews || [], 'description', 'idTemplate')}
      value={utils.input
        .buildOptions(subviews || [], 'description', 'idTemplate')
        .find((el) => el.label === activeView)}
      margin="0px 15px"
      onChange={(option: { label: ActiveViewNotifications; value: string }) => {
        dispatch(actions.configurator.seTNotificationActiveView(option.label));
      }}
      disabled={isActiveEditView}
    />
  );
};
export default NotificationsDropdowns;
