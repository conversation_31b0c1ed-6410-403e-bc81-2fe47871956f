import { ScriptableContext } from 'chart.js';
import React from 'react';
import { Line } from 'react-chartjs-2';
import { useTheme } from 'providers/ThemeProvider';
import 'chartjs-adapter-moment';
import utils from 'utils';
import { AXIS_TYPE } from 'utils/constants';

type Value = { x: string; y: number | string };

export interface LineChartProps {
  colors?: string[];
  fillPointsOnSelected?: boolean;
  datasets: {
    axisFormatTypes: {
      x: string;
      y: string;
    };
    data: Value[];
    label: string;
    name?: string;
    fill?: boolean;
    backgroundColor?: string;
    average?: boolean;
  }[];
  percentage?: boolean;
  selectedDatasetName?: string;
  showAverage?: boolean;
}

const LineChart = ({
  datasets,
  colors = [],
  percentage,
  selectedDatasetName,
  fillPointsOnSelected,
  showAverage,
}: LineChartProps) => {
  const { theme, isDarkMode } = useTheme();

  const colorDefault = isDarkMode ? theme.colorPalette.grey.grey8 : theme.colorPalette.grey.grey10;
  const colorAverage = isDarkMode ? theme.colorPalette.red.medium : theme.colorPalette.red.light;

  const hasAverageDataset = datasets.find((dataset) => dataset.average);

  const setOpacity = (hex = colorDefault, alpha: number) =>
    `${hex}${Math.floor(alpha * 255)
      .toString(16)
      .padStart(2, '0')}`;

  const createGradient = (context: ScriptableContext<'line'>, color?: string) => {
    const ctx = context.chart.ctx;

    const standardizeColor = (str: string) => {
      ctx.fillStyle = str;
      return ctx.fillStyle;
    };

    const gradient = ctx.createLinearGradient(0, 0, 0, 500);
    gradient.addColorStop(0, setOpacity(standardizeColor(color || colorDefault), 0.5));
    gradient.addColorStop(1, setOpacity(standardizeColor(color || colorDefault), 0.1));
    return gradient;
  };

  const isDateFormat = datasets.every((dataset) => dataset.axisFormatTypes.x === AXIS_TYPE.date);
  const isDateMonth = datasets.every((dataset) => dataset.axisFormatTypes.x === AXIS_TYPE.dateMonth);

  const datasetsMod = datasets
    .sort((x) => (x.average ? -1 : 1))
    .map((dataset, index) => {
      const isSelected = selectedDatasetName === dataset.name;
      const isAverage = dataset.average;

      const getColor = () => {
        if (isAverage) {
          return colorAverage;
        }
        return hasAverageDataset ? colors[index - 1] : colors[index];
      };
      const color = getColor();

      const convertDatasetDates = dataset.data.map(({ x, y }) => {
        return { x: utils.date.convertDdMmYyyyToMmDdYyyy(x), y };
      });

      return {
        ...dataset,
        data: isDateFormat || isDateMonth ? convertDatasetDates : dataset.data,
        fill: isSelected,
        color,
        borderColor: isSelected || !selectedDatasetName ? color : setOpacity(color, 0.1),
        backgroundColor: (context: ScriptableContext<'line'>) =>
          isSelected ? createGradient(context, color) : theme.colorPalette.white,
        pointBackgroundColor:
          isSelected && fillPointsOnSelected
            ? color
            : setOpacity(theme.colorPalette.white, selectedDatasetName && !isSelected ? 0.1 : 1),
        pointHoverBackgroundColor: color,
        pointRadius: isAverage ? 0 : 8,
        pointHoverRadius: isAverage ? 0 : 8,
      };
    });

  const datasetWithoutAverage = datasetsMod.filter(({ average }) => !average);

  const data = {
    datasets: showAverage && hasAverageDataset ? datasetsMod : datasetWithoutAverage,
  };

  const options = {
    type: 'line',
    responsive: true,
    maintainAspectRatio: false,
    borderSkipped: false,
    pointStyle: 'circle',
    pointBorderWidth: 2,
    pointHoverBorderWidth: 2,
    pointBorderHoverWidth: 2,
    clip: false,
    scales: {
      x: {
        ...(isDateFormat || isDateMonth ? { type: 'time' } : {}),
        time: {
          minUnit: isDateMonth ? 'month' : 'day',
          displayFormats: {
            day: isDateMonth ? 'MM/YYYY' : 'DD/MM/YYYY',
          },
        },
        grid: {
          display: false,
        },
        offset: true,
        ticks: {
          padding: 20,
          color: isDarkMode ? theme.colorPalette.grey.grey7 : theme.colorPalette.grey.grey8,
          font: {
            size: 11,
            family: 'Roboto',
            weight: 'lighter',
          },
        },
      },
      y: {
        max: percentage ? 100 : null,
        min: percentage ? 0 : null,
        grid: {
          color: isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.grey.grey2,
        },
        ticks: {
          callback: (value: number | string) => (percentage ? value + ' %' : value),
          color: isDarkMode ? theme.colorPalette.grey.grey7 : theme.colorPalette.grey.grey8,
          font: {
            size: 9,
            family: 'Roboto',
            weight: 'lighter',
          },
        },
      },
    },
    plugins: {
      legend: {
        align: 'end',
        labels: {
          boxWidth: 20,
          boxHeight: 10,
          padding: 15,
          color: isDarkMode ? theme.colorPalette.grey.grey9 : theme.colorPalette.grey.grey8,
          font: {
            size: 13,
            family: 'Roboto',
            weight: 'lighter',
          },
        },
      },
      tooltip: {
        callbacks: {
          title: () => '',
          label: (tooltipItem: { raw: { y: number } }) => {
            const value = tooltipItem.raw.y;
            return percentage ? value + ' %' : value;
          },
        },
        bodyFont: {
          size: 12,
          weight: 400,
        },
        caretPadding: 10,
        borderColor: isDarkMode ? theme.colorPalette.grey.grey4 : theme.colorPalette.grey.grey3,
        borderWidth: 1,
        backgroundColor: isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.white,
        titleColor: isDarkMode ? theme.colorPalette.grey.grey9 : theme.colorPalette.turquoise.dark,
        bodyColor: isDarkMode ? theme.colorPalette.grey.grey9 : theme.colorPalette.turquoise.dark,
        displayColors: false,
      },
    },
  };

  const verticalLine = {
    beforeDraw(chart: { tooltip: { _active: any[] }; ctx: any; scales: { y: { bottom: number } } }) {
      if (chart.tooltip._active && chart.tooltip._active.length) {
        const activePoint = chart.tooltip._active[0];
        const ctx = chart.ctx;
        const x = activePoint.element.x;
        const y = activePoint.element.y;
        const bottomY = chart.scales.y.bottom;

        ctx.save();
        ctx.beginPath();
        ctx.moveTo(x, bottomY);
        ctx.setLineDash([10, 10]);
        ctx.lineTo(x, y);
        ctx.lineWidth = 1;
        ctx.strokeStyle = isDarkMode ? theme.colorPalette.grey.grey4 : theme.colorPalette.grey.grey5;
        ctx.stroke();
        ctx.restore();
      }
    },
  };

  const legendPadding = {
    id: 'legendPadding',
    beforeInit(chart: { legend: { fit: () => void; height: number } }) {
      const originalFit = chart.legend.fit;
      chart.legend.fit = function fit() {
        originalFit.bind(chart.legend)();
        this.height += 20;
      };
    },
  };

  const styleLegendBoxes = {
    id: 'styleLegendBoxes',
    beforeDraw(chart: { legend: { legendItems: [] }; data: { datasets: { color: string }[] } }) {
      const legend = chart.legend;
      const legends = legend.legendItems;
      const datasets = chart.data.datasets;

      legends.forEach((legend: { fillStyle: string; borderRadius: number; lineWidth: number }, index: number) => {
        legend.fillStyle = datasets[index].color;
        legend.borderRadius = 1;
        legend.lineWidth = 0;
      });
    },
  };

  return <Line data={data} options={options as any} plugins={[verticalLine, legendPadding, styleLegendBoxes] as any} />;
};

export default LineChart;
