import React from 'react';
import { useSelector } from 'react-redux';
import { actionType } from 'utils/constants';

import utils from 'utils';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';
import { useHistory, useRouteMatch } from 'react-router';

const ViewDetailVendor = () => {
  const t = utils.intl.useTranslator();
  const history = useHistory();
  const { path } = useRouteMatch();

  const selectedCompany = useSelector(selectors.configurator.getSelectedCompanyVendorConfig);
  const editedVendorId = useSelector(selectors.configurator.getIdEditedVendorFromList);
  const selectedVendorId = useSelector(selectors.configurator.getSelectedVendorIdFromList);

  const companyName = selectedCompany?.label;
  const hasPermission = utils.user.isActionByCompanyActive(actionType.VIEW_DETAIL_VENDOR, companyName);

  return (
    <CommonButton
      action={() => {
        history.push(`${path}/VMD/vendor-detail`);
      }}
      scope="tertiary"
      value={t(actionType.VIEW_DETAIL_VENDOR)}
      icon="circle"
      disabled={!hasPermission || !selectedCompany || !!editedVendorId || !selectedVendorId}
    />
  );
};

export default ViewDetailVendor;
