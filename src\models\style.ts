// colorPalette
export interface Grey {
  light: string;
  mediumLight: string;
  medium: string;
  mediumDark: string;
  dark: string;
  black: string;
  grey1: string;
  grey2: string;
  grey3: string;
  grey4: string;
  grey5: string;
  grey6: string;
  grey7: string;
  grey8: string;
  /**
   * #4D4D4D
   */
  grey9: string;
  grey10: string;
  grey11: string;
  grey12: string;
  grey13: string;
  grey14: string;
  grey15: string;
}

export interface Red {
  error: string;
  light: string;
  medium: string;
  dark: string;
}

export interface Orange {
  medium: string;
}

export interface Yellow {
  medium: string;
}

export interface Green {
  medium: string;
}

export interface Cyan {
  light: string;
  medium: string;
  dark: string;
}

export interface LightBlue {
  light: string;
  medium: string;
  dark: string;
}

export interface Blue {
  button: string;
  buttonHover: string;
  light: string;
  medium: string;
}

export interface Turquoise {
  dark: string;
  light: string;
  normal: string;
  background: string;
}

export interface ColorPalette {
  isDarkMode: boolean;
  black: string;
  white: string;
  grey: Grey;
  red: Red;
  orange: Orange;
  yellow: Yellow;
  green: Green;
  cyan: Cyan;
  lightBlue: LightBlue;
  blue: Blue;
  turquoise: Turquoise;
}

export interface StatusColors {
  error: '#E61B34';
  warning: '#FF9400';
  success: '#7ED321';
}

// fontSizePalette
export interface Heading {
  H1: string;
  H2: string;
  H3: string;
  H4: string;
}

export interface Body {
  XL: string;
  L: string;
  M: string;
  S: string;
  XS: string;
  XXS: string;
  mini: string;
}

export interface FontSizePalette {
  xxSmall: string;
  xSmall: string;
  small: string;
  medium: string;
  large: string;
  xLarge: string;
  xxLarge: string;
  xxxLarge: string;
  heading: Heading;
  body: Body;
}

export interface FontWeightPalette {
  light: number;
  regular: number;
  medium: number;
  bold: number;
}
export interface ZIndexPalette {
  lowest: number;
  low: number;
  medium: number;
  high: number;
  highest: number;
}

export interface BoxShadowPalette {
  medium: string;
}
export interface BreakpointsPalette {
  s: string;
  m: string;
  l: string;
}
