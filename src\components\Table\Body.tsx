import React from 'react';
import { TableBodyProps } from 'react-table';
import { TBody } from './styles';

interface BodyProps {
  getTableBodyProps: () => TableBodyProps;
  light: boolean;
  headerHeight: string;
  hideHeader: boolean;
}

const Body: React.FC<BodyProps> = (props) => {
  const { getTableBodyProps, light, headerHeight, children, hideHeader } = props;

  return (
    <TBody headerHeight={headerHeight} light={light} hideHeader={hideHeader} {...getTableBodyProps()}>
      {children}
    </TBody>
  );
};

export default Body;
