import ErrorBoundary from 'components/ErrorBoundary';
import React from 'react';
import ReactDOM from 'react-dom';
import { Provider } from 'react-redux';
import { BrowserRouter as Router } from 'react-router-dom';
import services from 'services';
import store from 'state/store';
import { ThemeProvider } from './providers/ThemeProvider';
import { Reset } from 'styled-reset';

import { library } from '@fortawesome/fontawesome-svg-core';
import * as Icons from '@fortawesome/free-solid-svg-icons';

import App from './App';
import { StylesGlobal } from './styles-global';

services.configureApiClient();

// Disable missing translation message as translations will be added later.
// We can add a toggle for this later when we have most translations.
const consoleError = console.error.bind(console);
// @ts-ignore
console.error = (error, ...args) => {
  if (error?.code === 'MISSING_TRANSLATION') return;
  consoleError(error, ...args);
};

// importing fontawesome icons
const iconList = Object.keys(Icons)
  .filter((key) => key !== 'fas' && key !== 'prefix')
  // @ts-ignore
  .map((icon) => Icons[icon]);

library.add(...iconList);

const AppWithProviders = () => {
  return (
    <>
      <Reset />
      <ThemeProvider>
        <StylesGlobal />
        <Provider store={store}>
          <Router>
            <ErrorBoundary>
              <App />
            </ErrorBoundary>
          </Router>
        </Provider>
      </ThemeProvider>
    </>
  );
};

ReactDOM.render(<AppWithProviders />, document.querySelector('#root'));
