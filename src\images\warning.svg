<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" xml:space="preserve" viewBox="0.5 0.5 44 38">
<desc>Created with Fabric.js 3.6.6</desc>
<defs>
</defs>
<g transform="matrix(1 0 0 1 22.5 19.5)">
<path style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-dashoffset: 0; stroke-linejoin: miter; stroke-miterlimit: 4; fill: rgb(255,1,7); fill-rule: nonzero; opacity: 1;" transform=" translate(-24, -23)" d="M 24.05 24.45 Z M 2 42 L 24 4 l 22 38 Z m 20.7 -11.4 h 3 V 19.4 h -3 Z m 1.5 5.55 q 0.65 0 1.075 -0.425 q 0.425 -0.425 0.425 -1.075 q 0 -0.65 -0.425 -1.075 q -0.425 -0.425 -1.075 -0.425 q -0.65 0 -1.075 0.425 Q 22.7 34 22.7 34.65 q 0 0.65 0.425 1.075 q 0.425 0.425 1.075 0.425 Z M 7.2 39 h 33.6 L 24 10 Z" stroke-linecap="round"/>
</g>
</svg>