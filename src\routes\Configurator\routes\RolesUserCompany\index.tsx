/* eslint-disable max-lines */
import React, { useState, useEffect } from 'react';
import { useAsync } from 'react-use';
import { useDispatch, useSelector } from 'react-redux';

import { Company } from 'models/response';

import utils from 'utils';
import actions from 'state/actions';
import selectors from 'state/selectors';
import services from 'services';

import { Wrapper, Title, Container, Left, Right } from '../../styles';

import Table from 'components/Table';
import { TableColumn } from 'components/Table';

import { notDraggable } from 'utils/constants';
import Modal from 'components/Modal';
import ModalsContent from '../../components/ModalsContent/ModalSwitcher';

import UserRolesTable from './UserRolesTable';

interface PrivilegesValues {
  role: string;
  module: string;
  privilege: string;
}

const Roles = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const idProgram = useSelector(selectors.app.getProgramCode) || -1;
  const idUser = useSelector(selectors.user.getIdUser) || -1;
  const selectedCompanyId = useSelector(selectors.configurator.getSelectedCompanyForRoles);
  const companies = useSelector(selectors.configurator.getCompaniesForRoles);
  const selectedUser = useSelector(selectors.configurator.getSelectedUserForRoles);

  const editRowIdUserRoles = useSelector(selectors.configurator.getEditRowIdUserRoles);

  // Modal
  const closeModal = () => dispatch(actions.modal.closeModal());
  const isModalVisible = useSelector(selectors.modal.getVisibility);
  const currentModal = useSelector(selectors.modal.getActionType);
  const isModalDraggable = notDraggable.includes(currentModal || '');
  const usersForCompanyRoles = useSelector(selectors.configurator.getUsersForCompanyRoles);

  const [privilegesValues, setPrivilegesValues] = useState<PrivilegesValues[]>([]);

  const companyColumns: TableColumn[] = [
    { accessor: 'socialReason', Header: t('socialReason'), filterType: 'free' },
    { accessor: 'name', Header: t('companyCode'), filterType: 'free' },
    { accessor: 'fiscalCode', Header: t('fiscalCode'), filterType: 'free' },
    { accessor: 'vatNumber', Header: t('vatNumber'), filterType: 'free' },
  ];

  // Set all companies
  useAsync(async () => {
    dispatch(actions.configurator.setSelectedCompanyForRoles(null));
    try {
      const { data } = await services.getDocuments({ idUser, idProgram, idTemplate: 15, allAuthorized: false });
      dispatch(actions.configurator.setCompaniesForRoles(utils.input.removeDefaultValue(data, 'idCompany')));
    } catch (e) {
      console.error(e);
    }
  }, []);

  // CompanySelection
  const onCompanySelection = (rows: Company[]) => {
    dispatch(actions.configurator.setSelectedCompanyForRoles(rows[0]?.idCompany));
  };

  // Set all Users For Company Role
  useAsync(async () => {
    if (selectedCompanyId !== null) {
      try {
        const { data } = await services.getUserCompanyRoles(selectedCompanyId);
        dispatch(actions.configurator.setUsersForCompanyRoles(data));
      } catch (e) {
        console.error(e);
      }
    } else {
      dispatch(actions.configurator.setUsersForCompanyRoles(null));
    }
  }, [selectedCompanyId]);

  // Privileges
  useEffect(() => {
    if (selectedUser.length) {
      if (selectedUser.length === 1) {
        const privileges = selectedUser[0].programResponseList.map((e, i) => {
          return {
            role: i === 0 ? selectedUser[0].role : '-',
            module: e.programName,
            privilege: e.description,
          };
        });
        setPrivilegesValues(privileges);
      } else {
        const privilegesRole: PrivilegesValues[] = [];
        for (let iOne = 1; iOne < selectedUser.length; iOne++) {
          for (let iTwo = 0; iTwo < selectedUser[iOne].programResponseList.length; iTwo++) {
            const row = {
              role: iTwo === 0 ? selectedUser[iOne].role : '-',
              module: selectedUser[iOne].programResponseList[iTwo].programName,
              privilege: selectedUser[iOne].programResponseList[iTwo].description,
            };
            privilegesRole.push(row);
          }
        }
        setPrivilegesValues(privilegesRole);
      }
    } else {
      setPrivilegesValues([]);
    }
  }, [selectedUser, selectedCompanyId]);

  const rolesColumns = [
    { accessor: 'role', Header: t('role') },
    { accessor: 'module', Header: t('module') },
    { accessor: 'privilege', Header: t('privilege') },
  ];

  return (
    <>
      <Modal onClose={closeModal} open={isModalVisible} isDraggable={!isModalDraggable}>
        <ModalsContent />
      </Modal>
      {selectedCompanyId === null ? (
        <Wrapper>
          <Title>
            <h2>{t('company_users_role')}</h2>
          </Title>
          <Container>
            {companies.length > 0 ? (
              <Table
                rowId="idCompany"
                onSelection={(rows: Company[]) => onCompanySelection(rows)}
                hasToolbar
                columns={companyColumns}
                rows={companies}
                hasSelection
                hasPagination
                hasResize
                hasSort
                hasFilter
              />
            ) : null}
          </Container>
        </Wrapper>
      ) : (
        <Wrapper>
          <Title>
            <h2>{t('roles')}</h2>
          </Title>
          {usersForCompanyRoles ? (
            <Container>
              <Left>
                <UserRolesTable usersForCompanyRoles={usersForCompanyRoles} editRowIdUserRoles={editRowIdUserRoles} />
              </Left>
              {privilegesValues.length ? (
                <Right noBorder>
                  <Table
                    hasToolbar
                    columns={rolesColumns}
                    rows={privilegesValues}
                    hasPagination
                    hasResize
                    hasSort
                    hasFilter
                  />
                </Right>
              ) : null}
            </Container>
          ) : null}
        </Wrapper>
      )}
    </>
  );
};

export default Roles;
