import React, { useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { modalActionType } from 'utils/constants';
import { actionType } from 'utils/constants';

import utils from 'utils';
import services from 'services';
import selectors from 'state/selectors';
import actions from 'state/actions';

import CommonButton from 'components/Buttons/CommonButton';
import { GetDocument } from 'models/request';


const ImportUsers = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const idUser = useSelector(selectors.user.getIdUser) || -1;
  const inputFile = useRef<HTMLInputElement>(null);
  const importUsersKey = useSelector(selectors.configurator.getImportUserKey);

  const setAllUsersFunction = async () => {
    try {
      const body: GetDocument = {
        idProgram: 111,
        idUser: idUser,
        idTemplate: 13,
        allAuthorized: false,
      };

      const { data } = await services.getDocuments(body);
      dispatch(actions.configurator.setUsers(data));
    } catch (e) {
      console.error(e);
    }
  };

  const importUsersClick = () => inputFile?.current?.click();

  const importUsers = async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files?.length) {
      const { files } = e.target;
      const base64Csv = await utils.file.toBase64(files[0]);
      if (base64Csv && typeof base64Csv === 'string') {
        try {
          dispatch(actions.configurator.setImportUserKey(importUsersKey + 1));
          const { data } = await services.importUsers({ base64Csv });
          setAllUsersFunction();
          utils.app.notify('success', t('users-imported'));
          if (data.length) {
            dispatch(actions.modal.openModal(modalActionType.configurator.IMPORTED_DUPLICATE_USERS, { users: data }));
          }
        } catch (e) {
          console.error(e);
        }
      }
    }
  };

  return (
    <>
      <CommonButton
        action={importUsersClick}
        disabled={!utils.user.isActionByCompanyActive(actionType.IMPORT_USER)}
        scope="tertiary"
        value={t('import-users')}
        icon="circle"
      />
      <input
        key={importUsersKey}
        ref={inputFile}
        style={{ display: 'none' }}
        accept={'.csv'}
        id="importUsers"
        type="file"
        onChange={importUsers}
      />
    </>
  );
};

export default ImportUsers;
