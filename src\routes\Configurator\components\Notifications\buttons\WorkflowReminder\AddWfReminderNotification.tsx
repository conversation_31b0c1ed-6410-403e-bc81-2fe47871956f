import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import utils from 'utils';
import actions from 'state/actions';
import { actionType } from 'utils/constants';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';

const AddWfReminderNotification = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const selectedRow = useSelector(selectors.configurator.getSelectedWfReminderSettings);
  const formMode = useSelector(selectors.configurator.getFormModeWfReminder);

  return (
    <CommonButton
      action={() => dispatch(actions.configurator.setFormModeWfReminder('add'))}
      scope="tertiary"
      value={t(actionType.ADD_WF_REMINDER_NOTIFICATIONS)}
      icon="circle"
      disabled={
        !utils.user.isActionByCompanyActive(actionType.ADD_WF_REMINDER_NOTIFICATIONS) || !!selectedRow || !!formMode
      }
    />
  );
};

export default AddWfReminderNotification;
