import React from 'react';
import { colorPalette } from 'utils/styleConstants';
import SuggestionBox from '../SuggestionBox';
import { hex2Rgb } from 'utils/helpers/test.helpers';
import { renderWithStyle } from 'utils/helpers/test.helpers';

const suggestions = [
  {
    boundingBox: {
      x1: 1,
      x2: 1,
      y1: 1,
      y2: 1,
    },
    confidence: 1,
    content: 'test-value',
    number: 1,
    pageNumber: 1,
    status: 1,
  },
];

describe('SuggestionBox', () => {
  test('SuggestionBox wrapper styles check', () => {
    renderWithStyle(<SuggestionBox id="test" suggestions={suggestions} />);

    const box = document.querySelector('#test');
    const styles = getComputedStyle(box);

    expect(styles.backgroundColor).toBe(hex2Rgb(colorPalette.turquoise.background));
    expect(styles.borderRadius).toBe('4px');
  });

  test('SuggestionBox styles check', () => {
    renderWithStyle(<SuggestionBox id="test" suggestions={suggestions} />);

    const box = document.querySelector('#test > ul');
    const styles = getComputedStyle(box);

    expect(styles.backgroundColor).toBe(hex2Rgb(colorPalette.white));
    expect(styles.borderRadius).toBe('4px');
  });

  test('SuggestionBox value text check', () => {
    renderWithStyle(<SuggestionBox id="test" suggestions={suggestions} />);

    const box = document.querySelector('#test > ul > li:first-child > div:first-child label');
    expect(box.textContent).toBe('test-value');
  });

  test('SuggestionBox page number check', () => {
    renderWithStyle(<SuggestionBox id="test" suggestions={suggestions} />);

    const box = document.querySelector('#test > ul > li:first-child > div:nth-child(2) p');

    expect(box.textContent).toBe('2');
  });
});
