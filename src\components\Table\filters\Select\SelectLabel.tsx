import React from 'react';
import styled from 'styled-components/macro';

interface Props {
  name: string | number | JSX.Element;
  value: number;
}
const StyledSelect = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
`;

const StyledSelectCount = styled.div`
  padding: 0 10px;
`;
export const SelectLabel = ({ name, value }: Props) => {
  return (
    <StyledSelect>
      {name}
      <StyledSelectCount>({value})</StyledSelectCount>
    </StyledSelect>
  );
};
