/* eslint-disable max-lines */
import React, { useEffect, useRef, useState } from 'react';
import styled from 'styled-components/macro';
import axios from 'axios';
import { useDispatch, useSelector } from 'react-redux';
import * as yup from 'yup';
import { mutate } from 'swr';

import { CompanyGroups, NewCompanyGroups, OUserList } from 'models/response';

import services from 'services';
import actions from 'state/actions';
import selectors from 'state/selectors';
import utils from 'utils';

import Table, { OriginalRow, TableColumn } from 'components/Table';
import Checkbox from 'components/Buttons/CheckboxButton';
import { useAsync } from 'react-use';

const CustomTitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.M};
  font-weight: bold;
`;
const Wrapper = styled.div`
  margin-top: 20px;
  display: flex;
  gap: 20px;
  overflow: auto;
`;

const Groups = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const [userListGroupSelected, setUserListGroupSelected] = useState<OUserList[]>([]);

  const editedRowId = useSelector(selectors.configurator.getEditedRowIdWfGroup);
  const selectedCompanyDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  const getListOfUserForGroup = useSelector(selectors.configurator.getListOfUserForGroup);
  const selectedGroupFromCompanySelected = useSelector(selectors.configurator.getSelectedGroupFromCompanySelected);
  const isWfDefinitionInEdit = useSelector(selectors.configurator.getIsWfTableEdit);

  // I use Ref so I can access to the latest state
  const latestSelectedGroup = useRef<CompanyGroups | null>(null);
  const latestUsersList = useRef<OUserList[]>([]);

  const { groups, revalidate } = services.useGetCompanyGroups(
    'getCompanyGroupsKey',
    selectedCompanyDefinition?.idCompany,
  );
  const { listOfGroupsForAssociations } = services.useGetGroupsForTaskAssociations(
    'getCompanyGroupsForAssociationsKey',
    selectedCompanyDefinition?.idCompany,
  );
  const { revalidateAssociations } = services.useGetDefinitionTaskGroupsAssociations(
    'getDefinitionTaskGroupsAssociationsKey',
    selectedCompanyDefinition?.idCompany,
  );

  useEffect(() => {
    revalidate();
  }, [selectedCompanyDefinition, revalidate]);

  useEffect(() => {
    dispatch(actions.configurator.setSelectedGroupFromCompanySelected(null));
  }, [dispatch]);

  // Update the Refs every time the Redux state changes
  useEffect(() => {
    latestSelectedGroup.current = selectedGroupFromCompanySelected;
  }, [selectedGroupFromCompanySelected]);

  useEffect(() => {
    latestUsersList.current = getListOfUserForGroup;
  }, [getListOfUserForGroup]);

  useAsync(async () => {
    if (!selectedCompanyDefinition?.idCompany || !selectedGroupFromCompanySelected?.idGroup) return;

    const { data } = await services.getCompanyGroupUsers(
      selectedCompanyDefinition.idCompany,
      selectedGroupFromCompanySelected.idGroup,
    );

    setUserListGroupSelected(data.oUserList);
  }, [selectedGroupFromCompanySelected, selectedCompanyDefinition]);

  const handleChangeAssignedUser = (idUser: number) => {
    if (isWfDefinitionInEdit && selectedGroupFromCompanySelected) {
      const usersCopy = [...userListGroupSelected];
      const users = usersCopy.map((obj) => {
        if (obj.idUser === idUser) {
          return {
            ...obj,
            assignedUser: !obj.assignedUser,
          };
        }
        return obj;
      });

      setUserListGroupSelected([...users]);
    } else {
      const usersCopy = [...getListOfUserForGroup];
      const users = usersCopy.map((obj) => {
        if (obj.idUser === idUser) {
          return {
            idUser: obj.idUser,
            name: obj.name,
            username: obj.username,
            email: obj.email,
            assignedUser: !obj.assignedUser,
          };
        }
        return obj;
      });
      dispatch(actions.configurator.setListOfUserForGroup(users));
    }
  };

  const columsGroup: TableColumn[] = [
    {
      accessor: 'idGroup',
      Header: t('idGroup'),
    },
    {
      accessor: 'groupName',
      Header: t('groupName'),
      inputType: 'text',
    },
    {
      accessor: 'groupDesc',
      Header: t('groupDesc'),
      inputType: 'text',
    },
  ];

  const columsGroupUser: TableColumn[] = [
    {
      accessor: 'assignedUser',
      Header: t('assignedUser'),
      id: '1',
      sortType: 'boolean',
      Cell: ({ row, value }: any) => {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox
              isActive={value}
              isEditable={isWfDefinitionInEdit}
              action={() => handleChangeAssignedUser(row.original.idUser)}
            ></Checkbox>
          </div>
        );
      },
      inputType: 'checkbox',
    },
    {
      accessor: 'username',
      Header: t('username'),
    },
    {
      accessor: 'name',
      Header: t('name'),
    },
    {
      accessor: 'email',
      Header: t('email'),
    },
    {
      accessor: 'startDeltaReminder',
      Header: t('startDeltaReminder'),
    },
    {
      accessor: 'endDeltaReminder',
      Header: t('endDeltaReminder'),
    },
    {
      accessor: 'reminderDays',
      Header: t('reminderDays'),
    },
    {
      accessor: 'maxReminder',
      Header: t('maxReminder'),
    },
  ];

  const clearState = () => {
    dispatch(actions.configurator.setEditedRowIdWfGroup(null));
    dispatch(actions.configurator.setListOfUserForGroup([]));
    dispatch(actions.configurator.setIsWfTableEdit(false));
    dispatch(actions.configurator.setSelectedGroupFromCompanySelected(null));
  };

  const onCancel = () => {
    const newRows = groups?.filter((el) => el.idGroup !== -1);
    mutate('getCompanyGroupsKey', newRows, {
      revalidate: false,
    });

    clearState();
  };

  const confirmCreate = async (newRow: OriginalRow) => {
    try {
      if (latestUsersList.current.find((el) => el.assignedUser) && selectedCompanyDefinition?.idCompany) {
        const group: NewCompanyGroups = {
          idCompany: selectedCompanyDefinition.idCompany,
          groupName: newRow.groupName,
          groupDesc: newRow.groupDesc,
          oUserList: latestUsersList.current.filter((el) => el.assignedUser),
        };
        const { data } = await services.createWfGroup(group);

        const record: CompanyGroups = {
          idGroup: data.idGroup,
          groupName: data.groupName,
          groupDesc: data.groupDesc,
        };

        const newList = [record, ...(groups?.filter((el) => el.idGroup !== -1) || [])];

        mutate('getCompanyGroupsKey', newList, {
          revalidate: false,
        });
        mutate(
          'getCompanyGroupsForAssociationsKey',
          [
            {
              idGroup: data.idGroup,
              groupName: data.groupName,
              relation: 1,
              amountLimit: 0,
              assigned: false,
              currency: data.currency,
              oUserList: latestUsersList.current.filter((el) => el.assignedUser),
            },
            ...(listOfGroupsForAssociations || []),
          ],
          {
            revalidate: false,
          },
        );

        // update api associations, because when create a group, you can add it to an association
        revalidateAssociations();
        clearState();
        utils.app.notify('success', t('wf-group-created'));
      } else {
        utils.app.notify('warning', t('select_user_for_group'));
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        utils.app.notify('fail', `${t('error')} ${error.response}`);
        return;
      }
      console.error(error);
    }
  };

  const confirmEdit = async (row: OriginalRow) => {
    try {
      if (userListGroupSelected.find((el) => el.assignedUser)) {
        if (latestSelectedGroup.current && selectedCompanyDefinition?.idCompany) {
          const groupEdited = {
            idCompany: selectedCompanyDefinition.idCompany,
            idGroup: row.idGroup,
            groupName: row.groupName,
            groupDesc: row.groupDesc,
          };

          const groupEditedForApi = {
            oUserList: userListGroupSelected.filter((el) => el.assignedUser) || [],
            ...groupEdited,
          };

          await services.editWfGroup(groupEditedForApi);

          const find = groups?.findIndex((el) => el.idGroup === latestSelectedGroup.current?.idGroup);
          const list = [...(groups || [])];
          if (find !== undefined)
            list[find] = {
              ...groupEdited,
            };

          const newListOfGroupsForAssociations = listOfGroupsForAssociations?.map((group) => {
            if (group.idGroup === selectedGroupFromCompanySelected?.idGroup) {
              return {
                ...group,
                groupName: row.groupName,
                groupDesc: row.groupDesc,
              };
            }
            return group;
          });

          mutate('getCompanyGroupsKey', list, {
            revalidate: false,
          });
          mutate('getCompanyGroupsForAssociationsKey', newListOfGroupsForAssociations, {
            revalidate: false,
          });
          // update api get associations
          revalidateAssociations();
          clearState();

          utils.app.notify('success', t('wf-group-edited'));
        }
      } else {
        utils.app.notify('warning', t('select_user_for_group'));
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        utils.app.notify('fail', `${t('error')} ${error.response}`);
        return;
      }
      console.error(error);
    }
  };

  const saveSchema = yup.object().shape({
    groupName: yup.string().required(),
    groupDesc: yup.string().required(),
  });

  const groupTable = React.useMemo(() => {
    const initialSelection = (() => {
      if (editedRowId === -1) return [-1];
      if (selectedGroupFromCompanySelected) return [selectedGroupFromCompanySelected.idGroup];
      return undefined;
    })();

    return (
      <div>
        <CustomTitle>{t('groups')}</CustomTitle>
        <Table
          rowId="idGroup"
          columns={columsGroup}
          rows={groups || []}
          hasToolbar
          hasSelection
          hasPagination
          hasResize
          hasSort
          hasFilter
          initialSelection={initialSelection}
          onSelection={(row: CompanyGroups[]) => {
            if (editedRowId === -1) return;
            dispatch(actions.configurator.setSelectedGroupFromCompanySelected(row.length ? row[0] : null));
          }}
          isEditable
          editRowID={editedRowId?.toString()}
          editRow={groups?.find((el) => el.idGroup === editedRowId)}
          onCancel={onCancel}
          onSave={(row) => (editedRowId === -1 ? confirmCreate(row) : confirmEdit(row))}
          onSaveValidation={saveSchema}
        />
      </div>
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [groups, editedRowId, userListGroupSelected, selectedGroupFromCompanySelected]);

  const UserListTable =
    selectedGroupFromCompanySelected || getListOfUserForGroup.length ? (
      <div>
        <CustomTitle>{t('users')}</CustomTitle>
        <Table
          key={selectedGroupFromCompanySelected ? 'group-selected' : 'user-list'}
          rowId="idUser"
          columns={columsGroupUser}
          rows={selectedGroupFromCompanySelected ? userListGroupSelected : getListOfUserForGroup}
          hasToolbar
          hasSelection={editedRowId === null}
          hasPagination
          hasResize
          hasSort
          hasFilter
          onSelection={(row: OUserList[]) => {
            dispatch(actions.configurator.setSelectedOUserList(row.length === 1 ? row[0] : null));
          }}
        />
      </div>
    ) : null;
  return (
    <Wrapper>
      {groups?.length !== 0 ? (
        <>
          {groupTable}
          {UserListTable}
        </>
      ) : (
        <>{t('no-data-groups')}</>
      )}
    </Wrapper>
  );
};

export default Groups;
