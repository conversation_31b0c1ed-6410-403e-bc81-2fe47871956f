import { FontAwesomeIcon, FontAwesomeIconProps } from '@fortawesome/react-fontawesome';
import { ColorPalette } from 'models/style';
import React, { ComponentProps, useState } from 'react';
import styled, { css } from 'styled-components/macro';
import { defaultDelay } from 'utils/configs';
import { fontSizePalette, fontWeightPalette } from 'utils/styleConstants';

const primary = (selected: boolean, colorPalette: ColorPalette) => css`
  background: ${selected
    ? colorPalette.isDarkMode
      ? colorPalette.turquoise.dark
      : colorPalette.turquoise.normal
    : colorPalette.isDarkMode
    ? colorPalette.turquoise.dark
    : colorPalette.turquoise.light};
  color: ${selected
    ? colorPalette.isDarkMode
      ? colorPalette.black
      : colorPalette.white
    : colorPalette.isDarkMode
    ? colorPalette.black
    : colorPalette.turquoise.dark};
  border: solid 1px transparent;
  &:disabled {
    cursor: default;
    background: ${colorPalette.isDarkMode ? colorPalette.grey.grey8 : colorPalette.grey.grey1};
    color: ${colorPalette.isDarkMode ? colorPalette.grey.grey6 : colorPalette.grey.grey5};
  }
`;

const secondary = (selected: boolean, colorPalette: ColorPalette) => css`
  color: ${colorPalette.isDarkMode ? colorPalette.turquoise.light : colorPalette.turquoise.dark};
  border: solid 1px
    ${selected
      ? colorPalette.isDarkMode
        ? colorPalette.white
        : colorPalette.turquoise.dark
      : colorPalette.isDarkMode
      ? colorPalette.turquoise.light
      : colorPalette.turquoise.normal};
  &:disabled {
    cursor: default;
    color: ${colorPalette.isDarkMode ? colorPalette.grey.grey6 : colorPalette.grey.grey5};
    border: solid 1px ${colorPalette.isDarkMode ? colorPalette.grey.grey8 : colorPalette.grey.grey5};
  }
`;

const tertiary = (selected: boolean, colorPalette: ColorPalette) => css`
  background: transparent;
  color: ${selected
    ? colorPalette.isDarkMode
      ? colorPalette.white
      : colorPalette.grey.grey9
    : colorPalette.isDarkMode
    ? colorPalette.turquoise.light
    : colorPalette.turquoise.normal};
  text-decoration: underline;
  &:disabled {
    cursor: default;
    color: ${colorPalette.isDarkMode ? colorPalette.grey.grey6 : colorPalette.grey.grey5};
  }
`;

interface CommonStyleButton {
  icon: boolean;
  scope: 'primary' | 'secondary' | 'tertiary';
  noPadding?: boolean;
  selected: boolean;
  customFixedWidth?: string;
  disabledColor?: string;
  iconColor?: string;
}

export const CommonStyledButton = styled.button<CommonStyleButton>`
  ${(props) => props.icon && props.scope === 'secondary' && 'width: 23px'};
  ${(props) => props.icon && props.scope === 'secondary' && 'height: 23px'};
  font-family: inherit;
  padding: ${(props: CommonStyleButton) =>
    props.scope === 'secondary' && props.icon && props.noPadding ? '1px' : '5px 8px'};
  border: solid 1px transparent;
  background: transparent;
  border-radius: ${(props) => (props.icon && props.scope === 'secondary' ? '50%' : '15px')};
  font-size: ${(props) =>
    props.scope === 'tertiary'
      ? fontSizePalette.body.M
      : props.scope === 'secondary' && props.noPadding
      ? fontSizePalette.body.XS
      : fontSizePalette.body.M};
  font-weight: ${fontWeightPalette.regular};
  font-weight: ${(props) => props.selected && fontWeightPalette.medium};
  letter-spacing: -0.5px;
  line-height: 19px;
  cursor: pointer;
  white-space: nowrap;
  width: ${(props) => props?.customFixedWidth};

  &:disabled {
    cursor: default;
    ${(props) => props.disabledColor && `background: ${props.disabledColor} !important`};
  }

  svg {
    padding-left: ${(props) => (props.scope === 'secondary' && props.noPadding ? 0 : '4px')};
    color: ${(props) => props.iconColor};
  }
  ${({ scope, selected, theme }) => {
    switch (scope) {
      case 'primary':
        return primary(selected, theme.colorPalette);
      case 'secondary':
        return secondary(selected, theme.colorPalette);
      case 'tertiary':
        return tertiary(selected, theme.colorPalette);
      default:
        return primary(selected, theme.colorPalette);
    }
  }}
`;

export interface Props extends ComponentProps<'button'> {
  selected?: boolean;
  scope?: 'primary' | 'secondary' | 'tertiary';
  action?: (e: React.MouseEvent<HTMLButtonElement>) => void;
  white?: boolean;
  icon?: FontAwesomeIconProps['icon'];
  noPadding?: boolean;
  customFixedWidth?: string;
  tooltip?: string;
  iconColor?: string;
  useThrottle?: boolean;
  disabledColor?: string;
}

const DivImg = styled.div<{ iconColor?: string; disabled?: boolean }>`
  color: ${({ iconColor, disabled, theme }) =>
    disabled
      ? theme.colorPalette.grey.grey6
      : iconColor || theme.colorPalette.isDarkMode
      ? theme.colorPalette.turquoise.light
      : theme.colorPalette.turquoise.normal};
  display: inline-block;
`;

const CommonButton = (props: Props) => {
  const {
    type,
    value,
    id,
    disabled,
    selected = false,
    scope = 'primary',
    action,
    noPadding,
    icon,
    customFixedWidth,
    tooltip,
    iconColor,
    useThrottle = true,
    disabledColor,
    style,
  } = props;

  const [throttle, setThrottle] = useState(false);
  const internalAction = (e: React.MouseEvent<HTMLButtonElement>) => {
    setThrottle(true);
    if (throttle === false) {
      action?.(e);
    }
    setTimeout(() => {
      setThrottle(false);
    }, defaultDelay);
  };

  return (
    <CommonStyledButton
      title={tooltip}
      customFixedWidth={customFixedWidth}
      id={id}
      type={type}
      value={value}
      onClick={useThrottle ? internalAction : action}
      noPadding={!value ? true : noPadding}
      selected={selected}
      icon={icon ? true : false}
      scope={scope}
      disabled={disabled !== undefined ? disabled : useThrottle ? throttle : undefined}
      disabledColor={disabledColor}
      style={style}
    >
      {icon && scope === 'secondary' ? null : value}
      <DivImg iconColor={iconColor} disabled={disabled}>
        {icon && <FontAwesomeIcon icon={icon} color={iconColor} />}
      </DivImg>
    </CommonStyledButton>
  );
};

export default CommonButton;
