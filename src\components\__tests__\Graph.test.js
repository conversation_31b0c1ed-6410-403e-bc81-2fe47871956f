import React from 'react';
import { screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import '@testing-library/jest-dom/extend-expect';
import { renderWithStyle } from 'utils/helpers/test.helpers';
import Graph from 'components/Graph';

const graphData = {
  percentage: 75
};

describe('Graph Component', () => {

  let graph = null;
  beforeEach(() => {
    graph = <Graph {...graphData}/>;
    renderWithStyle(graph);
  });

  afterEach(() => {
    graph = null;
  });

  it('should render component with value', () => {
    const value = screen.getByText(graphData.percentage + '%')
    expect(value).toBeInTheDocument();
  });

  it('should render value with default color', () => {
    const bar = document.querySelectorAll('svg > circle')[1];
    expect(bar).toHaveStyle('stroke: #319AA6');
  });

  it('should render bar with default color', () => {
    const value = screen.getByText(graphData.percentage + '%')
    expect(value).toHaveStyle('fill: #319AA6');
  });
});
