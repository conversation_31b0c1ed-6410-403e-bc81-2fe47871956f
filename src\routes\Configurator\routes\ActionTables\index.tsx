import React from 'react';
import {  useSelector } from 'react-redux';

import utils from 'utils';
import selectors from 'state/selectors';

import { Wrapper, Title } from '../../styles';
import Po from './Po';
import NoPo from './NoPo';
import CorporateApproval from './CorporateApproval';
import { actionTableViews } from 'models/configurator';

const ActionTables = () => {

  const t = utils.intl.useTranslator();
  const activeView = useSelector(selectors.configurator.selectActionsTableView);


  const returnView = () => {
    if (activeView) {
      switch (activeView.value) {
        case actionTableViews.PO: {
          return <Po />;
        }
        case actionTableViews.NO_PO: {
          return <NoPo />;
        }
        case actionTableViews.CORPORATE_APPROVAL: {
          return <CorporateApproval />;
        }
        default:
          return <Po />;
      }
    }
  };

  return (
    <>
      <Wrapper>
        <Title>
          <h2>{t('actions_tables')}</h2>
        </Title>
        {returnView()}
      </Wrapper>
    </>
  );
};

export default ActionTables;
