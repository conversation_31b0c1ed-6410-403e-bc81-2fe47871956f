/**
 * @deprecated This file is deprecated and will be removed in a future version.
 * Please use the new implementation in the updated module.
 */
import styled from 'styled-components/macro';

export const OcrIcon = styled.img`
  height: 20px;
  margin-top: 5px;
`;

export const PdfWrapper = styled.div<{ height: string }>`
  width: 100%;
  height: ${({ height }) => (height ? `${height}` : 'auto')};
  border: 1px solid ${({ theme }) => theme.colorPalette.grey.grey5};
  border-radius: 5px;
  position: relative;
  .react-pdf__Page__annotations.annotationLayer {
    display: none;
  }
`;

export const PageNavigation = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 90px;
`;

export const VerticalBar = styled.div`
  background-color: ${({ theme }) => theme.colorPalette.grey.grey5};
  height: 26px;
  width: 1px;
  border-radius: 0.5px;
  margin-left: 10px;
`;

export const NumPages = styled.p`
  font-family: Roboto;
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  font-size: ${({ theme }) => theme.fontSizePalette.body.XS};
`;

export const Left = styled.div`
  display: flex;
  align-items: center;

  > * {
    margin-right: 10px;
  }
`;

export const Center = styled.div`
  display: flex;
  align-items: center;
  > * {
    margin-right: 10px;
  }
`;

export const Right = styled.div`
  display: flex;
  align-items: center;

  > * {
    margin-left: 15px;
  }
`;

export const Zoom = styled.div`
  display: flex;
  align-items: center;

  > * {
    margin-right: 10px;
  }
`;

export const InputContainer = styled.div`
  margin: 0 10px;
  display: flex;
  align-items: center;
`;

export const PdfButton = styled.div<{ disabled: boolean }>`
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.turquoise.light : theme.colorPalette.turquoise.normal};

  &:hover {
    cursor: pointer;
  }

  ${({ disabled }) =>
    disabled &&
    `
    opacity: 0.3;
    pointer-events: none;
  `}
`;

export const PageInput = styled.input`
  font-family: Roboto;
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  width: 25px;
  height: 17px;
  border-radius: 3px;
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey5 : theme.colorPalette.turquoise.background};
  border: none;
  outline: 0;
  margin-right: 3px;

  &:focus {
    outline: 1px solid ${({ theme }) => theme.colorPalette.grey.grey5};
  }
`;

export const PdfToolbar = styled.div`
  padding: 10px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid ${({ theme }) => theme.colorPalette.grey.grey5};
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.white};
  border-radius: 5px 5px 0 0;
  height: 50px;
  box-sizing: border-box;
  overflow-x: auto;
  overflow-y: hidden;
`;

export const PdfDocument = styled.div<{ isFullWidth: boolean }>`
  border-radius: 5px;
  z-index: ${({ theme }) => theme.zIndexPalette.lowest};
  width: 100%;
  height: calc(100% - 50px);
  background-color: ${({ theme }) => theme.colorPalette.grey.grey1};
  overflow: auto;
  ${({ isFullWidth }) =>
    isFullWidth &&
    `
    overflow-x: hidden;
  `}
  canvas {
    margin: auto;
  }
`;
