import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components/macro';

import utils from 'utils';
import actions from 'state/actions';

import CommonButton from 'components/Buttons/CommonButton';
import Header from 'components/Modal/Header';
import selectors from 'state/selectors';
import Table, { TableColumn } from 'components/Table';
import { WorkflowGroupList } from 'models/response';

export const ButtonContainer = styled.div`
  text-align: center;
  button {
    margin: 10px;
  }
`;
const WrapperTables = styled.div`
  display: flex;
  flex-direction: row;
`;
const FirstTable = styled.div`
  padding-right: 15px;
`;
const CustomTitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.M};
  font-weight: bold;
`;

const TableWrapper = styled.div`
  display: flex;
  flex-direction: column;
`;

const GroupsUsersModal = () => {
  const dispatch = useDispatch();

  const t = utils.intl.useTranslator();

  const selectedCompanyDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  const selectedTask = useSelector(selectors.configurator.getSelectedTask);
  const selectedGroup = useSelector(selectors.configurator.getSelectedGroup);

  const [titleHeader, setTitleHeader] = useState('');
  const [groupsList, setGroupsList] = useState<WorkflowGroupList[]>([]);

  const groupsColumns: TableColumn[] = [
    { id: '0', accessor: 'idGroup', Header: t('idGroup'), filterType: 'free' },
    { id: '1', accessor: 'groupName', Header: t('groupName'), filterType: 'free' },
    { id: '2', accessor: 'companyName', Header: t('companyName'), filterType: 'free' },
  ];

  const oUserColumn: TableColumn[] = [
    { id: '3', accessor: 'username', Header: t('username'), filterType: 'free' },
    { id: '4', accessor: 'name', Header: t('name'), filterType: 'free' },
    { id: '5', accessor: 'email', Header: t('email'), filterType: 'free' },
  ];

  useEffect(() => {
    dispatch(actions.configurator.setSelectedGroup(null));

    const title =
      (selectedCompanyDefinition?.wfName ? selectedCompanyDefinition?.wfName : '') + ' - ' + selectedTask?.taskName;
    setTitleHeader(title);

    const groups = selectedTask?.groupList
      ? selectedTask.groupList.map((el) => {
          return {
            ...el,
            companyName: selectedCompanyDefinition?.companyName,
          };
        })
      : [];
    setGroupsList(groups);
  }, [selectedCompanyDefinition, selectedTask, dispatch]);

  return (
    <>
      <Header title={titleHeader} />
      <WrapperTables>
        <FirstTable>
          <CustomTitle>{t('groups')}</CustomTitle>
          <Table
            hasSelection
            onSelection={(rows: WorkflowGroupList[]) => {
              dispatch(actions.configurator.setSelectedGroup(rows[0]));
            }}
            columns={groupsColumns}
            rows={groupsList}
            hasPagination
            hasResize
            hasSort
            hasFilter
            hasToolbar
          />
        </FirstTable>
        {selectedGroup ? (
          <TableWrapper>
            <CustomTitle>{t('o-users')}</CustomTitle>
            <Table
              columns={oUserColumn}
              rows={selectedGroup.oUserList}
              hasPagination
              hasResize
              hasSort
              hasFilter
              hasToolbar
            />
          </TableWrapper>
        ) : null}
      </WrapperTables>
      <ButtonContainer>
        <CommonButton
          scope="secondary"
          value={t('close')}
          action={() => {
            dispatch(actions.modal.closeModal());
          }}
        />
      </ButtonContainer>
    </>
  );
};

export default GroupsUsersModal;
