import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actionType } from 'utils/constants';

import utils from 'utils';
import actions from 'state/actions';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';
import services from 'services';

const SeeRoles = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const getUserRoles = services.getUserRoles;
  const selectedUser = useSelector(selectors.configurator.getSelectedUser);

  const setFormModeToCreateUser = async () => {
    dispatch(actions.configurator.setFormMode('allRoles'));
    if (selectedUser?.idUser === undefined) {
      return;
    }
    const { data } = await getUserRoles(selectedUser?.idUser);
    dispatch(actions.configurator.setUserRoles(utils.input.removeDefaultValue(data, 'idCompany')));
  };
  return (
    <CommonButton
      scope="tertiary"
      action={setFormModeToCreateUser}
      disabled={selectedUser?.idUser === undefined || !utils.user.isActionByCompanyActive(actionType.USER_ROLES)}
      value={t('configurator.users.seeRoles')}
    ></CommonButton>
  );
};

export default SeeRoles;
