import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { actionType } from 'utils/constants';

import selectors from 'state/selectors';
import utils from 'utils';
import actions from 'state/actions';
import services from 'services';

import CommonButton from 'components/Buttons/CommonButton';

const EditRole = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const selectedCompanyId = useSelector(selectors.configurator.getSelectedCompanyForRoles);
  const companies = useSelector(selectors.configurator.getCompaniesForRoles);
  const selectedUser = useSelector(selectors.configurator.getSelectedUserForRoles);
  const isEditingUserRows = useSelector(selectors.configurator.getIsEditingUserRows);
  const transformedUserRows = useSelector(selectors.configurator.getTransformedUserRows);

  const editRoleAction = async () => {
    if (selectedCompanyId !== null) {
      const idSelected = selectedUser[0].idUser;
      const idRoleToEdit = selectedUser[0].idRole !== null ? selectedUser[0].idRole : undefined;
      try {
        const { data } = await services.getUserAvailableRoles(selectedCompanyId, idSelected, idRoleToEdit);
        dispatch(actions.configurator.setUserAvailableRoles(data));
        const id = selectedUser[0].id;

        dispatch(actions.configurator.setIsEditingUserRows(transformedUserRows));
        dispatch(actions.configurator.setRowIdUserRoles(id.toString()));
        dispatch(actions.configurator.setExternalSelectedUserId([id]));
        dispatch(actions.configurator.setOpenRowId(id));
      } catch (e) {
        console.error(e);
      }
    }
  };

  return (
    <CommonButton
      action={editRoleAction}
      disabled={
        !selectedUser.length ||
        selectedUser[0]?.idRole === null ||
        isEditingUserRows.length > 0 ||
        !utils.user.isActionByCompanyActive(
          actionType.EDIT_ROLE,
          companies.find((e) => e.idCompany === selectedCompanyId).name,
        )
      }
      scope="tertiary"
      value={t('editRole')}
      icon="circle"
    />
  );
};

export default EditRole;
