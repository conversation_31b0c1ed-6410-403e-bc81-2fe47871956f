import { createSelector } from '@reduxjs/toolkit';
import { RootState } from 'models';
import mapperSelectors from './selectors/mapper';
import usersSelectors from './selectors/users';
import tableSelectors from './selectors/tables';
import actionTablesSelectors from './selectors/actionTables';
import rolesUserCompanySelectors from './selectors/rolesUserCompany';
import workflowSelectors from './selectors/workflow';
import vendorSelectors from './selectors/vendor';
import notificationsSelectors from './selectors/notifications';

const selectTemplates = (state: RootState) => state.configurator.views;

const selectActiveTemplateId = (state: RootState) => state.configurator.activeTemplateId;

// memoization to avoid rerendering (useSelector use an reference check between current and previous value)
const selectTemplateActions = createSelector(
  selectActiveTemplateId,
  selectTemplates,
  (activeIdTemplate, templates) =>
    // eslint-disable-next-line camelcase
    templates.find((el) => el.idTemplate === activeIdTemplate)?.actions_document_list,
);

const getSubviewTemplateActions = createSelector(
  selectActiveTemplateId,
  selectTemplates,
  (activeIdTemplate, templates) => {
    return templates?.find((el) => el.idTemplate === activeIdTemplate)?.subViews;
  },
);

const getCurrentSelectedSubview = createSelector(
  getSubviewTemplateActions,
  actionTablesSelectors.selectActionsTableView,
  workflowSelectors.getSelectedTab,
  selectActiveTemplateId,
  selectTemplates,
  notificationsSelectors.getNotificationActiveView,
  (subViews, currentViewActionTable, currentViewWorkflow, activeTemplate, templates, notificationsActiveView) => {
    const description = templates.find((el) => el.idTemplate === activeTemplate)?.description;
    if (subViews && description === 'ACTIONS') {
      return subViews.find((el) => el.description === currentViewActionTable?.value);
    }
    if (subViews && description === 'WORKFLOW') {
      return subViews.find((el) => el.description === currentViewWorkflow);
    }
    if (subViews && description === 'NOTIFICATIONS') {
      return subViews.find((el) => el.description === notificationsActiveView);
    }
    return null;
  },
);

const selectTemplateActionsOpen = createSelector(
  getCurrentSelectedSubview,
  // eslint-disable-next-line camelcase
  (subView) => subView?.actions_document_open,
);

const selectTemplateActionsList = createSelector(
  getCurrentSelectedSubview,
  // eslint-disable-next-line camelcase
  (subView) => subView?.actions_document_list,
);

const getCompanies = (state: RootState) => state.configurator.companies.companies;

const getSelectedCompany = (state: RootState) => state.configurator.companies.selectedCompany;

const getFormModeCompany = (state: RootState) => state.configurator.companies.formModeCompany;

// workflow

const getSubviewTemplateActionsDoubleSubview = createSelector(
  selectActiveTemplateId,
  selectTemplates,
  (activeIdTemplate, templates) => {
    return templates?.find((el) => el.idTemplate === activeIdTemplate)?.subViews;
  },
);

const getCurrentSelectedDoubleSubview = createSelector(
  getSubviewTemplateActionsDoubleSubview,
  workflowSelectors.getSelectedTab,
  workflowSelectors.getViewMode,
  workflowSelectors.getSelectedDefinitionTaskGroup,
  (subViews, currentView, viewMode, selectedTab) => {
    if (subViews && !viewMode) {
      const subview = subViews.find((el) => el.description === currentView);
      if (subview && subview.subViews) {
        return subview.subViews.find((el) => el.description === selectedTab);
      }
    }
    return null;
  },
);

const selectTemplateActionsListDoubleSubView = createSelector(
  getCurrentSelectedDoubleSubview,
  // eslint-disable-next-line camelcase
  (subView) => subView?.actions_document_list,
);

const selectTemplateActionsOpenDetail = createSelector(
  selectActiveTemplateId,
  selectTemplates,
  (activeIdTemplate, templates) =>
    // eslint-disable-next-line camelcase
    templates.find((el) => el.idTemplate === activeIdTemplate)?.actions_document_open,
);

export default {
  ...mapperSelectors,
  ...usersSelectors,
  ...tableSelectors,
  ...rolesUserCompanySelectors,
  ...actionTablesSelectors,
  ...workflowSelectors,
  ...vendorSelectors,
  ...notificationsSelectors,
  selectActiveTemplateId,
  selectTemplates,
  selectTemplateActions,
  getCompanies,
  getSelectedCompany,
  getFormModeCompany,
  getSubviewTemplateActions,
  getCurrentSelectedSubview,
  selectTemplateActionsOpen,
  selectTemplateActionsList,
  selectTemplateActionsListDoubleSubView,
  selectTemplateActionsOpenDetail,
};
