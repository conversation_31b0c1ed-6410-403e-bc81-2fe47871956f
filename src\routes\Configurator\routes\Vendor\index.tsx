import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { notDraggable } from 'utils/constants';
import selectors from 'state/selectors';
import actions from 'state/actions';

import Modal from 'components/Modal';
import ModalsContent from '../../components/ModalsContent/ModalSwitcher';
import Routes from './routes';

const Vendor = () => {
  const dispatch = useDispatch();

  const closeModal = () => dispatch(actions.modal.closeModal());
  const isModalVisible = useSelector(selectors.modal.getVisibility);
  const currentModal = useSelector(selectors.modal.getActionType);
  const isModalDraggable = notDraggable.includes(currentModal || '');

  return (
    <>
      <Modal onClose={closeModal} open={isModalVisible} isDraggable={!isModalDraggable}>
        <ModalsContent />
      </Modal>
      <Routes />
    </>
  );
};

export default Vendor;
