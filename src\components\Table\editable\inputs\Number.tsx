import React from 'react';
import { colorPalette, fontWeightPalette } from 'utils/styleConstants';
import { NumberInput } from 'components/Input';

interface Props {
  value: string;
  onChange: (value: string | number) => void;
}

export default (props: Props) => {
  const { value, onChange } = props;
  return (
    <NumberInput
      value={value}
      onChange={(newValue) => onChange(newValue || '')}
      width="100%"
      backgroundColor={colorPalette.white}
      height="24px"
      border="none"
      fontSize="12px"
      padding="0"
      fontColor={colorPalette.grey.grey9}
      fontWeight={`${fontWeightPalette.light}`}
    />
  );
};
