import RadioButton from 'components/Buttons/RadioButton';
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import utils from 'utils';
import { definitionTaskGroupTabs } from 'utils/constants';
import Definitions from './Definitions';
import Groups from './Groups';
import Tasks from './Tasks';

const SubTitle = styled.div`
  display: flex;
  align-items: center;
`;

const Label = styled.div`
  margin-right: 20px;
`;

const DefTasksGroupTab = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const [selectedTab, setSelectedTab] = useState<string>(definitionTaskGroupTabs.WORKFLOW_DEFINITION);

  const isWfDefinitionInEdit = useSelector(selectors.configurator.getIsWfTableEdit);

  const options = [
    { value: definitionTaskGroupTabs.WORKFLOW_DEFINITION, label: t('definitions'), disabled: isWfDefinitionInEdit },
    { value: definitionTaskGroupTabs.WORKFLOW_TASK, label: t('tasks'), disabled: isWfDefinitionInEdit },
    { value: definitionTaskGroupTabs.WORKFLOW_GROUP, label: t('groups'), disabled: isWfDefinitionInEdit },
  ];

  const checkSelectedTab = () => {
    switch (selectedTab) {
      case definitionTaskGroupTabs.WORKFLOW_DEFINITION:
        return <Definitions />;
      case definitionTaskGroupTabs.WORKFLOW_TASK:
        return <Tasks />;
      case definitionTaskGroupTabs.WORKFLOW_GROUP:
        return <Groups />;

      default:
        return <Definitions />;
    }
  };

  return (
    <>
      <SubTitle>
        <Label>{t('select_which_table')}</Label>
        <RadioButton
          options={options}
          inline
          value={selectedTab}
          onChange={(value: string) => {
            setSelectedTab(value);
            dispatch(actions.configurator.setSelectedDefinitionTaskGroup(value));
          }}
        />
      </SubTitle>
      {checkSelectedTab()}
    </>
  );
};

export default DefTasksGroupTab;
