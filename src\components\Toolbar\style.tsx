import styled from 'styled-components/macro';

export const BlueBar = styled.div`
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey2: theme.colorPalette.turquoise.dark};
  width: 100%;
  min-height: 50px;
  padding-left: 10px;
  padding-right: 30px;
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

export const ActionBar = styled.div`
  height: 50px;
  min-height: 50px;
  display: flex;
  align-items: center;
  border-bottom: solid 1px ${({ theme }) => theme.colorPalette.grey.grey5};
`;

export const ButtonsContainer = styled.div<{ hidden?: boolean }>`
  display: ${({ hidden }) => (hidden ? 'none' : 'flex')};
  align-items: center;
  overflow-x: auto;
  overflow-y: hidden;
  > * {
    margin: 6px 10px;
  }
  > button {
    // colud be interesting to do that inside the common button component
    // to have this behaviour by default
    white-space: nowrap;
  }
  background-color: ${({ theme }) => theme.colorPalette.white};
`;

export const DropContainer = styled.div`
  width: 170px;
  min-width: 170px;
  height: 50px;
  display: flex;
  align-items: center;
`;

export const Left = styled.div`
  display: flex;
  align-items: center;

  > * {
    margin: 0 10px;
  }
`;

export const Right = styled.div`
  display: flex;
  align-items: flex-end;
  position: relative;
`;

export const ModuleName = styled.h2`
  color: ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.black : theme.colorPalette.grey.grey1)};
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  font-size: clamp(
    ${({ theme }) => theme.fontSizePalette.medium},
    1.5vw,
    ${({ theme }) => theme.fontSizePalette.xxxLarge}
  );
  margin-right: 30px;
  display: inline-block;
`;

export const ButtonsWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-right: 30px;
`;
