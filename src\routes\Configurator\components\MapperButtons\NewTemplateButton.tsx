import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import utils from 'utils';
import actions from 'state/actions';
import CommonButton from 'components/Buttons/CommonButton';
import { actionType } from 'utils/constants';
import selectors from 'state/selectors';

const NewTemplateButton = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const editRowId = useSelector(selectors.configurator.selectMapperEditRowId);
  const setFormModeToCreateUser = () => {
    dispatch(
      actions.modal.setModal({
        actionType: actionType.NEW_TEMPLATE,
        props: {},
      }),
    );
  };
  return (
    <CommonButton
      action={setFormModeToCreateUser}
      scope="tertiary"
      value={t('AddTemplate')}
      icon="circle"
      disabled={editRowId !== '' || !utils.user.isActionByCompanyActive(actionType.NEW_TEMPLATE)}
    />
  );
};
export default NewTemplateButton;
