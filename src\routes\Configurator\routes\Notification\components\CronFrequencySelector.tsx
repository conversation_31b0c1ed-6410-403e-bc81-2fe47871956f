import Checkbox from 'components/Buttons/CheckboxButton';
import Dropdown from 'components/Input/Dropdown';
import MultiSelectInput from 'components/Input/MultiSelect';
import React, { useEffect } from 'react';
import { CheckboxContainer, CheckboxLabel, InputContainer, InputLabel } from 'routes/Configurator/styles';
import styled from 'styled-components';
import utils from 'utils';
import { getDaysOfWeekList, getHoursOptions, minutesOptions } from '../utils';

interface IProps {
  hour: { label: string; value: string } | null;
  setHour: Function;
  minute: { label: string; value: number } | null;
  setMinute: Function;
  allDays: boolean;
  setAllDays: Function;
  daysOfWeek: { label: string; value: number }[];
  setDaysOfWeek: Function;
}

export const ExtendedInputLabel = styled(InputLabel)`
  width: 200px;
  white-space: normal;
  overflow: hidden;
`;
export const ExtendedCheckboxLabel = styled(CheckboxLabel)`
  width: 200px;
  white-space: normal;
  overflow: hidden;
`;

const CronFrequencySelector = ({
  hour,
  setHour,
  minute,
  setMinute,
  allDays,
  setAllDays,
  daysOfWeek,
  setDaysOfWeek,
}: IProps) => {
  const t = utils.intl.useTranslator();

  const daysOfWeekList = getDaysOfWeekList(t);
  const hoursOptions = getHoursOptions();

  useEffect(() => {
    // If all available days are selected, enable alldays
    if (daysOfWeek.length === daysOfWeekList.length && !allDays) {
      setAllDays(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [daysOfWeek]);

  useEffect(() => {
    if (allDays) {
      setDaysOfWeek([]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [allDays]);

  return (
    <div>
      <InputContainer>
        <div style={{ textAlign: 'center', fontWeight: 'bold' }}>{t('choose_frequency')}</div>
      </InputContainer>
      <InputContainer>
        <ExtendedInputLabel> {t('hour')}</ExtendedInputLabel>
        <Dropdown
          name="hour"
          value={hour}
          onChange={(value) => {
            setHour(value);
          }}
          options={hoursOptions}
          placeholder={t('select_hour')}
          width="170px"
        />
      </InputContainer>
      <InputContainer>
        <ExtendedInputLabel> {t('minute')}</ExtendedInputLabel>
        <Dropdown
          name="minute"
          value={minute}
          onChange={(value) => {
            setMinute(value);
          }}
          options={minutesOptions}
          placeholder={t('select_minute')}
          width="170px"
        />
      </InputContainer>
      <InputContainer>
        <ExtendedInputLabel>{t('daysOfWeek')}</ExtendedInputLabel>
        <MultiSelectInput
          labelledBy="daysOfWeek"
          value={daysOfWeek}
          options={daysOfWeekList}
          onChange={(value: { label: string; value: number }[]) => {
            setDaysOfWeek(value);
          }}
          hasSelectAll={false}
          disabled={allDays}
        />
      </InputContainer>
      <CheckboxContainer>
        <ExtendedCheckboxLabel>{t('allDays')}</ExtendedCheckboxLabel>
        <Checkbox
          name="allDays"
          isActive={allDays}
          action={(e) => {
            setAllDays(e);
          }}
        />
      </CheckboxContainer>
    </div>
  );
};
export default CronFrequencySelector;
