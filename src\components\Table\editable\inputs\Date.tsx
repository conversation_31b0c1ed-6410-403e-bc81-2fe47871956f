import React from 'react';
import { colorPalette } from 'utils/styleConstants';
import { DatePicker } from 'components/Input';
import utils from 'utils';

interface Props {
  value: string;
  onChange: (value: string | number) => void;
  dateFormat?: string;
}

export default (props: Props) => {
  const { value, onChange, dateFormat } = props;
  return (
    <DatePicker
      inputWidth="100%"
      selected={value ? utils.date.convertToObjectDate(value) : null}
      isClearable
      fontSize={12}
      portal
      noBorder
      dateFormat={dateFormat}
      disabled={false}
      onChange={(newDate) => onChange(newDate ? utils.date.formatDateForDb(newDate) : '')}
      borderRadius="0px"
      backgroundColor={colorPalette.white}
      height="24px"
      paddingLeft="0px"
      fontColor={colorPalette.grey.grey9}
    />
  );
};
