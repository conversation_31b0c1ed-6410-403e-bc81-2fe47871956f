import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { modalActionType } from 'utils/constants';
import actions from 'state/actions';
import selectors from 'state/selectors';
import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';
import Dropdown from 'components/Input/Dropdown';
import Toolbar from 'components/Toolbar';
import ToolbarButton from './buttons';
import DropdownButton from 'components/Buttons/DropDownButton';

const ToolBar = () => {
  const [more, setMore] = useState(false);
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const programName = useSelector(selectors.app.getProgramName);
  const templates = useSelector(selectors.documents.selectTemplates);
  const activeTemplateId = useSelector(selectors.documents.selectActiveTemplateId);
  const allAuthorized = useSelector(selectors.documents.selectIsAllAuth);
  const columns = useSelector(selectors.documents.selectTableColumns);
  const rows = useSelector(selectors.documents.selectTableRows);
  const templateActionsList = useSelector(selectors.documents.selectTemplateActionsList);
  const refresh = () => dispatch(actions.documents.updateDocuments());
  type GetButtons = (string | { [key: string]: string[] })[];
  const records = useSelector(selectors.documents.selectAllActiveDocuments);

  const isSearchFilterVisible = useSelector(selectors.documents.selectIsSearchFilterVisible);
  const userPreference = useSelector(selectors.user.getUserPreference);

  const getButtons = (initialButtons: string[]) =>
    initialButtons.reduce((accumulator: GetButtons, currentVal) => {
      if (!currentVal.includes('_')) {
        return [...accumulator, currentVal];
      } else {
        const prefix = currentVal.split('_')[0];
        const prefixObj = accumulator.find((el) => typeof el !== 'string' && el.hasOwnProperty(prefix));
        if (prefixObj) {
          (prefixObj as { [key: string]: string[] })[prefix] = [
            ...(prefixObj as { [key: string]: string[] })[prefix],
            currentVal,
          ];
          return accumulator;
        } else {
          return [...accumulator, { [prefix]: [currentVal] }];
        }
      }
    }, []);

  const TemplateOptions = templates.map(
    ({ idTemplate, description, archive, customLabel, hideAllDocumentsDropdown }) => ({
      label: customLabel ? customLabel : description,
      value: idTemplate,
      archive: archive,
      hideAllDocumentsDropdown: hideAllDocumentsDropdown,
    }),
  );

  const AllAuthOptions = [
    { label: t('auth_one'), value: false },
    { label: t('auth_two'), value: true },
  ];

  const activeTemplateOption = TemplateOptions.find(({ value }) => activeTemplateId === value);

  const activeAllAuthOption = AllAuthOptions.find(({ value }) => allAuthorized === value);

  const handleTemplateChange = (option: { value: number; label: string }) => {
    // it should reset the image of the document when the user changes the template
    dispatch(actions.documents.setDocumentImageId(0));
    dispatch(actions.documents.setDocumentImage(''));
    dispatch(actions.documents.setActiveDocumentId([]));
    const { value } = option;
    dispatch(actions.documents.setActiveTemplateId(value));
  };

  const handleAllAuthChange = (option: { value: boolean; label: string }) => {
    const { value } = option;
    dispatch(actions.documents.setIsAllAuth(value));
  };

  const downloadCsv = () => {
    try {
      const header = columns
        .filter(({ position }) => position > -1) // only visible columns
        .sort((a, b) => (a.position > b.position ? 1 : -1)) // sort by position property
        .map(({ name, translate }) => ({ label: translate, accessor: name }));

      const columnsTypeDateIDs = columns?.filter(({ type }) => type === 'Date')?.map(({ name }) => name) || [];

      const body =
        rows?.map((row) => {
          const enhancedRow: { [key: string]: string } = {};
          const rowIDs = Object.keys(row);
          rowIDs.forEach((rowID) => {
            if (columnsTypeDateIDs.includes(rowID)) {
              enhancedRow[rowID] = utils.date.convertDate(row[rowID], userPreference.timeZone ?? 'UTC') || '';
            } else {
              enhancedRow[rowID] = row[rowID] || '';
            }
          });
          return enhancedRow;
        }) || [];

      const csv = utils.file.convertToCSV(header, body);
      utils.file.downloadCsv('documents', csv);
    } catch (e) {
      console.error(e);
      utils.app.notify('fail', e as Error);
    }
  };

  const dropDownDisabled = (mainActionName: string, subActions: string[]) => {
    return (
      !utils.user.isAllActionsEnabled(records, subActions) || !utils.user.isActionEnabled(records, mainActionName, true)
    );
  };

  return (
    <>
      <Toolbar.BlueBar>
        <Toolbar.Left>
          <Toolbar.DropContainer>
            <Dropdown
              id="test"
              onChange={handleTemplateChange}
              options={TemplateOptions}
              value={activeTemplateOption}
            />
          </Toolbar.DropContainer>
          {activeTemplateOption && !activeTemplateOption.hideAllDocumentsDropdown && (
            <Toolbar.DropContainer>
              <Dropdown onChange={handleAllAuthChange} options={AllAuthOptions} value={activeAllAuthOption} />
            </Toolbar.DropContainer>
          )}
          <CommonButton scope="primary" value={t('Refresh')} action={refresh} disabled={isSearchFilterVisible} />
          <CommonButton selected={!more} scope="primary" value={t('Actions')} action={() => setMore(false)} />
          <CommonButton selected={more} scope="primary" value={t('More')} action={() => setMore(true)} />
        </Toolbar.Left>
        <Toolbar.Right>
          <Toolbar.ModuleName>{programName}</Toolbar.ModuleName>
        </Toolbar.Right>
      </Toolbar.BlueBar>
      <Toolbar.ActionBar>
        <Toolbar.ButtonsContainer hidden={!more}>
          <CommonButton
            action={() => dispatch(actions.modal.openModal(modalActionType.documents.EDIT_COLUMNS))}
            scope="tertiary"
            value={t('customize-grid')}
            icon="border-all"
          />
          <CommonButton
            action={() => dispatch(actions.modal.openModal(modalActionType.documents.CHANGE_LAYOUT))}
            scope="tertiary"
            value={t('choose-layout')}
            icon="th"
          />
          <CommonButton value={t('csv')} icon="file-csv" scope="tertiary" action={downloadCsv} />
          <CommonButton
            value={t('hot-keys')}
            scope="tertiary"
            icon="keyboard"
            action={() => dispatch(actions.modal.openModal(modalActionType.documents.HOT_KEYS))}
          />
        </Toolbar.ButtonsContainer>
        <Toolbar.ButtonsContainer hidden={more}>
          {getButtons(templateActionsList || []).map((action, i) => {
            return typeof action === 'string' ? (
              <ToolbarButton key={i} action={action} />
            ) : (
              <DropdownButton
                disabled={dropDownDisabled(Object.keys(action)[0], action[Object.keys(action)[0]])}
                Button={({ isActive }) => {
                  const name = Object.keys(action)[0];
                  return (
                    <CommonButton
                      icon={isActive ? 'caret-up' : 'caret-down'}
                      scope="tertiary"
                      value={t(name)}
                      disabled={dropDownDisabled(name, action[Object.keys(action)[0]])}
                    />
                  );
                }}
                color="blue"
                id={Object.keys(action)[0]}
                idDropContainer={Object.keys(action)[0] + '-dropdown'}
              >
                {action[Object.keys(action)[0]].map((nestedAction, x) => (
                  <ToolbarButton key={i + x} action={nestedAction} />
                ))}
              </DropdownButton>
            );
          })}
        </Toolbar.ButtonsContainer>
      </Toolbar.ActionBar>
    </>
  );
};

export default ToolBar;
