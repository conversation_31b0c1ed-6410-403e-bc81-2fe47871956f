import React from 'react';
import { actionType, modalActionType } from 'utils/constants';

import utils from 'utils';

import CommonButton from 'components/Buttons/CommonButton';
import services from 'services';
import { useDispatch, useSelector } from 'react-redux';
import selectors from 'state/selectors';
import actions from 'state/actions';

const RemoveMassiveUser = () => {
  const t = utils.intl.useTranslator();
  const groupSelected = useSelector(selectors.configurator.getSelectedGroupFromCompanySelected);
  const selectedCompanyDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  const { revalidate } = services.useGetCompanyGroups('getCompanyGroupsKey', selectedCompanyDefinition?.idCompany);
  const oUserSelected = useSelector(selectors.configurator.getSelectedOUser);
  const dispatch = useDispatch();

  const clearState = () => {
    dispatch(actions.configurator.setListOfUserForGroup([]));
    dispatch(actions.configurator.setSelectedGroupFromCompanySelected(null));
    dispatch(actions.configurator.setSelectedOUserList(null));
  };

  const onAction = async () => {
    dispatch(
      actions.modal.setModal({
        actionType: modalActionType.configurator.REMOVE_MASSIVE_USER,
        props: {
          subtitle: t('remove-massive-subtitle'),
          title: t('remove-massive-title'),
          backText: t('remove-massive-back'),
          func: async () => {
            if (!groupSelected) return;
            await services.removeUserMassivelyWfGroup(oUserSelected?.idUser ?? 0);
            await revalidate();
            dispatch(actions.modal.closeModal());
            clearState();

            utils.app.notify('success', t('remove-massive-success'));
          },
        },
      }),
    );
  };

  return (
    <CommonButton
      action={onAction}
      disabled={oUserSelected === null || !utils.user.isActionByCompanyActive(actionType.REMOVE_MASSIVE_USER)}
      scope="tertiary"
      value={t('remove-massive-user')}
      icon="circle"
    />
  );
};

export default RemoveMassiveUser;
