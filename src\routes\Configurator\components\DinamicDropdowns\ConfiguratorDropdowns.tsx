import React from 'react';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';
import selectors from 'state/selectors';
import actions from 'state/actions';
import Dropdown from 'components/Input/Dropdown';
import { useHistory } from 'react-router-dom';

export const ConfiguratorDropdowns = () => {
  const templates = useSelector(selectors.configurator.selectTemplates);
  const activeTemplateId = useSelector(selectors.configurator.selectActiveTemplateId);
  const configActions: any = actions.configurator;
  const dispatch = useDispatch();
  const history = useHistory();
  const options: { label: string; value: number }[] = templates.map(
    (template: { description: string; idTemplate: number }) => ({
      label: template.description,
      value: template.idTemplate,
    }),
  );
  const activeTemplateOption = options.find((option) => option.value === activeTemplateId);
  // generic onChange for multiple dropdowns
  const OnChange = (option: { value: number; label: string }) => {
    const { value, label } = option;
    dispatch(configActions.setActiveTemplateId(value));
    history.push(`/configurator/${label}`);
  };
  return <Dropdown onChange={OnChange} options={options} value={activeTemplateOption} />;
};
