import { Company } from 'models/response';
import { PayloadAction, createSlice, CaseReducer } from '@reduxjs/toolkit';
import { configuratorNotificationsViews, ConfiguratorState } from 'models/configurator';

import templatesReducers from './actions/mapper';
import usersReducers from './actions/users';
import tablesReducers from './actions/tables';
import rolesUserCompanyReducers from './actions/rolesUserCompany';
import actionTablesReducers from './actions/actionsTable';
import workflowReducers from './actions/workflow';
import vendorReducers from './actions/vendor';
import notificationsReducers from './actions/notifications';

const initialState: ConfiguratorState = {
  users: {
    users: [],
    selectedUser: null,
    formMode: null,
    importUserKey: 0,
    userRoles: [],
  },
  mapper: {
    docTypes: [],
    activeDocTypeId: null,
    activeOTemplate: null,
    activeOTemplateId: null,
    fields: null,
    templateEditRowID: '',
    templateSelectedRowID: '',
    rows: [],
    areas: [],
    pdfCurrentPage: 1,
  },
  tables: {
    tablesSelectValues: [],
    activeTable: null,
    tableColumns: [],
    tableRows: [],
    companyPriv: false,
    selectedRow: [],
    externalTablesRowSelected: [],
    editRowID: '',
    tableMode: null,
  },
  rolesUserCompany: {
    companiesForRoles: [],
    selectedCompanyIdForRoles: null,
    usersForCompanyRoles: null,
    selectedUserForRoles: [],
    externalSelectedUserId: [],
    editRowId: null,
    openRowId: null,
    transformedUserRows: [],
    /* This state is used to store rows during the editing mode
    If there are rows here than we use them to create the table
    Otherwise we use the state transformedUserRows */
    isEditingUserRows: [],
    userAvailableRoles: [],
    selectedProgram: null,
  },
  activeTemplateId: null,
  views: [],
  companies: {
    companies: [],
    selectedCompany: null,
    formModeCompany: null,
  },
  actionTables: {
    selectedView: null,
    selectedTableToSearch: 'microcategory',
    companies: [],
    selectedCompanies: [],
    leftTableRows: [],
    selectedRow: null,
    editedRowId: undefined,
    allMicroCategories: [],
    allSubjects: [],
    optionDescriptionDropdown: [],
    optionValueDropdown: [],
    selectedOptionValue: [],
    corporateApproval: {
      editRowId: undefined,
      selectedRow: null,
      corporateApprovalList: [],
    },
    wfCasuals: [],
    wfDefinitions: [],
    AP2OptionsList: {
      costCenter: [],
      sign: [],
      glAccount: [],
      taxCode: [],
    },
    selectedAP2row: null,
    editedRowAP2ID: null,
    isEditAP2Template: false,
    templateAP2List: [],
    isAddAP2Template: false,
    vendorListAp2Template: [],
    NOPO: {
      selectedRow: null,
      editedRowId: undefined,
      leftTableRows: [],
      notModifiedRow: undefined,
    },
  },
  workflow: {
    selectedOUserList: null,
    viewMode: true,
    selectedTabColeCorp: 'WORKFLOW_DEFINITION_TASK_GROUP',
    selectedWorkflowCompanies: [],
    selectedDefinitionTaskGroup: 'WORKFLOW_DEFINITION',
    selectedCompanyDefinition: null,
    companiesDefinition: [],
    selectedTask: null,
    selectedGroup: null,
    listOfDefinitionsForCompanySelected: [],
    selectedDefinitionFromCompanySelected: null,
    editedRowIdWfDefinition: null,
    isWfDefinitionTableEdit: false,
    selectedDefinitionTaskGroupsAssociations: null,
    editedTaskWfRow: null,
    selectedTaskWf: null,
    isWfTableEdit: false,
    editedRowIdWfGroup: null,
    listOfUserForGroup: [],
    selectedGroupFromCompanySelected: null,
    selectedGroupUserAssociation: null,
    listOfAssociations: [],
    editedRowIdWfAssociation: null,
    listOfGroupsForTaskAssociations: [],
    isActiveCreateNewAssociation: false,
    isActiveEditAssociation: false,
    isActiveAddTaskToAssociation: false,
    openRowIdAssociationTable: null,
    selectedIdDefFromAssociations: undefined,
    selectedIdTaskDefFromAssociations: undefined,
  },
  vendor: {
    selectedCompanyVendorConfig: null,
    appliedFilters: {
      column: null,
      operator: null,
      value: '',
    },
    subjectList: [],
    selectedVendorIdFromList: null,
    idEditedVendorFromList: null,
    isEditedVendorDetail: false,
    subjectTableColumns: null,
    subjectDetails: {
      editedRowId: undefined,
      selectedRowId: undefined,
      tableRows: [],
    },
  },
  notifications: {
    activeView: configuratorNotificationsViews.OPEN_WORKFLOW,
    templatesList: [],
    openWf: {
      formModeOpenWF: null,
      selectedOpenWfSettings: null,
      listOfOpenWfSettings: [],
    },
    wfReminder: {
      listOfWfReminderSettings: [],
      selectedWfReminderSettings: null,
      formModeWfReminder: null,
    },
    overdue: {
      listOfOverdueSettings: [],
      selectedOverdueSettings: null,
      formModeOverdue: null,
    },
  },
};

const setActiveTemplateId: CaseReducer<ConfiguratorState, PayloadAction<number | null>> = (state, { payload }) => {
  state.activeTemplateId = payload;
};

export const slice = createSlice({
  name: 'configurator',
  initialState,
  reducers: {
    ...templatesReducers,
    ...usersReducers,
    ...tablesReducers,
    ...rolesUserCompanyReducers,
    ...actionTablesReducers,
    ...workflowReducers,
    ...vendorReducers,
    ...notificationsReducers,
    setActiveTemplateId,
    setCompanies: (state, { payload }: PayloadAction<Company[]>) => {
      state.companies.companies = payload;
    },
    setSelectedCompany: (state, { payload }: PayloadAction<Company | null>) => {
      state.companies.selectedCompany = payload;
    },
    setFormModeCompany: (state, { payload }: PayloadAction<'edit' | 'new' | 'clone' | null>) => {
      state.companies.formModeCompany = payload;
    },
    resetConfWfState: (state) => {
      state.workflow = initialState.workflow;
    },
    resetConfVendorState: (state) => {
      state.vendor = initialState.vendor;
    },
  },
});

export default slice.reducer;
