import React, { useRef, useState } from 'react';
import { usePromiseTracker } from 'react-promise-tracker';
import { useUpdateEffect } from 'react-use';
import styled, { keyframes } from 'styled-components';
import { defaultDelay } from 'utils/configs';
import { turquoiseGradient } from 'utils/styleConstants';

export const rotate = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;

const StyledPreloaderWrapper = styled.div<{ area: string }>`
  position: absolute;
  top: ${({ area }) => (area === 'leftBottom' ? 'auto' : 0)};
  right: ${({ area }) => (area === 'leftBottom' ? 'auto' : 0)};
  left: ${({ area }) => (area === 'leftBottom' ? '15px' : 0)};
  bottom: ${({ area }) => (area === 'leftBottom' ? '15px' : 0)};
  z-index: ${({ theme }) => theme.zIndexPalette.highest};
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  background-color: ${({ theme, area }) => (area === 'leftBottom' ? 'none' : theme.colorPalette.white)}99;
`;

export const Spinner = styled.div`
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  background: ${turquoiseGradient};
  border-radius: 50%;
  animation: ${rotate} 0.5s linear infinite;
`;

const SpinnerInner = styled.div`
  width: 50px;
  height: 50px;
  background: ${({ theme }) => theme.colorPalette.white};
  border-radius: 50%;
`;

interface Props {
  area?: string;
}

/* avoid white flickering for request
just a bit longer than delay */
const durationAtLeast = 1000;

const Preloader = ({ area = 'app' }: Props) => {
  const timerId = useRef(0);
  const [visible, setVisible] = useState(false);
  const { promiseInProgress } = usePromiseTracker({ area: area, delay: defaultDelay });

  useUpdateEffect(() => {
    if (!promiseInProgress && durationAtLeast > 499) {
      setVisible(true);
      timerId.current = setTimeout(() => setVisible(false), durationAtLeast);
    }
    return () => clearTimeout(timerId.current);
  }, [promiseInProgress]);

  return promiseInProgress || visible ? (
    <StyledPreloaderWrapper area={area}>
      <Spinner>
        <SpinnerInner />
      </Spinner>
    </StyledPreloaderWrapper>
  ) : null;
};

export default Preloader;
