import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actionType, modalActionType } from 'utils/constants';

import utils from 'utils';
import actions from 'state/actions';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';

const ReplaceMassiveUser = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const oUserSelected = useSelector(selectors.configurator.getSelectedOUser);

  const onAction = () => {
    dispatch(
      actions.modal.openModal(modalActionType.configurator.ADD_REPLACE_MASSIVE_USER,{
        type: 'replace',
      }),
    );
  };


  return (
    <CommonButton
      action={onAction}
      disabled={oUserSelected === null || !utils.user.isActionByCompanyActive(actionType.REPLACE_MASSIVE_USER)}
      scope="tertiary"
      value={t('replace-massive-user')}
      icon="circle"
    />
  );
};

export default ReplaceMassiveUser;
