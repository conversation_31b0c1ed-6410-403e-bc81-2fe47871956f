import styled from 'styled-components/macro';

export const SpreadsheetGridContainer = styled.div`
  position: relative;
  height: 100%;
  input {
    box-sizing: border-box;
  }
`;

export const SpreadsheetGridScroll = styled.div<{ scrollable: boolean; headerHeight: number }>`
  position: relative;
  overflow: unset;
  transition: height 0.3s;
  border-bottom: none;
  height: ${({ scrollable, headerHeight }) => (scrollable ? `calc(100% - ${headerHeight}px)` : 'auto')};
  ${({ scrollable }) =>
    scrollable &&
    `
    overflow-x: hidden;
    overflow-y: auto;
  `}
`;

export const SpreadsheetGridHeader = styled.div<{ scrollable: boolean }>`
  overflow: hidden;
  display: flex;
  white-space: nowrap;
  ${({ scrollable }) =>
    scrollable &&
    `
    overflow-y: scroll;
  `}
`;

export const SpreadsheetGridHeadCell = styled.div`
  display: block;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  background-color: ${({ theme }) => theme.colorPalette.grey.grey3};
  border: none;
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  position: relative;
  line-height: 14px;
  white-space: nowrap;
  border-bottom: none;
  padding: 10px 8px 8px 8px;
  text-align: left;
  align-items: center;
  :not(:last-child) {
    border-right: 1px solid ${({ theme }) => theme.colorPalette.grey.grey5};
  }
  :first-child {
    border-top-left-radius: 4px;
  }
  :last-child {
    border-top-right-radius: 4px;
  }
`;

export const SpreadsheetGridResizer = styled.div`
  width: 20px;
  position: absolute;
  top: 0;
  right: -10px;
  cursor: col-resize;
  z-index: ${({ theme }) => theme.zIndexPalette.low};
`;

export const SpreadsheetGridScrollDummyDiv = styled.div`
  position: relative;
  min-width: 100%;
`;

export const SpreadsheetGridStyle = styled.div`
  width: 100%;
  border-collapse: collapse;
  position: absolute;
  top: 0;
  background-color: #827789;
  box-sizing: border-box;
`;

export const SpreadsheetGridPlaceholder = styled.div`
  display: none;
`;

export const SpreadsheetGridRow = styled.div<{ height: number }>`
  background-color: ${({ theme }) => theme.colorPalette.white};
  border-bottom: 1px solid #e6e1e8;
  user-select: none;
  white-space: nowrap;
  height: ${({ height }) => `${height}px`};
  .SpreadsheetGrid__cell_active {
    box-shadow: 0 3px 1px -2px ${({ theme }) => theme.colorPalette.grey.grey9};
    z-index: ${({ theme }) => theme.zIndexPalette.low};
  }
  .SpreadsheetGrid__cell_focused::after {
    content: '';
    box-shadow: none;
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
  }
  .SpreadsheetGrid__cell_focused > * {
    pointer-events: auto;
    position: relative;
  }
  .SpreadsheetGrid__cell_disabled {
    cursor: default;
    background: rgba(239, 236, 236, 0.3);
    > * {
      opacity: 0.4;
    }
  }
`;

export const SpreadsheetGridCell = styled.div`
  display: inline-flex;
  align-items: center;
  position: relative;
  color: #565059;
  cursor: pointer;
  border-top: none;
  vertical-align: middle;
  white-space: normal;
  font-size: 13px;
  line-height: 15px;
  text-overflow: ellipsis;
  padding: 10px;
  height: 100%;
  border-right: none;
  border-left: none;
  > div {
    width: 100%;
  }
  > * {
    pointer-events: none;
  }
  :last-child {
    text-align: right;
  }
  .SpreadsheetGridInput {
    font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
    font-weight: ${({ theme }) => theme.fontWeightPalette.light};
    color: ${({ theme }) => theme.colorPalette.grey.grey9};
  }
`;
