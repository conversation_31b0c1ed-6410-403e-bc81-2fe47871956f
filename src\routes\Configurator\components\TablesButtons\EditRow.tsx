import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actionType } from 'utils/constants';

import utils from 'utils';
import actions from 'state/actions';
import selectors from 'state/selectors';

import CommonButton from 'components/Buttons/CommonButton';
import { idAccessor } from 'utils/constants';

const EditRow = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const selectedRow = useSelector(selectors.configurator.selectSelectedRow);
  const editRowId = useSelector(selectors.configurator.selectTablesEditRowID);
  const activeTable = useSelector(selectors.configurator.selectActiveTable);
  const tablesSelectValues = useSelector(selectors.configurator.selectTablesSelectValues);

  const isTableEditable = tablesSelectValues.find((el) => el.table === activeTable?.value)?.editable;

  const editRow = () => {
    if (selectedRow.length) {
      dispatch(actions.configurator.setTablesEditRowID(selectedRow[0][idAccessor]));
      dispatch(actions.configurator.setTableMode('edit'));
    }
  };

  return (
    <CommonButton
      action={editRow}
      disabled={
        !isTableEditable ||
        !selectedRow.length ||
        selectedRow.length > 1 ||
        (editRowId ? true : false) ||
        !utils.user.isActionByCompanyActive(actionType.EDIT_TABLE_ROW)
      }
      scope="tertiary"
      value={t('edit-row')}
      icon="circle"
    />
  );
};

export default EditRow;
