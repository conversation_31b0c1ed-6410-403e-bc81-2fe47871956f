import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components/macro';

import utils from 'utils';
import actions from 'state/actions';

import CommonButton from 'components/Buttons/CommonButton';
import Header from 'components/Modal/Header';
import selectors from 'state/selectors';
import Table, { TableColumn } from 'components/Table';
import { TaskGroupAssociations } from 'models/response';
import Checkbox from 'components/Buttons/CheckboxButton';
import MultiSwitch from 'components/MultiSwitch';
import { relationList } from 'routes/Configurator/routes/Workflow/routes/WorkflowCompanyDetails/tabs/Associations/utils';
import services from 'services';

export const ButtonContainer = styled.div`
  text-align: center;
  button {
    margin: 10px;
  }
`;
const WrapperTables = styled.div`
  display: flex;
  flex-direction: row;
  overflow-x: auto;
`;
const FirstTable = styled.div`
  padding-right: 15px;
`;
const CustomTitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.M};
  font-weight: bold;
`;

const TableWrapper = styled.div`
  display: flex;
  flex-direction: column;
`;

const GroupsUsersAssociationModal = () => {
  const dispatch = useDispatch();

  const t = utils.intl.useTranslator();

  const selectedDefinitionTaskGroupsAssociations = useSelector(
    selectors.configurator.getSelectedDefinitionTaskGroupsAssociations,
  );
  const selectedAssociation = useSelector(selectors.configurator.getSelectedGroupUserAssociation);
  const listOfGroups = useSelector(selectors.configurator.getListOfGroupsForTaskAssociations);
  const isActiveCreateNewAssociation = useSelector(selectors.configurator.getIsActiveCreateNewAssociation);
  const isActiveAddTaskToAssociation = useSelector(selectors.configurator.getIsActiveAddTaskToAssociation);
  const modalProps = useSelector(selectors.modal.getModalProps);
  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);

  const { listOfTasksForAssociations } = services.useGetCompanyTaskForAssociation(
    'getCompanyTasksForAssociationsKey',
    companiesDefinition?.idCompany,
    selectedDefinitionTaskGroupsAssociations?.idDefinition,
  );
  const { listOfDefinitionsForAssociations } = services.useGetCompanyDefinitionsForAssociation(
    'getCompanyDefinitionForAssociationsKey',
    companiesDefinition?.idCompany,
  );

  const { associations } = services.useGetDefinitionTaskGroupsAssociations(
    'getDefinitionTaskGroupsAssociationsKey',
    companiesDefinition?.idCompany,
  );

  const groupsColumns: TableColumn[] = [
    {
      accessor: 'assigned',
      Header: t('assigned'),
      sortType: 'boolean',
      Cell: ({ value }: any) => {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox isActive={value} isEditable={false} />
          </div>
        );
      },
    },
    { id: '1', accessor: 'idGroup', Header: t('idGroup'), filterType: 'free' },
    { id: '2', accessor: 'groupName', Header: t('groupName'), filterType: 'free' },
    {
      id: '3',
      accessor: 'relation',
      Header: t('relation'),
      Cell: ({ value }: any) => {
        return (
          <>
            {value !== null ? (
              <div style={{ display: 'flex', height: '100%', alignItems: 'center' }}>
                <MultiSwitch elements={relationList} value={value} disabled={true} />
              </div>
            ) : (
              '-'
            )}
          </>
        );
      },
      hasLocalKeys: ['LocalizableStringFe', 'workflowAssociation']
    },
    { id: '4', accessor: 'amountLimit', Header: t('amountLimit'), filterType: 'free' },
  ];

  const oUserColumn: TableColumn[] = [
    { accessor: 'username', Header: t('username'), filterType: 'free' },
    { accessor: 'name', Header: t('name'), filterType: 'free' },
    { accessor: 'email', Header: t('email'), filterType: 'free' },
  ];

  const checkTitle = () => {
    if (isActiveAddTaskToAssociation || isActiveCreateNewAssociation) {
      const { idDefinition, definitionName, idTask } = modalProps.rowNewAssociation;

      const nameOfTask = listOfTasksForAssociations?.find((el) => el.idTask === idTask)?.name;
      const nameOfDef = listOfDefinitionsForAssociations?.find((el) => el.idDefinition === idDefinition)?.wfName;

      return (idDefinition ? nameOfDef : definitionName) + ' - ' + nameOfTask;
    }
    if (selectedDefinitionTaskGroupsAssociations) {
      if (selectedDefinitionTaskGroupsAssociations.isSubRow) {
        const findedRecord = associations?.find(
          (el) =>
            !!el.subRows.find((e) => e.idTaskDefinition === selectedDefinitionTaskGroupsAssociations.idTaskDefinition),
        );
        return findedRecord?.definitionName + ' - ' + selectedDefinitionTaskGroupsAssociations?.taskName;
      }

      return (
        selectedDefinitionTaskGroupsAssociations?.definitionName +
        ' - ' +
        selectedDefinitionTaskGroupsAssociations?.taskName
      );
    }
    return '';
  };

  return (
    <>
      <Header title={checkTitle()} />
      <WrapperTables>
        <FirstTable>
          <CustomTitle>{t('groups')}</CustomTitle>
          <Table
            rowId="idGroup"
            hasSelection
            onSelection={(rows: TaskGroupAssociations[]) =>
              dispatch(actions.configurator.setSelectedGroupUserAssociation(rows.length ? rows[0] : null))
            }
            columns={groupsColumns}
            rows={
              isActiveAddTaskToAssociation || isActiveCreateNewAssociation
                ? listOfGroups
                : selectedDefinitionTaskGroupsAssociations?.taskGroupAssociations || []
            }
            hasPagination
            hasResize
            hasSort
            hasFilter
            hasToolbar
            initialSelection={selectedAssociation ? [selectedAssociation.idGroup] : undefined}
          />
        </FirstTable>
        {selectedAssociation ? (
          <TableWrapper>
            <CustomTitle>{t('o-users')}</CustomTitle>
            <Table
              rowId="idUser"
              columns={oUserColumn}
              rows={selectedAssociation?.oUserList || []}
              hasPagination
              hasResize
              hasSort
              hasFilter
              hasToolbar
            />
          </TableWrapper>
        ) : null}
      </WrapperTables>
      <ButtonContainer>
        <CommonButton
          scope="secondary"
          value={t('close')}
          action={() => {
            dispatch(actions.modal.closeModal());
          }}
        />
      </ButtonContainer>
    </>
  );
};

export default GroupsUsersAssociationModal;
