/* eslint-disable max-lines */
/**
 * @deprecated This file is deprecated and will be removed in a future version.
 * Please use the new implementation in the updated module.
 */
import React, { useCallback, useEffect, useRef, useState } from 'react';
// @ts-ignore
import { Document, Page, pdfjs } from 'react-pdf';
import styled from 'styled-components/macro';
import fileHelper from 'utils/helpers/file.helper';
import { colorPalette } from 'utils/styleConstants';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Preloader from 'components/Preloader';
import { PageInput } from './advancedPdf.style';
import TopPage from 'images/topPage';

pdfjs.GlobalWorkerOptions.workerSrc = `${window.location.origin}/${pdfjs.version}.pdf.worker.min.js`;

const PdfWrapper = styled.div<{ height: string; width?: string }>`
  width: ${({ width }) => (width ? `${width}` : '100%')};
  height: ${({ height }) => (height ? `${height}` : 'auto')};
  border: 1px solid ${({ theme }) => theme.colorPalette.grey.grey5};
  border-radius: 5px;
  position: relative;
`;

const PageNavigation = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 90px;
`;

const VerticalBar = styled.div`
  background-color: ${({ theme }) => theme.colorPalette.grey.grey5};
  height: 26px;
  width: 1px;
  border-radius: 0.5px;
  margin-left: 10px;
`;

const NumPages = styled.p`
  font-family: Roboto;
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  font-size: ${({ theme }) => theme.fontSizePalette.body.XS};
`;

const Left = styled.div`
  display: flex;
  align-items: center;

  > * {
    margin-right: 10px;
  }
`;

const Center = styled.div`
  display: flex;
  align-items: center;
  > * {
    margin-right: 10px;
  }
`;

const Right = styled.div`
  display: flex;
  align-items: center;

  > * {
    margin-left: 15px;
  }
`;

const Zoom = styled.div`
  display: flex;
  align-items: center;

  > * {
    margin-right: 10px;
  }
`;

const InputContainer = styled.div`
  margin: 0 10px;
  display: flex;
  align-items: center;
`;

const PdfButton = styled.div<{ disabled: boolean }>`
  color: ${({ theme }) => theme.colorPalette.turquoise.normal};

  &:hover {
    cursor: pointer;
  }

  > * {
    height: 30px;
  }

  ${({ disabled }) =>
    disabled &&
    `
    opacity: 0.3;
    pointer-events: none;
  `}
`;

const PdfToolbar = styled.div`
  padding: 10px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid ${({ theme }) => theme.colorPalette.grey.grey5};
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.white};
  border-radius: 5px 5px 0 0;
  height: 50px;
  box-sizing: border-box;
`;

const PdfDocument = styled.div<{ isFullWidth: boolean }>`
  z-index: ${({ theme }) => theme.zIndexPalette.lowest};
  width: 100%;
  height: calc(100% - 50px);
  text-align: center;
  background-color: ${({ theme }) => theme.colorPalette.grey.grey1};
  border-radius: 5px;
  overflow: auto;
  cursor: grab;
  ${({ isFullWidth }) =>
    isFullWidth &&
    `
    overflow-x: hidden;
  `}

  canvas {
    margin: auto;
  }
`;

let pos = { top: 0, left: 0, x: 0, y: 0 };

export interface PdfProps {
  docImage: string;
  handleClick?: Function;
  docName?: string;
  hasDownload?: boolean;
  currentPage?: number | null;
  setCurrentPage?: Function;
  currentZoom?: number | null;
  setCurrentZoom?: Function;
  fixedHeight?: string;
  width?: string;
}

const Pdf = (props: PdfProps) => {
  const { docImage, setCurrentPage, currentPage, setCurrentZoom, currentZoom, fixedHeight } = props;

  const pdfContainer = useRef<HTMLDivElement | null>(null);
  const pageRef = useRef<any>(null);
  const [numPages, setNumPages] = useState(0);
  const [pageNumber, _setPageNumber] = useState(1);
  const [zoom, setZoom] = useState(currentZoom || 1);
  const [width, setWidth] = useState<number | null>(null);
  const [height, setHeight] = useState<number | null>(null);
  const page = useRef<HTMLDivElement | null>(null);
  const [fitMode, setFitMode] = useState('width');
  const lastPage = () => setNumPages(numPages);
  const firstPage = () => setNumPages(1);
  const [rotation, setRotation] = useState(0);
  const rotateClockwise = () => setRotation((prevRotation) => (prevRotation + 90) % 360);
  const rotateCounterClockwise = () => setRotation((prevRotation) => (prevRotation - 90 + 360) % 360);

  const mouseDownHandler = (e: React.MouseEvent<HTMLDivElement>) => {
    if (pdfContainer.current) {
      // Change the cursor and prevent user from selecting the text
      pdfContainer.current.style.cursor = 'grabbing';
      pdfContainer.current.style.userSelect = 'none';
      pos = {
        // The current scroll
        left: pdfContainer.current.scrollLeft,
        top: pdfContainer.current.scrollTop,
        // Get the current mouse position
        x: e.clientX,
        y: e.clientY,
      };
    }

    const mouseMoveHandler = (e: any) => {
      if (pdfContainer.current) {
        // How far the mouse has been moved
        const dx = e.clientX - pos.x;
        const dy = e.clientY - pos.y;
        // Scroll the element
        pdfContainer.current.scrollTop = pos.top - dy;
        pdfContainer.current.scrollLeft = pos.left - dx;
      }
    };

    const mouseUpHandler = () => {
      if (pdfContainer.current) {
        pdfContainer.current.style.cursor = 'grab';
        pdfContainer.current.style.removeProperty('user-select');
      }
      document.removeEventListener('mousemove', mouseMoveHandler);
      document.removeEventListener('mouseup', mouseUpHandler);
    };

    document.addEventListener('mousemove', mouseMoveHandler);
    document.addEventListener('mouseup', mouseUpHandler);
  };

  const setZoomFun = () => {
    setCurrentZoom && setCurrentZoom(zoom);
  };

  const stableSetZoomFun = useCallback(setZoomFun, [zoom]);

  useEffect(() => {
    stableSetZoomFun();
  }, [stableSetZoomFun]);

  const setPageNumber = (page: number) => {
    setCurrentPage && setCurrentPage(page);
    _setPageNumber(page);
  };

  const zoomOut = () => zoom > 0.25 && setZoom(zoom - 0.25);
  const zoomIn = () => zoom < 2.5 && setZoom(zoom + 0.25);
  const pageNext = () => pageNumber < numPages && setPageNumber(pageNumber + 1);
  const pagePrevious = () => pageNumber > 1 && setPageNumber(pageNumber - 1);

  const fitToWidth = () => {
    setFitMode('height');
    const ref = pdfContainer.current;
    setZoom(1);
    setWidth(ref?.clientWidth || null);
  };

  const fitToHeight = () => {
    setFitMode('width');
    const ref = pdfContainer.current;
    setZoom(1);
    setWidth(null);
    setHeight(ref?.clientHeight || null);
  };

  const handleClick = (e: Event) => {
    if (props.handleClick) {
      props.handleClick(e);
    }
  };

  let timer: number;
  const handleClicks = (event: any) => {
    clearTimeout(timer);
    if (event.detail === 1) {
      timer = setTimeout(handleClick, 400);
    }
  };

  const onDocumentLoadSuccess = (pdf: { numPages: number }) => {
    setNumPages(pdf.numPages);
    _setPageNumber(currentPage || 1);
    fitToWidth();
  };

  return (
    <PdfWrapper height={fixedHeight ? fixedHeight : '100%'} width={props.width} onClick={handleClicks}>
      <Preloader area="pdf" />
      <PdfToolbar>
        <Left>
          {fitMode === 'height' ? (
            <PdfButton
              style={{ color: colorPalette.grey.grey9 }}
              disabled={false}
              onClick={() => {
                fitToHeight();
              }}
            >
              <FontAwesomeIcon size="lg" icon="arrows-alt-v" />
            </PdfButton>
          ) : (
            <PdfButton
              style={{ color: colorPalette.grey.grey9 }}
              disabled={false}
              onClick={() => {
                fitToWidth();
              }}
            >
              <FontAwesomeIcon size="lg" icon="arrows-alt-h" />
            </PdfButton>
          )}
        </Left>
        <Center>
          <PdfButton disabled={false} onClick={rotateCounterClockwise}>
            <FontAwesomeIcon size="lg" icon="undo-alt" />
          </PdfButton>
          <PdfButton disabled={false} onClick={rotateClockwise}>
            <FontAwesomeIcon size="lg" icon="redo-alt" />
          </PdfButton>
          <VerticalBar />
          <PageNavigation>
            <PdfButton disabled={pageNumber === 1} onClick={firstPage}>
              <TopPage></TopPage>
            </PdfButton>
            <PdfButton disabled={pageNumber === 1} onClick={pagePrevious}>
              <FontAwesomeIcon size="lg" icon="arrow-up" />
            </PdfButton>
            <InputContainer>
              <PageInput onChange={(e: any) => setPageNumber(Number(e.target.value))} value={pageNumber} />
              <NumPages>/{numPages}</NumPages>
            </InputContainer>
            <PdfButton disabled={pageNumber === numPages} onClick={pageNext}>
              <FontAwesomeIcon size="lg" icon="arrow-down" />
            </PdfButton>
            <PdfButton disabled={pageNumber === numPages} onClick={lastPage}>
              <TopPage rotation={180}></TopPage>
            </PdfButton>
          </PageNavigation>
          <VerticalBar />
          <Zoom>
            <PdfButton disabled={false} onClick={zoomOut}>
              <FontAwesomeIcon size="lg" icon="search-minus" />
            </PdfButton>
            <PdfButton disabled={false} onClick={zoomIn}>
              <FontAwesomeIcon size="lg" icon="search-plus" />
            </PdfButton>
          </Zoom>
        </Center>
        <Right>
          <VerticalBar />
          <PdfButton
            style={{ color: colorPalette.grey.grey9 }}
            disabled={false}
            onClick={() => fileHelper.openPdfInNewTab(docImage)}
          >
            <FontAwesomeIcon size="lg" icon="file-pdf" />
          </PdfButton>
        </Right>
      </PdfToolbar>
      <PdfDocument isFullWidth={zoom === 1 && fitMode === 'height'} ref={pdfContainer} onMouseDown={mouseDownHandler}>
        {docImage && (
          <Document
            file={`data:application/pdf;base64, ${docImage}`}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={console.error}
            options={{
              cMapUrl: `//cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjs.version}/cmaps/`,
              cMapPacked: true,
            }}
          >
            <div ref={page} style={{ position: 'relative' }}>
              <Page
                ref={pageRef}
                rotate={rotation}
                height={height}
                width={width}
                scale={zoom}
                pageNumber={pageNumber}
              />
            </div>
          </Document>
        )}
      </PdfDocument>
    </PdfWrapper>
  );
};

export default Pdf;
