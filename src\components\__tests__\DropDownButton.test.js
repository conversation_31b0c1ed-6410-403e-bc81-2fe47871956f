import DropDownButton from '../Buttons/DropDownButton';
import React from 'react';
import { act } from 'react-dom/test-utils';
import { unmountComponentAtNode } from 'react-dom';
import { renderWithStyle } from 'utils/helpers/test.helpers';

let container = null;
beforeEach(() => {
  // setup a DOM element as a render target
  container = document.createElement('div');
  document.body.appendChild(container);
});
afterEach(() => {
  // cleanup on exiting
  unmountComponentAtNode(container);
  container.remove();
  container = null;
});

describe('DropDownButton', () => {
  it('renders the markup at Button prop', () => {
    renderWithStyle(<DropDownButton Button={<button>click me</button>}>test</DropDownButton>, container);
    // get a hold of the button element
    const button = document.querySelector('button');
    expect(button.tagName).toBe('BUTTON');
    expect(button.textContent).toBe('click me');
  });

  it('does not show the content when disabled', () => {
    renderWithStyle(
      <DropDownButton Button={<button disabled>click me</button>}>
        <div className="test">test</div>
      </DropDownButton>,
      container,
    );
    // get a hold of the button element, and trigger some clicks on it
    const button = document.querySelector('button');
    button.dispatchEvent(new MouseEvent('click', { bubbles: false })); // disabled stops the bubbling I think
    // get a hold of the dropdownButton content;
    const content = document.querySelector('.test');
    const styles = getComputedStyle(content);
    expect(styles.display).toBe('none');
  });

  it('shows the content after clicking on it', () => {
    renderWithStyle(
      <DropDownButton Button={<button disabled>click me</button>}>
        <div className="test">test</div>
      </DropDownButton>,
      container,
    );
    // get a hold of the button element, and trigger some clicks on it
    const button = document.querySelector('button');
    act(() => {
      button.dispatchEvent(new MouseEvent('click', { bubbles: true }));
    });
    // get a hold of the dropdownButton content;
    const content = document.querySelector('.test');
    const styles = getComputedStyle(content);
    expect(styles.display).toBe('block');
    expect(content.textContent).toBe('test');
  });

  it('manages the close status correctly', () => {
    renderWithStyle(
      <DropDownButton Button={({ isActive }) => <button>{`isOpen: ${isActive}`}</button>}>
        <div className="test">test</div>
      </DropDownButton>,
      container,
    );
    // get a hold of the button element
    const button = document.querySelector('button');
    expect(button.textContent).toBe('isOpen: false');
  });

  it('manages the open status correctly', () => {
    act(() => {
      renderWithStyle(
        <DropDownButton Button={({ isActive }) => <button>{`isOpen: ${isActive}`}</button>}>
          <div className="test">test</div>
        </DropDownButton>,
        container,
      );
    });
    // get a hold of the button element
    const button = document.querySelector('button');
    act(() => {
      button.dispatchEvent(new MouseEvent('click', { bubbles: true }));
    });
    expect(button.textContent).toBe('isOpen: true');
  });
});
