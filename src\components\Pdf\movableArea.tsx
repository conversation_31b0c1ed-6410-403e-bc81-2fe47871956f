/* eslint-disable max-lines */
/**
 * @deprecated This file is deprecated and will be removed in a future version.
 * Please use the new implementation in the updated module.
 */
import React, { useRef, useState } from 'react';
import ResizableRect from 'react-resizable-rotatable-draggable';
import { BoundingBox } from 'models/response';
import styled from 'styled-components/macro';

export type PdfBoxSizes = {
  id: number;
  width: number;
  height: number;
  top: number;
  left: number;
};

export type PdfMovableAreaProps = {
  id: number;
  pageNumber: number;
  currentPage: number;
  rectangleArea: PdfBoxSizes;
  minWidth?: number;
  minHeight?: number;
  rotateAngle?: number;
  activeColor?: string;
  notActiveColor?: string;
  restrictedArea?: Omit<PdfBoxSizes, 'id'>;
  isSelected: boolean;
  isEditing: boolean;
  onAreaChange?: (area: BoundingBox) => void;
  name?: string;
};

const Div = styled.div<{
  isSelected: boolean;
  isEditing: boolean;
  activeColor: string;
  notActiveColor: string;
}>`
  position: absolute;
  top: 0;
  left: 0;
  & > div {
    cursor: ${({ isEditing }) => (isEditing ? 'grab' : 'pointer')};

    border-color: ${({ isEditing, isSelected, activeColor, notActiveColor }) =>
      isEditing ? activeColor : isSelected ? notActiveColor : 'transparent'};
  }
`;

const PdfMovableArea: React.FC<PdfMovableAreaProps> = (props) => {
  const {
    minHeight,
    minWidth,
    activeColor = 'red',
    notActiveColor = 'blue',
    restrictedArea,
    rectangleArea,
    pageNumber,
    currentPage,
    onAreaChange,
    name,
    isSelected,
    isEditing,
  } = props;

  const [width, setWidth] = useState<number>(rectangleArea.width);
  const [height, setHeight] = useState<number>(rectangleArea.height);
  const [top, setTop] = useState<number>(rectangleArea.top);
  const [left, setLeft] = useState<number>(rectangleArea.left);
  const resizeType = useRef<string>('');
  const areaRef = useRef<HTMLDivElement>(null);

  // Only render the component if it's on the correct page
  if (pageNumber !== currentPage) {
    return null;
  }

  const verifyRestriction = (
    restrictedArea: Omit<PdfBoxSizes, 'id'>,
    top: number,
    left: number,
    width: number,
    height: number,
  ): [boolean, boolean, boolean, boolean, boolean] => {
    const RA = restrictedArea;
    const bottom = RA.top + RA.height >= top + height + RA.top;
    const right = RA.left + RA.width >= left + width + RA.left;
    const leftL = left >= 0;
    const topL = top >= 0;
    return [bottom && leftL && right && topL, topL, leftL, bottom, right];
  };

  const verifyDragInRange = (RA: Omit<PdfBoxSizes, 'id'>, value: [boolean, boolean, boolean, boolean]) => {
    if (!value[0] && !value[1]) {
      setTop(0);
      setLeft(0);
      return { top: 0, height, width, left: 0 };
    }
    if (!value[1] && !value[2]) {
      setTop(RA.height - height);
      setLeft(0);
      return { top: RA.height - height, height, width, left: 0 };
    }
    if (!value[2] && !value[3]) {
      setTop(RA.height - height);
      setLeft(RA.width - width);
      return { top: RA.height - height, height, width, left: RA.width - width };
    }
    if (!value[3] && !value[0]) {
      setTop(0);
      setLeft(RA.width - width);
      return { top: 0, height, width, left: RA.width - width };
    }
    if (!value[0]) {
      setTop(0);
      return { top: 0, height, width, left };
    }
    if (!value[1]) {
      setLeft(0);
      return { top, height, width, left: 0 };
    }
    if (!value[2]) {
      setTop(RA.height - height);
      return { top: RA.height - height, height, width, left };
    }
    if (!value[3]) {
      setLeft(RA.width - width);
      return { top, height, width, left: RA.width - width };
    }
    return { top, height, width, left };
  };

  const handleDrag = (deltaX: number, deltaY: number) => {
    setTop((prevTop) => prevTop + deltaY);
    setLeft((prevLeft) => prevLeft + deltaX);
    resizeType.current = 'd';
  };

  const onDragEnd = () => {
    if (restrictedArea !== undefined) {
      const restrictions = verifyRestriction(restrictedArea, top, left, width, height);
      if (!restrictions[0]) {
        const values = restrictions.slice(1) as [boolean, boolean, boolean, boolean];
        const newValues = verifyDragInRange(restrictedArea, values);
        onAreaChange?.({
          x1: newValues.left,
          y1: newValues.top,
          x2: newValues.left + newValues.width,
          y2: newValues.top + newValues.height,
        });
        return;
      }
    }
    onAreaChange?.({
      x1: left,
      y1: top,
      x2: left + width,
      y2: top + height,
    });
  };

  const fixResizePosition = (restrictedArea: Omit<PdfBoxSizes, 'id'>, typeOfResize: string) => {
    const RA = restrictedArea;
    switch (typeOfResize) {
      case 'r':
        setLeft(RA.width - width < 0 ? 0 : RA.width - width);
        setWidth(RA.width - width < 0 ? RA.width : width);
        return {
          top,
          height,
          width: RA.width - width < 0 ? RA.width : width,
          left: RA.width - width < 0 ? 0 : RA.width - width,
        };
      case 'b':
        setTop(RA.height - height < 0 ? 0 : RA.height - height);
        setHeight(RA.height - height < 0 ? RA.height : height);
        return {
          top: RA.height - height < 0 ? 0 : RA.height - height,
          height: RA.height - height < 0 ? RA.height : height,
          width,
          left,
        };
      case 'l':
        setLeft(0);
        setWidth(RA.width - width < 0 ? RA.width : width);
        return { top, height, width: RA.width - width < 0 ? RA.width : width, left: 0 };
      case 't':
        setTop(0);
        setHeight(RA.height - height < 0 ? RA.height : height);
        return { top: 0, height: RA.height - height < 0 ? RA.height : height, width, left };
      case 'tr':
        setTop((current) => (current < 0 ? 0 : current));
        setLeft(RA.width - width < 0 ? 0 : RA.width - width);
        setHeight(RA.height - height < 0 ? RA.height : height);
        setWidth(RA.width - width < 0 ? RA.width : width);
        return {
          top: top < 0 ? 0 : top,
          height: RA.height - height < 0 ? RA.height : height,
          width: RA.width - width < 0 ? RA.width : width,
          left: RA.width - width < 0 ? 0 : RA.width - width,
        };
      case 'tl':
        setTop((current) => (current < 0 ? 0 : current));
        setLeft((current) => (current < 0 ? 0 : current));
        setHeight(RA.height - height < 0 ? RA.height : height);
        setWidth(RA.width - width < 0 ? RA.width : width);
        return {
          top: top < 0 ? 0 : top,
          height: RA.height - height < 0 ? RA.height : height,
          width: RA.width - width < 0 ? RA.width : width,
          left: left < 0 ? 0 : left,
        };
      case 'bl':
        setLeft((current) => (current < 0 ? 0 : current));
        setTop(Math.min(top, RA.height - height));
        setHeight(RA.height - height < 0 ? RA.height : height);
        setWidth(RA.width - width < 0 ? RA.width : width);
        return {
          top: RA.height - height < 0 ? 0 : RA.height - height,
          height: RA.height - height < 0 ? RA.height : height,
          width: RA.width - width < 0 ? RA.width : width,
          left: left < 0 ? 0 : left,
        };
      case 'br':
        setTop(Math.min(top, RA.height - height));
        setLeft(RA.width - width < 0 ? 0 : RA.width - width);
        setHeight(RA.height - height < 0 ? RA.height : height);
        setWidth(RA.width - width < 0 ? RA.width : width);
        return {
          top: RA.height - height < 0 ? 0 : RA.height - height,
          height: RA.height - height < 0 ? RA.height : height,
          width: RA.width - width < 0 ? RA.width : width,
          left: RA.width - width < 0 ? 0 : RA.width - width,
        };

      default:
        return {
          top,
          height,
          width,
          left,
        };
    }
  };

  const handleResize = (
    style: {
      top: number;
      left: number;
      width: number;
      height: number;
      rotateAngle: number;
    },
    _isShiftKey: boolean,
    type: string,
  ): void => {
    const { top, left, width, height } = style;
    setTop(top);
    setLeft(left);
    setWidth(width);
    setHeight(height);
    resizeType.current = type;
  };

  const onResizeEnd = () => {
    if (restrictedArea !== undefined) {
      const restrictions = verifyRestriction(restrictedArea, top, left, width, height)[0];
      let newValues;
      if (!restrictions) {
        const newPosition = fixResizePosition(restrictedArea, resizeType.current);
        newValues = {
          x1: newPosition.left,
          y1: newPosition.top,
          x2: newPosition.left + newPosition.width,
          y2: newPosition.top + newPosition.height,
        };
      } else {
        newValues = {
          x1: left,
          y1: top,
          x2: left + width,
          y2: top + height,
        };
      }
      onAreaChange?.(newValues);
    }
  };

  return (
    <Div
      ref={areaRef}
      isSelected={isSelected}
      isEditing={isEditing}
      notActiveColor={notActiveColor}
      activeColor={activeColor}
      title={name}
    >
      <ResizableRect
        left={left}
        top={top}
        width={width}
        height={height}
        rotateAngle={props.rotateAngle}
        minWidth={minWidth}
        minHeight={minHeight}
        zoomable={isEditing ? 'n, w, s, e, nw, ne, se, sw' : ''}
        rotatable={false}
        onResize={isEditing ? handleResize : undefined}
        onResizeEnd={isEditing ? onResizeEnd : undefined}
        onDrag={isEditing ? handleDrag : undefined}
        onDragEnd={isEditing ? onDragEnd : undefined}
      />
    </Div>
  );
};

export default PdfMovableArea;
