/* eslint-disable max-lines */
import CommonButton from '../Buttons/CommonButton';
import DropdownButton from '../Buttons/DropDownButton';
import React, { useEffect } from 'react';
import Toggle from '../Input/Toggle';
import styled from 'styled-components/macro';
import utils from 'utils';
import { actionType } from 'utils/constants';
import { useSelector } from 'react-redux';
import selectors from 'state/selectors';
import services from 'services';
import { useField, useFormikContext } from 'formik';
import { FormValues, Q1Value } from 'models/documents';
import { DataGridRow } from '.';

const HeaderContainer = styled.div`
  padding: 5px 0;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XS};
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const HeaderWrapper = styled.div`
  display: flex;
  align-items: center;
`;

const HeaderTitle = styled.div`
  padding: 5px 8px;
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
`;

const HeaderButtons = styled.div`
  display: flex;
  align-items: center;
  > button {
    margin: 0 5px;
  }
`;

const HeaderMenu = styled.div`
  display: flex;
  align-items: center;
  > div {
    margin: 0 5px;
  }
`;

const ToggleTitle = styled.span``;

const SearchButtonContainer = styled.div`
  position: relative;
`;

const SearchButtonFilterOn = styled.div`
  width: 8px;
  height: 8px;
  background: ${({ theme }) => theme.colorPalette.red.medium};
  border-radius: 50%;
  position: absolute;
  top: 0;
  right: 0;
`;

const Width = styled.p`
  margin: 0 8px;
`;
const Input = styled.input`
  opacity: 0;
  position: absolute;
  top: 36px;
  left: 0;
  width: 100%;
  height: 50%;
  border: 0;
  background-color: ${({ theme }) => theme.colorPalette.turquoise.dark};
  cursor: pointer;
`;
const ButtonListContainer = styled.div<{ minWidth?: string }>`
  display: flex;
  flex-direction: column;
  background-color: ${({ theme }) => theme.colorPalette.white};
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
  box-shadow: 0 0px 4px 0 rgba(155, 155, 155, 0.23);
  min-width: 68px;
  border: 1px solid ${({ theme }) => theme.colorPalette.turquoise.normal};
`;

interface IHeader {
  disabled: boolean;
  headerTitle?: string;
  selectedRow: boolean;

  hasHeaderToggle?: boolean;
  headerToggleTitle?: string;
  headerToggleStatus?: boolean;
  headerToggleFunction?: (e: React.ChangeEvent<HTMLInputElement>) => void;

  hasDefaultButtons?: boolean;
  addRowAction?: () => void;
  removeRowAction?: () => void;
  removeRowsAction?: () => void;
  copyRowAction?: () => void;
  pasteRowAction?: () => void;

  hasDropDownMenu?: boolean;
  dropDownMenuItems?: {
    label: string;
    action: () => void;
  }[];

  hasFilterToggle?: boolean;
  filterToggleAction?: () => void;
  hasFilterSet?: boolean;
  closeTableFields?: () => void;
  buttonInTitleStatus?: boolean;
  buttonInTitleAction?: () => void;
  buttonInTitleLabel?: string;
  increaseWidth?: () => void;
  decreaseWidth?: () => void;
  downloadCsv?: () => void;
  width: number;
  minWidth: number;

  isPredictionAvailable: boolean;
  selectedRowPrediction?: DataGridRow;
  addPredictionToField?: () => void;
}

const Header = ({
  disabled,
  headerTitle,
  selectedRow,
  hasHeaderToggle,
  headerToggleTitle,
  headerToggleStatus,
  headerToggleFunction,
  hasDefaultButtons,
  addRowAction,
  removeRowAction,
  removeRowsAction,
  copyRowAction,
  pasteRowAction,
  hasDropDownMenu,
  dropDownMenuItems,
  hasFilterToggle = false,
  filterToggleAction,
  closeTableFields,
  hasFilterSet,
  buttonInTitleAction,
  buttonInTitleLabel,
  buttonInTitleStatus,
  increaseWidth,
  decreaseWidth,
  downloadCsv,
  width,
  minWidth,
  isPredictionAvailable,
  selectedRowPrediction,
  addPredictionToField,
}: IHeader) => {
  const t = utils.intl.useTranslator();
  const [fieldDocType] = useField<FormValues['documentTypeHeader']>('documentTypeHeader');
  const idCompany = useSelector(selectors.documents.selectAllActiveDocuments)[0]?.idCompany || -1;
  const idFieldGroup = useSelector(selectors.documents.selectIdFieldGroup);
  const idProtocol = useSelector(selectors.documents.selectActiveDocumentID);
  const nameBody = useSelector(selectors.documents.selectBodyName) || '';
  const [field, , helpers] = useField<Q1Value>(nameBody);
  const { dirty, resetForm, values } = useFormikContext();
  const [dirtyState, setDirtyState] = React.useState(false);
  useEffect(() => {
    if (dirtyState) {
      resetForm({ values });
    }
    setDirtyState(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dirtyState]);

  const csvUpload = async (file: File | null) => {
    try {
      if (!idProtocol || !idFieldGroup || !file) {
        utils.app.notify('fail', 'missing idCompany or fieldDocType or idFieldGroup');
        return;
      }
      const base64 = await utils.file.toBase64(file);
      if (typeof base64 !== 'string') return;
      const response = await services.importTableFields(idProtocol, idFieldGroup, base64);
      const body: DataGridRow[] = field.value.body || [];
      response.forEach((item, idx: number) => {
        if (item != null) {
          body.push({
            id: idx.toString(),
            ...item,
          });
        }
      });
      // setting to null to reset the state
      helpers.setValue({
        ...field.value,
        body: null,
      });
      helpers.setValue({
        ...field.value,
        body,
      });
      setDirtyState(true);
      utils.app.notify('success', t('tableFields.import.success'));
    } catch (error) {
      utils.app.notify('fail', error as string);
    }
  };

  const csvDownload = async () => {
    try {
      if (idCompany == null || idFieldGroup == null || fieldDocType == null || fieldDocType.value == null) {
        utils.app.notify('fail', 'missing idCompany or fieldDocType or idFieldGroup');
        return;
      }

      const response = await services.getCsvForCreateTableFields(idCompany, idFieldGroup, fieldDocType.value.value);
      const csv = atob(response.data) + '\n';

      if (response == null) {
        utils.app.notify('fail', 'missing response');
        return;
      }
      utils.file.downloadCsv('table.csv', csv);
    } catch (error) {
      utils.app.notify('fail', error as string);
    }
  };

  return (
    <HeaderContainer>
      <HeaderWrapper>
        <HeaderTitle>{headerTitle}</HeaderTitle>
        {buttonInTitleStatus && (
          <>
            |
            <CommonButton scope="tertiary" value={buttonInTitleLabel} action={buttonInTitleAction} />
          </>
        )}
      </HeaderWrapper>
      {hasDefaultButtons ? (
        <HeaderButtons>
          <CommonButton
            tooltip={t('add-row')}
            scope="secondary"
            action={addRowAction}
            disabled={disabled}
            icon="plus"
          />
          <CommonButton
            tooltip={t('remove-row')}
            scope="secondary"
            action={removeRowAction}
            disabled={disabled || !selectedRow}
            icon="minus"
          />
          <CommonButton
            tooltip={t('delete-rows')}
            scope="secondary"
            action={removeRowsAction}
            disabled={disabled}
            icon="trash"
          />
          <CommonButton
            tooltip={t('clone-rows')}
            scope="secondary"
            action={copyRowAction}
            disabled={disabled || !selectedRow}
            icon="clone"
          />
          <CommonButton tooltip={t('paste-rows')} scope="secondary" action={pasteRowAction} icon="paste" />
        </HeaderButtons>
      ) : null}
      <HeaderMenu>
        <CommonButton
          tooltip={t('decrease-width')}
          disabled={width <= minWidth}
          icon="minus"
          scope="secondary"
          action={decreaseWidth}
        />
        <Width>width</Width>
        <CommonButton tooltip={t('increase-width')} icon="plus" scope="secondary" action={increaseWidth} />
        {hasHeaderToggle ? (
          <div>
            <Toggle
              disabled={disabled}
              onChange={headerToggleFunction}
              checked={Boolean(headerToggleStatus)}
              tooltip={t('saveQ3_toggle')}
            />
            <ToggleTitle>{headerToggleTitle}</ToggleTitle>
          </div>
        ) : null}
        {hasFilterToggle ? (
          <SearchButtonContainer>
            <CommonButton tooltip={t('show-filter')} scope="secondary" action={filterToggleAction} icon="search" />
            {hasFilterSet ? <SearchButtonFilterOn /> : null}
          </SearchButtonContainer>
        ) : null}
        {hasDropDownMenu && (
          <DropdownButton disabled={disabled} Button={<button>button</button>}>
            <div>
              {dropDownMenuItems?.map((item) => (
                <p key={item.label} onClick={item.action}>
                  {item.label}
                </p>
              ))}
            </div>
          </DropdownButton>
        )}
        {downloadCsv && (
          <CommonButton tooltip={t('download-csv')} icon="file-csv" scope="secondary" action={downloadCsv} />
        )}
        <DropdownButton
          Button={
            <CommonButton tooltip={t('csv')} disabled={disabled} icon="upload" scope="secondary" action={() => null} />
          }
          style={{ right: '-30px', position: 'absolute', minWidth: 'fit-content' }}
        >
          <ButtonListContainer>
            <CommonButton scope="tertiary" value={t('download_csv')} disabled={disabled} action={csvDownload} />
            <CommonButton
              scope="tertiary"
              value={t('import_csv')}
              disabled={disabled}
              action={() => utils.app.notify('warning', t('Please save changes before uploading a CSV file'))}
              customFixedWidth="30px"
            />
            {!dirty && (
              <Input
                type="file"
                value=""
                name=""
                disabled={disabled}
                onChange={(e) => {
                  csvUpload(e.target.files ? e.target.files[0] : null);
                }}
                accept={'.csv'}
              />
            )}
          </ButtonListContainer>
        </DropdownButton>

        {closeTableFields && (
          <div>
            <CommonButton
              id={actionType.CLOSE_Q3}
              tooltip={t('close-table')}
              icon="times"
              scope="secondary"
              action={closeTableFields}
            />
          </div>
        )}
        {isPredictionAvailable && addPredictionToField && (
          <CommonButton
            tooltip={t('accept_prediction')}
            disabled={selectedRowPrediction === undefined}
            icon="check"
            scope="secondary"
            action={() => addPredictionToField()}
          />
        )}
      </HeaderMenu>
    </HeaderContainer>
  );
};

export default Header;
