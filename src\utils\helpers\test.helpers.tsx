import React from 'react';
import { render, RenderResult } from '@testing-library/react';
import { Reset } from 'styled-reset';
import { StylesGlobal } from 'styles-global';
import { IntlProvider } from 'react-intl';
import { StyleSheetManager, ServerStyleSheet } from 'styled-components';
import { renderToString } from 'react-dom/server';
import nodeHtmlToImage from 'node-html-to-image';
import { Provider as ReduxProvider } from 'react-redux';
import { createStore } from 'redux';
import user, { initialState as userState } from '../../state/user/slice';
import app, { initialState as appState } from '../../state/app/slice';
import { combineReducers } from '@reduxjs/toolkit';
import { ThemeProvider } from 'providers/ThemeProvider';

const reducer = combineReducers({
  user,
  app,
});

/**
 * Render a component wrapped with a css style reset, a styled-components theme provider, and Redux.
 * @param {JSX.Element} ComponentToBeTested - a react component to be tested.
 * @param {object} initialState - initial state for the redux store.
 * @return {RenderResult} render result.
 */
function renderWithStyle(
  ComponentToBeTested: JSX.Element,
  initialState: object = {
    user: userState,
    app: appState,
  }, // Default initial state
): RenderResult {
  // Create a store with the initial state
  // @ts-ignore
  const store = createStore(reducer, initialState);

  return render(
    <>
      <ThemeProvider>
        <Reset />
        <StylesGlobal />
        <IntlProvider locale={navigator.language} messages={{}}>
          <ReduxProvider store={store}>{ComponentToBeTested}</ReduxProvider>
        </IntlProvider>
      </ThemeProvider>
    </>,
  );
}

/**
 * Return a component wrapped with a css style reset and a styledcomponents theme provider.
 * @param {JSX.Element} ComponentToBeTested - a react component to be tested.
 * @return {JSX.Element} render result.
 */
function returnWithStyle(ComponentToBeTested: JSX.Element) {
  const sheet = new ServerStyleSheet();
  const enhancedJSx = (
    <>
      <ThemeProvider>
        <StylesGlobal />
        <Reset />
        {ComponentToBeTested}
      </ThemeProvider>
    </>
  );
  const html = renderToString(<StyleSheetManager sheet={sheet.instance}>{enhancedJSx}</StyleSheetManager>);
  const styleTags = sheet.getStyleTags();
  return {
    html,
    styleTags,
    enhancedJSx,
  };
}

interface Options {
  width: number;
  heigth: number;
}

/**
 * createSnapshot
 * @param {JSX.Element} ComponentToBeTested - a react component.
 * @param {Options} options - options
 * @return {null}
 */
async function createSnapshot(ComponentToBeTested: JSX.Element, options: Options) {
  const { width = 1024, heigth = 768 } = options;
  const { styleTags, html } = returnWithStyle(ComponentToBeTested);
  const image = await nodeHtmlToImage({
    html: `<html>
      <head>
        <meta charset="utf-8" />
        <link rel="preconnect" href="https://fonts.gstatic.com">
        <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500&display=swap" rel="stylesheet">
        ${styleTags}
        <style>
          body {
            width: ${width}px;
            height: ${heigth}px;
            background-color: grey;
          }
        </style>
      </head>
      <body>${html}</body>
    </html>
    `,
  });
  return image;
}

/**
 * Converts a CSS hex color value to RGB.
 * @param {string} hex - Expanded hexadecimal CSS color value.
 * @param {number} alpha - Alpha as a decimal.
 * @return {string} RGBA CSS color value.
 */
function hex2Rgb(hex: string): string {
  const r = parseInt(hex.substring(1, 3), 16);
  const g = parseInt(hex.substring(3, 5), 16);
  const b = parseInt(hex.substring(5, 7), 16);
  return `rgb(${r}, ${g}, ${b})`;
}

const snapshotConfig = {
  failureThreshold: 0.1,
  failureThresholdType: 'percent',
};

export { renderWithStyle, createSnapshot, hex2Rgb, snapshotConfig };
