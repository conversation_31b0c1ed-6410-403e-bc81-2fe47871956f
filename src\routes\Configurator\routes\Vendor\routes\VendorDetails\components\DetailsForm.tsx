import React from 'react';
import { useSelector } from 'react-redux';
import selectors from 'state/selectors';
import utils from 'utils';
import { Dropdown, TextInput } from 'components/Input';
import { Checkbox } from 'components/Buttons';
import styled from 'styled-components/macro';
import { dateTypeOptions } from '../../utils';

const FormWrapper = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(4, 1fr);
  grid-auto-flow: column;
  gap: 0px 20px;
  overflow: auto;

  & > span {
    color: ${({ theme }) => theme.colorPalette.grey.grey5};
  }
`;

const InputLabel = styled.div`
  margin-right: 5px;
  font-size: ${({ theme }) => theme.fontSizePalette.body.S};
  width: 100px;
  overflow: hidden;
  letter-spacing: -0.31px;
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
`;

const InputContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-start;
  padding-bottom: 15px;
  font-size: ${({ theme }) => theme.fontSizePalette.small};

  .react-select {
    margin: 0;
  }
`;

const DetailsForm = ({ updateData, newData }: { updateData: (data: any) => void, newData: any }) => {
  const t = utils.intl.useTranslator();

  const isEdited = useSelector(selectors.configurator.getIsEditedVendorDetail);
  const subjectTableColumns = useSelector(selectors.configurator.getSubjectTableColumns);

  const inputsText = [
    { name: 'subject', value: newData?.subject },
    { name: 'email', value: newData?.email },
    { name: 'vatNumber', value: newData?.vatNumber },
    { name: 'url', value: newData?.url },
    { name: 'fiscalCode', value: newData?.fiscalCode },
    { name: 'fax', value: newData?.fax },
    { name: 'address', value: newData?.address },
    { name: 'iban', value: newData?.iban },
    { name: 'tel', value: newData?.tel },
    { name: 'vatReason', value: newData?.vatReason },
  ]

  const inputsCheckbox = [
    {
      name: 'blocked',
      value: newData?.blocked,
      inputType: 'number'
    },
    {
      name: 'forceValidation',
      value: newData?.forceValidation,
      inputType: 'boolean'
    },
  ];

  const isDisabled = (name: string): boolean => {
    const column = subjectTableColumns?.find(({ accessor }) => accessor === name);
    return !isEdited || column?.readOnly || !column;
  };

  return (
    <FormWrapper>
      {inputsText.map(({ name, value }) =>
        <InputContainer key={name}>
          <InputLabel>{t(name)}</InputLabel>
          <TextInput
            id={name}
            value={value || ''}
            disabled={isDisabled(name)}
            borderRadius="5px"
            width="170px"
            onChange={(e: React.FocusEvent<HTMLInputElement>) => {
              const value = e.target.value;
              updateData({ [name]: value })
            }}
          />
        </InputContainer>
      )}

      {inputsCheckbox.map(({ name, value, inputType }) =>
        <InputContainer key={name}>
          <InputLabel>{t(name)}</InputLabel>
          <Checkbox
            isActive={Boolean(value)}
            isEditable={!isDisabled(name)}
            action={(active) => {
              const formatValue = (value: number) => inputType === 'boolean' ? Boolean(value) : value
              updateData({ [name]: active ? formatValue(1) : formatValue(0) })
            }}
          />
        </InputContainer>
      )}

      <InputContainer>
        <InputLabel>{t('dateType')}</InputLabel>
        <Dropdown
          placeholder={t('pleaseSelect')}
          options={dateTypeOptions}
          value={dateTypeOptions.find(({ value }) => value === newData?.dateType)}
          margin="0px 15px"
          disabled={isDisabled('dateType')}
          onChange={({ value }) => {
            updateData({ dateType: value })
          }}
        />
      </InputContainer>
    </FormWrapper>
  )
};

export default DetailsForm;
