/* eslint-disable max-lines */

import AutoResizeTextArea from 'components/Input/AutoResizeTextArea';
import Dropdown from 'components/Input/Dropdown';
import TextInput, { InputLabel } from 'components/Input/TextInput';
import { IFilterValue, IOptionValue } from 'models/request';
import { IFilterDinamicForm } from 'models/response';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components/macro';
import utils from 'utils';
import { replaceNewLineWithArray } from '../index.types';

const ErrorMessage = styled.div`
  color: ${({ theme }) => theme.colorPalette.red.error};
  font-size: 10px;
  position: absolute;
  right: 0;
  bottom: -13px;
`;

const InputWrapper = styled.div`
  display: flex;
  flex-direction: row;
  align-items: center;
`;

const InputsContainer = styled.div`
  position: relative;
  display: flex;
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  justify-content: flex-start;
  margin-bottom: 25px;
  align-items: flex-start;
`;

const Span = styled.span`
  font-size: 10px;
  margin-right: 2px;
  color: ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.black : theme.colorPalette.grey.grey9)};
`;

interface CombinedInputProps {
  filter: IFilterDinamicForm;
  setFilterValues: React.Dispatch<React.SetStateAction<any>>;
  filterErrors: { [key: string]: string };
  validateFilter: (filter: IFilterDinamicForm, valore: any, selectedOptInputCombined: any) => void;
  defaultValue?: IOptionValue;
  shouldReset?: boolean;
}

const CombinedInput: React.FC<CombinedInputProps> = ({
  filter,
  setFilterValues,
  filterErrors,
  validateFilter,
  defaultValue,
  shouldReset,
}) => {
  const t = utils.intl.useTranslator();

  const [combinedValue, setCombinedValue] = useState<{
    value: {
      value: number | string;
      label: string;
      filterName: string;
      isTextArea?: boolean;
      isRange?: boolean;
      inputType?: string;
    };
    inputText: string | string[];
  }>({
    value: (filter.inputProp?.inputCombinedOptions && filter.inputProp?.inputCombinedOptions[0]) || {
      value: '',
      label: '',
      filterName: '',
      isTextArea: false,
      isRange: false,
    },
    inputText: '',
  });

  const errorMessage = filterErrors[combinedValue.value?.filterName];
  const inputCombinedOptions = filter.inputProp?.inputCombinedOptions;

  useEffect(() => {
    if (defaultValue) {
      const defaultKey = Object.keys(defaultValue)[0]; // Get the key from the defaultValue object
      const defaultVal = defaultValue[defaultKey]; // Get the value associated with the key

      // Find the option that matches the key of defaultValue
      const matchedOption = inputCombinedOptions?.find((option) => option.filterName === defaultKey);

      if (matchedOption) {
        // Update combinedValue with the matched option and default value
        setCombinedValue((prevCombinedValue) => ({
          ...prevCombinedValue,
          value: matchedOption,
          inputText: defaultVal || '',
        }));
      }
    }
  }, [defaultValue, inputCombinedOptions]);

  useEffect(() => {
    if (shouldReset === true) {
      setCombinedValue({
        value: (filter.inputProp?.inputCombinedOptions && filter.inputProp?.inputCombinedOptions[0]) || {
          value: '',
          label: '',
          filterName: '',
          isTextArea: false,
          isRange: false,
        },
        inputText: '',
      });
    }
  }, [shouldReset, filter.inputProp]);

  const handleCombinedOptionChange = (selectedOption: { value: number | string; label: string }) => {
    const selectedFilter = inputCombinedOptions?.find((option: any) => option.value === selectedOption.value);

    if (selectedFilter) {
      setCombinedValue({
        value: selectedFilter,
        inputText: '',
      });

      setFilterValues((prevFilterValues: IFilterValue[]) =>
        prevFilterValues.filter((fv) => fv.idFilter !== filter.idFilter),
      );
    }
  };

  const handleCombinedValueChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    let newValue: any = e.target.value;

    if (combinedValue.value.isTextArea && newValue.length) {
      newValue = replaceNewLineWithArray(newValue);
    }

    setCombinedValue({
      ...combinedValue,
      inputText: newValue,
    });

    setFilterValues((prevFilterValues: IFilterValue[]) => {
      // Remove the option if newValue is empty
      if (!newValue) {
        return prevFilterValues
          .map((fv) => {
            if (fv.idFilter === filter.idFilter) {
              const newOptions = fv.options.filter((option) => !option.hasOwnProperty(combinedValue.value.filterName));
              return { ...fv, options: newOptions };
            }
            return fv;
          })
          .filter((fv) => fv.options.length > 0); // Rimuovi il filtro se non ci sono opzioni
      }

      // Update the option value
      const updatedFilterValues = prevFilterValues.map((fv) => {
        if (fv.idFilter === filter.idFilter) {
          return { ...fv, options: {} };
        }
        return fv;
      });

      const existingFilter = updatedFilterValues.find((fv) => fv.idFilter === filter.idFilter);
      if (existingFilter) {
        return updatedFilterValues.map((fv) =>
          fv.idFilter === filter.idFilter ? { ...fv, options: [{ [combinedValue.value.filterName]: newValue }] } : fv,
        );
      } else {
        return [
          ...updatedFilterValues,
          {
            idFilter: filter.idFilter,
            options: [{ [combinedValue.value.filterName]: newValue }],
          },
        ];
      }
    });

    validateFilter(filter, newValue, combinedValue.value);
  };

  const handleRangeValueChange = (newValue: string, inputIndex: number) => {
    // Create a copy of the array of values
    const updatedValues = [...(combinedValue.inputText as string[])];
    // Update the value in the array based on the TextInput index
    updatedValues[inputIndex] = newValue;

    setCombinedValue({
      ...combinedValue,
      inputText: updatedValues,
    });

    // Check if all values in updatedValues are empty
    const allValuesEmpty = updatedValues.every((value) => value.trim() === '');

    // Update the filter values
    setFilterValues((prevFilterValues: IFilterValue[]) => {
      // Remove the filter if all values are empty
      if (allValuesEmpty) {
        return prevFilterValues.filter((fv) => fv.idFilter !== filter.idFilter);
      }

      // Clear the previous value for the filter
      const updatedFilterValues = prevFilterValues.map((fv) => {
        if (fv.idFilter === filter.idFilter) {
          return { ...fv, options: [{ [combinedValue.value.filterName]: updatedValues }] };
        }
        return fv;
      });

      // If the filter does not already exist, add a new filter
      const existingFilter = updatedFilterValues.find((fv) => fv.idFilter === filter.idFilter);

      if (existingFilter) {
        // Update the existing filter
        return updatedFilterValues;
      } else {
        // Add a new filter
        return [
          ...updatedFilterValues,
          {
            idFilter: filter.idFilter,
            options: [{ [combinedValue.value.filterName]: updatedValues }],
          },
        ];
      }
    });

    validateFilter(filter, updatedValues, combinedValue.value);
  };

  return (
    <InputsContainer key={filter.idFilter}>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <InputLabel>{t(filter.label)}</InputLabel>
        <Dropdown
          name={filter.label}
          value={combinedValue.value}
          onChange={handleCombinedOptionChange}
          options={filter.inputProp?.inputCombinedOptions || []}
          minWidthLabel="74px"
          margin="0px 10px 0px 0px"
        />
      </div>
      {combinedValue.value.isTextArea ? (
        <AutoResizeTextArea
          name={filter.label}
          value={Array.isArray(combinedValue.inputText) ? combinedValue.inputText.join('\n') : combinedValue.inputText}
          onChange={handleCombinedValueChange}
          maxHeight={60}
          hasFeedBack={!!errorMessage}
          feedBackMessage={t(errorMessage)}
        />
      ) : combinedValue.value.isRange ? (
        <div style={{ display: 'flex', gap: '22px' }}>
          <InputWrapper>
            <Span>{t('from')}</Span>
            <TextInput
              name={`from_${filter.label}`}
              value={combinedValue.inputText[0]}
              onChange={(e: any) => handleRangeValueChange(e.target.value, 0)} // Passa l'indice del TextInput
              borderRadius="3px"
              width="54px"
              hasFeedback={!!errorMessage}
              type={combinedValue.value.inputType ?? filter.inputType}
            />
          </InputWrapper>
          <InputWrapper>
            <Span>{t('to')}</Span>
            <TextInput
              name={`to_${filter.label}`}
              value={combinedValue.inputText[1]}
              onChange={(e: any) => handleRangeValueChange(e.target.value, 1)} // Passa l'indice del TextInput
              borderRadius="3px"
              width="54px"
              hasFeedback={!!errorMessage}
              type={combinedValue.value.inputType ?? filter.inputType}
            />
          </InputWrapper>
          {errorMessage && <ErrorMessage>{t(errorMessage)}</ErrorMessage>}
        </div>
      ) : (
        <TextInput
          name={filter.label}
          value={Array.isArray(combinedValue.inputText) ? combinedValue.inputText[0] : combinedValue.inputText}
          onChange={handleCombinedValueChange}
          borderRadius="3px"
          hasFeedback={!!errorMessage}
          feedbackMessage={t(errorMessage)}
          type={combinedValue.value.inputType ?? filter.inputType}
        />
      )}
    </InputsContainer>
  );
};

export default CombinedInput;
