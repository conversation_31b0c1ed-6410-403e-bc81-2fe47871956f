import { Program, UserState } from 'models/user';
import React, { useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import { modalActionType } from 'utils/constants';
import { headerHeight, toogleHeight } from 'utils/styleConstants';

import frontEndInfo from '../../../package.json';
import SearchInput from '../Input/SearchInput';
import Group from './Group';
import Item from './Item';
import action from 'state/actions';

const StyledMenu = styled.menu`
  overflow-y: auto;
  height: calc(100vh - ${headerHeight + toogleHeight}px);
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`;

const SearchContainer = styled.div`
  padding: 10px;
  z-index: ${({ theme }) => theme.zIndexPalette.highest};
  background-color: ${({ theme }) => theme.colorPalette.white};
`;

const MenuContainer = styled.div`
  width: 100%;
  padding: 10px 10px 0 10px;
  background-color: ${({ theme }) => theme.colorPalette.white};
`;

const Footer = styled.div`
  height: 150px;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 10px;
`;

const TextVersion = styled.p`
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  font-size: ${({ theme }) => theme.fontSizePalette.body.mini};
  text-align: left;
  cursor: pointer;
`;

const { REACT_APP_SHORT_SHA: SHORT_SHA, REACT_APP_SHA: SHA, NODE_ENV } = process.env;
const REPO_URL = 'https://bitbucket.org/ecslucystar/lucy4fe/commits/';

const RepoLink = (
  <a target="_blank" rel="noopener noreferrer" href={REPO_URL + SHA}>
    ({SHORT_SHA})
  </a>
);

const Menu = () => {
  const [searchResults, setSearchResults] = useState<any>([]);
  const menuPrograms = useSelector(selectors.user.getMenuPrograms);
  const beBuildNumber = useSelector(selectors.app.getBuildNumber);
  const dispatch = useDispatch();

  useEffect(() => {
    setSearchResults(menuPrograms);
  }, [menuPrograms]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value.toLowerCase();

    if (menuPrograms) {
      const results = menuPrograms.map((parent) => ({
        ...parent,
        children: parent.children.filter((child) => child.programName.toLowerCase().includes(input)),
      }));

      const final = results.filter(({ children }) => children.length > 0);

      setSearchResults(final);
    }
  };

  const renderMenu = (elements: UserState['menuPrograms'], deep: number) => {
    if (elements) {
      return elements.map((elm: Program, i: number) => {
        const id = `${elm}-${i}`;
        if (elm.children.length) {
          return (
            <Group name={elm.programName} key={id}>
              {renderMenu(elm.children, deep + 1)}
            </Group>
          );
        }
        return <Item name={elm.programName} key={id} />;
      });
    }
  };

  return (
    <StyledMenu>
      <div>
        <SearchContainer>
          <SearchInput fullWidth onChange={handleSearch} />
        </SearchContainer>
        <MenuContainer>{renderMenu(searchResults, 0)}</MenuContainer>
      </div>
      <Footer>
        <span onClick={() => dispatch(action.modal.openModal(modalActionType.CHANGELOG))}>
          <TextVersion>
            FE: {frontEndInfo.version}
            {NODE_ENV !== 'development' && RepoLink} BE: {beBuildNumber}
          </TextVersion>
        </span>
      </Footer>
    </StyledMenu>
  );
};

export default Menu;
