import React from 'react';

import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';
import { actionType } from 'utils/constants';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import selectors from 'state/selectors';
import services from 'services';
import { useSWRConfig } from 'swr';
import { transformedRows } from 'routes/Configurator/routes/Workflow/routes/WorkflowCompanyDetails/tabs/Associations/utils';

const AddAssociation = () => {
  const t = utils.intl.useTranslator();

  const dispatch = useDispatch();
  const { mutate } = useSWRConfig();

  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  const editedRowId = useSelector(selectors.configurator.getEditedRowIdWfAssociations);
  const { listOfGroupsForAssociations } = services.useGetGroupsForTaskAssociations(
    'getCompanyGroupsForAssociationsKey',
    companiesDefinition?.idCompany,
  );
  const { associations } = services.useGetDefinitionTaskGroupsAssociations(
    'getDefinitionTaskGroupsAssociationsKey',
    companiesDefinition?.idCompany,
  );
  const addRow = async () => {
    const newRow = {
      id: -1,
      idDefinition: -1,
      definitionName: '',
      definitionTaskAssociations: [],
      subRows: [],
    };

    mutate('getDefinitionTaskGroupsAssociationsKey', transformedRows([newRow, ...(associations || [])]), {
      revalidate: false,
    });
    dispatch(actions.configurator.setEditedRowIdWfAssocations(-1));

    if (companiesDefinition?.idCompany) {
      dispatch(actions.configurator.setListOfGroupsForTaskAssociations(listOfGroupsForAssociations || []));
    }

    dispatch(actions.configurator.setIsActiveCreateNewAssociation(true));
  };

  return (
    <CommonButton
      action={() => addRow()}
      scope="tertiary"
      value={t(actionType.ADD_WF_ASSOCIATION)}
      icon="circle"
      disabled={
        editedRowId !== null ||
        !utils.user.isActionByCompanyActive(actionType.ADD_WF_ASSOCIATION, companiesDefinition?.companyName)
      }
    />
  );
};

export default AddAssociation;
