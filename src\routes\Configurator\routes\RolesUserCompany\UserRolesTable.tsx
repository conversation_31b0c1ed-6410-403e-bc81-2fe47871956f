import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { UserForRoleRow } from 'models/configurator';
import { GetUserCompanyRoles } from 'models/response';

import utils from 'utils';
import actions from 'state/actions';
import selectors from 'state/selectors';
import services from 'services';
import * as yup from 'yup';

import Table, { TableProps, OriginalRow } from 'components/Table';

interface Props {
  usersForCompanyRoles: GetUserCompanyRoles | null;
  editRowIdUserRoles: string | null;
}

const UserRolesTable = (props: Props) => {
  const { usersForCompanyRoles, editRowIdUserRoles } = props;

  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();

  const userAvailableRoles = useSelector(selectors.configurator.getUserAvailableRoles);
  const transformedUserRows = useSelector(selectors.configurator.getTransformedUserRows);
  const isEditingUserRows = useSelector(selectors.configurator.getIsEditingUserRows);
  const openRowId = useSelector(selectors.configurator.getOpenRowId);
  const externalUserSelected = useSelector(selectors.configurator.getExternalSelectedUserId);

  const selectedCompanyId = useSelector(selectors.configurator.getSelectedCompanyForRoles);
  const selectedUser = useSelector(selectors.configurator.getSelectedUserForRoles);

  // UserSelection
  const onUserSelection = (rows: UserForRoleRow[]) => {
    dispatch(actions.configurator.setSelectedUserForRoles(rows));
  };

  const userColumns: TableProps['columns'] = [
    {
      accessor: 'username',
      Header: t('username'),
      readOnly: true,
      filterType: 'free',
      Cell: 'withCounter',
    },
    {
      accessor: 'name',
      Header: t('name'),
      readOnly: true,
      Cell: ({ value, row }: { value: any; row: any }) => {
        return <>{row.depth > 0 ? '-' : value}</>;
      },
    },
    {
      accessor: 'email',
      Header: t('email'),
      readOnly: true,
      Cell: ({ value, row }: { value: any; row: any }) => {
        return <>{row.depth > 0 ? '-' : value}</>;
      },
    },
    {
      accessor: 'role',
      Header: t('role'),
      inputType: 'select',
      selectOptionsTypes: utils.input.buildOptions(userAvailableRoles, 'roleName', 'idRole'),
    },
  ];

  const transformedRows = (rows: GetUserCompanyRoles['userRoleResponsesList']) => {
    let id = 0;
    const newRows: UserForRoleRow[] = [];
    rows.forEach((row) => {
      const { idUser, email, name, roleProgramResponseList, username } = row;
      const userRow: UserForRoleRow = {
        id,
        idUser,
        idRole: roleProgramResponseList.length > 1 ? null : roleProgramResponseList[0].idRole,
        username,
        name,
        email,
        role: roleProgramResponseList.length > 1 ? '-' : roleProgramResponseList[0].roleName,
        programResponseList: roleProgramResponseList.length > 1 ? [] : roleProgramResponseList[0].programResponseList,
        subRows:
          roleProgramResponseList.length > 1
            ? roleProgramResponseList.map(({ idRole, programResponseList, roleName }) => ({
                id: ++id,
                idUser,
                idRole,
                username,
                name,
                email,
                role: roleName,
                programResponseList,
                subRows: [],
                isSubRow: true,
              }))
            : [],
      };
      newRows.push(userRow);
      id++;
    });
    return newRows;
  };

  const saveSchema = yup.object().shape({
    role: yup.string().required(),
  });

  useEffect(() => {
    dispatch(
      actions.configurator.setTransformedUserRows(transformedRows(usersForCompanyRoles?.userRoleResponsesList || [])),
    );
  }, [usersForCompanyRoles, dispatch]);

  // This function allows me to exit form the Edit mode
  const onCancelEdit = () => {
    dispatch(actions.configurator.setExternalSelectedUserId([]));
    dispatch(actions.configurator.setRowIdUserRoles(null));
    dispatch(actions.configurator.setOpenRowId(null));
    dispatch(actions.configurator.setIsEditingUserRows([]));
    dispatch(actions.configurator.setUserAvailableRoles([]));
  };

  // This function allows me to save the added row or the edited row
  const onSaveEdit = async (newRow: OriginalRow) => {
    if (selectedCompanyId !== null && selectedUser && newRow) {
      try {
        if (selectedUser[0].idRole !== null) {
          await services.editUserCompanyRoles([
            {
              idUser: selectedUser[0]?.idUser,
              idCompany: selectedCompanyId,
              oldIdRole: selectedUser[0].idRole || 0,
              newIdRole: newRow.role,
            },
          ]);
          utils.app.notify('success', t('user_role_edited'));
        } else {
          await services.addUserCompanyRoles([
            {
              idUser: selectedUser[0]?.idUser,
              idCompany: selectedCompanyId,
              idRole: newRow.role,
            },
          ]);
          utils.app.notify('success', t('user_role_added'));
        }
        const { data } = await services.getUserCompanyRoles(selectedCompanyId);
        dispatch(actions.configurator.setUsersForCompanyRoles(data));
        onCancelEdit();
      } catch (e) {
        console.error(e);
      }
    }
  };

  return (
    <Table
      onSelection={(rows: UserForRoleRow[]) => onUserSelection(rows)}
      hasToolbar
      columns={userColumns}
      rows={isEditingUserRows.length ? isEditingUserRows : transformedUserRows}
      hasSelection
      hasPagination
      hasResize
      hasSort
      hasFilter
      useExpandibleRows
      isEditable
      onCancel={onCancelEdit}
      onSave={onSaveEdit}
      rowId="id"
      editRowID={editRowIdUserRoles || undefined}
      initialSelection={externalUserSelected.length ? externalUserSelected : undefined}
      openRowId={openRowId}
      pageIndex={Number(editRowIdUserRoles) === -1 ? 0 : undefined}
      onSaveValidation={saveSchema}
    />
  );
};

export default UserRolesTable;
