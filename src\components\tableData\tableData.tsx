import React from 'react';
import styled from 'styled-components/macro';
import { fontSizePalette, fontWeightPalette } from 'utils/styleConstants';

export interface DynamicTableProps {
  headers?: string[];
  row: string[];
  rowName: string;
}

const TableContainer = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 16px;
  background-color: ${({ theme }) => theme.colorPalette.white};
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  width: fit-content;
`;

const HeaderRow = styled.div`
  display: flex;
  width: 100%;
  background-color: ${({ theme }) => theme.colorPalette.grey.grey2};
  border: solid 1px ${({ theme }) => theme.colorPalette.grey.grey5};
  border-bottom: none;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
`;

const DataRow = styled.div<{ headers?: boolean }>`
  display: flex;
  align-items: center;
  width: 100%;
  background-color: ${({ theme }) => theme.colorPalette.white};
  border: solid 1px ${({ theme }) => theme.colorPalette.grey.grey5};
  ${({ headers }) => (headers ? 'border-top:0' : 'border-top-left-radius: 4px; border-top-right-radius: 4px;')};
  border-bottom-left-radius: 4px;
  border-bottom-right-radius: 4px;
`;

const Cell = styled.div`
  width: 160px;
  min-width: 160px;
  max-width: 160px;
  padding: 12px 16px;
  word-break: break-word;
  white-space: normal;
  text-align: right;
  margin-right: 4px;
  background-color: inherit;

  ${HeaderRow} & {
    background-color: ${({ theme }) => theme.colorPalette.grey.grey2};
    &:first-child {
      border-top-left-radius: 4px;
    }
    &:last-child {
      border-top-right-radius: 4px;
    }
  }

  ${DataRow} & {
    background-color: ${({ theme }) => theme.colorPalette.white};
    &:first-child {
      border-bottom-left-radius: 4px;
    }
    &:last-child {
      border-bottom-right-radius: 4px;
    }
  }

  font-size: ${({ theme }) => theme.fontSizePalette.body.XS};
`;

const DynamicTable: React.FC<DynamicTableProps> = ({ headers, row, rowName }) => {
  return (
    <TableContainer>
      {headers && (
        <HeaderRow>
          <Cell key={-1}></Cell>
          {headers.map((header, index) => (
            <Cell
              key={index}
              style={{ fontWeight: fontWeightPalette.bold, textAlign: 'left', fontSize: fontSizePalette.xSmall }}
            >
              {header}
            </Cell>
          ))}
        </HeaderRow>
      )}
      <DataRow headers={(headers?.length ?? 0) > 0}>
        <Cell key={-1} style={{ fontWeight: fontWeightPalette.bold, textAlign: 'left' }}>
          {rowName}
        </Cell>
        {row.map((cell, index) => (
          <Cell key={index}>{cell}</Cell>
        ))}
      </DataRow>
    </TableContainer>
  );
};

export default DynamicTable;
