import React from 'react';
import LoadingBar from '../BarLoader';
import { renderWithStyle } from 'utils/helpers/test.helpers';
import { colorPalette } from 'utils/styleConstants';
import { hex2Rgb } from 'utils/helpers/test.helpers';

describe('LoadingBar', () => {
  test('LoadingBar width check based on percentage', () => {
    renderWithStyle(<LoadingBar id="test" currentPercentage={20} />);

    const bar = document.querySelector('#test > div');
    const styles = getComputedStyle(bar);

    setTimeout(() => {
      expect(styles.width).toBe('20%');
    }, 350);
  });

  test('LoadingBar container styles check', () => {
    renderWithStyle(<LoadingBar id="test" currentPercentage={20} />);

    const bar = document.querySelector('#test');
    const styles = getComputedStyle(bar);

    expect(styles.backgroundColor).toBe(hex2Rgb(colorPalette.grey.grey2));
    expect(styles.borderRadius).toBe('10px');
    expect(styles.width).toBe('400px');
    expect(styles.height).toBe('10px');
  });

  test('LoadingBar styles check', () => {
    renderWithStyle(<LoadingBar id="test" currentPercentage={20} />);

    const bar = document.querySelector('#test > div');
    const styles = getComputedStyle(bar);
    expect(styles.height).toBe('100%');
    expect(styles.backgroundColor).toBe(hex2Rgb(colorPalette.turquoise.dark));
    expect(styles.maxWidth).toBe('100%');
  });
});
