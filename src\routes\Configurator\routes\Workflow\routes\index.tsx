import React from 'react';
import { Route, Switch, useRouteMatch } from 'react-router-dom';
import WorkflowCompanyDetails from './WorkflowCompanyDetails';
import WorkflowMainView from './WorkflowMainView';

const Routes = () => {
  const { path } = useRouteMatch();
  return (
    <>
      <Switch>
        <Route exact path={`${path}`} component={WorkflowMainView} />
        <Route path={`${path}/workflow-company`} component={WorkflowCompanyDetails} />
      </Switch>
    </>
  );
};

export default Routes;
