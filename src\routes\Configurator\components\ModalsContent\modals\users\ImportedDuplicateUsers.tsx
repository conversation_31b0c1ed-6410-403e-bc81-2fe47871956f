import React from 'react';
import { useSelector } from 'react-redux';
import IntlHelper from 'utils/helpers/intl.helper';

import selectors from 'state/selectors';

import Modal from 'components/Modal';
import Table from 'components/Table';

const ImportedDuplicateUsers = () => {
  const t = IntlHelper.useTranslator();
  const modalProps = useSelector(selectors.modal.getModalProps);

  const userColumns = [
    { accessor: 'username', Header: t('username') },
    { accessor: 'name', Header: t('name') },
    { accessor: 'email', Header: t('email') },
  ];

  return (
    <>
      <Modal.Header title={t('imported_duplicate_users_title')} subtitle={t('imported_duplicate_users_subtitle')} />
      {modalProps.users && modalProps.users.length > 0 ? (
        <Table hasToolbar columns={userColumns} rows={modalProps.users} hasPagination hasResize hasSort hasFilter />
      ) : null}
    </>
  );
};

export default ImportedDuplicateUsers;
