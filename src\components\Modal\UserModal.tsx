/* eslint max-lines: 0 */
import Avatar from 'components/avatar';
import { FormikProps, useFormik } from 'formik';
import _ from 'lodash';
import { UserDate } from 'models/user';
import React, { useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import service from 'services';
import actions from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import { dateOptions, modalActionType, notDraggable } from 'utils/constants';
import utils from 'utils';
import * as Yup from 'yup';
import moment from 'moment-timezone';

import RadioButton from '../Buttons/RadioButton';
import Dropdown from '../Input/Dropdown';
import Modal from './';
import ThemeSwitch from 'components/ThemeSwitch';

const P = styled.p`
  margin-right: 10px;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
`;

const Container = styled.div`
  padding: 0 30px;
`;

const Wrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
`;

const UserInfo = styled.div`
  margin-top: 25px;
  margin-bottom: 35px;
`;

const InfoText = styled.div`
  display: flex;
  align-items: baseline;
  margin-bottom: 10px;
`;

const InfoP = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.S};
`;

const InfoPBold = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.S};
  font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  margin-right: 10px;
`;

const Field = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;

  label {
    width: 100%;
    line-height: 30px;
  }

  input,
  select {
    width: 100%;
  }

  input[type='checkbox'] {
    width: auto;
  }

  margin-bottom: 20px;

  input:read-only,
  select:disabled {
    background-image: none;
    color: ${({ theme }) => theme.colorPalette.grey.dark};
  }
`;

const RadioField = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
`;

const Err = styled.div`
  color: ${({ theme }) => theme.colorPalette.red.medium};
  font-size: ${({ theme }) => theme.fontSizePalette.xSmall};
  text-align: right;
  margin-top: 5px;
  width: 100%;
`;

// this is flag not related to the UI
// that's why a simple variable here is
// better than a state. We can use a simple
// variable and not a ref, because UserModal
// is singularly used
let flagUpdateImage: 0 | 1 = 0;
interface UserSettings {
  base64Image: string | undefined;
  preferredDateFormat: UserDate | undefined;
  decimalSeparator: '.' | ',' | undefined;
  sendMail: 0 | 1 | undefined;
  csvSeparator: ';' | ',' | undefined;
  timeZone: string | undefined;
  openNextDocument: boolean | undefined;
  openInvoiceLogAutomatically: boolean | undefined;
}

const User = (props: FormikProps<UserSettings>) => {
  const name = useSelector(selectors.user.getUserFullName);
  const mail = useSelector(selectors.user.getUserMail);
  const t = utils.intl.useTranslator();
  const inputFile = useRef<HTMLInputElement | null>(null);
  const { base64Image } = props.values;
  const onChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.currentTarget.files && e.currentTarget.files.length > 0 && e.currentTarget.files[0];
    if (file) {
      const base64 = await utils.file.toBase64(file, true);
      props.setFieldValue('base64Image', base64);
      flagUpdateImage = 1;
    }
  };

  const changeAvatar = () => {
    if (inputFile && inputFile.current) {
      inputFile.current.click();
    }
  };

  const onRemoveImage = () => {
    props.setFieldValue('base64Image', '');
    flagUpdateImage = 1;
  };

  return (
    <>
      <Wrapper>
        <Avatar
          onRemoveImage={onRemoveImage}
          onClick={changeAvatar}
          image={base64Image}
          userName={name || ''}
          size={100}
        />
      </Wrapper>
      <input
        ref={inputFile}
        style={{ display: 'none' }}
        onChange={onChange}
        type="file"
        accept="image/*"
        name="base64Image"
      />
      <UserInfo>
        <InfoText>
          <InfoPBold>{t('name')}</InfoPBold>
          <InfoP>{name}</InfoP>
        </InfoText>
        <InfoText>
          <InfoPBold>{t('email')}</InfoPBold>
          <InfoP>{mail}</InfoP>
        </InfoText>
      </UserInfo>
    </>
  );
};

const FormatoData = (props: FormikProps<UserSettings>) => {
  const t = utils.intl.useTranslator();

  return (
    <>
      <Field>
        <P>{t('dateFormat')}</P>
        <Dropdown
          name="preferredDateFormat"
          value={{ label: props.values['preferredDateFormat'], value: props.values['preferredDateFormat'] }}
          options={dateOptions.map((option) => ({ label: option, value: option }))}
          onChange={({ value }) => props.setFieldValue('preferredDateFormat', value)}
        />
        {props.errors['preferredDateFormat'] && <Err>{props.errors['preferredDateFormat']}</Err>}
      </Field>
    </>
  );
};

const Separatore = (props: FormikProps<UserSettings>) => {
  const t = utils.intl.useTranslator();

  return (
    <>
      <Field>
        <P>{t('decimalSep')}</P>
        <Dropdown
          name="decimalSeparator"
          value={{ label: props.values['decimalSeparator'], value: props.values['decimalSeparator'] }}
          options={[
            { label: '.', value: '.' },
            { label: ',', value: ',' },
          ]}
          onChange={({ value }) => props.setFieldValue('decimalSeparator', value)}
        />
        {props.errors['decimalSeparator'] && <Err>{props.errors['decimalSeparator']}</Err>}
      </Field>
      <Field>
        <P>{t('csvSep')}</P>
        <Dropdown
          name="csvSeparator"
          value={{ label: props.values['csvSeparator'], value: props.values['csvSeparator'] }}
          options={[
            { label: ',', value: ',' },
            { label: ';', value: ';' },
          ]}
          onChange={({ value }) => props.setFieldValue('csvSeparator', value)}
        />
        {props.errors['csvSeparator'] && <Err>{props.errors['csvSeparator']}</Err>}
      </Field>
    </>
  );
};

const RicezioneMail = (props: FormikProps<UserSettings>) => {
  const t = utils.intl.useTranslator();

  const mailOptions = [
    { label: t('no'), value: 0 },
    { label: t('yes'), value: 1 },
  ];

  return (
    <RadioField>
      <P>{t('notifyMail')}</P>
      <RadioButton
        inline
        value={props.values['sendMail']}
        onChange={(value) => props.setFieldValue('sendMail', parseInt(value, 10))}
        options={mailOptions}
      />
    </RadioField>
  );
};

const UtcFormatter = (props: FormikProps<UserSettings>) => {
  const t = utils.intl.useTranslator();
  const dropValues = [
    { label: t('automatic'), value: 'automatic' },
    ...moment.tz.names().map((name) => ({ label: name, value: name })),
  ];
  return (
    <Field>
      <P>{t('timeZone')}</P>
      <Dropdown
        name="timeZone"
        value={{ label: props.values['timeZone'], value: props.values['timeZone'] }}
        options={dropValues}
        onChange={({ value }) => props.setFieldValue('timeZone', value)}
      />
      {props.errors['timeZone'] && <Err>{props.errors['timeZone']}</Err>}
    </Field>
  );
};

const OpenNextDocument = (props: FormikProps<UserSettings>) => {
  const t = utils.intl.useTranslator();

  const mailOptions = [
    { label: t('no'), value: 'false' },
    { label: t('yes'), value: 'true' },
  ];

  return (
    <RadioField>
      <P>{t('open-next-document')}</P>
      <RadioButton
        inline
        value={String(props.values['openNextDocument'])}
        onChange={(value) => props.setFieldValue('openNextDocument', value === 'true')}
        options={mailOptions}
      />
    </RadioField>
  );
};

const OpenInvoiceLogAuto = (props: FormikProps<UserSettings>) => {
  const t = utils.intl.useTranslator();

  const options = [
    { label: t('no'), value: 'false' },
    { label: t('yes'), value: 'true' },
  ];

  return (
    <RadioField>
      <P>{t('open-invoice-automatically')}</P>
      <RadioButton
        inline
        value={String(props.values['openInvoiceLogAutomatically'])}
        onChange={(value) => props.setFieldValue('openInvoiceLogAutomatically', value === 'true')}
        options={options}
      />
    </RadioField>
  );
};

const UserModal = () => {
  const activeModalType = useSelector(selectors.modal.getActionType);
  const modalVisibility = useSelector(selectors.modal.getVisibility);
  const isModalDraggable = notDraggable.includes(activeModalType || '');
  const userPreference = useSelector(selectors.user.getUserPreference);
  const idUser = useSelector(selectors.user.getIdUser);
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();

  const initialValues: UserSettings = {
    base64Image: userPreference.base64Image,
    preferredDateFormat: userPreference.dateFormat,
    decimalSeparator: userPreference.decimalSep,
    sendMail: userPreference.notifyMail,
    csvSeparator: userPreference.csvSep,
    timeZone: userPreference.timeZone,
    openNextDocument: userPreference.openNextDocument,
    openInvoiceLogAutomatically: userPreference.openInvoiceLogAutomatically,
  };

  const onSubmit = async (values: UserSettings) => {
    const {
      decimalSeparator,
      preferredDateFormat,
      sendMail,
      base64Image,
      csvSeparator,
      timeZone,
      openNextDocument,
      openInvoiceLogAutomatically,
    } = values;
    if (
      idUser &&
      preferredDateFormat &&
      decimalSeparator &&
      sendMail !== undefined &&
      base64Image !== undefined &&
      csvSeparator &&
      timeZone !== undefined &&
      openNextDocument !== undefined &&
      openInvoiceLogAutomatically !== undefined
    ) {
      try {
        await service.changeUserSettings({
          decimalSeparator,
          sendMail,
          base64Image: flagUpdateImage ? base64Image : '',
          preferredDateFormat,
          idUser,
          flagUpdateImage,
          csvSeparator,
          timeZone: timeZone === 'automatic' ? moment.tz.guess() : timeZone,
          openNextDocument,
          openInvoiceLogAutomatically,
        });
        dispatch(actions.modal.closeModal());
        dispatch(actions.user.setUserImage(base64Image));
        dispatch(actions.user.setUserTimezone(timeZone === 'automatic' ? moment.tz.guess() : timeZone));
        dispatch(actions.user.setSendMail(sendMail));
        dispatch(actions.user.setUserDateFormat(preferredDateFormat));
        dispatch(actions.user.setUserDaecimalSep(decimalSeparator));
        dispatch(actions.user.setUserCsvSep(csvSeparator));
        dispatch(actions.user.setOpenNextDocument(openNextDocument));
        dispatch(actions.user.setOpenInvoiceLogAuto(openInvoiceLogAutomatically));
      } catch (error) {
        utils.app.notify('warning', error as Error);
      }
    }
  };

  const Switcher = (
    <RadioField>
      <P>{t('theme')}</P>
      <ThemeSwitch />
    </RadioField>
  );
  const formikProps = useFormik({
    initialValues,
    onSubmit,
    validationSchema: Yup.object().shape({
      preferredDateFormat: Yup.string().required('Required'),
      decimalSeparator: Yup.string().required('Required'),
      sendMail: Yup.number().required('Required'),
    }),
  });
  return modalVisibility && activeModalType && activeModalType === modalActionType.USER_SETTINGS ? (
    <Modal open={modalVisibility} onClose={() => dispatch(actions.modal.closeModal())} isDraggable={!isModalDraggable}>
      <>
        <Modal.Header title={t('userSettings')} subtitle={t('user-settings-description')} />
        <Modal.Content>
          <>
            <Container>
              <User {...formikProps} />
              <FormatoData {...formikProps} />
              <Separatore {...formikProps} />
              <UtcFormatter {...formikProps} />
              <RicezioneMail {...formikProps} />
              <OpenNextDocument {...formikProps} />
              <OpenInvoiceLogAuto {...formikProps} />
              {Switcher}
            </Container>
          </>
        </Modal.Content>
        <Modal.Footer
          confirmAction={() => {
            try {
              const errors = formikProps.validateForm();
              if (!_.isEmpty(errors)) throw new Error('Some filed are required');
              formikProps.submitForm();
            } catch (error) {
              utils.app.notify('warning', error as Error);
            }
          }}
          confirmText={t('save')}
        />
      </>
    </Modal>
  ) : null;
};

export default UserModal;
