import React from 'react';
import { THead, TH, <PERSON><PERSON><PERSON>, Sorter, Filter } from './styles';
import { HeaderGroup } from 'react-table';
import { zIndexPalette } from 'utils/styleConstants';

interface HeaderProps {
  headerGroups: HeaderGroup[];
  hasSort?: boolean;
  hasResize?: boolean;
  fixedColumns: number;
  onSortingChange?: Function;
  light: boolean;
  hideHeader: boolean;
}

const Header = (props: HeaderProps) => {
  const { headerGroups, hasSort, hasResize, fixedColumns, light, onSortingChange, hideHeader } = props;

  return (
    <THead light={light} hideHeader={hideHeader}>
      {headerGroups.map((headerGroup, i) => (
        <tr {...headerGroup.getHeaderGroupProps()} key={`thead-tr-${i}`}>
          {headerGroup.headers.map((column, i) => {
            const customProps: { style?: React.CSSProperties } = {};
            if (fixedColumns > i && hasResize) {
              customProps.style = {
                position: 'sticky',
                zIndex: zIndexPalette.medium,
              };
            }

            return (
              <TH
                {...column.getHeaderProps(customProps)}
                width={fixedColumns > i ? column.width : 'auto'}
                left={fixedColumns > i ? column.totalLeft : undefined}
                key={`th-${i}`}
                className={`${fixedColumns > i ? 'fixed' : ''} ${fixedColumns === i + 1 ? 'last-fixed' : ''}`}
              >
                <div className="thLabel">
                  {column.canFilter && <Filter>{column.render('Filter')}</Filter>}
                  {hasSort && !column.disableSortBy && column.id !== 'selection' ? (
                    <span
                      onClick={() => {
                        column?.toggleSortBy?.(!column.isSortedDesc);
                        onSortingChange?.({ id: column.id, desc: !column.isSortedDesc });
                      }}
                    >
                      <Sorter isSorted={column.isSorted} isSortedDesc={column.isSortedDesc} />
                      {column.render('Header')}
                    </span>
                  ) : (
                    <span>{column.render('Header')}</span>
                  )}
                  {hasResize && column.canResize && (
                    <Resizer
                      lastItem={fixedColumns === i + 1 || headerGroup.headers.length === i + 1}
                      {...column.getResizerProps()}
                    />
                  )}
                </div>
              </TH>
            );
          })}
        </tr>
      ))}
    </THead>
  );
};

export default React.memo(Header);
