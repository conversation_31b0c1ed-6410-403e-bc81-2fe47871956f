import CommonButton from 'components/Buttons/CommonButton';
import TextInput from 'components/Input/TextInput';
import { Formik } from 'formik';
import lucyLogo from 'images/lucy4/lucy4cloud-logo.svg';
import netIcon from 'images/lucy4/netIQ.png';
import { Login } from 'models/request';
import React from 'react';
import styled from 'styled-components/macro';
import intlHelper from 'utils/helpers/intl.helper';
import * as Yup from 'yup';

export interface Props {
  isFormValid?: boolean;
  performLogin: Function;
  logo?: any;
  oauthLoginLink?: string;
  message?: string;
  setErrorMessage: Function;
  version?: string;
  resetPassword: () => void;
}

const Text = styled.p`
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-family: Roboto;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  font-weight: 300;
  text-align: center;
  margin-bottom: 25px;
`;

const TextReset = styled.p`
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-family: Roboto;
  font-size: ${({ theme }) => theme.fontSizePalette.body.S};
  font-weight: 300;
  text-align: center;
  margin-bottom: 25px;
  text-decoration: underline;
`;

const TextVersion = styled.p`
  position: absolute;
  bottom: 10px;
  left: 10px;
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  font-size: ${({ theme }) => theme.fontSizePalette.body.mini};
  text-align: left;
`;

const Logo = styled.img`
  max-height: 27px;
`;

const LucyLogo = styled.img`
  width: 58px;
  margin-right: 20px;
`;

const LogoContainer = styled.div`
  padding: 30px 40px 40px 40px;
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
`;

const Message = styled.p`
  margin-bottom: 15px;
  color: ${({ theme }) => theme.colorPalette.red.error};
  text-align: center;
`;

const NetIcon = styled.img`
  width: 15px;
  height: 15px;
  margin-right: 5px;
`;

const Form = styled.form`
  position: relative;
  width: 312px;
  border-radius: 4px;
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.turquoise.background};
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-bottom: 20px;

  > button#loginButton {
    height: 35px;
    width: 95px;
    border-radius: 20px;
    margin-top: 10px;
    margin-bottom: 25px;
  }
`;

const InputLoginContainer = styled.div`
  margin-bottom: 17px;

  input {
    width: 195px;
    height: 38px;
  }
`;

const NetLogin = styled.button`
  display: flex;
  align-items: center;
  margin-top: 20px;
  margin-bottom: 20px;
  height: 24px;
  width: 135px;
  border-radius: 3px;
  background-color: ${({ theme }) => theme.colorPalette.white};
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-family: Roboto;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  font-weight: 300;
  border: none;
  cursor: pointer;
  box-shadow: 0 1px 5px 0 rgba(129, 129, 129, 0.35);
`;

export default ({
  setErrorMessage,
  performLogin,
  logo,
  oauthLoginLink = '',
  message = '',
  version = '',
  resetPassword,
}: Props) => {
  const t = intlHelper.useTranslator();
  return (
    <Formik
      initialValues={{ username: '', password: '' }}
      onSubmit={(data: Login) => {
        performLogin(data);
      }}
      validationSchema={Yup.object().shape({
        username: Yup.string().required(),
        password: Yup.string().required(),
      })}
    >
      {({ values, errors, touched, handleBlur, handleChange, handleSubmit, isValid, isValidating }) => (
        <Form onSubmit={handleSubmit}>
          <LogoContainer>
            <LucyLogo src={lucyLogo} />
            <Logo src={logo} />
          </LogoContainer>
          <Text>{t('login-please')}</Text>
          <InputLoginContainer>
            <TextInput
              id="username"
              hasFeedback={
                (errors['username'] && touched['username']) || message === 'wrong_credentials' ? true : false
              }
              feedbackMessage={message === 'wrong_credentials' ? '' : errors['username']}
              value={values.username || ''}
              onChange={(e: React.ChangeEvent) => {
                handleChange(e);
                setErrorMessage('');
              }}
              onBlur={handleBlur}
              placeholder={t('username')}
              tall={true}
            />
          </InputLoginContainer>
          <InputLoginContainer>
            <TextInput
              id="password"
              hasFeedback={
                (errors['password'] && touched['password']) || message === 'wrong_credentials' ? true : false
              }
              feedbackMessage={message === 'wrong_credentials' ? '' : errors['password']}
              value={values.password || ''}
              onChange={(e: React.ChangeEvent) => {
                handleChange(e);
                setErrorMessage('');
              }}
              onBlur={handleBlur}
              placeholder={t('password')}
              type="password"
              tall={true}
            />
          </InputLoginContainer>

          <CommonButton
            id="loginButton"
            scope="primary"
            disabled={!isValid || isValidating}
            type="submit"
            value={t('login').toUpperCase()}
          />
          {message && <Message>{t(message)}</Message>}
          {oauthLoginLink && (
            <>
              <TextReset style={{ marginBottom: 25, cursor: 'pointer' }} onClick={resetPassword}>
                {t('reset-password')}
              </TextReset>
              <Text style={{ margin: 0 }}>{t('login-with')}</Text>
              <NetLogin
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  window.location.href = oauthLoginLink;
                }}
              >
                <NetIcon src={netIcon} />
                {t('net-iq-login')}
              </NetLogin>
            </>
          )}
          {version && <TextVersion>{version}</TextVersion>}
        </Form>
      )}
    </Formik>
  );
};
