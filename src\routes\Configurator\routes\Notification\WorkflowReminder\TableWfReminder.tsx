import Checkbox from 'components/Buttons/CheckboxButton';
import Table, { TableColumn } from 'components/Table/Table';
import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import utils from 'utils';
import CronCell from '../components/CronCell';
import cronstrue from 'cronstrue';
import selectors from 'state/selectors';
import { IWfReminderNotifications } from 'models/response';
import actions from 'state/actions';

const TableWfReminder = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const formMode = useSelector(selectors.configurator.getFormModeWfReminder);

  const notificationColumns: TableColumn[] = [
    { accessor: 'idUser', Header: t('idUser'), filterType: 'free' },
    { accessor: 'name', Header: t('name'), filterType: 'free' },
    {
      accessor: 'reminderSendMail',
      Header: t('reminderSendMail'),
      sortType: 'boolean',
      Cell: ({ value }: any) => {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox isActive={value} isEditable={false} />
          </div>
        );
      },
    },
    {
      accessor: 'reminderFrequency',
      Header: t('reminderFrequency'),
      Cell: ({ value }: any) => {
        const [tooltip, setTooltip] = useState('');

        return (
          <div
            onMouseEnter={() =>
              setTooltip(cronstrue.toString(value, { verbose: true, locale: navigator.language.split('-')[0] || 'en' }))
            }
            onMouseLeave={() => setTooltip('')}
            title={tooltip}
          >
            <CronCell value={value} />
          </div>
        );
      },
    },
    { accessor: 'startDeltaReminder', Header: t('startDeltaReminder'), filterType: 'free' },
    { accessor: 'endDeltaReminder', Header: t('endDeltaReminder'), filterType: 'free' },
    { accessor: 'reminderDays', Header: t('reminderDays'), filterType: 'free' },
    { accessor: 'maxReminder', Header: t('maxReminder'), filterType: 'free' },
    { accessor: 'reminderMaxDocuments', Header: t('reminderMaxDocuments'), filterType: 'free' },
    { accessor: 'reminderMaxDocumentDetails', Header: t('reminderMaxDocumentDetails'), filterType: 'free' },
    { accessor: 'idTemplateScheduledReminder', Header: t('idTemplateScheduledReminder'), filterType: 'free' },
  ];

  const wfReminderList = useSelector(selectors.configurator.getListOfWfReminderSettings);

  const onUserSelection = (rows: IWfReminderNotifications[]) => {
    dispatch(actions.configurator.setSelectedWfReminderSettings(rows[0]));
  };

  return (
    <Table
      rowId="idUser"
      hasToolbar
      columns={notificationColumns}
      rows={wfReminderList}
      hasSelection={!formMode}
      hasPagination
      hasResize
      hasSort
      hasFilter
      onSelection={(rows: IWfReminderNotifications[]) => onUserSelection(rows)}
    />
  );
};
export default TableWfReminder;
