import React from 'react';
import { useSelector } from 'react-redux';
import selectors from 'state/selectors';
import Toolbar from 'components/Toolbar';
import ToolbarButton from './ToolbarButton';

const ToolBarDetail = () => {
  const actionOpen = useSelector(selectors.configurator.selectTemplateActionsOpen);

  return (
    <Toolbar.ButtonsContainer>
      {actionOpen?.map((action, i) => (
        <ToolbarButton action={action} key={`${action}-${i}`} />
      ))}
    </Toolbar.ButtonsContainer>
  );
};

export default ToolBarDetail;
