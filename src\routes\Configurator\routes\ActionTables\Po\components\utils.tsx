/* eslint-disable max-lines */
import { Dispatch } from '@reduxjs/toolkit';
import Checkbox from 'components/Buttons/CheckboxButton';
import Icon from 'components/Input/Icon';
import { TableColumn } from 'components/Table';
import { OptionAT } from 'models/configurator';
import { CompanyActionTable, PoDefaultRow } from 'models/response';
import React from 'react';
import services from 'services';
import actions from 'state/actions';
import * as yup from 'yup';

interface DefaultPoRow {
  companyName: number;
  fullOcr: boolean;
  id: number;
  idAction: number;
  wfCausal: number;
  workflowDefinition: number;
  fluxName: string;
}

export const poSchemaNewRow = (t: (value: string) => string, selectedTable: string) => {
  return yup.object().shape({
    companyName: yup.mixed().required().notOneOf([undefined, null, ''], t('missing company')),
    wfCausal: yup.mixed().required().notOneOf([undefined, null, ''], t('missing wfCausal')),
    workflowDefinition: yup.mixed().required().notOneOf([undefined, null, ''], t('missing workflowDefinition')),
    fluxName: yup.string().required().notOneOf([undefined, null, ''], t('missing fluxName')),
    fullOcr: yup.boolean().required().notOneOf([undefined, null, ''], t('missing fullOcr')),
    microcategoryDescription:
      selectedTable === 'microcategory'
        ? yup.mixed().required().notOneOf([undefined, null, ''], t('missing microcategoryDescription'))
        : yup.mixed(),
    subjectName:
      selectedTable === 'vendor'
        ? yup.mixed().required().notOneOf([undefined, null, ''], t('missing vendorName'))
        : yup.mixed(),
  });
};

export const poSchema = (t: (value: string) => string, oldRow: PoDefaultRow | undefined) => {
  return yup
    .object()
    .shape({
      wfCasual: yup.mixed(),
      workflowDefinition: yup.mixed(),
      fullOcr: yup.boolean().required(),
    })
    .test(
      'is-same-row',
      t('The row is not modified'),
      (value: {
        companyName: string;
        fullOcr: boolean;
        id: number | string;
        microcategoryCode: number | string;
        microcategoryDescription: number | string;
        wfCausal: number | string;
        workflowDefinition: number | string;
      }) => {
        if (oldRow === undefined) return true;
        if (value === undefined) return false;
        const { wfCausal, workflowDefinition, fullOcr } = value;
        if (
          (wfCausal === oldRow.wfCausal || wfCausal === oldRow.wfIdCausal) &&
          (workflowDefinition === oldRow.wfIdWorkflowDefinition || workflowDefinition === oldRow.workflowDefinition) &&
          fullOcr === oldRow.fullOcr
        )
          return false;
        return true;
      },
    );
};

export interface Microcategory extends DefaultPoRow {
  microcategoryCode: string;
  microcategoryDescription: string;
}

export interface Vendor extends DefaultPoRow {
  subjectName: string;
  supplierCode: string;
}
type Options = {
  label: string;
  value: string | number;
  [key: string]: any;
};
export const microCategoryColumns = (
  casuals: Options[],
  definitions: Options[],
  microCategories: Options[],
  companies: Options[],
  fluxes: Options[],
  isANewRow: boolean,
  t: (key: string) => string,
): TableColumn[] => [
  {
    id: '1',
    accessor: 'idAction',
    Header: t('idAction'),
    isVisible: false,
  },
  {
    id: '2',
    accessor: 'idCompany',
    Header: t('idCompany'),
    isVisible: false,
  },
  {
    id: '3',
    accessor: 'wfIdCausal',
    Header: t('wfIdCausal'),
    isVisible: false,
  },
  {
    id: '4',
    accessor: 'wfIdTaskDefinition',
    Header: t('wfIdTaskDefinition'),
    isVisible: false,
  },
  {
    id: '5',
    accessor: 'companyName',
    Header: t('companyCode'),
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: companies,
    readOnly: !isANewRow,
  },
  {
    id: '6',
    accessor: 'microcategoryCode',
    Header: t('microcategoryCode'),
    filterType: 'free',
    inputType: 'text',
    readOnly: true,
    Cell: ({ row, value }: any) => {
      if (row.original.microcategoryDetails?.find((el: any) => el.fullOcr)) {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            {value}
            <Icon custom icon="exclamation-mark" style={{ width: 15, margin: 7 }} />
          </div>
        );
      }
      return <div style={{ display: 'flex', height: '100%' }}>{value}</div>;
    },
  },
  {
    id: '7',
    accessor: 'microcategoryDescription',
    Header: t('microcategoryDescription'),
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: microCategories,
    readOnly: !isANewRow,
  },
  {
    id: '8',
    accessor: 'wfCausal',
    Header: t('wfCausal'),
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: casuals,
    Cell: ({ value }: any) => {
      return casuals.find((el) => el.value === value)?.label ?? value;
    },
  },
  {
    id: '9',
    accessor: 'workflowDefinition',
    Header: t('workflowDefinition'),
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: definitions,
    Cell: ({ value }: any) => {
      return definitions.find((el) => el.value === value)?.label ?? value;
    },
  },
  {
    id: '10',
    accessor: 'fullOcr',
    Header: t('fullOcr'),
    filterType: 'free',
    inputType: 'checkbox',
    Cell: ({ value }: any) => {
      return (
        <div style={{ display: 'flex', height: '100%' }}>
          <Checkbox isEditable={false} isActive={value}></Checkbox>
        </div>
      );
    },
  },
  {
    id: '11',
    accessor: 'fluxName',
    Header: t('fluxName'),
    filterType: 'select',
    inputType: 'select',
    selectOptionsTypes: fluxes,
    readOnly: !isANewRow,
  },
];

export const vendorColumns = (
  casuals: Options[],
  definitions: Options[],
  companies: Options[],
  fluxes: Options[],
  isANewRow: boolean,
  subjects: Options[],
  t: (key: string) => string,
): TableColumn[] => [
  {
    id: '1',
    accessor: 'idAction',
    Header: t('idAction'),
    isVisible: false,
  },
  {
    id: '2',
    accessor: 'idAction',
    Header: t('idCompany'),
    isVisible: false,
  },
  {
    id: '3',
    accessor: 'wfIdCausal',
    Header: t('wfIdCausal'),
    isVisible: false,
  },
  {
    id: '4',
    accessor: 'wfIdTaskDefinition',
    Header: t('wfIdTaskDefinition'),
    isVisible: false,
  },
  {
    id: '5',
    accessor: 'companyName',
    Header: t('companyName'),
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: companies,
    readOnly: !isANewRow,
  },
  { accessor: 'supplierCode', Header: t('vendorCode'), filterType: 'free', inputType: 'text', readOnly: true },
  {
    accessor: 'subjectName',
    Header: t('vendorDescription'),
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: subjects,
    readOnly: !isANewRow,
  },
  {
    id: '6',
    accessor: 'wfCausal',
    Header: t('wfCausal'),
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: casuals,
  },
  {
    id: '7',
    accessor: 'workflowDefinition',
    Header: t('workflowDefinition'),
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: definitions,
  },
  {
    id: '8',
    accessor: 'fullOcr',
    Header: t('fullOcr'),
    filterType: 'free',
    inputType: 'checkbox',
    Cell: ({ value }: any) => {
      return (
        <div style={{ display: 'flex', height: '100%' }}>
          <Checkbox isEditable={false} isActive={value}></Checkbox>
        </div>
      );
    },
  },
  {
    id: '11',
    accessor: 'fluxName',
    Header: t('fluxName'),
    filterType: 'select',
    inputType: 'select',
    selectOptionsTypes: fluxes,
    readOnly: !isANewRow,
  },
];

export const subMicroCategoryColumns = (t: (key: string) => string): TableColumn[] => {
  return [
    {
      accessor: 'supplierCode',
      Header: t('supplierCode'),
      filterType: 'free',
      inputType: 'text',
      Cell: ({ row, value }: any) => {
        if (row.original.fullOcr) {
          return (
            <div style={{ display: 'flex', height: '100%' }}>
              {value}
              <Icon custom icon="exclamation-mark" style={{ width: 15, margin: 7 }} />
            </div>
          );
        }
        return <div style={{ display: 'flex', height: '100%' }}>{value}</div>;
      },
    },
    { accessor: 'supplierName', Header: t('supplierName'), filterType: 'free', inputType: 'text' },
    { accessor: 'latency', Header: t('latency'), filterType: 'free', inputType: 'text' },
    {
      accessor: 'fullOcr',
      Header: t('fullOcr'),
      filterType: 'free',
      inputType: 'checkbox',
      sortType: 'boolean',
      Cell: ({ value }: any) => {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox isEditable={false} isActive={value}></Checkbox>
          </div>
        );
      },
    },
  ];
};

export const subVendorColumns = (t: (key: string) => string): TableColumn[] => {
  return [
    { accessor: 'microcategoryCode', Header: t('microcategoryCode'), filterType: 'free', inputType: 'text' },
    {
      accessor: 'microcategoryDescription',
      Header: t('microcategoryDescription'),
      filterType: 'free',
      inputType: 'text',
    },
    { accessor: 'latency', Header: t('latency'), filterType: 'free', inputType: 'text' },
  ];
};

export function searchFunc(
  selectedTable: string,
  companiesSelected: CompanyActionTable[],
  checkMicroCategory: () => string[],
  dispatch: Dispatch<any>,
  checkVendorList: () => string[],
) {
  return async () => {
    if (selectedTable === 'microcategory') {
      const request = {
        companyNames: companiesSelected.map((el) => el.companyName),
        microcategoryCodes: checkMicroCategory(),
      };
      if (request.microcategoryCodes.length === 0) return;
      const { data } = await services.getPoMicrocategory(request);
      data.length > 0 &&
        dispatch(actions.configurator.setLeftTableRows(data.map((el, idx) => ({ id: `${idx}`, ...el }))));
      return;
    }
    if (selectedTable === 'vendor') {
      const request = {
        companyNames: companiesSelected.map((el) => el.companyName),
        supplierCodes: checkVendorList(),
      };
      if (request.supplierCodes.length === 0) return;
      const { data } = await services.getPoVendors(request);
      dispatch(actions.configurator.setLeftTableRows(data.map((el, idx) => ({ id: `${idx}`, ...el }))));
    }
  };
}

export function checkMicrocategoryFunc(selectedOptionValue: OptionAT[]) {
  return () => {
    let microcategory: string[] = [];
    if (selectedOptionValue?.length) {
      microcategory = microcategory.concat(selectedOptionValue.map((el) => el.value.toString()));
    }
    return Array.from(new Set(microcategory));
  };
}

export function checkVendorListFunc(selectedOptionValue: OptionAT[]): () => string[] {
  return () => {
    const vendor = new Set<string>();
    selectedOptionValue.forEach((el) => {
      vendor.add(el.value.toString());
    });
    return Array.from(vendor);
  };
}
