import styled from 'styled-components/macro';

export const List = styled.div`
  padding-bottom: 20px;
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.white};
`;

export const Filter = styled.div`
  padding-bottom: 10px;
  line-height: 1.5;
  font-size: ${({ theme }) => theme.fontSizePalette.small};
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey15 : theme.colorPalette.grey.grey9};
`;

export const Key = styled.div`
  font-weight: ${({ theme }) => theme.fontWeightPalette.bold};
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey15 : theme.colorPalette.grey.grey9};
`;

export const Values = styled.div`
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey15 : theme.colorPalette.grey.grey9};
`;

export const OpenLink = styled.div`
  display: inline-block;
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.turquoise.light : theme.colorPalette.turquoise.dark};
  cursor: pointer;
`;
