import styled from 'styled-components/macro';

const Textarea = styled.textarea<{ minWidth?: string; height?: string; minHeight?: string }>`
  min-width: ${({ minWidth }) => minWidth ?? '400px'};
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  width: 100%;
  min-height: ${({ minHeight }) => minHeight ?? '100px'};
  border-radius: 5px;
  resize: none;
  border: 1px solid
    ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.grey.grey5)};
  padding-left: 5px;
  font-family: Roboto;
  ${({ height }) => height && `height: ${height};`}
  &:focus {
    outline: 0;
    border: 1px solid ${({ theme }) => theme.colorPalette.turquoise.normal};
  }
  background-color: ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : 'inherit')};
  &:focus {
    border-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.turquoise.light : theme.colorPalette.turquoise.normal};
  }
  &:disabled {
    border: ${({ theme }) => (theme.colorPalette.isDarkMode ? 'none' : `1px solid ${theme.colorPalette.grey.grey5}`)};
    background-color: ${({ theme }) => theme.colorPalette.grey.grey12};
    color: ${({ theme }) => theme.colorPalette.grey.grey12};
    background-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.grey.grey3}!important;
    opacity: 0.7;
  }
`;

export default Textarea;
