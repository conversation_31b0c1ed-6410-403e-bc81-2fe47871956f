import React from 'react';
import { ReactElement } from 'react-markdown/lib/react-markdown';
import styled from 'styled-components/macro';

const TooltipContainer = styled.div`
  position: relative;
  display: inline-block;
  cursor: pointer;
  display: flex;
`;

const TooltipText = styled.div`
  visibility: hidden;
  width: max-content;
  background-color: ${({ theme }) => theme.colorPalette.turquoise.normal};
  color: ${({ theme }) => theme.colorPalette.white};
  border-radius: 6px;
  padding: 5px;
  position: absolute;
  z-index: ${({ theme }) => theme.zIndexPalette.highest};
  font-size: 13px;
  bottom: 135%;
  left: 0;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;

  /* Triangle styles */
  &::before {
    content: ' ';
    position: absolute;
    top: 100%; /* At the bottom of the tooltip */
    left: 52.5%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: ${({ theme }) => `${theme.colorPalette.turquoise.normal} transparent transparent transparent`};
  }

  ${TooltipContainer}:hover & {
    visibility: visible;
    opacity: 1;
  }
`;

export interface TooltipProps {
  text: string;
  children: ReactElement;
}

const Tooltip = ({ text, children }: TooltipProps) => {
  return (
    <TooltipContainer>
      {children}
      <TooltipText role="tooltip">{text}</TooltipText>
    </TooltipContainer>
  );
};

export default Tooltip;
