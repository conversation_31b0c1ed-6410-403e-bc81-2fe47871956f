import React from 'react';
import { useDispatch } from 'react-redux';
import { actionType } from 'utils/constants';

import utils from 'utils';
import actions from 'state/actions';

import CommonButton from 'components/Buttons/CommonButton';

const AddUser = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();

  const setFormModeToCreateUser = () => {
    dispatch(actions.configurator.setSelectedUser(null));
    setTimeout(() => {
      dispatch(actions.configurator.setFormMode('new'));
    }, 100);
  };

  return (
    <CommonButton
      action={setFormModeToCreateUser}
      disabled={!utils.user.isActionByCompanyActive(actionType.ADD_USER)}
      scope="tertiary"
      value={t('add-user')}
      icon="circle"
    />
  );
};

export default AddUser;
