import styled from 'styled-components/macro';

export const Wrapper = styled.div`
  padding: 25px;
  overflow: auto;
  height: 100%;
`;

export const Container = styled.div`
  display: flex;
  justify-content: space-between;
`;

export const Title = styled.div`
  margin-bottom: 25px;
`;

export const Left = styled.div`
  flex-basis: 60%;
  padding-right: 15px;
`;

export const RightWrapper = styled.div<{ noBasis?: boolean }>`
  flex-basis: ${({ noBasis }) => (noBasis ? '0' : '60%')};
  margin-top: 9px;
`;

export const RightTitle = styled.div`
  text-align: center;
  color: ${({ theme }) => theme.colorPalette.turquoise.dark};
  font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  border-bottom: 5px solid ${({ theme }) => theme.colorPalette.turquoise.normal};
  font-size: ${({ theme }) => theme.fontSizePalette.body.XS};
  margin-bottom: 5px;
`;

export const Right = styled.div<{ noBorder?: boolean }>`
  border: ${({ theme, noBorder }) => (noBorder ? 'none' : `1px solid ${theme.colorPalette.grey.mediumDark}`)};
  ${({ noBorder }) =>
    !noBorder &&
    `
    border-radius: 5px;
    padding: 15px 15px;
  `}
`;

export const InputContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 15px;
  color: ${({ theme }) => theme.colorPalette.grey.grey2};
`;

export const InputLabel = styled.div`
  margin-right: 15px;
  font-size: ${({ theme }) => theme.fontSizePalette.body.M};
  width: 100px;
  text-align: right;
  overflow: hidden;
  letter-spacing: -0.31px;
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
`;

export const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 145px;
  padding-bottom: 15px;
`;

export const CheckboxLabel = styled.div`
  font-size: ${({ theme }) => theme.fontSizePalette.body.M};
  width: 100px;
  margin-right: 15px;
  text-align: right;
  letter-spacing: -0.31px;
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
`;

export const ButtonsContainer = styled.div`
  padding-top: 20px;
  text-align: center;
  > button {
    margin: 0 10px;
  }
`;

export const IconsStatusContainer = styled.div`
  display: flex;
  justify-content: center;
  span > div {
    width: 30px;
    height: 30px;
  }
`;

export const CustomTitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.M};
  font-weight: bold;
`;

export const Content = styled.div`
  padding-right: 15px;
  display: flex;
  flex-direction: column;
`;

export const TablesContainer = styled.div`
  display: flex;
  justify-content: flex-start;
  gap: 15px;
`;
