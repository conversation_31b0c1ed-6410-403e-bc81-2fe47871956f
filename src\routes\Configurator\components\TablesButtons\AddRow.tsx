import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actionType } from 'utils/constants';

import utils from 'utils';
import actions from 'state/actions';
import selectors from 'state/selectors';

import CommonButton from 'components/Buttons/CommonButton';

const AddRow = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const tableRows = useSelector(selectors.configurator.selectTableRows);
  const editRowId = useSelector(selectors.configurator.selectTablesEditRowID);
  const activeTable = useSelector(selectors.configurator.selectActiveTable);
  const tablesSelectValues = useSelector(selectors.configurator.selectTablesSelectValues);
  
  const isAddDisabled = tablesSelectValues.find((el) => el.table === activeTable?.value)?.addDisabled;

  const addRow = () => {
    const idRow = -1;
    const newRows = [{ idRow, newRow: true }, ...tableRows];
    dispatch(actions.configurator.setTableMode('add'));
    dispatch(actions.configurator.setTableRows(newRows));
    dispatch(actions.configurator.setTablesEditRowID(idRow.toString()));
    dispatch(actions.configurator.setExternalTablesRowSelected([idRow]));
  };

  return (
    <CommonButton
      action={addRow}
      disabled={
        isAddDisabled || (editRowId ? true : false) || !utils.user.isActionByCompanyActive(actionType.ADD_TABLE_ROW)
      }
      scope="tertiary"
      value={t('add-row')}
      icon="circle"
    />
  );
};

export default AddRow;
