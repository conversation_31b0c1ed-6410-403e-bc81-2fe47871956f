import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import utils from 'utils';
import selectors from 'state/selectors';
import CommonButton from 'components/Buttons/CommonButton';
import { modalActionType } from 'utils/constants';
import { actionType } from 'utils/constants';

const CloneRole = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();

  const selectedUser = useSelector(selectors.configurator.getSelectedUserForRoles);
  const selectedCompanyId = useSelector(selectors.configurator.getSelectedCompanyForRoles);
  const companies = useSelector(selectors.configurator.getCompaniesForRoles);

  return (
    <CommonButton
      action={() => dispatch(actions.modal.openModal(modalActionType.configurator.CLONE_ROLE_MODAL))}
      disabled={
        !selectedUser.length ||
        selectedUser[0]?.isSubRow === true ||
        !utils.user.isActionByCompanyActive(
          actionType.CLONE_ROLE,
          companies.find((e) => e.idCompany === selectedCompanyId).name,
        )
      }
      scope="tertiary"
      value={t('CloneRole')}
      icon="circle"
    />
  );
};

export default CloneRole;
