/* eslint-disable max-lines */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import Header from '../../components/Header';
import { useDispatch, useSelector } from 'react-redux';
import { useAsync } from 'react-use';
import { Wrapper, Title, Buttons } from '../../styles';
import Pdf from 'components/Pdf/Pdf';
import utils from 'utils';
import services from 'services';
import selectors from 'state/selectors';

import { UserCompany, Error } from 'models/response';
import TwoRowsLayout from '../../../../components/Layout/TwoRowsLayout';
import Table, { TableProps } from 'components/Table';
import CommonButton from 'components/Buttons/CommonButton';
import actions from 'state/actions';

export interface CompanyOption {
  label: string;
  value: string;
}

const Errors = () => {
  const t = utils.intl.useTranslator();
  const companies: UserCompany[] = useSelector(selectors.app.getCompanies);
  const dispatch = useDispatch();
  const [selectedCompany, setSelectedCompany] = useState<{ label: string; value: string } | null>(null);
  const [firstLoad, setFirstLoad] = useState(true);
  const errors = useSelector(selectors.normalizer.getSelectedErrorRows);
  const setErrors = useCallback(
    (selected: Error[]) => {
      dispatch(actions.normalizer.setErrorRow(selected));
    },
    [dispatch],
  );
  const [selectedErrors, setSelectedErrors] = useState<number[]>([]);
  const [pdfBase64, setPdfBase64] = useState<string | null>(null);
  const { dateFormat } = useSelector(selectors.user.getUserPreference);
  const errorRows = useRef<Error[]>([]);

  const userPreference = useSelector(selectors.user.getUserPreference);
  const isModalVisible = useSelector(selectors.modal.getVisibility);

  const columns: TableProps['columns'] = [
    {
      Header: t('errorType'),
      accessor: 'errorType',
      filterType: 'select',
    },
    {
      Header: t('protocol'),
      accessor: 'protocol',
      filterType: 'free',
    },
    {
      Header: t('dateIn'),
      accessor: 'dateIn',
      filterType: 'date',
      filter: utils.date.epochFilterFunction(userPreference.timeZone),
      Cell: ({ value }: { value: number }) => utils.date.convertDate(value, userPreference.timeZone ?? 'UTC'),
      dateFormat,
    },
    {
      Header: t('dateTry'),
      accessor: 'dateTry',
      filterType: 'date',
      filter: utils.date.epochFilterFunction(userPreference.timeZone),
      Cell: ({ value }: { value: number }) => utils.date.convertDate(value, userPreference.timeZone ?? 'UTC'),
      dateFormat,
    },
    {
      Header: t('errorMessage'),
      accessor: 'errorMessage',
      filterType: 'free',
    },
    {
      Header: t('company'),
      accessor: 'company',
      filterType: 'select',
    },
    {
      Header: t('sourceChannel'),
      accessor: 'sourceChannel',
      filterType: 'free',
    },
    {
      Header: t('formAdr'),
      accessor: 'formAdr',
      filterType: 'free',
    },
    {
      Header: t('toAdr'),
      accessor: 'toAdr',
      filterType: 'free',
    },
    {
      Header: t('subject'),
      accessor: 'subject',
      filterType: 'free',
    },
    {
      Header: t('docName'),
      accessor: 'docName',
      filterType: 'free',
    },
    {
      Header: t('originalProtIn'),
      accessor: 'originalProtIn',
      filterType: 'free',
    },
    {
      Header: t('originalProtocol'),
      accessor: 'originalProtocol',
      filterType: 'free',
    },
    {
      Header: t('documentType'),
      accessor: 'documentType',
      filterType: 'free',
    },
  ];

  useAsync(async () => {
    try {
      const { data } = await services.getErrors(null);
      errorRows.current = data;
      setErrors(data);
      setFirstLoad(false);
      setPdfBase64(null);
    } catch (e) {
      console.error(e);
    }
  }, [setErrors]);

  useEffect(() => {
    if (selectedCompany === null) return;
    const filteredErrors = errorRows.current.filter((ele) => ele.company === selectedCompany.label);
    setErrors(filteredErrors);
  }, [selectedCompany, setErrors]);

  const companyOptions = useMemo(() => {
    const values = utils.input.removeDefaultValue(companies, 'idCompany');
    const options = utils.input.buildOptions(values, 'name', 'idCompany');

    return options;
  }, [companies]);

  const onSelection = (selection: Error[]) => {
    setSelectedErrors(selection.map(({ protocol }) => protocol));
  };

  useAsync(async () => {
    try {
      if (selectedErrors.length === 1) {
        const { data } = await services.getMonitorDocumentImage(selectedErrors[0]);
        setPdfBase64(data);
      } else {
        setPdfBase64(null);
      }
    } catch (err) {
      console.error(err);
      setPdfBase64(null);
    }
  }, [selectedErrors]);

  useEffect(() => {
    setPdfBase64(null);
  }, [isModalVisible, selectedCompany]);

  const deleteErrors = async () => {
    try {
      await services.deleteErrors(selectedErrors);
      const { data } = await services.getErrors(Number(selectedCompany?.value) || null);
      setErrors(data);
      setSelectedCompany(null);
      utils.app.notify('success', t('deleted'));
    } catch (e) {
      utils.app.notify('fail', t('not_deleted'));
      console.error(e);
    }
  };

  const upperRowSection = () => {
    return errors.length ? (
      <>
        <Table
          fixedColumns={3}
          rowId="protocolIn"
          hasToolbar
          columns={columns}
          rows={errors}
          onSelection={onSelection}
          hasMultiSelection
          hasPagination
          hasResize
          hasSort
          hasFilter
        />
      </>
    ) : firstLoad ? null : (
      <div>{t('no_data')}</div>
    );
  };
  const retryErrors = async (props: { selectedErrors: number[]; selectedCompany?: string }) => {
    dispatch(
      actions.modal.setModal({
        actionType: 'normalizer/loadingError',
        props,
      }),
    );
    setSelectedCompany(null);
    setSelectedErrors([]);
  };

  const bottomRowSection = () =>
    pdfBase64 && !isModalVisible ? <Pdf fixedHeight={'70vh'} docImage={pdfBase64} key="right-col" /> : null;

  return companies ? (
    <>
      <Header
        companies={companyOptions}
        companySelection={true}
        setSelectedCompany={setSelectedCompany}
        selectedCompany={selectedCompany}
      />
      <Wrapper>
        <Title>
          <h2>{t('errors_title')}</h2>
        </Title>
        <Buttons>
          <CommonButton
            scope="secondary"
            value={t('retry-all')}
            type="button"
            action={() =>
              dispatch(
                actions.modal.setModal({
                  actionType: 'normalizer/retryErrors',
                  props: {
                    errorNumber: errors.length,
                    func: () =>
                      retryErrors({
                        selectedErrors: errors.map(({ protocol }) => protocol),
                        selectedCompany: selectedCompany?.value,
                      }),
                  },
                }),
              )
            }
            disabled={errors.length === 0 || utils.user.isActionByCompanyActive('retryAllErrors') === false}
          />
          <CommonButton
            scope="secondary"
            value={t('retry')}
            type="button"
            action={() =>
              retryErrors({
                selectedErrors,
                selectedCompany: selectedCompany?.value,
              })
            }
            disabled={!selectedErrors.length}
          />
          <CommonButton
            scope="secondary"
            value={t('delete')}
            type="button"
            action={() => deleteErrors()}
            disabled={!selectedErrors.length}
          />
        </Buttons>
        <TwoRowsLayout upperRow={upperRowSection()} bottomRow={bottomRowSection()} />
      </Wrapper>
    </>
  ) : null;
};

export default Errors;
