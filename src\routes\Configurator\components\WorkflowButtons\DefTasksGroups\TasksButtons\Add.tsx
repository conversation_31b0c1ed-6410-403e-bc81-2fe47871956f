import CommonButton from 'components/Buttons/CommonButton';
import { GetCompanyWithTasks } from 'models/response';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import services from 'services';
import actions from 'state/actions';
import selectors from 'state/selectors';
import { useSWRConfig } from 'swr';
import utils from 'utils';
import { actionType } from 'utils/constants';

export default function Add() {
  const t = utils.intl.useTranslator();
  const { mutate } = useSWRConfig();
  const { tasks } = services.useGetCompanyWithTasks('getCompanyWithTasksKey');
  const editedRowId = useSelector(selectors.configurator.getEditedWfTask);
  const isWfDefinitionInEdit = useSelector(selectors.configurator.getIsWfTableEdit);
  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);

  const newTask: GetCompanyWithTasks = {
    idTask: -1,
    desc: '',
    name: '',
  };
  const dispatch = useDispatch();
  return (
    <CommonButton
      action={() => {
        if (!tasks) return;
        mutate('getCompanyWithTasksKey', [newTask, ...tasks], {
          revalidate: false,
        });
        dispatch(actions.configurator.setSelectedWfTask(-1));
        dispatch(actions.configurator.setEditedTaskWfRow(-1));
        dispatch(actions.configurator.setIsWfTableEdit(true));
      }}
      scope="tertiary"
      value={t('add-task')}
      disabled={
        editedRowId !== null ||
        isWfDefinitionInEdit ||
        !utils.user.isActionByCompanyActive(actionType.ADD_WF_TASK, companiesDefinition?.companyName)
      }
    />
  );
}
