import { ChartData, Series, Value } from 'models/response';
import React, { useState } from 'react';
import { useHistory, useLocation } from 'react-router';
import { useAsync } from 'react-use';
import { Heading, Wrapper } from './style';
import { CommonButton } from 'components/Buttons';
import Toolbar from 'components/Toolbar';
import utils from 'utils';
import { URLS } from 'utils/constants';
import ChartArea from '../../Components/ChartArea';
import TableArea from '../../Components/TableArea';
import { ChartFilters } from 'models/request';
import { createFiltersObject } from 'routes/ControlTower/utils';
import { SearchParams } from 'routes/ControlTower/interfaces';

export interface PieChartData extends ChartData {
  series: PieChartSeries[];
}

export interface LineChartData extends ChartData {
  series: LineChartSeries[];
}

export interface LineChartSeries extends Series {
  color: string;
}

export interface PieChartSeries extends Series {
  data: PieChartValue[];
}

export interface PieChartValue extends Value {
  color: string;
}

interface ChartPageProps {
  path: string;
  data?: (ChartData | PieChartData | LineChartData)[];
  loadCharts: (params: ChartFilters) => Promise<void>;
  searchParams?: SearchParams;
  message?: string;
  setMessage: (value: string) => void;
}

const ChartContent = ({ data }: { data: ChartData | PieChartData | LineChartData }) => {
  const [checkboxedRowNames, setCheckboxedRowNames] = useState<string[]>();
  const [selectedName, setSelectedName] = useState<string>();

  const { series, chartType, description } = data || {};

  if (!series) return null;

  return (
    <Wrapper>
      <Heading>{description}</Heading>
      <ChartArea
        series={series}
        checkboxedRowNames={checkboxedRowNames}
        chartType={chartType}
        selectedName={selectedName}
      />

      <TableArea
        series={series}
        checkboxedRowNames={checkboxedRowNames}
        chartType={chartType}
        setCheckboxedRowNames={setCheckboxedRowNames}
        selectedName={selectedName}
        setSelectedName={setSelectedName}
      />
    </Wrapper>
  );
};

const Chart = ({ path, loadCharts, data, searchParams, message, setMessage }: ChartPageProps) => {
  const location = useLocation();
  const history = useHistory();
  const t = utils.intl.useTranslator();

  const { idCharts, categoryId } = (location?.state as { idCharts: number[]; categoryId: number }) || {};

  useAsync(async () => {
    const params = createFiltersObject(searchParams);

    if (idCharts && idCharts.length) {
      setMessage('');
      loadCharts({ ...params, idCharts });
    }
  }, [idCharts]);

  return (
    <>
      <Toolbar.ActionBar>
        <Toolbar.ButtonsContainer>
          <CommonButton
            action={() =>
              history.push({
                pathname: `${path}/${URLS.controlTower.detailCategories}`,
                state: {
                  categoryId: categoryId,
                },
              })
            }
            scope="tertiary"
            value={t('backToDetailCategories')}
          />
        </Toolbar.ButtonsContainer>
      </Toolbar.ActionBar>

      {data && data.length <= 0 && message && <Wrapper>{message}</Wrapper>}

      {data?.map((chart) => (
        <ChartContent key={chart.id} data={chart} />
      ))}
    </>
  );
};

export default Chart;
