import React from 'react';
import { useSelector, useDispatch } from 'react-redux';

import utils from 'utils';
import actions from 'state/actions';
import selectors from 'state/selectors';

import MultiSelectInput from 'components/Input/MultiSelect';
import { Option } from 'models/configurator';
import services from 'services';
import { useHistory, useRouteMatch } from 'react-router';
import { CommonButton } from 'components/Buttons';
import { UserCompany } from 'models/response';
import { useAsync } from 'react-use';

export const WorkflowDropDowns = () => {
  const dispatch = useDispatch();
  const history = useHistory();
  const { path } = useRouteMatch();
  const selectedCompanies = useSelector(selectors.configurator.getSelectedWorkflowCompanies);
  const companies = useSelector(selectors.app.getCompanies);
  const t = utils.intl.useTranslator();
  const isInEditDefTaskGroup = useSelector(selectors.configurator.getIsWfTableEdit);
  const isInEditAssociation = useSelector(selectors.configurator.getIsActiveEditAssociation);
  const isViewMode = useSelector(selectors.configurator.getViewMode);
  const isActiveCreateNewAssociation = useSelector(selectors.configurator.getIsActiveCreateNewAssociation);
  const isActiveAddTaskToAssociation = useSelector(selectors.configurator.getIsActiveAddTaskToAssociation);
  const viewMode = useSelector(selectors.configurator.getViewMode);

  const selectCompany = (value: Option[]) => {
    const commonElement: any[] = [];
    if (value) {
      for (let i = 0; i < companies.length; i++) {
        for (let j = 0; j < value.length; j++) {
          if (companies[i].idCompany === value[j].value) {
            commonElement.push(companies[i]);
          }
        }
      }
    }
    dispatch(actions.configurator.setSelectedWorkflowCompanies(commonElement));
    if (value.length === 0) {
      // if value is 0 all companies have been deselected, so let's reset the main table
      dispatch(actions.configurator.setViewMode(true));
      dispatch(actions.configurator.setSelectedWorkflowCompanies([]));
      dispatch(actions.configurator.setCompaniesDefinition([]));
      dispatch(actions.configurator.setSelectedTab('WORKFLOW_DEFINITION_TASK_GROUP'));
      dispatch(actions.configurator.setSelectedCompanyDefinition(null));
      history.push(`${path}`);
    }
  };

  const getCompanyDefTaskGroups = async () => {
    const idCompanies = selectedCompanies.map((el) => el.idCompany);
    const { data } = await services.getCompanyDefinitionTaskGroups(idCompanies);
    dispatch(actions.configurator.setCompaniesDefinition(data));
  };

  const searchCompanies = () => {
    getCompanyDefTaskGroups();
    if (!isViewMode) {
      dispatch(actions.configurator.setViewMode(true));
      dispatch(actions.configurator.setSelectedTab('WORKFLOW_DEFINITION_TASK_GROUP'));
      dispatch(actions.configurator.setSelectedDefinitionTaskGroup('WORKFLOW_DEFINITION'));
      history.push(`${path}`);
    }
  };

  useAsync(async () => {
    if (viewMode && selectedCompanies.length) {
      getCompanyDefTaskGroups();
    }
  }, [viewMode]);

  const companyOptions = (data: UserCompany[]) => {
    const values = utils.input.removeDefaultValue(data, 'idCompany');
    const options = utils.input.buildOptions(values, 'name', 'idCompany');

    return options;
  };

  return (
    <>
      <MultiSelectInput
        labelledBy="company"
        value={utils.input.buildOptions(selectedCompanies, 'name', 'idCompany')}
        options={companyOptions(companies)}
        onChange={selectCompany}
        hasSelectAll={false}
        style={{ margin: '0px 15px' }}
        disabled={
          isInEditDefTaskGroup || isInEditAssociation || isActiveCreateNewAssociation || isActiveAddTaskToAssociation
        }
      />
      <CommonButton
        value={t('search')}
        scope="primary"
        disabled={
          selectedCompanies.length === 0 ||
          isInEditDefTaskGroup ||
          isInEditAssociation ||
          isActiveCreateNewAssociation ||
          isActiveAddTaskToAssociation
        }
        action={searchCompanies}
      />
    </>
  );
};
