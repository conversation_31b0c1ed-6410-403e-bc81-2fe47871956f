import React from 'react';
import { useSelector, useDispatch } from 'react-redux';

import utils from 'utils';
import actions from 'state/actions';
import selectors from 'state/selectors';

import Dropdown from 'components/Input/Dropdown';
import { actionTableViews, Option } from 'models/configurator';
import { CompanyActionTable } from 'models/response';
import services from 'services';

import MultiSelectInput from 'components/Input/MultiSelect';
import { useAsync } from 'react-use';
import { CommonButton } from 'components/Buttons';
import styled from 'styled-components/macro';

const SelectDiv = styled.div`
  display: flex;
  align-items: center;
  gap: 15px;
`;

export const ActionTablesDropdowns = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const activeView = useSelector(selectors.configurator.selectActionsTableView);
  const selectedCompanies = useSelector(selectors.configurator.getSelectedCompanies);
  const poEditRowId = useSelector(selectors.configurator.getEditedRowId);
  const isDisabled = useSelector(selectors.app.getDisabledDropdown);
  const idProgram = useSelector(selectors.app.getProgramCode) || -1;
  const idUser = useSelector(selectors.user.getIdUser) || -1;
  const options = [
    { label: t('po'), value: actionTableViews.PO },
    { label: t('no_po'), value: actionTableViews.NO_PO },
    { label: t('corporate_approval'), value: actionTableViews.CORPORATE_APPROVAL },
  ];
  const [allCompanies, setAllCompanies] = React.useState<CompanyActionTable[]>([]);

  const getHeader = async (value: string) => {
    if (value === actionTableViews.CORPORATE_APPROVAL) {
      const { data } = await services.getCorporateApproval();
      dispatch(
        actions.configurator.setCorporateApprovalList(
          data.map((row, id) => {
            return {
              ...row,
              id: `${id}`,
            };
          }),
        ),
      );
    }
  };
  useAsync(async () => {
    const { data } = await services.getConfigurationsByCompanies(idUser, idProgram);
    setAllCompanies(
      utils.input.removeDefaultValue(data, 'idCompany').map((row) => {
        return {
          companyName: row.name,
          idCompany: row.idCompany,
          microCategoryList: [],
          wSubjectsList: [],
        };
      }),
    );
  }, [idUser, idProgram]);

  const onChangeOption = (option: { label: string; value: string }) => {
    const { value } = option;
    dispatch(actions.configurator.setATCompanies([]));
    dispatch(actions.configurator.setSelectedAtCompanies([]));
    dispatch(actions.configurator.setActionTablesView(option));

    // Reset values
    dispatch(actions.configurator.setLeftTableRows([]));
    dispatch(actions.configurator.setSelectedRow(null));
    // nopo
    dispatch(actions.configurator.setSelectedOptionValue([]));
    dispatch(actions.configurator.setNoPoLeftTableRows([]));
    dispatch(actions.configurator.setNoPoSelectedRow(null));

    getHeader(value);
  };

  const selectCompany = (value: Option[]) => {
    let commonElement: CompanyActionTable[] = [];

    if (value.length > 0) {
      const selectedCompanyIds = new Set(value.map((option) => option.value));
      commonElement = allCompanies.filter((company) => selectedCompanyIds.has(company.idCompany));
    } else {
      dispatch(actions.configurator.setATCompanies([]));
      dispatch(actions.configurator.setSelectedOptionValue([]));
      dispatch(actions.configurator.setLeftTableRows([]));
      dispatch(actions.configurator.setNoPoLeftTableRows([]));
      dispatch(actions.configurator.setSelectedRow(null));
      dispatch(actions.configurator.setNoPoSelectedRow(null));
      dispatch(actions.configurator.setSelectedTableToSearch('microcategory'));
    }

    dispatch(actions.configurator.setSelectedAtCompanies(commonElement));
  };

  return (
    <>
      <Dropdown
        options={options}
        value={activeView}
        margin="0px 15px"
        onChange={(value) => {
          onChangeOption(value);
        }}
        disabled={poEditRowId !== undefined || isDisabled}
      />
      {activeView && activeView.value !== actionTableViews.CORPORATE_APPROVAL && (
        <SelectDiv>
          <MultiSelectInput
            labelledBy="company"
            value={utils.input.buildOptions(selectedCompanies, 'companyName', 'idCompany')}
            options={allCompanies ? utils.input.buildOptions(allCompanies, 'companyName', 'idCompany') : []}
            onChange={selectCompany}
            hasSelectAll={false}
            disabled={allCompanies.length === 0 || poEditRowId !== undefined || isDisabled}
          />
          <CommonButton
            value={'search'}
            action={async () => {
              const { data } = await services.getHeaderConfig(
                activeView.value === actionTableViews.NO_PO ? 'NOPO' : activeView.value,
                selectedCompanies.map((company) => company.idCompany).join(','),
              );
              dispatch(actions.configurator.setATCompanies(data));
              dispatch(actions.configurator.setSelectedAtCompanies(data));
            }}
            disabled={selectedCompanies.length === 0}
          />
        </SelectDiv>
      )}
    </>
  );
};
