/* eslint-disable max-lines */
import axios from 'axios';
import Checkbox from 'components/Buttons/CheckboxButton';
import Table, { OriginalRow, TableColumn } from 'components/Table';
import {
  EditWorkflowDefinition,
  NewWorkflowDefinition,
  WorkflowCompaniesDefinitionList,
  WorkflowTaskList,
} from 'models/response';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import services from 'services';
import actions from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import utils from 'utils';
import * as yup from 'yup';
import { suspendTypes } from '../../../utils';
import { mutate } from 'swr';

const CustomTitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.M};
  font-weight: bold;
`;
const Wrapper = styled.div`
  margin-top: 20px;
`;

interface TableList {
  idDefinition: number;
  companyName: string;
  wfName: string;
  suspendType: number | null;
  noteMandatory: boolean;
  currencyExchange: boolean;
  takeNextSingleTask: boolean;
  taskOnMinCausalWeight: boolean;
  taskList: WorkflowTaskList[];
}

const Definitions = () => {
  const t = utils.intl.useTranslator();

  const dispatch = useDispatch();

  const [list, setList] = useState<TableList[]>([]);

  const listOfDefinitions = useSelector(selectors.configurator.getListOfDefinitionsForCompanySelected);
  const editedRowId = useSelector(selectors.configurator.getEditedRowIdWfDefinition);
  const selectedCompanyDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  const selectedDefinitionFromCompanySelected = useSelector(
    selectors.configurator.getSelectedDefinitionFromCompanySelected,
  );
  const isWfDefinitionInEdit = useSelector(selectors.configurator.getIsWfTableEdit);

  const suspendTypesList = suspendTypes('workflowDefinition');

  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);

  const { listOfDefinitionsForAssociations, revalidate } = services.useGetCompanyDefinitionsForAssociation(
    'getCompanyDefinitionForAssociationsKey',
    companiesDefinition?.idCompany,
  );

  const { associations } = services.useGetDefinitionTaskGroupsAssociations(
    'getDefinitionTaskGroupsAssociationsKey',
    companiesDefinition?.idCompany,
  );

  useEffect(() => {
    revalidate();
  }, [revalidate]);

  useEffect(() => {
    setList(listOfDefinitions);
  }, [listOfDefinitions]);

  const checkProgram = (
    id: number,
    accessor: 'noteMandatory' | 'currencyExchange' | 'takeNextSingleTask' | 'taskOnMinCausalWeight',
  ) => {
    const listCopy = list.map((obj) => {
      if (obj.idDefinition === id) {
        return {
          ...obj,
          [accessor]: !obj[accessor],
        };
      }
      return obj;
    });

    setList(listCopy);
  };

  const definitionColumns: TableColumn[] = [
    {
      accessor: 'idDefinition',
      Header: t('idDefinition'),
    },
    {
      accessor: 'wfName',
      Header: t('wfName'),
      inputType: 'text',
    },
    {
      accessor: 'suspendType',
      Header: t('suspendType'),
      inputType: 'select',
      selectOptionsTypes: suspendTypesList,
      hasLocalKeys: ['LocalizableStringFe', 'workflowDefinition'],
    },
    {
      accessor: 'noteMandatory',
      Header: t('noteMandatory'),
      inputType: 'checkbox',
      sortType: 'boolean',
      Cell: ({ row, value }: any) => {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox
              isEditable={isWfDefinitionInEdit}
              isActive={value}
              action={() => {
                checkProgram(row?.id, 'noteMandatory');
              }}
            ></Checkbox>
          </div>
        );
      },
    },
    {
      accessor: 'currencyExchange',
      Header: t('currencyExchange'),
      inputType: 'checkbox',
      sortType: 'boolean',
      Cell: ({ row, value }: any) => {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox
              isEditable={isWfDefinitionInEdit}
              isActive={value}
              action={() => {
                checkProgram(row?.id, 'currencyExchange');
              }}
            ></Checkbox>
          </div>
        );
      },
    },
    {
      accessor: 'takeNextSingleTask',
      Header: t('takeNextSingleTask'),
      inputType: 'checkbox',
      sortType: 'boolean',
      Cell: ({ row, value }: any) => {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox
              isEditable={isWfDefinitionInEdit}
              isActive={value}
              action={() => {
                checkProgram(row?.id, 'takeNextSingleTask');
              }}
            ></Checkbox>
          </div>
        );
      },
    },
    {
      accessor: 'taskOnMinCausalWeight',
      Header: t('taskOnMinCausalWeight'),
      inputType: 'checkbox',
      sortType: 'boolean',
      Cell: ({ row, value }: any) => {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox
              isEditable={isWfDefinitionInEdit}
              isActive={value}
              action={() => {
                checkProgram(row?.id, 'taskOnMinCausalWeight');
              }}
            ></Checkbox>
          </div>
        );
      },
    },
  ];

  const clearState = () => {
    dispatch(actions.configurator.setIsWfTableEdit(false));
    dispatch(actions.configurator.setEditedRowIdWfDefinition(null));
  };

  const confirmCreate = async (newRow: OriginalRow) => {
    try {
      const definition: NewWorkflowDefinition = {
        wfName: newRow.wfName,
        idCompany: selectedCompanyDefinition?.idCompany || null,
        suspendType: newRow.suspendType,
        noteMandatory: newRow.noteMandatory,
        currencyExchange: newRow.currencyExchange,
        takeNextSingleTask: newRow.takeNextSingleTask,
        taskOnMinCausalWeight: newRow.taskOnMinCausalWeight,
      };
      const { data } = await services.createWfDefinition(definition);

      const list = listOfDefinitions.filter((el) => el.idDefinition !== -1);

      const { wfName, suspendType, noteMandatory, currencyExchange, takeNextSingleTask, taskOnMinCausalWeight } =
        definition;

      const record: WorkflowCompaniesDefinitionList = {
        idDefinition: data,
        companyName: selectedCompanyDefinition?.companyName || '',
        taskList: [],
        currencyExchange: currencyExchange,
        noteMandatory: noteMandatory,
        suspendType: suspendType,
        takeNextSingleTask: takeNextSingleTask,
        taskOnMinCausalWeight: taskOnMinCausalWeight,
        wfName: wfName,
      };

      dispatch(actions.configurator.setListOfDefinitionsForCompanySelected([record, ...list]));
      const newListOfDefinitionsForAssociations = listOfDefinitionsForAssociations?.concat({
        idDefinition: data,
        wfName: wfName,
        wfDesc: wfName,
        idCompany: companiesDefinition?.idCompany ?? -1,
        suspendType: suspendType,
        noteMandatory: noteMandatory,
        currencyExchange: currencyExchange,
        takeNextSingleTask: takeNextSingleTask,
        taskOnMinCausalWeight: taskOnMinCausalWeight,
      });
      mutate('getCompanyDefinitionForAssociationsKey', newListOfDefinitionsForAssociations, {
        revalidate: false,
      });
      clearState();
      utils.app.notify('success', t('wf-definition-created'));
    } catch (error) {
      if (axios.isAxiosError(error)) {
        utils.app.notify('fail', `${t('error')} ${error.response}`);
        return;
      }
      console.error(error);
    }
  };

  const confirmEdit = async (row: OriginalRow) => {
    try {
      if (selectedDefinitionFromCompanySelected) {
        const definitionEdited: EditWorkflowDefinition = {
          idDefinition: selectedDefinitionFromCompanySelected?.idDefinition,
          wfName: row.wfName,
          idCompany: selectedCompanyDefinition?.idCompany || null,
          suspendType: row.suspendType,
          noteMandatory: row.noteMandatory,
          currencyExchange: row.currencyExchange,
          takeNextSingleTask: row.takeNextSingleTask,
          taskOnMinCausalWeight: row.taskOnMinCausalWeight,
        };

        await services.editWfDefinition(definitionEdited);

        const {
          idDefinition,
          wfName,
          suspendType,
          noteMandatory,
          currencyExchange,
          takeNextSingleTask,
          taskOnMinCausalWeight,
        } = definitionEdited;

        const record: WorkflowCompaniesDefinitionList = {
          idDefinition: idDefinition,
          companyName: selectedCompanyDefinition?.companyName || '',
          taskList: [],
          currencyExchange: currencyExchange,
          noteMandatory: noteMandatory,
          suspendType: suspendType,
          takeNextSingleTask: takeNextSingleTask,
          taskOnMinCausalWeight: taskOnMinCausalWeight,
          wfName: wfName,
        };

        const list = listOfDefinitions?.map((def) => {
          if (def.idDefinition === idDefinition) {
            return record;
          }
          return def;
        });

        const newListDefinitionsAssociations = listOfDefinitionsForAssociations?.map((def) => {
          if (def.idDefinition === selectedDefinitionFromCompanySelected.idDefinition) {
            return {
              idDefinition: idDefinition,
              wfName: wfName,
              wfDesc: wfName,
              idCompany: companiesDefinition?.idCompany || undefined,
              suspendType: suspendType,
              noteMandatory: noteMandatory,
              currencyExchange: currencyExchange,
              takeNextSingleTask: takeNextSingleTask,
              taskOnMinCausalWeight: taskOnMinCausalWeight,
            };
          }
          return def;
        });
        dispatch(actions.configurator.setListOfDefinitionsForCompanySelected(list));

        mutate('getCompanyDefinitionForAssociationsKey', newListDefinitionsAssociations, {
          revalidate: false,
        });

        mutate(
          'getDefinitionTaskGroupsAssociationsKey',
          (associations ?? []).map((association) => {
            if (association.idDefinition === selectedDefinitionFromCompanySelected.idDefinition) {
              return {
                ...association,
                definitionName: wfName,
              };
            }
            return association;
          }),
          {
            revalidate: false,
          },
        );

        clearState();
        utils.app.notify('success', t('wf-definition-edited'));
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        utils.app.notify('fail', `${t('error')} ${error.response}`);
        return;
      }
      console.error(error);
    }
  };

  const onCancel = () => {
    const newRows = listOfDefinitions.filter((el) => el.idDefinition !== -1);
    dispatch(actions.configurator.setListOfDefinitionsForCompanySelected(newRows));
    clearState();
  };

  const saveSchema = yup.object().shape({
    wfName: yup.string().required(),
    suspendType: yup.number().required(),
  });

  return (
    <Wrapper>
      {list.length > 0 ? (
        <>
          <CustomTitle>{t('definitions')}</CustomTitle>
          <Table
            rowId="idDefinition"
            columns={definitionColumns}
            hasToolbar
            rows={list}
            hasSelection
            onSelection={(rows: TableList[]) => {
              dispatch(actions.configurator.setSelectedDefinitionFromCompanySelected(rows.length ? rows[0] : null));
            }}
            hasPagination
            hasResize
            hasSort
            hasFilter
            isEditable
            editRowID={editedRowId?.toString()}
            editRow={listOfDefinitions.find((el) => el.idDefinition === editedRowId)}
            initialSelection={editedRowId ? [editedRowId] : undefined}
            onCancel={onCancel}
            onSave={(row) => (editedRowId === -1 ? confirmCreate(row) : confirmEdit(row))}
            onSaveValidation={saveSchema}
          />
        </>
      ) : (
        <div>{t('no_data')}</div>
      )}
    </Wrapper>
  );
};
export default Definitions;
