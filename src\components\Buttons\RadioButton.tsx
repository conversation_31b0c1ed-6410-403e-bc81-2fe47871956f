import React from 'react';
import {  fontSizePalette } from 'utils/styleConstants';

import { createMuiTheme, FormControl, FormControlLabel, MuiThemeProvider, Radio, RadioGroup } from '@material-ui/core';
import { useTheme } from 'providers/ThemeProvider';

interface Option {
  value: string | number;
  label?: string;
  disabled?: boolean;
}

export interface RadioButtonProps {
  options: Array<Option>;
  onChange?: (value: string) => void;
  value?: string | number;
  inline?: boolean;
}



const RadioButton = ({ options, onChange, value = '', inline = false }: RadioButtonProps) => {
  const [internalValue, setInternalValue] = React.useState(value);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange && onChange(e.target.value);
    setInternalValue(e.target.value);
  };

  React.useEffect(() => {
    setInternalValue(value);
  }, [value]);

  const handleCLick = (value: string) => {
    onChange && onChange(value);
    setInternalValue(value);
  };
  const styleTheme = useTheme();

  const theme = createMuiTheme({
    palette: {
      secondary: { main: styleTheme.theme.colorPalette.turquoise.normal },
    },
    overrides: {
      MuiFormControlLabel: {
        label: {
          fontFamily: 'Roboto',
          fontSize: fontSizePalette.body.XS,
          fontWeight: 300,
          color: styleTheme.theme.colorPalette.grey.grey9,
        },
      },
    },
  });

  return (
    <MuiThemeProvider theme={theme}>
      <FormControl>
        <RadioGroup row={inline} aria-label="radio-group" name="radio" value={internalValue} onChange={handleChange}>
          {options?.map((el, index) => (
            <FormControlLabel
              disabled={el.disabled}
              key={index}
              value={el.value}
              control={<Radio disableRipple onClick={() => handleCLick(`${el.value}`)} />}
              label={el.label}
            />
          ))}
        </RadioGroup>
      </FormControl>
    </MuiThemeProvider>
  );
};

export default RadioButton;
