import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components/macro';

import utils from 'utils';
import actions from 'state/actions';
import CommonButton from 'components/Buttons/CommonButton';
import Header from 'components/Modal/Header';
import selectors from 'state/selectors';
import axios from 'axios';

import Dropdown from 'components/Input/Dropdown';
import services from 'services';
import { useSWRConfig } from 'swr';
import { transformedRows } from 'routes/Configurator/routes/Workflow/routes/WorkflowCompanyDetails/tabs/Associations/utils';

export const ButtonContainer = styled.div`
  text-align: center;
  button {
    margin: 10px;
  }
  margin-top: 10px;
`;

const CloneAssociationModal = () => {
  const dispatch = useDispatch();

  const t = utils.intl.useTranslator();

  const [selectedDefinition, setSelectedDefinition] = useState<{ label: string; value: number } | null>(null);

  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  const selectedAssociation = useSelector(selectors.configurator.getSelectedDefinitionTaskGroupsAssociations);

  const { associations } = services.useGetDefinitionTaskGroupsAssociations(
    'getDefinitionTaskGroupsAssociationsKey',
    companiesDefinition?.idCompany,
  );
  const { listOfDefinitionsForAssociations } = services.useGetCompanyDefinitionsForAssociation(
    'getCompanyDefinitionForAssociationsKey',
    companiesDefinition?.idCompany,
  );
  const { mutate } = useSWRConfig();

  const cloneConfirm = async () => {
    try {
      if (selectedAssociation?.idDefinition) {
        const { data } = await services.cloneDefinitionTaskAssociations(
          selectedAssociation?.idDefinition,
          selectedDefinition?.value || null,
        );

        dispatch(actions.configurator.setSelectedDefinitionTaskGroupsAssociations(null));

        const transformedList = transformedRows([data, ...(associations || [])]);

        mutate('getDefinitionTaskGroupsAssociationsKey', transformedList, {
          revalidate: false,
        });

        mutate(
          'getCompanyDefinitionForAssociationsKey',
          (listOfDefinitionsForAssociations ?? []).filter((def) => def.idDefinition !== selectedDefinition?.value),
          {
            revalidate: false,
          },
        );

        utils.app.notify('success', t('wf-association-cloned'));
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        utils.app.notify('fail', `${t('error')} ${error.response}`);
        return;
      }
      console.error(error);
    } finally {
      dispatch(actions.modal.closeModal());
    }
  };

  return (
    <>
      <Header title={t('clone-wf-association')} subtitle={t('clone-wf-association-subtitle')} />
      <Dropdown
        onChange={(value) => setSelectedDefinition(value)}
        options={utils.input.buildOptions(listOfDefinitionsForAssociations ?? [], 'wfName', 'idDefinition')}
        value={selectedDefinition}
        margin="0px 15px"
      />
      <ButtonContainer>
        <CommonButton value={t('continua')} action={() => cloneConfirm()} disabled={!selectedDefinition} />
        <CommonButton
          scope="secondary"
          value={t('close')}
          action={() => {
            dispatch(actions.modal.closeModal());
          }}
        />
      </ButtonContainer>
    </>
  );
};

export default CloneAssociationModal;
