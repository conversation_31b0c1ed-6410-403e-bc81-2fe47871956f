/* eslint-disable max-lines */
import React, { useCallback, useEffect, useState } from 'react';
import { useHistory, useLocation, useRouteMatch } from 'react-router';
import { useAsync } from 'react-use';
import Routes from './routes';
import { useDispatch, useSelector } from 'react-redux';
import { Option } from 'react-multi-select-component';
import Header from './Components/Header';
import { RangeDate } from './Components/DateFilters';
import Modal from 'components/Modal';
import ModalsContent from './Components/ModalsContent';
import selectors from 'state/selectors';
import action from 'state/actions';
import { UserCompany, DocumentType, GeoCompany, SummaryCategory, DetailCategory, ChartData } from 'models/response';
import helper from 'utils';
import utils from 'utils';
import { actionType, CHANNELS, modalActionType, moduleProgramCode } from 'utils/constants';
import { createFiltersObject } from './utils';
import { SearchParams, ViewMode } from './interfaces';
import { CategoryDetailsFilters, ChartFilters, SummaryKpiFilters } from 'models/request';
import {
  getCategoryDetails,
  getCharts,
  getDocumentTypes,
  getGbsCompanies,
  getKpiSummary,
  getUserCompanies,
} from './helpers';
import { RootState } from 'models';
import { LineChartData, PieChartData } from './routes/Chart';
import { DrillDownFilters } from './Components/ModalsContent/DrillDownKpiModal';

const ControlTower = () => {
  const t = utils.intl.useTranslator();

  const idProgram = useSelector(selectors.app.getProgramCode) || -1;
  const idUser = useSelector(selectors.user.getIdUser) || -1;

  const dispatch = useDispatch();
  const location = useLocation();
  const history = useHistory();
  const { path } = useRouteMatch();

  const { idCharts, categoryId } = (location?.state as { idCharts: number[]; categoryId: number }) || {};
  const { val: allInOneViewDefault } = useSelector((store: RootState) =>
    selectors.app.getConfig(store, actionType.ENABLE_ALL_IN_ONE_VIEW),
  );

  const [hierarchicalCategories, setHierarchicalCategories] = useState<DetailCategory[]>([]);
  const [detailedCategories, setDetailedCategories] = useState<DetailCategory[]>([]);
  const [viewMode, setViewMode] = useState<ViewMode>(allInOneViewDefault ? 'table' : 'list');

  const [summaryCategories, setSummaryCategories] = useState<SummaryCategory[]>([]);

  const [companies, setCompanies] = useState<UserCompany[]>([]);
  const [types, setTypes] = useState<DocumentType[]>([]);
  const [gbsList, setGbsList] = useState<GeoCompany[]>([]);

  const dateToDefault = new Date();
  const dateFromDefault = utils.date.getFirstDayOfPreviousMonth(new Date());
  const channelsDefault = CHANNELS.filter(({ value }) => [1, 3, 4, 5].includes(value));
  const typeNamesDefault = ['supplier_invoice', 'credit_note', 'downpayment', 'payment_request'];

  const [rangeDate, setRangeDate] = useState<RangeDate>({ from: dateFromDefault, to: dateToDefault });

  const [selectedGbsList, setSelectedGbsList] = useState<Option[]>();
  const [selectedCompanies, setSelectedCompanies] = useState<Option[]>();
  const [selectedTypes, setSelectedTypes] = useState<Option[]>();
  const [defaultSelectedTypes, setDefaultSelectedTypes] = useState<Option[]>();
  const [selectedChannels, setSelectedChannels] = useState<Option[] | undefined>(channelsDefault);

  const [chartDataArray, setChartDataArray] = useState<(ChartData | PieChartData | LineChartData)[]>([]);
  const [searchParams, setSearchParams] = useState<SearchParams>();

  const [message, setMessage] = useState('');

  const isModalVisible = useSelector(selectors.modal.getVisibility);
  const closeModal = () => dispatch(action.modal.closeModal());

  const formattedDateFrom = rangeDate.from ? helper.date.formatDateForDb(rangeDate.from) : '';
  const formattedDateTo = rangeDate.to ? helper.date.formatDateForDb(rangeDate.to) : '';

  const optionsObject = {
    dateFrom: formattedDateFrom,
    dateTo: formattedDateTo,
    companies: selectedCompanies,
    types: selectedTypes,
    channels: selectedChannels,
    gbsList: selectedGbsList,
  };

  const openSummaryPage = () => history.push(path);

  const loadCharts = async (params: ChartFilters) => {
    setChartDataArray([]);
    const dataChartArray = await getCharts(params);
    setChartDataArray(dataChartArray);
  };

  const exportReport = async (idReport: number) => {
    const { dateFrom, dateTo, idCompanyList, idDocTypeList, incChannelList } = createFiltersObject(searchParams);
    const filters = { dateFrom, dateTo, idCompanyList, idDocTypeList, incChannelList, idReport };

    dispatch(action.modal.openModal(modalActionType.controlTower.EXPORT_KPI_REPORT, { filters }));
  };
  const drillDownProtocols = async (idMetric: number, idGeoList: any[] | null = null) => {
    const { dateFrom, dateTo, idDocTypeList, incChannelList, idCompanyList, idGbsList } =
      createFiltersObject(searchParams);
    const filters: DrillDownFilters = {
      dateFrom,
      dateTo,
      idDocTypeList,
      incChannelList,
      idCompanyList,
      idGbsList,
      idGeoList,
      idMetric,
    };

    dispatch(action.modal.openModal(modalActionType.controlTower.DRILL_DOWN_KPI, { filters }));
  };
  const loadSummaryData = useCallback(async (params: SummaryKpiFilters) => {
    setSummaryCategories([]);

    setHierarchicalCategories([]);
    setDetailedCategories([]);

    const summaryCategoriesData = await getKpiSummary(params);
    setSummaryCategories(summaryCategoriesData);
  }, []);

  const loadCategoryDetails = useCallback(async (params: CategoryDetailsFilters) => {
    const data = await getCategoryDetails(params);

    if (params.allInOneView) {
      setDetailedCategories(data);
    } else {
      setHierarchicalCategories(data);
    }
  }, []);

  const clearAllFilters = () => {
    setSelectedGbsList(undefined);
    setSelectedCompanies(undefined);
    setSelectedTypes(undefined);
    setSelectedChannels(undefined);
    setRangeDate({ from: null, to: null });

    setHierarchicalCategories([]);
    setDetailedCategories([]);

    setSummaryCategories([]);

    setMessage(t('page_filter_clear_filters_message.localKeyVal'));
  };

  useEffect(() => {
    setViewMode(allInOneViewDefault ? 'table' : 'list');
  }, [allInOneViewDefault]);

  useAsync(async () => {
    if (idProgram === moduleProgramCode.controlTower) {
      const userCompanies = await getUserCompanies(idUser, idProgram);
      setCompanies(utils.input.removeDefaultValue(userCompanies, 'idCompany'));

      const gbsCompanies = await getGbsCompanies();
      setGbsList(gbsCompanies);

      const types = await getDocumentTypes();
      setTypes(utils.input.removeDefaultValue(types, 'idDocType'));

      const typesDefault = types.filter(({ fixedName }) => typeNamesDefault.includes(fixedName));
      setSelectedTypes(utils.input.buildOptions(typesDefault, 'docName', 'idDocType'));
      setDefaultSelectedTypes(utils.input.buildOptions(typesDefault, 'docName', 'idDocType'));
    }
  }, [idProgram]);

  useAsync(async () => {
    if (defaultSelectedTypes) {
      const params = createFiltersObject(optionsObject);
      loadSummaryData(params);

      setSearchParams(optionsObject);
    }
  }, [defaultSelectedTypes]);

  const handleSearch = () => {
    setMessage('');

    const params = createFiltersObject(optionsObject);

    loadSummaryData(params);
    openSummaryPage();

    setSearchParams(optionsObject);
  };

  const handleRefresh = () => {
    setMessage('');

    const params = createFiltersObject(optionsObject);

    setHierarchicalCategories([]);
    setDetailedCategories([]);

    if (categoryId) {
      loadCategoryDetails({ ...params, idCategory: categoryId, allInOneView: viewMode === 'table' });
    }

    if (idCharts) {
      loadCharts({ ...params, idCharts });
    }

    setSearchParams(optionsObject);
  };

  const restoreDefaultFilters = () => {
    if (!defaultSelectedTypes) return;

    setSelectedGbsList(undefined);
    setSelectedCompanies(undefined);

    setRangeDate({ from: dateFromDefault, to: dateToDefault });
    setSelectedChannels(channelsDefault);
    setSelectedTypes(defaultSelectedTypes);

    setHierarchicalCategories([]);
    setDetailedCategories([]);

    setSummaryCategories([]);

    setChartDataArray([]);

    setMessage(t('page_filter_reset_filters_message.localKeyVal'));
  };

  return (
    <>
      <Header
        rangeDate={rangeDate}
        setRangeDate={setRangeDate}
        gbsList={gbsList}
        selectedGbsList={selectedGbsList}
        setSelectedGbsList={setSelectedGbsList}
        companies={companies}
        selectedCompanies={selectedCompanies}
        setSelectedCompanies={setSelectedCompanies}
        types={types}
        selectedTypes={selectedTypes}
        setSelectedTypes={setSelectedTypes}
        channels={CHANNELS}
        selectedChannels={selectedChannels}
        setSelectedChannels={setSelectedChannels}
        handleSearch={handleSearch}
        handleClear={clearAllFilters}
        handleRefresh={handleRefresh}
        handleRestoreDefaultFilters={restoreDefaultFilters}
      />
      <Routes
        data={{ summaryCategories, hierarchicalCategories, detailedCategories, chartDataArray }}
        loadCharts={loadCharts}
        loadCategoryDetails={loadCategoryDetails}
        loadSummaryData={loadSummaryData}
        exportReport={exportReport}
        searchParams={searchParams}
        setViewMode={setViewMode}
        viewMode={viewMode}
        drillDownProtocols={drillDownProtocols}
        message={message}
        setMessage={setMessage}
      />
      <Modal onClose={closeModal} open={isModalVisible}>
        <ModalsContent />
      </Modal>
    </>
  );
};

export default ControlTower;
