import iconSearch from 'images/lucy4/icon_search_bar.svg';
import iconClear from 'images/lucy4/icon_x.svg';
import React, { useEffect, useState } from 'react';
import styled from 'styled-components/macro';

const SearchBar = styled.input<{ small: boolean }>`
  height: ${(props) => (props.small ? '18px' : '30px')};
  line-height: ${(props) => (props.small ? '18px' : '30px')};
  width: inherit;
  border-style: solid;
  border-width: 1px;
  border-color: ${({ small, theme }) =>
    small
      ? 'transparent'
      : theme.colorPalette.isDarkMode
      ? theme.colorPalette.grey.grey3
      : theme.colorPalette.grey.grey7};
  border-radius: ${(props) => (props.small ? '12px' : '25px')};
  font-size: ${({ small, theme }) => (small ? theme.fontSizePalette.body.XXS : theme.fontSizePalette.body.XS)};
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.grey.grey9};
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.white};
  padding: 0 0 0 10px;
  font-family: Roboto;
  padding-right: 22px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;

  &:focus {
    outline: 0;
    border: 1px solid ${({ theme }) => theme.colorPalette.turquoise.normal};
  }

  &::placeholder {
    font-weight: light;
    color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey6 : theme.colorPalette.grey.grey7};
  }
`;
const SearchContainer = styled.div<{ fullWidth: boolean }>`
  position: relative;
  width: ${({ fullWidth }) => (fullWidth ? '100%' : '200px')};
  background-color: inherit;
`;

const SearchIcon = styled.img<{ small: boolean }>`
  position: absolute;
  width: ${(props) => (props.small ? '12px' : '20px')};
  height: ${(props) => (props.small ? '12px' : '20px')};
  right: ${(props) => (props.small ? '8px' : '10px')};
  top: ${(props) => (props.small ? '3px' : '5px')};
  opacity: ${({ theme }) => (theme.colorPalette.isDarkMode ? 0.7 : 1)};
  filter: ${({ theme }) => (theme.colorPalette.isDarkMode ? 'brightness(2.5)' : 'none')};
`;

const ClearIcon = styled.input<{ small: boolean }>`
  position: absolute;
  width: ${(props) => (props.small ? '9px' : '15px')};
  height: ${(props) => (props.small ? '9px' : '15px')};
  right: 10px;
  top: ${(props) => (props.small ? '5px' : '8px')};
  opacity: ${({ theme }) => (theme.colorPalette.isDarkMode ? 0.7 : 1)};
  filter: ${({ theme }) => (theme.colorPalette.isDarkMode ? 'brightness(2.5)' : 'none')};
  cursor: pointer;
`;

export interface SearchProps {
  value?: string;
  onChange?: Function;
  onClick?: React.MouseEventHandler<HTMLInputElement>;
  onBlur?: Function;
  fullWidth?: boolean;
  placeholder?: string;
  small?: boolean;
  name?: string;
  id?: string;
  autocomplete?: boolean;
}

const SearchInput = ({
  id,
  value,
  placeholder = 'Search',
  onChange,
  small = false,
  name = '',
  fullWidth = false,
  onClick,
  autocomplete = true,
}: SearchProps) => {
  const [currentValue, setCurrentValue] = useState(value || '');

  useEffect(() => {
    setCurrentValue(value || '');
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCurrentValue(e.target.value);
    onChange && onChange(e);
  };

  const handleClear = (e: React.MouseEvent<HTMLInputElement>) => {
    const { value } = e.currentTarget;
    setCurrentValue(value);
    onChange && onChange(e);
  };

  return (
    <SearchContainer fullWidth={fullWidth}>
      <SearchBar
        id={id}
        key={`input-${id}`}
        name={name}
        small={small}
        placeholder={placeholder}
        type="text"
        value={currentValue}
        onChange={handleChange}
        onClick={onClick}
        autoComplete={autocomplete ? 'on' : 'off'}
      />
      {currentValue.length === 0 ? (
        <SearchIcon small={small} src={iconSearch} alt="search" />
      ) : (
        <ClearIcon name={name} value="" onClick={handleClear} type="image" src={iconClear} small={small} alt="clear" />
      )}
    </SearchContainer>
  );
};

export default SearchInput;
