import React from 'react';
import styled from 'styled-components/macro';

export interface ChipProps {
  keyRef?: string;
  text: string;
  onClick: Function;
}

const Body = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: ${({ theme }) => theme.colorPalette.turquoise.normal};
  width: fit-content;
  height: 25px;
  border-radius: 12px;
  margin: 8px 10px 8px 0;
`;

const Text = styled.p`
  color: ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.black : theme.colorPalette.white)};
  font-family: Roboto;
  font-size: clamp(6px, 50%, ${({ theme }) => theme.fontSizePalette.body.XXS});
  margin: 0 6px;
  font-weight: 500;
`;

const Circle = styled.div`
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.turquoise.light : theme.colorPalette.turquoise.dark};
  width: 14px;
  height: 14px;
  margin-right: 6px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const X = styled.span`
  color: ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.black : theme.colorPalette.white)};
  font-family: Roboto;
  font-weight: medium;
  text-align: center;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
`;

const Chip = ({ keyRef, text, onClick }: ChipProps) => {
  return (
    <Body>
      <Text>{text}</Text>
      <Circle onClick={() => onClick({ text, keyRef })}>
        <X>X</X>
      </Circle>
    </Body>
  );
};

export default Chip;
