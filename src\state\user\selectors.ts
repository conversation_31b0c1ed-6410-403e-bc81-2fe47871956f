import { createSelector } from '@reduxjs/toolkit';
import { RootState } from 'models';
import {
  defaultUserCsvSeparator,
  defaultUserDateFormat,
  defaultUserDecimalSeparator,
  defaultUserTimeZoneFormat,
} from 'utils/constants';

const getIdUser = (state: RootState) => state.user.userData?.idUser;

const getLoginType = (state: RootState) => state.user.userData?.loginType;

const getUserName = (state: RootState) => state.user.userData?.username;

const getUserMail = (state: RootState) => state.user.userData?.email;

const getUserFullName = (state: RootState) => state.user.userData?.name;

const getUserImage = (state: RootState) => state.user.userData?.userImage;

const getUserPrograms = (state: RootState) => state.user.userPrograms;

const getMenuPrograms = (state: RootState) => state.user.menuPrograms;

const getLoggedInStatus = (state: RootState) => state.user.loggedInStatus;

const getUserData = (state: RootState) => state.user.userData;

const dateFormat = createSelector(getUserData, (userData) => userData?.preferredDateFormat || defaultUserDateFormat);

const getUserPreference = createSelector(getUserData, (userData) => ({
  base64Image: userData?.userImage,
  dateFormat: userData?.preferredDateFormat || defaultUserDateFormat,
  decimalSep: userData?.decimalSeparator || defaultUserDecimalSeparator,
  notifyMail: userData?.sendMail,
  csvSep: userData?.csvSeparator || defaultUserCsvSeparator,
  timeZone: userData?.timeZone || defaultUserTimeZoneFormat,
  openNextDocument: userData?.openNextDocument,
  openInvoiceLogAutomatically: userData?.openInvoiceLogAutomatically,
}));

const getDateFormat = createSelector(getUserData, (userData) => userData?.preferredDateFormat || defaultUserDateFormat);

export default {
  getUserName,
  getUserPrograms,
  getUserImage,
  getUserFullName,
  getUserPreference,
  getIdUser,
  getMenuPrograms,
  getLoggedInStatus,
  getUserMail,
  getLoginType,
  getDateFormat,
  dateFormat
};
