import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import { modalActionType } from 'utils/constants';
import { Link } from 'react-router-dom';
import UserBox from './UserBox';
import { useTheme } from 'providers/ThemeProvider';

import forCloud from '../../images/lucy4/lucy4cloud-logo.svg';
import forCloudWhite from '../../images/lucy4/lucy4cloud-logo-white.svg';

const Header = styled.header`
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.turquoise.normal : theme.colorPalette.turquoise.light};
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  & > Link {
    display: contents;
  }
`;

const LucyLogoContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const ImgContainer = styled.div<{ isLeft?: boolean }>`
  max-width: ${(props) => (props.isLeft ? '80px' : '70px')};
  margin-right: ${(props) => (props.isLeft ? '20px' : '')};
  & > img,
  & > svg {
    width: 100%;
  }
`;

const Container = styled.div`
  display: flex;
  justify-content: space-between;
`;

const LayoutHeader = () => {
  const image = useSelector(selectors.user.getUserImage);
  const userName = useSelector(selectors.user.getUserFullName);
  const dispatch = useDispatch();
  const { logo } = useSelector(selectors.app.getAppStyle);
  const { isDarkMode } = useTheme();

  const openSetting = () => {
    dispatch(actions.modal.openModal(modalActionType.USER_SETTINGS));
  };

  return (
    <Header>
      <Link to="/">
        <LucyLogoContainer>
          <ImgContainer isLeft>{<img src={isDarkMode ? forCloudWhite : forCloud} alt="lucy for cloud" />}</ImgContainer>
          <ImgContainer>
            <img src={logo} alt="header logo" />
          </ImgContainer>
        </LucyLogoContainer>
      </Link>
      <Container>
        <UserBox image={image} userName={userName || 'User'} onAvatarClick={openSetting} />
      </Container>
    </Header>
  );
};

export default LayoutHeader;
