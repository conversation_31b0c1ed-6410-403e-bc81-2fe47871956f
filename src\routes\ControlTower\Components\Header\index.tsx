import React from 'react';
import { useSelector } from 'react-redux';
import selectors from 'state/selectors';
import MultiSelectInput from 'components/Input/MultiSelect';
import { CommonButton } from 'components/Buttons';
import Toolbar from 'components/Toolbar';
import DateFilters, { RangeDate } from '../DateFilters';
import { Option } from 'react-multi-select-component';
import utils from 'utils';
import { colorPalette } from 'utils/styleConstants';
import styled from 'styled-components/macro';
import { getCompaniesFromGbsList } from 'routes/ControlTower/utils';
import { GeoCompany, DocumentType, UserCompany } from 'models/response';
import { URLS } from 'utils/constants';
import { useLocation, useRouteMatch } from 'react-router';

const ToolbarWrapper = styled.div`
  min-width: 100%;
  display: grid;
`;

interface HeaderProps {
  rangeDate?: RangeDate;
  setRangeDate: (rangeDate: RangeDate) => void;
  children?: any;
  gbsList: GeoCompany[];
  setSelectedGbsList: (gbs: Option[]) => void;
  selectedGbsList?: Option[];
  companies: UserCompany[];
  setSelectedCompanies: (company: Option[]) => void;
  selectedCompanies?: Option[];
  types: DocumentType[];
  setSelectedTypes: (type: Option[]) => void;
  selectedTypes?: Option[];
  channels: Option[];
  setSelectedChannels: (channel: Option[]) => void;
  selectedChannels?: Option[];
  handleSearch: () => void;
  handleRefresh: () => void;
  handleClear?: () => void;
  handleRestoreDefaultFilters?: () => void;
}

const Header = (props: HeaderProps) => {
  const t = utils.intl.useTranslator();
  const { path } = useRouteMatch();
  const location = useLocation();

  const programName = useSelector(selectors.app.getProgramName);

  const {
    rangeDate,
    setRangeDate,
    gbsList,
    setSelectedGbsList,
    selectedGbsList,
    companies,
    setSelectedCompanies,
    selectedCompanies,
    types,
    setSelectedTypes,
    selectedTypes,
    channels,
    setSelectedChannels,
    selectedChannels,
    handleSearch,
    handleClear,
    handleRefresh,
    handleRestoreDefaultFilters,
  } = props;

  const isSearchEnabled = rangeDate?.from && rangeDate?.to;
  const showRefreshButton =
    location.pathname === `${path}/${URLS.controlTower.detailCategories}` ||
    location.pathname === `${path}/${URLS.controlTower.detailCategories}/${URLS.controlTower.chart}`;

  const selectedGeoCompaniesList = getCompaniesFromGbsList(gbsList, selectedGbsList);

  const companiesOptions =
    selectedGbsList?.length && selectedGeoCompaniesList
      ? utils.input.buildOptionsConcatLabel(selectedGeoCompaniesList, ['name', 'socialReason'], 'idCompany')
      : utils.input.buildOptionsConcatLabel(companies, ['name', 'socialReason'], 'idCompany');

  const onChangeSelectedGbsList = (selectedGbsOptions: Option[]) => {
    const companiesFromSelectedGbs = getCompaniesFromGbsList(gbsList, selectedGbsOptions);
    const companiesOptionsFromSelectedGbs = utils.input.buildOptions(companiesFromSelectedGbs, 'name', 'idCompany');

    const selectedGbsListValues = selectedGbsList?.map(({ value }) => value);
    const addedGbsList = selectedGbsOptions.filter(({ value }) => !selectedGbsListValues?.includes(value));
    const addedCompanies = getCompaniesFromGbsList(gbsList, addedGbsList);
    const addedCompaniesOptions = utils.input.buildOptions(addedCompanies, 'name', 'idCompany');

    setSelectedGbsList(selectedGbsOptions);

    const newCompaniesOptions = [...(selectedCompanies || []), ...(addedCompaniesOptions || [])];
    const newCompaniesValues = newCompaniesOptions?.map(({ value }) => value);
    const filteredCompanies = companiesOptionsFromSelectedGbs.filter(({ value }) => newCompaniesValues.includes(value));

    setSelectedCompanies(filteredCompanies);
  };

  return (
    <ToolbarWrapper>
      <Toolbar.BlueBar>
        <Toolbar.Left>
          <DateFilters rangeDate={rangeDate} setRangeDate={setRangeDate} />
          <MultiSelectInput
            labelledBy="gbs"
            placeholder={t('filterByGbs')}
            options={utils.input.buildOptions(gbsList, 'gbsName', 'idGbs')}
            value={selectedGbsList || []}
            onChange={onChangeSelectedGbsList}
          />
          <MultiSelectInput
            labelledBy="company"
            options={companiesOptions}
            value={selectedCompanies || []}
            onChange={setSelectedCompanies}
            placeholder={t('filterByCompany')}
          />
          <MultiSelectInput
            labelledBy="type"
            options={utils.input.buildOptions(types, 'docName', 'idDocType')}
            value={selectedTypes || []}
            onChange={setSelectedTypes}
            placeholder={t('filterByType')}
          />
          <MultiSelectInput
            labelledBy="channel"
            options={channels}
            value={selectedChannels || []}
            onChange={setSelectedChannels}
            placeholder={t('filterByChannel')}
          />
          <CommonButton value={t('search')} action={handleSearch} disabled={!isSearchEnabled} />
          {showRefreshButton && (
            <CommonButton value={t('refresh')} action={handleRefresh} disabled={!isSearchEnabled} />
          )}
          <CommonButton
            value={utils.app.textTranslated('Default-filters', t, 'Default filters')}
            action={handleRestoreDefaultFilters}
            scope='secondary'
            style={{ backgroundColor: colorPalette.white }}
          />
          <CommonButton
            value={t('clear')}
            action={handleClear}
            scope="secondary"
            style={{ backgroundColor: colorPalette.white }}
          />
        </Toolbar.Left>
        <Toolbar.Right>
          <Toolbar.ModuleName>{programName}</Toolbar.ModuleName>
        </Toolbar.Right>
      </Toolbar.BlueBar>
    </ToolbarWrapper>
  );
};

export default Header;
