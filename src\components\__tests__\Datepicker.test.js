import DatePicker from '../Input/Datepicker';
import React from 'react';
import '@testing-library/jest-dom';
import { screen } from '@testing-library/react';
// import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom/extend-expect';
import { renderWithStyle } from 'utils/helpers/test.helpers';

describe('DatePicker', () => {
  it('gets rendered in DOM and has a starting date', async () => {
    renderWithStyle(<DatePicker />);
    const datePicker = screen.getByDisplayValue('');
    expect(datePicker).toBeInTheDocument();
  });

  it('has a feedback color', async () => {
    renderWithStyle(<DatePicker selected={new Date('December 17, 2005')} feedbackColor="#7ED321" hasFeedback={true} />);
    const datePicker = screen.getByDisplayValue('12/17/2005');
    expect(datePicker).toHaveStyle('border-color: #7ED321');
  });

  describe('input', () => {
    it(' has a feedback with label', async () => {
      renderWithStyle(<DatePicker feedbackColor="#7ED321" hasFeedback={true} feedbackMessage="test" />);
      const datePickerParagraph = screen.getByText('test');
      expect(datePickerParagraph).toBeInTheDocument();
      expect(datePickerParagraph).toHaveStyle('color: #7ED321');
    });

    it(' has a custom fontsize', async () => {
      renderWithStyle(<DatePicker selected={new Date('December 17, 2005')} fontSize={'1.5rem'} />);
      const datePicker = screen.getByDisplayValue('12/17/2005');
      expect(datePicker).toHaveStyle('font-size: 1.5rem');
    });

    it(' is small', async () => {
      renderWithStyle(<DatePicker selected={new Date('December 17, 2005')} small={true} />);
      const datePicker = screen.getByDisplayValue('12/17/2005');
      expect(datePicker).toHaveStyle('height: 18px');
    });

    it('is disabled', async () => {
      renderWithStyle(<DatePicker disabled={true} selected={new Date('December 17, 2005')} />);
      const datePicker = screen.getByDisplayValue('12/17/2005');
      expect(datePicker).toBeDisabled();
    });

    it('input has a custom width', async () => {
      renderWithStyle(<DatePicker selected={new Date('December 17, 2005')} inputWidth={'200px'} />);
      const datePicker = screen.getByDisplayValue('12/17/2005');
      expect(datePicker).toHaveStyle('width: 200px');
    });

    it('has range on and 2 default date', async () => {
      renderWithStyle(
        <DatePicker
          isRange={true}
          selected={new Date('December 17, 2005')}
          selectedTo={new Date('December 18, 2000')}
        />,
      );
      const datePicker = screen.getByDisplayValue('12/17/2005 - 12/18/2000');
      expect(datePicker).toBeInTheDocument();
    });
    // it('should change value', async () =>{
    //   renderWithStyle(<DatePicker />);
    //   const datePicker = screen.getByDisplayValue('');
    //   // errore e causato dall'userEvent perche replica le azioni di un vero utente
    //   // tipo keydown, keyup, keypress, click, focus, blur, change, etc
    //   // quindi quando clicca il campo di input, attiva il react popper che causa l'errore
    //   userEvent.type(datePicker, '12/18/2000');
    //   // sembra che userEvent.type scrive al contrario, e un bug relativo ad una vecchia versione della libreria
    //   expect(datePicker).toHaveValue('0002/81/21');
    // })
  });

  it(' has portal', async () => {
    renderWithStyle(<DatePicker portal={true} selected={new Date('December 17, 2005')} />);
    const datePicker = screen.getByDisplayValue('12/17/2005');
    expect(datePicker).toBeInTheDocument();
  });
});
