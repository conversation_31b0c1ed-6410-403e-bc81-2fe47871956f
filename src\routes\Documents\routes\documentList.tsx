/* eslint-disable max-lines */
import React, { useCallback, useEffect, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import ErrorBoundary from 'components/ErrorBoundary';
import FullLayout from 'components/Layout/FullLayout';
import Modal from 'components/Modal';
import ModalsContent from '../components/ModalsContent/ListModalsContent';
import Pdf from 'components/Pdf/newPdf';

import { Row } from 'models/table';
import Table, { TableProps } from 'components/Table';
import Toolbar from '../components/Toolbar/List';
import TwoColumnsLayout from 'components/Layout/TwoColumnsLayout';
import TwoRowsLayout from 'components/Layout/TwoRowsLayout';
import actions from 'state/actions';
import { match, useLocation } from 'react-router';
import {
  EVENTSTATUS,
  EXPORTSTATUS,
  actionType,
  modalActionType,
  moduleProgramCode,
  notDraggable,
} from 'utils/constants';
import selectors from 'state/selectors';
import services from 'services';
import { useHistory } from 'react-router-dom';
import utils from 'utils';
import styled from 'styled-components/macro';
import { RootState } from 'models';
import Legenda from 'components/Legenda';
import { Column } from 'models/response';
import { FilePosition, FilePositionTypes } from '../components/DocumentSearch/index.types';
import DinamicFilter from '../components/DocumentSearch';
import { GetDocumentsDynamic, IFilterValue } from 'models/request';
import { useAsync } from 'react-use';

const Title = styled.h2`
  margin-top: 20px;
`;
const H2 = styled.h2`
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
  padding: 0;
  font-size: ${({ theme }) => theme.fontSizePalette.xxLarge};
  font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  color: ${({ theme }) => theme.colorPalette.grey.grey6};
  margin: 20px;
`;

const Wrapper = styled.div`
  margin-left: 20px;
`;

export type ConditionType = {
  [modules: string]: {
    condition: Function;
    message: string;
  };
};

interface Props {
  match: match;
}

const Main = (props: Props) => {
  const {
    match: { url },
  } = props;
  const t = utils.intl.useTranslator();
  const originalColumns = useSelector(selectors.documents.selectTableColumns);
  const rows = useSelector(selectors.documents.selectTableRows);
  const isSearchVisible = useSelector(selectors.documents.selectIsSearchFilterVisible);
  const fixedColumns = useSelector(selectors.documents.selectTableFixedColumns);
  const pageSize = useSelector(selectors.documents.selectTablePageSize);
  const idUser = useSelector(selectors.user.getIdUser);
  const layout = useSelector(selectors.documents.selectLayout);
  const idTemplate = useSelector(selectors.documents.selectActiveTemplateId);
  const documentImage = useSelector(selectors.documents.selectDocumentImage);
  const allActiveDocumentsID = useSelector(selectors.documents.selectAllActiveDocumentsID);
  const activeDocumentID = useSelector(selectors.documents.selectActiveDocumentID);
  const documentImageId = useSelector(selectors.documents.selectDocumentImageId);
  const { dateFormat } = useSelector(selectors.user.getUserPreference);
  const isArchived = useSelector(selectors.documents.selectIsTemplateArchived);
  const history = useHistory();
  const dispatch = useDispatch();
  const isModalVisible = useSelector(selectors.modal.getVisibility);
  const currentModal = useSelector(selectors.modal.getActionType);
  const closeModal = () => dispatch(actions.modal.closeModal());
  const isModalDraggable = notDraggable.includes(currentModal ?? '');
  const isAllDocuments = useSelector(selectors.documents.selectIsAllAuth);
  const initialPageIndex = useSelector(selectors.documents.selectCurrentPageIndex);
  const userPreference = useSelector(selectors.user.getUserPreference);
  const idProgram = useSelector(selectors.app.getProgramCode) || -1;
  const location = useLocation();
  const state = location.state;

  const { val: DUPLICATED_PROTOCOL } = useSelector((store: RootState) =>
    selectors.app.getConfig(store, actionType.DUPLICATED_PROTOCOL),
  );

  const { val: DOC_STATUS_STOPLIGHT } = useSelector((store: RootState) =>
    selectors.app.getConfig(store, actionType.DOC_STATUS_STOPLIGHT),
  );
  const { val: DOC_PRIORITY } = useSelector((store: RootState) =>
    selectors.app.getConfig(store, actionType.DOC_PRIORITY),
  );
  const { val: NO_SUBJECT } = useSelector((store: RootState) => selectors.app.getConfig(store, actionType.NO_SUBJECT));

  const initialFilters = useSelector(selectors.documents.selectActiveFilters);
  const initialSorting = useSelector(selectors.documents.selectCurrentSorting);
  const allAuthorized = useSelector(selectors.documents.selectIsAllAuth);
  const templates = useSelector(selectors.documents.selectTemplates);
  const documentSearchFormValues = useSelector(selectors.documents.selectDocumentSearchFormValues);

  const { data: filterList, isLoading } = services.useProgramsWithFilters(idProgram, idTemplate, isSearchVisible);

  const currentIsAllDocuments = useRef<boolean>(isAllDocuments);

  useEffect(() => {
    dispatch(actions.documents.setDefaultEditState());
  }, [dispatch]);

  useAsync(async () => {
    if (!(state as any)?.drillDownData) {
      return;
    }
    try {
      // Check if drillDownData exists in state
      if ((state as any).drillDownData) {
        const getDocDynamic: {
          idProgram: number;
          idUser: number;
          idTemplate: number;
          allAuthorized: boolean;
          filters: IFilterValue[];
          idGroupEquals: number | null;
          idGroupIn: number[];
          protocols: number[];
        } = (state as any).drillDownData;

        const customFilter = templates.find((template) => template.idTemplate === idTemplate)?.customFilter;

        const getDocRequest: GetDocumentsDynamic = {
          allAuthorized: getDocDynamic.allAuthorized,
          idProgram: getDocDynamic.idProgram,
          idUser: getDocDynamic.idUser,
          idTemplate: getDocDynamic.idTemplate,
          protocolsToFilter: getDocDynamic.protocols,
          filters: [],
          idGroupEquals: getDocDynamic.idGroupEquals,
          idGroupIn: getDocDynamic.idGroupIn,
        };

        const {
          data: { columnsList, fixedColumns },
        } = await services.getDocumentGridColumns(
          getDocDynamic.idUser,
          getDocDynamic.idProgram,
          getDocDynamic.idTemplate,
        );

        dispatch(actions.documents.setActiveDocumentId([]));
        dispatch(actions.documents.setActiveTemplateId(getDocDynamic.idTemplate));
        dispatch(actions.documents.setIsAllAuth(getDocDynamic.allAuthorized));
        dispatch(actions.documents.setDocumentGridColumns(columnsList.map((column) => ({ ...column, minWidth: 80 }))));
        dispatch(actions.documents.setDocumentGridFixedColumns(fixedColumns));
        const fetchDocs = await services.getDocumentsDynamic(getDocRequest, customFilter);
        dispatch(actions.documents.setIsSearchFilterVisible(false));
        dispatch(actions.documents.setDocumentFilterForm([]));
        dispatch(actions.documents.setDocumentGridRows(fetchDocs.data));
      }
    } catch (error) {
      console.error('Error processing drill down data:', error);
    }
  }, [state]);

  useEffect(() => {
    const updateDocumentImage = async (protocol: number) => {
      try {
        // find the row with the protocol
        const filePosition = rows.find((ele) => ele.protocol === protocol)?.position as FilePosition;
        const exportStatus = rows.find((ele) => ele.protocol === protocol)?.exportStatus as number | undefined;
        const eventStatus = rows.find((ele) => ele.protocol === protocol)?.eventStatus as number | undefined;
        if (
          eventStatus === EVENTSTATUS.deleted ||
          (exportStatus !== undefined && exportStatus < EXPORTSTATUS.workingQueue) ||
          (filePosition !== undefined && filePosition === FilePositionTypes.acquisition)
        ) {
          utils.app.notify('warning', t('image-error'));
          dispatch(actions.documents.setDocumentImage(''));
          return;
        }
        const { data } =
          isArchived || filePosition === FilePositionTypes.archived
            ? await services.getArchivedDocumentImage(protocol)
            : await services.getDocumentImage(protocol);
        dispatch(actions.documents.setDocumentImage(data));
        dispatch(actions.documents.setDocumentImageId(protocol));
      } catch (e) {
        console.error(e);
        dispatch(actions.documents.setDocumentImage(''));
      }
    };

    const checkLayout = () => {
      if (layout !== 'full' && allActiveDocumentsID.length) {
        if (activeDocumentID && activeDocumentID !== documentImageId && allActiveDocumentsID.length < 2) {
          updateDocumentImage(activeDocumentID);
        }
      }
    };
    checkLayout();
  }, [layout, activeDocumentID, allActiveDocumentsID.length, documentImageId, dispatch, isArchived, rows, t]);

  useEffect(() => {
    if (isSearchVisible) currentIsAllDocuments.current = isAllDocuments;
  }, [isSearchVisible, isAllDocuments]);

  const columns = React.useMemo(() => {
    const HAS_STATUS_COLUMN = DOC_STATUS_STOPLIGHT + DUPLICATED_PROTOCOL + NO_SUBJECT;
    let dataColumns: TableProps['columns'] = [...originalColumns]
      .sort(utils.table.sortColumnsByPosition)
      .map((column) => utils.table.prepareColumns(column, dateFormat || 'dd/MM/yyyy', rows, userPreference.timeZone));
    if (HAS_STATUS_COLUMN) {
      dataColumns = [
        utils.table.makeStatusColumn({ DUPLICATED_PROTOCOL, DOC_STATUS_STOPLIGHT, NO_SUBJECT, t }),
        ...dataColumns,
      ];
    }
    return dataColumns;
  }, [
    originalColumns,
    dateFormat,
    DUPLICATED_PROTOCOL,
    DOC_STATUS_STOPLIGHT,
    NO_SUBJECT,
    rows,
    t,
    userPreference.timeZone,
  ]);

  const TABLE = React.useMemo(() => {
    const onResizeEnd = async (columnId: string | number, columnWidth: number) => {
      try {
        const isTheSame =
          originalColumns.find((ele) => ele.idTemplateColumn === Number(columnId))?.width === columnWidth;
        if (isTheSame) return;
        if (idUser && idTemplate) {
          const prepared: Column[] = originalColumns.map((ele) => {
            const { minWidth, maxWidth, ...rest } = ele;
            if (rest.idTemplateColumn === Number(columnId)) {
              return {
                ...rest,
                width:
                  columnWidth < (minWidth ?? 80)
                    ? minWidth ?? 80
                    : columnWidth > (maxWidth ?? 500)
                    ? maxWidth ?? 500
                    : columnWidth,
              };
            }
            return rest;
          });
          await services.setDocumentGridColumns({
            idUser,
            idTemplate,
            fixedColumns,
            documentGridColumns: prepared,
          });
          dispatch(actions.documents.setDocumentGridColumns(prepared));
        }
      } catch (e) {
        console.error(e);
      }
    };
    const onSelection = (selection: Row[]) => {
      dispatch(actions.documents.setActiveDocumentId(selection.map(({ protocol }) => protocol)));
    };

    const onRowDoubleClick = (allRows: Row[], selection: Row) => {
      if (idProgram === moduleProgramCode.monitor) {
        const filePosition = selection?.position as FilePosition;
        const exportStatus = selection?.exportStatus as number | undefined;
        const eventStatus = selection?.eventStatus as number | undefined;
        if (eventStatus === EVENTSTATUS.deleted) {
          dispatch(
            actions.modal.setModal({
              actionType: modalActionType.documents.FILE_IN_ACQUISITION,
              props: {
                subtitle: t('file-eventStatus-deleted-subtitle'),
                title: t('file-eventStatus-deleted-title'),
                backText: t('file-eventStatus-deleted-back'),
              },
            }),
          );
          return;
        }
        if (exportStatus !== undefined && exportStatus < EXPORTSTATUS.workingQueue) {
          dispatch(
            actions.modal.setModal({
              actionType: modalActionType.documents.FILE_IN_ACQUISITION,
              props: {
                subtitle: t('file-exportStatus-subtitle'),
                title: t('file-exportStatus-title'),
                backText: t('file-exportStatus-back'),
              },
            }),
          );
          return;
        }
        if (filePosition !== undefined && filePosition === FilePositionTypes.acquisition) {
          dispatch(
            actions.modal.setModal({
              actionType: modalActionType.documents.FILE_IN_ACQUISITION,
              props: {
                subtitle: t('file-acquisition-subtitle'),
                title: t('file-acquisition-title'),
                backText: t('file-acquisition-back'),
              },
            }),
          );
          return;
        }
      }
      const { protocol } = selection;
      dispatch(actions.documents.setActiveDocumentId([protocol]));
      if (selection.protocol !== documentImageId) {
        dispatch(actions.documents.setDocumentImage(''));
      }
      if (allRows.length === 0) return null;
      const rowIndex = allRows.findIndex((ele) => ele?.original?.id === selection?.id);
      if (rowIndex === -1) return null;
      services.unlockDocument({ idUser: idUser || 1, idProgram, protocol: 482863 });
      history.push(`${url}/edit`);
    };

    return (
      <ErrorBoundary key="left-col">
        <Table
          filters={initialFilters}
          sortBy={initialSorting}
          onUnmount={(state, rows) => {
            const isAllDocumentsDifferent = isAllDocuments !== currentIsAllDocuments.current;
            dispatch(actions.documents.setCurrentPageIndex(isAllDocumentsDifferent ? 0 : state.pageIndex));
            dispatch(actions.documents.setCurrentFilters(isAllDocumentsDifferent ? [] : state.filters));
            dispatch(actions.documents.setCurrentSorting(isAllDocumentsDifferent ? [] : state.sortBy));
            dispatch(actions.documents.setFilteredDocFromList((rows as any) || []));
            currentIsAllDocuments.current = isAllDocuments;
          }}
          pageIndex={initialPageIndex}
          rowId="protocol"
          hasToolbar
          resetFiltersDependency={`${isAllDocuments}${idTemplate}`}
          fixedColumns={fixedColumns + (DUPLICATED_PROTOCOL || DOC_STATUS_STOPLIGHT || NO_SUBJECT)}
          columns={columns}
          rows={rows}
          pageSize={pageSize}
          hasMultiSelection
          initialSelection={allActiveDocumentsID}
          onSelection={onSelection}
          onRowDoubleClick={onRowDoubleClick}
          onResizeEnd={onResizeEnd}
          hasPagination
          hasResize
          hasSort
          hasFilter
          maxHeight={500}
        />
      </ErrorBoundary>
    );
  }, [
    rows,
    pageSize,
    columns,
    initialPageIndex,
    initialFilters,
    initialSorting,
    DOC_STATUS_STOPLIGHT,
    url,
    DUPLICATED_PROTOCOL,
    fixedColumns,
    idTemplate,
    allActiveDocumentsID,
    isAllDocuments,
    dispatch,
    history,
    idUser,
    originalColumns,
    documentImageId,
    NO_SUBJECT,
    t,
    idProgram,
  ]);

  const PDF = React.useMemo(
    () =>
      allActiveDocumentsID.length === 1 && documentImage !== '' ? (
        <Pdf height="64vh" base64={documentImage} showTranslateButtons={false} showOcrButtons={false} />
      ) : null,
    [documentImage, allActiveDocumentsID.length],
  );

  const renderLayout = useCallback(() => {
    switch (layout) {
      case 'rows':
        return <TwoRowsLayout upperRow={TABLE} bottomRow={PDF} />;
      case 'columns':
        return <TwoColumnsLayout leftColumn={TABLE} rightColumn={PDF} />;
      case 'full':
        return <FullLayout content={TABLE} />;
      default:
        return <TwoRowsLayout upperRow={TABLE} bottomRow={PDF} />;
    }
  }, [TABLE, PDF, layout]);

  const checkItems = () => {
    const legendaProps = [];
    const priorityProps = [];

    if (NO_SUBJECT) {
      legendaProps.push({
        icon: 'warning',
        label: 'no-subject',
        style: { width: 15 },
      });
    }

    if (DOC_STATUS_STOPLIGHT && !isArchived) {
      legendaProps.push(
        {
          icon: 'red-triangle',
          label: 'doc-status-red',
        },
        {
          icon: 'yellow-square',
          label: 'doc-status-yellow',
        },
        {
          icon: 'green-circle',
          label: 'doc-status-green',
        },
      );
    }
    if (DUPLICATED_PROTOCOL) {
      legendaProps.push({
        icon: 'duplicated-icon',
        label: 'duplicated',
        style: { width: 15 },
      });
    }
    if (DOC_PRIORITY) {
      priorityProps.push(
        {
          icon: 'priority-low',
          label: 'priority-low',
        },
        {
          icon: 'priority-medium',
          label: 'priority-medium',
        },
        {
          icon: 'priority-high',
          label: 'priority-high',
        },
        {
          icon: 'critical-priority',
          label: 'critical-priority',
        },
      );
    }
    return [legendaProps, priorityProps];
  };

  const getDocuments = async (filterValues: IFilterValue[]) => {
    try {
      if (!(idProgram && idTemplate && idUser)) return;

      const CommonData = {
        idProgram,
        idUser,
        idTemplate,
      };

      if (idProgram === moduleProgramCode.monitor) {
        const objMonitor = {
          filters: filterValues,
          ...CommonData,
        };

        const { data: rows } = await services.getDocumentsFromDataRepositoryDynamic(objMonitor);
        dispatch(actions.documents.setDocumentGridRows(rows));
        dispatch(actions.documents.setDocumentFilterForm(filterValues));
      } else {
        const getDocumentDataDynamic = {
          filters: filterValues,
          allAuthorized: isArchived ? true : allAuthorized,
          ...CommonData,
        };

        const customFilter = templates.find((template) => template.idTemplate === idTemplate)?.customFilter;
        const { data: rows } = await services.getDocumentsDynamic(getDocumentDataDynamic, customFilter);
        dispatch(actions.documents.setDocumentFilterForm(filterValues));
        dispatch(actions.documents.setDocumentGridRows(rows));
      }

      dispatch(actions.documents.setIsSearchFilterVisible(false));
    } catch (error) {
      console.error(error);
    }
  };

  const searchDocuments = (filterValues: IFilterValue[]) => {
    if (filterValues.length === 0) {
      dispatch(
        actions.modal.openModal(modalActionType.documents.GO_AHEAD, {
          func: () => getDocuments(filterValues),
          subtitle: 'confirm_archive_search',
        }),
      );
    } else {
      getDocuments(filterValues);
    }
  };

  return (
    <>
      <Toolbar />
      <Wrapper>
        {!isSearchVisible && (
          <>
            <Title>{t('Document list')}</Title>
            <Legenda items={checkItems()} />
          </>
        )}
      </Wrapper>
      {isSearchVisible && idUser ? (
        !isLoading &&
        idTemplate && (
          <DinamicFilter
            key={idTemplate}
            filters={filterList}
            prePopulatedFilters={documentSearchFormValues}
            onSearch={searchDocuments}
            idLoggedUser={idUser}
          />
        )
      ) : (
        <>{rows.length > 0 ? renderLayout() : !isSearchVisible && <H2>{t('no rows')}</H2>}</>
      )}

      {currentModal !== 'userPassword' && currentModal !== 'userSettings' && currentModal !== 'documents/selection' && (
        <Modal onClose={closeModal} open={isModalVisible} isDraggable={!isModalDraggable}>
          <ModalsContent />
        </Modal>
      )}
    </>
  );
};

export default Main;
