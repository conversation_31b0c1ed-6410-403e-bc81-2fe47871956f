import React from 'react';
import { colorPalette, fontWeightPalette } from 'utils/styleConstants';
import { TextInput } from 'components/Input';

interface Props {
  value: string;
  onChange: (value: string) => void;
}

export default (props: Props) => {
  const { value, onChange } = props;
  return (
    <TextInput
      value={value}
      onChange={(e: React.ChangeEvent<HTMLInputElement>) => onChange(e.target.value)}
      width="100%"
      noBorder
      backgroundColor={colorPalette.white}
      height="24px"
      fontSize="12"
      fullWidth
      borderRadius="0px"
      fontColor={colorPalette.grey.grey9}
      fontWeight={`${fontWeightPalette.light}`}
    />
  );
};
