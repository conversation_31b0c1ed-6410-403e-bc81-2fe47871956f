import React from 'react';
import { fireEvent, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import '@testing-library/jest-dom/extend-expect';
import Tooltip from 'components/Tooltip';
import { renderWithStyle } from 'utils/helpers/test.helpers';

const tooltipData = {
    text: 'tooltip'
};

describe('Tooltip Component', () => {
  let tooltip = null;
  beforeEach(() => {
    tooltip = <Tooltip {...tooltipData}><div>Hover me</div></Tooltip>;
    renderWithStyle(tooltip);
  });

  afterEach(() => {
    tooltip = null;
  });

  it('renders the tooltip text when hovering over the tooltip child element', async () => {
    const tooltipTrigger = screen.getByText('Hover me');
    userEvent.hover(tooltipTrigger);

    fireEvent.mouseOver(tooltipTrigger);
  
    const tooltip = screen.getByText('tooltip');
    setTimeout(() => {
      expect(tooltip).toBeVisible();
    }, 0);
  });

  it('hides the tooltip text when mouse leaves the tooltip child element', () => {
    const tooltipTrigger = screen.getByText('Hover me');

    fireEvent.mouseEnter(tooltipTrigger);
    fireEvent.mouseLeave(tooltipTrigger);

    const tooltip = screen.getByText('tooltip');

    setTimeout(() => {
      expect(tooltip).not.toBeVisible();
    }, 0);
  });
});
