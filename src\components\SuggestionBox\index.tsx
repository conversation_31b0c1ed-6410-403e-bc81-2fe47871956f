import React, { useState, useEffect } from 'react';
import styled from 'styled-components/macro';

import intlHelper from 'utils/helpers/intl.helper';

import { AIsuggestion } from 'models/response';
import { ActiveBox } from 'models/documents';

import RadioButton from 'components/Buttons/RadioButton';

const Wrapper = styled.div<{ marginLeft?: string }>`
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode
      ? theme.colorPalette.grey.grey2
      : theme.colorPalette.turquoise.background};
  border-radius: 4px;
  padding: 15px;
  font-size: ${({ theme }) => theme.fontSizePalette.xSmall};
  max-width: 400px;
  margin: 0 0 10px 0;
  margin-left: ${({ marginLeft }) => marginLeft};
`;

const Title = styled.p`
  text-align: center;
  margin-bottom: 10px;
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode
      ? theme.colorPalette.grey.grey10
      : theme.colorPalette.grey.grey9};
`;

const Info = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 0 8px;
`;

const LabelBase = styled.div`
  font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  padding: 0 5px;
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode
      ? theme.colorPalette.grey.grey10
      : theme.colorPalette.grey.grey9};
`;

const LabelValue = styled(LabelBase)`
  width: 150px;
  text-align: center;
  padding: 0 5px;
`;

const LabelPageNumber = styled(LabelBase)`
  width: 50px;
  text-align: center;
`;

const LabelConfidence = styled(LabelBase)`
  width: 80px;
  text-align: center;
`;

const LabelPageAction = styled(LabelBase)`
  width: 100px;
  text-align: center;
`;

const Suggestions = styled.ul`
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode
      ? theme.colorPalette.grey.grey3
      : theme.colorPalette.white};
  border-radius: 4px;
  border: solid 1px ${({ theme }) =>
    theme.colorPalette.isDarkMode
      ? theme.colorPalette.grey.grey4
      : theme.colorPalette.grey.grey5};
  max-height: 115px;
  padding: 0 8px;
  overflow-y: auto;

  &::-webkit-scrollbar {
    display: none;
  }

  -ms-overflow-style: none;
  scrollbar-width: none;
`;

const Suggestion = styled.li`
  display: flex;
  align-items: center;
  border-bottom: solid 1px ${({ theme }) =>
    theme.colorPalette.isDarkMode
      ? theme.colorPalette.grey.grey4
      : theme.colorPalette.grey.grey5};
  justify-content: space-between;
  padding: 0 5px;
`;

const ValueContainer = styled.div`
  width: 150px;
  text-align: left;
`;

const PageNumberContainer = styled.div`
  width: 50px;
  text-align: center;
`;

const ConfidenceContainer = styled.div`
  width: 80px;
  text-align: center;
`;

const PageActionContainer = styled.div`
  width: 100px;
  text-align: right;
`;

const PageNumber = styled.p`
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode
      ? theme.colorPalette.grey.grey10
      : theme.colorPalette.grey.grey9};
`;

const Confidence = styled.p`
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode
      ? theme.colorPalette.grey.grey10
      : theme.colorPalette.grey.grey9};
`;

const PageAction = styled.a`
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode
      ? theme.colorPalette.turquoise.light
      : theme.colorPalette.turquoise.normal};
  font-size: ${({ theme }) => theme.fontSizePalette.xSmall};
  text-decoration: underline;
  cursor: pointer;

  &:hover {
    color: ${({ theme }) =>
      theme.colorPalette.isDarkMode
        ? theme.colorPalette.turquoise.normal
        : theme.colorPalette.turquoise.dark};
  }
`;

export interface Props {
  id?: string;
  suggestions: AIsuggestion[];
  onSelection?: (value: AIsuggestion) => void;
  defaultValue?: string;
  setActiveBox: (value: ActiveBox | null) => void;
  marginLeft?: string;
}

const SuggestionBox = (props: Props) => {
  const { defaultValue, suggestions, onSelection, setActiveBox, id, marginLeft } = props;
  const [selectedOption, setSelectedOption] = useState(defaultValue);
  const t = intlHelper.useTranslator();

  useEffect(() => {
    setSelectedOption(defaultValue);
  }, [defaultValue]);

  const onChange = (value: string) => {
    const option = suggestions.find((suggestion) => {
      const { confidence: c, pageNumber: n, boundingBox } = suggestion;
      const { x1, x2, y1, y2 } = boundingBox;
      return `${c}-${n}-${x1},${y1}-${x2},${y2}` === value;
    });
    onSelection && option && onSelection(option);
    setSelectedOption(value);
  };

  return (
    <Wrapper id={id} marginLeft={marginLeft}>
      <Title>{t('select-a-suggestion')}</Title>
      <Info>
        <LabelValue>{t('suggestion-value')}</LabelValue>
        <LabelPageNumber>{t('suggestion-page')}</LabelPageNumber>
        <LabelConfidence>{t('suggestion-confidence')}</LabelConfidence>
        <LabelPageAction />
      </Info>
      <Suggestions>
        {suggestions.map(({ content, pageNumber, confidence, boundingBox }, i) => {
          const { x1, x2, y1, y2 } = boundingBox;
          const id = `${confidence}-${pageNumber}-${x1},${y1}-${x2},${y2}`;
          return (
            <Suggestion key={id + i}>
              <ValueContainer>
                <RadioButton
                  onChange={onChange}
                  value={selectedOption}
                  options={[
                    {
                      value: id,
                      label: content || t('no-display-name'),
                    },
                  ]}
                />
              </ValueContainer>
              <PageNumberContainer>
                <PageNumber>{pageNumber + 1}</PageNumber>
              </PageNumberContainer>
              <ConfidenceContainer>
                <Confidence>{`${(confidence * 100).toFixed(1)}%`}</Confidence>
              </ConfidenceContainer>
              <PageActionContainer>
                <PageAction onClick={() => setActiveBox({ ...boundingBox, pageNumber })}>
                  {t('view in page')}
                </PageAction>
              </PageActionContainer>
            </Suggestion>
          );
        })}
      </Suggestions>
    </Wrapper>
  );
};

export default SuggestionBox;
