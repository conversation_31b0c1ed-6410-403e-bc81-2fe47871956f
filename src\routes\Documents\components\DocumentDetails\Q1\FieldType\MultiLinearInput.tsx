/* eslint-disable max-lines */
import React, { useEffect, useRef, useState } from 'react';
import { statusColors } from 'utils/styleConstants';
import styled from 'styled-components/macro';
import { useUpdateEffect } from 'react-use';
import utils from 'utils';
import { AIsuggestion } from 'models/response';
import { FieldStatus, Q1Value } from 'models/documents';
import { FieldHelperProps, FieldInputProps } from 'formik';

const InputWrapper = styled.div<{ justifyContent?: string }>`
  display: flex;
  align-items: center;
  justify-content: ${({ justifyContent }) => justifyContent ?? 'center'};
`;

const InputContainer = styled.div<{ fullWidth: boolean }>`
  position: relative;
  ${(props) => props.fullWidth && 'width: 100%'};
`;

export const InputLabel = styled.label<{
  marginRight?: string;
  labelFontSize?: string;
  labelColor?: string;
  labelWidth?: string;
  textAlign?: string;
}>`
  display: inline-block;
  margin-right: ${({ marginRight }) => (marginRight ? marginRight : '15px')};
  font-size: ${({ theme, labelFontSize }) => labelFontSize ?? theme.fontSizePalette.body.XXS};
  width: ${({ labelWidth }) => labelWidth ?? '80px'};
  text-align: ${({ textAlign }) => textAlign ?? 'right'};
  color: ${({ theme, labelColor }) =>
    labelColor ?? (theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.black)};
`;

interface StyledInput {
  hasFeedback: boolean;
  feedbackColor?: string;
  noBorder: boolean;
  fontSize?: string | null;
  paddingRight: boolean;
  tall?: boolean;
  fullWidth?: boolean;
  width?: string;
  backgroundColor?: string;
  height?: string;
  borderRadius?: string;
  fontColor?: string;
  fontWeight?: string;
  margin?: string;
}

const Input = styled.input<StyledInput>`
  font-family: Roboto;
  margin: ${({ margin }) => (margin ? margin : 'none')};
  color: ${({ theme, fontColor, disabled }) =>
    disabled
      ? theme.colorPalette.grey.grey12
      : fontColor ?? (theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.black)};
  font-weight: ${({ theme, fontWeight }) => fontWeight || theme.fontWeightPalette.regular};
  height: ${({ tall, height }) => (height ? `${height}` : tall ? '35px' : '25px')};
  width: ${({ fullWidth, width }) => (fullWidth ? '100%' : width)};
  border: ${({ hasFeedback, theme, noBorder, feedbackColor }) =>
    hasFeedback
      ? `1px solid ${feedbackColor ? feedbackColor : statusColors.error}`
      : noBorder
      ? 'none'
      : `1px solid ${theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.grey.grey5}`};
  border-radius: ${({ borderRadius }) => `${borderRadius}` || '3px'};
  padding-left: ${({ noBorder }) => (noBorder ? '0' : '5px')};
  padding-right: ${({ paddingRight }) => (paddingRight ? '25px' : '0')};
  ${({ fontSize }) => `font-size: ${fontSize ? fontSize : 12}px`};
  background-color: ${({ theme, backgroundColor }) =>
    backgroundColor ?? (theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.white)};

  &:focus {
    outline: 0;
    border: ${({ hasFeedback, noBorder, theme, feedbackColor }) =>
      hasFeedback
        ? `1px solid ${feedbackColor}`
        : noBorder
        ? 'none'
        : `1px solid ${
            theme.colorPalette.isDarkMode ? theme.colorPalette.turquoise.dark : theme.colorPalette.turquoise.normal
          }`};
  }

  &::placeholder {
    color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey6 : theme.colorPalette.grey.grey7};
  }

  ::-ms-reveal,
  ::-ms-clear {
    display: none;
  }
  &:disabled {
    border: ${({ theme }) => (theme.colorPalette.isDarkMode ? 'none' : `1px solid ${theme.colorPalette.grey.grey5}`)};
    background-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.grey.grey3}!important;
    color: ${({ theme }) => theme.colorPalette.grey.grey12};
    opacity: 0.7;
  }
`;

export const FeedbackText = styled.p<{
  feedbackColor: string | undefined;
  displayText?: string;
  textPositioning?: string;
}>`
  font-family: Roboto;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  color: ${({ feedbackColor, theme }) =>
    feedbackColor ?? (theme.colorPalette.isDarkMode ? statusColors.error : statusColors.error)} !important;
  margin: 1px 0 0 1px;
  text-align: right;
  position: absolute;
  right: 0;
  text-wrap: nowrap;
  padding-top: 3px;
  ${({ displayText }) => displayText && `display: ${displayText}`};
  ${({ textPositioning }) => textPositioning && `position: ${textPositioning}`};
`;

export interface TextInputProps {
  onChange?: Function;
  value?: string | number;
  placeholder?: string;
  hasFeedback?: boolean;
  feedbackMessage?: string;
  feedbackColor?: string;
  disabled?: boolean;
  onBlur?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLInputElement>) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLDivElement>) => void;
  focus?: boolean;
  id?: string;
  name?: string;
  noBorder?: boolean;
  fontSize?: string;
  fullWidth?: boolean;
  width?: string;
  displayText?: string;
  textPositioning?: string;
  backgroundColor?: string;
  height?: string;
  borderRadius?: string;
  fontColor?: string;
  fontWeight?: string;
  margin?: string;
  maxLength?: number;
  autoComplete?: string;
  justifyContent?: string;
  typeOfField: string | null;
  index: number | undefined;
  values: AIsuggestion[];
  field: FieldInputProps<Q1Value | undefined>;
  helpers: FieldHelperProps<Q1Value | undefined>;
}

const MultiLinearInput = ({
  id,
  onBlur,
  onFocus,
  onKeyDown,
  noBorder,
  focus,
  onChange,
  value = '',
  placeholder = '',
  hasFeedback = false,
  feedbackMessage = '',
  feedbackColor,
  fullWidth = false,
  name,
  fontSize,
  disabled = false,
  width = '165px',
  displayText,
  textPositioning = 'absolute',
  backgroundColor,
  height,
  borderRadius,
  fontColor,
  fontWeight,
  margin,
  maxLength,
  autoComplete,
  justifyContent,
  typeOfField,
  index,
  values,
  field,
  helpers,
}: TextInputProps) => {
  const [currentValue, setCurrentValue] = useState(value);
  const inputRef = useRef<HTMLInputElement | null>(null);

  const t = utils.intl.useTranslator();

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    if (typeOfField === 'number' && /[^0-9\s]/.test(inputValue)) {
      setCurrentValue('');
      onChange && onChange(undefined);
      return;
    }

    setCurrentValue(inputValue);
    onChange && onChange(e);
  };

  useEffect(() => {
    setCurrentValue(value);
  }, [value]);

  useUpdateEffect(() => {
    if (inputRef.current && focus) {
      inputRef.current.focus();
    }
  }, [focus]);

  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    event.preventDefault();
    const pastedText = (event.clipboardData && event.clipboardData.getData('text')) || '';
    if (!pastedText.length) return;
    if (typeOfField === 'number' && /[^0-9\s]/.test(pastedText)) {
      utils.app.notify('warning', t('digit_only_number'), 5000, 'digit-only-number');
      return;
    }

    const trimmedText = pastedText.trim();

    const data: AIsuggestion[] = trimmedText.split('\n').map((line, key) => ({
      boundingBox: { x1: -1, x2: -1, y1: -1, y2: -1 },
      confidence: -1,
      content: line.trim(),
      number: key + 1,
      pageNumber: -1,
      status: 3 as FieldStatus,
    }));

    if (field.value) {
      if (index !== undefined) {
        let updatedArrayValues: AIsuggestion[] = [];

        const selectionStart = (event.target as HTMLInputElement).selectionStart;
        const selectionEnd = (event.target as HTMLInputElement).selectionEnd;

        if (selectionStart === 0 && selectionEnd === currentValue.toString().length) {
          updatedArrayValues = values.slice(0, index !== undefined ? index : 0).concat(data);
        } else {
          if (index !== undefined) {
            updatedArrayValues = [...values.slice(0, index + 1), ...data];
          }
        }
        const filteredArrayValues = updatedArrayValues.filter((el) => el.content !== '');
        helpers.setValue({
          ...field.value,
          content: filteredArrayValues,
        });
      }
    }
  };

  return (
    <InputWrapper justifyContent={justifyContent}>
      <InputContainer fullWidth={fullWidth}>
        <Input
          ref={inputRef}
          fullWidth={fullWidth}
          width={width}
          fontSize={fontSize ? fontSize : null}
          noBorder={noBorder ? noBorder : false}
          id={id}
          name={name}
          onFocus={onFocus}
          onBlur={onBlur}
          onKeyDown={onKeyDown}
          hasFeedback={hasFeedback}
          feedbackColor={feedbackColor}
          disabled={disabled}
          placeholder={placeholder}
          value={currentValue}
          onChange={handleChange}
          type="text"
          paddingRight={false}
          backgroundColor={backgroundColor}
          height={height}
          borderRadius={borderRadius}
          fontColor={fontColor}
          fontWeight={fontWeight}
          margin={margin}
          maxLength={maxLength}
          autoComplete={autoComplete}
          onPaste={handlePaste}
        />
        {hasFeedback && feedbackMessage.length > 0 && (
          <FeedbackText displayText={displayText} feedbackColor={feedbackColor} textPositioning={textPositioning}>
            {feedbackMessage}
          </FeedbackText>
        )}
      </InputContainer>
    </InputWrapper>
  );
};

export default MultiLinearInput;
