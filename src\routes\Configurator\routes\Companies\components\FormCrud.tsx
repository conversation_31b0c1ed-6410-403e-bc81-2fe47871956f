import Checkbox from 'components/Buttons/CheckboxButton';
import CommonButton from 'components/Buttons/CommonButton';
import { Dropdown } from 'components/Input';
import TextInput from 'components/Input/TextInput';
import { FormikErrors, FormikState, FormikTouched } from 'formik';
import { FormValuesCompany, Option, OptionAT } from 'models/configurator';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import utils from 'utils';
import {
  Right,
  InputContainer,
  InputLabel,
  RightTitle,
  ButtonsContainer,
  CheckboxContainer,
  CheckboxLabel,
} from '../../../styles';

const RightWrapper = styled.div`
  flex-basis: 30%;
`;

interface Props {
  values: FormValuesCompany;
  optionsDocuments: Option[];
  optionsUsers: Option[];
  handleChange: {
    (e: React.ChangeEvent<any>): void;
    <T = string | React.ChangeEvent<any>>(field: T): T extends React.ChangeEvent<any>
      ? void
      : (e: string | React.ChangeEvent<any>) => void;
  };
  setFieldValue: (field: string, value: any, shouldValidate?: boolean | undefined) => void;
  handleBlur: {
    (e: React.FocusEvent<any>): void;
    <T = any>(fieldOrEvent: T): T extends string ? (e: any) => void : void;
  };
  errors: FormikErrors<FormValuesCompany>;
  touched: FormikTouched<FormValuesCompany>;
  dirty: boolean;
  isValid: boolean;
  isValidating: boolean;
  submitForm: (() => Promise<void>) & (() => Promise<any>);
  resetForm: (nextState?: Partial<FormikState<FormValuesCompany>> | undefined) => void;
  optionsCurrency: OptionAT[];
  optionsCountries: OptionAT[];
}

const FormCrud = ({
  values,
  optionsDocuments,
  optionsUsers,
  setFieldValue,
  handleChange,
  handleBlur,
  errors,
  touched,
  dirty,
  isValid,
  isValidating,
  submitForm,
  resetForm,
  optionsCurrency,
  optionsCountries,
}: Props) => {
  const formModeCompany = useSelector(selectors.configurator.getFormModeCompany);
  const dispatch = useDispatch();

  const t = utils.intl.useTranslator();

  return (
    <RightWrapper>
      <RightTitle>{t(`${formModeCompany}_company_details`)}</RightTitle>
      <Right>
        <InputContainer>
          <InputLabel>{t('name')}</InputLabel>
          <TextInput
            name="name"
            value={values['name']}
            onChange={handleChange}
            onBlur={handleBlur}
            hasFeedback={errors.name && touched.name ? true : false}
            feedbackMessage={errors.name ? errors.name : ''}
          />
        </InputContainer>
        {(formModeCompany === 'edit' || formModeCompany === 'new') && (
          <>
            <InputContainer>
              <InputLabel>{t('label')}</InputLabel>
              <TextInput name="label" value={values['label']} onChange={handleChange} />
            </InputContainer>
            <InputContainer>
              <InputLabel>{t('vatNumber')}</InputLabel>
              <TextInput name="vatNumber" value={values['vatNumber']} onChange={handleChange} />
            </InputContainer>
            <InputContainer>
              <InputLabel>{t('socialReason')}</InputLabel>
              <TextInput name="socialReason" value={values['socialReason']} onChange={handleChange} />
            </InputContainer>
            <InputContainer>
              <InputLabel>{t('fiscalCode')}</InputLabel>
              <TextInput name="fiscalCode" value={values['fiscalCode']} onChange={handleChange} />
            </InputContainer>
            <InputContainer>
              <InputLabel>{t('phone')}</InputLabel>
              <TextInput name="phone" value={values['phone']} onChange={handleChange} />
            </InputContainer>
            <InputContainer>
              <InputLabel>{t('fax')}</InputLabel>
              <TextInput name="fax" value={values['fax']} onChange={handleChange} />
            </InputContainer>
            <InputContainer>
              <InputLabel>{t('email')}</InputLabel>
              <TextInput name="email" value={values['email']} onChange={handleChange} />
            </InputContainer>
            <InputContainer>
              <InputLabel>{t('url')}</InputLabel>
              <TextInput name="url" value={values['url']} onChange={handleChange} />
            </InputContainer>
            <InputContainer>
              <InputLabel>{t('onlineCode')}</InputLabel>
              <TextInput name="onlineCode" value={values['onlineCode']} onChange={handleChange} />
            </InputContainer>
            <InputContainer>
              <InputLabel>{t('referenceUser')}</InputLabel>
              <Dropdown
                options={optionsUsers}
                onChange={({ value }) => setFieldValue('referenceUser', value)}
                value={optionsUsers.find((el) => el.value === values['referenceUser']) || null}
              />
            </InputContainer>
            <InputContainer>
              <InputLabel>{t('counterPrefix')}</InputLabel>
              <TextInput name="counterPrefix" value={values['counterPrefix']} onChange={handleChange} />
            </InputContainer>
            <InputContainer>
              <InputLabel>{t('country')}</InputLabel>
              <Dropdown
                options={optionsCountries}
                onChange={({ value }) => setFieldValue('country', value)}
                value={optionsCountries.find((el) => el.value === values['country']) || null}
              />
            </InputContainer>
            <CheckboxContainer>
              <CheckboxLabel>{t('ocrDoc')}</CheckboxLabel>
              <Checkbox action={(e) => setFieldValue('ocrDoc', e)} isActive={values['ocrDoc']} name="name" />
            </CheckboxContainer>
            <InputContainer>
              <InputLabel>{t('select_document')}</InputLabel>
              <Dropdown
                isMulti
                width="165px"
                options={optionsDocuments}
                customHeight="auto"
                value={values['documentTypeList']}
                onChange={(value) => setFieldValue('documentTypeList', value)}
              />
            </InputContainer>
            <InputContainer>
              <InputLabel>{t('currency')}</InputLabel>
              <Dropdown
                options={optionsCurrency}
                onChange={(selectedOption: OptionAT | null) =>
                  setFieldValue('currency', selectedOption ? selectedOption.value : null)
                }
                value={optionsCurrency.find((el) => el.value === values['currency']) || null}
                isClearable
              />
            </InputContainer>
            <InputContainer>
              <InputLabel>{t('country_currency')}</InputLabel>
              <TextInput name="countryCurrency" value={values['countryCurrency']} disabled />
            </InputContainer>
          </>
        )}
        <ButtonsContainer>
          <CommonButton
            id="saveButton"
            scope="primary"
            type="submit"
            disabled={!dirty || !isValid || isValidating}
            value={t(`${formModeCompany}_company`)}
            action={submitForm}
          />
          <CommonButton
            id="cancelButton"
            scope="secondary"
            type="button"
            value={t('cancel')}
            action={() => {
              dispatch(actions.configurator.setFormModeCompany(null));
              resetForm();
            }}
          />
        </ButtonsContainer>
      </Right>
    </RightWrapper>
  );
};
export default FormCrud;
