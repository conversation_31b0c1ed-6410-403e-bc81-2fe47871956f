import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Route, Switch, useHistory, useRouteMatch } from 'react-router-dom';
import actions from 'state/actions';
import VendorMainView from './VendorMainView';
import VendorDetail from './VendorDetails';
import selectors from 'state/selectors';

const Routes = () => {
  const { path } = useRouteMatch();
  const selectedCompany = useSelector(selectors.configurator.getSelectedCompanyVendorConfig);

  const dispatch = useDispatch();
  const history = useHistory();

  useEffect(() => {
    dispatch(actions.configurator.resetConfVendorState());
  }, [dispatch]);

  useEffect(() => {
    history.push(path);
    dispatch(actions.configurator.setSubjectList([]));

  }, [history, path, selectedCompany, dispatch]);

  return (
    <>
      <Switch>
        <Route exact path={path} component={VendorMainView} />
        <Route path={`${path}/vendor-detail`} component={VendorDetail} />
      </Switch>
    </>
  );
};

export default Routes;
