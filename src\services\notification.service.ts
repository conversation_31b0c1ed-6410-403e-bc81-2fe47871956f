import axios from 'axios';
import {
  AddOpenWfNotificationSettings,
  AddOverDueNotificationSettings,
  AddWfReminderNotificationSettings,
} from 'models/request';
import {
  GetTemplatesMailNotification,
  IOpenWorkflowNotifications,
  IOverdueNotifications,
  IWfReminderNotifications,
} from 'models/response';
import { trackPromise } from 'react-promise-tracker';
import { CONFIGURATOR, NOTIFICATIONS } from 'utils/constants';

const getTemplatesMailNotification = () =>
  trackPromise(
    axios.get<GetTemplatesMailNotification[]>(`${CONFIGURATOR}/${NOTIFICATIONS}/getTemplatesMail`),
    'module',
  );

// OPEN WORKFLOW
const getOpenWfNotificationSettings = () =>
  trackPromise(
    axios.get<IOpenWorkflowNotifications[]>(`${CONFIGURATOR}/${NOTIFICATIONS}/getOpenWfNotificationSettings`),
    'module',
  );
const getUsersForOpenWfNotificationSettings = () =>
  trackPromise(
    axios.get<{ idUser: number; name: string }[]>(
      `${CONFIGURATOR}/${NOTIFICATIONS}/getUsersForOpenWfNotificationSettings`,
    ),
    'module',
  );
/**
 * Create new user-notification open workflow
 * @param {Object} body
 * @param {number} body.idUser
 * @param {boolean} body.openWfSendMail
 * @param {boolean} body.openWfSendNotificationImmediately
 * @param {number} body.openWfMaxDocuments
 * @param {number} body.openWfMaxDocumentDetails
 * @param {number} body.idTemplateImmediateOpenWorkflow
 * @param {number} body.idTemplateScheduledOpenWorkflow
 * @param {string} body.openWfFrequency
 * @return {Promise<AxiosResponse>}
 */
const addOpenWfNotificationSettings = (body: AddOpenWfNotificationSettings) => {
  return trackPromise(axios.post(`${CONFIGURATOR}/${NOTIFICATIONS}/addOpenWfNotificationSettings`, body), 'module');
};

const deleteOpenWfNotificationSettings = (idUser: number) => {
  return trackPromise(
    axios.post(`${CONFIGURATOR}/${NOTIFICATIONS}/deleteOpenWfNotificationSettings`, { idUser }),
    'modal',
  );
};
/**
 * Edit user-notification open workflow
 * @param {Object} body
 * @param {number} body.idUser
 * @param {boolean} body.openWfSendMail
 * @param {boolean} body.openWfSendNotificationImmediately
 * @param {number} body.openWfMaxDocuments
 * @param {number} body.openWfMaxDocumentDetails
 * @param {number} body.idTemplateImmediateOpenWorkflow
 * @param {number} body.idTemplateScheduledOpenWorkflow
 * @param {string} body.openWfFrequency
 * @return {Promise<AxiosResponse>}
 */

const editOpenWfNotificationSettings = (body: AddOpenWfNotificationSettings) => {
  return trackPromise(axios.post(`${CONFIGURATOR}/${NOTIFICATIONS}/editOpenWfNotificationSettings`, body), 'module');
};

// WF REMINDER
const getUsersForWfReminderNotificationSettings = () =>
  trackPromise(
    axios.get<{ idUser: number; name: string }[]>(
      `${CONFIGURATOR}/${NOTIFICATIONS}/getUsersForWfReminderNotificationSettings`,
    ),
    'module',
  );

const deleteWfReminderNotificationSettings = (idUser: number) => {
  return trackPromise(
    axios.post(`${CONFIGURATOR}/${NOTIFICATIONS}/deleteWfReminderNotificationSettings`, { idUser }),
    'modal',
  );
};

const getWfReminderNotificationSettings = () =>
  trackPromise(
    axios.get<IWfReminderNotifications[]>(`${CONFIGURATOR}/${NOTIFICATIONS}/getWfReminderNotificationSettings`),
    'module',
  );

const getOverdueNotificationSettings = () =>
  trackPromise(
    axios.get<IOverdueNotifications[]>(`${CONFIGURATOR}/${NOTIFICATIONS}/getOverdueNotificationSettings`),
    'module',
  );

/**
 * Create new user-notification worfklow reminder
 * @param {Object} body
 * @param {number} body.idUser
 * @param {boolean} body.reminderSendMail
 * @param {number} body.startDeltaReminder
 * @param {number} body.endDeltaReminder
 * @param {number} body.reminderDays
 * @param {number} body.maxReminder
 * @param {number} body.reminderMaxDocuments
 *  * @param {number} body.reminderMaxDocumentDetails
 * @param {number} body.idTemplateScheduledReminder
 * @param {string} body.reminderFrequency
 * @return {Promise<AxiosResponse>}
 */
const addWfReminderNotificationSettings = (body: AddWfReminderNotificationSettings) => {
  return trackPromise(axios.post(`${CONFIGURATOR}/${NOTIFICATIONS}/addWfReminderNotificationSettings`, body), 'module');
};
/**
 * Edit new user-notification worfklow reminder
 * @param {Object} body
 * @param {number} body.idUser
 * @param {boolean} body.reminderSendMail
 * @param {number} body.startDeltaReminder
 * @param {number} body.endDeltaReminder
 * @param {number} body.reminderDays
 * @param {number} body.maxReminder
 * @param {number} body.reminderMaxDocuments
 *  * @param {number} body.reminderMaxDocumentDetails
 * @param {number} body.idTemplateScheduledReminder
 * @param {string} body.reminderFrequency
 * @return {Promise<AxiosResponse>}
 */
const editWfReminderNotificationSettings = (body: AddWfReminderNotificationSettings) => {
  return trackPromise(
    axios.post(`${CONFIGURATOR}/${NOTIFICATIONS}/editWfReminderNotificationSettings`, body),
    'module',
  );
};

// OVERDUE
const deleteOverdueNotificationSettings = (idUser: number) => {
  return trackPromise(
    axios.post(`${CONFIGURATOR}/${NOTIFICATIONS}/deleteOverdueNotificationSettings`, { idUser }),
    'modal',
  );
};
const getUsersForOverdueNotificationSettings = () =>
  trackPromise(
    axios.get<{ idUser: number; name: string }[]>(
      `${CONFIGURATOR}/${NOTIFICATIONS}/getUsersForOverdueNotificationSettings`,
    ),
    'module',
  );
/**
 * Create new user-notification overdue
 * @param {Object} body
 * @param {number} body.idUser
 * @param {boolean} body.overDueSendMail
 * @param {number} body.overDueMaxDocuments
 * @param {number} body.idTemplateScheduledOverdue
 * @param {string} body.overDueFrequency
 * @return {Promise<AxiosResponse>}
 */
const addOverdueNotificationSettings = (body: AddOverDueNotificationSettings) => {
  return trackPromise(axios.post(`${CONFIGURATOR}/${NOTIFICATIONS}/addOverdueNotificationSettings`, body), 'module');
};
/**
 * Edit new user-notification overdue
 * @param {Object} body
 * @param {number} body.idUser
 * @param {boolean} body.overDueSendMail
 * @param {number} body.overDueMaxDocuments
 * @param {number} body.idTemplateScheduledOverdue
 * @param {string} body.overDueFrequency
 * @return {Promise<AxiosResponse>}
 */
const editOverdueNotificationSettings = (body: AddOverDueNotificationSettings) => {
  return trackPromise(axios.post(`${CONFIGURATOR}/${NOTIFICATIONS}/editOverdueNotificationSettings`, body), 'module');
};

export default {
  getOpenWfNotificationSettings,
  getUsersForOpenWfNotificationSettings,
  getWfReminderNotificationSettings,
  getOverdueNotificationSettings,
  addOpenWfNotificationSettings,
  deleteOpenWfNotificationSettings,
  editOpenWfNotificationSettings,
  deleteWfReminderNotificationSettings,
  getUsersForWfReminderNotificationSettings,
  addWfReminderNotificationSettings,
  editWfReminderNotificationSettings,
  deleteOverdueNotificationSettings,
  getUsersForOverdueNotificationSettings,
  addOverdueNotificationSettings,
  editOverdueNotificationSettings,
  getTemplatesMailNotification,
};
