import utils from 'utils';
import { Circle } from './styles';
import React from 'react';
import { localizationString } from 'models/response';
import { prefixesNameList } from 'utils/constants';

type Props = {
  value?: string | null;
  column?: {
    id?: string;
    Header?: string;
    accessor?: string;
    Cell?: any;
    hasLocalKeys?: [localizationString | undefined, prefixesNameList | undefined];
  };
};

const DefaultCell = (props: Props) => {
  const { value, column } = props ?? {};
  const { Header, hasLocalKeys } = column ?? {};
  const t = utils.intl.useTranslator();
  const [localKeys, prefixName] = hasLocalKeys ?? [undefined, undefined];
  const values = () => {
    switch (localKeys) {
      case 'LocalizableString':
        return utils.app.textTranslated(value, t) ?? '';
      case 'LocalizableStringFe':
        return utils.app.setLocalizableString(value, Header ?? '', prefixName, t) ?? '';
      default:
        return String(value ?? '');
    }
  };
  return <div title={values()}>{values()}</div>;
};

const cellWithCounter = ({ value, row }: { value: any; row: any }) => {
  const currentValue = row?.depth === 1 ? '-' : value;
  return (
    <div style={{ display: 'flex' }}>
      <div
        style={{
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap',
          overflow: 'hidden',
        }}
      >
        {currentValue}
      </div>
      {row.canExpand && row.subRows.length > 0 && (
        <div>
          <Circle>{row.subRows.length}</Circle>
        </div>
      )}
    </div>
  );
};

export { DefaultCell, cellWithCounter };
