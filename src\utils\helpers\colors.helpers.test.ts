import colorHelpers from './colors.helpers';

describe('generateHexColors', () => {
  test('it should return the correct array of 5 colors from the whole spectrum', () => {
    const numColors = 5;
    const colors = colorHelpers.generateHexColors({ numColors });
    expect(colors).toStrictEqual(['#f07575', '#d7f075', '#75f0a6', '#75a6f0', '#d775f0']);
  });

  test('it should return the correct array of 5 colors without red color', () => {
    const numColors = 5;
    const colors = colorHelpers.generateHexColors({ numColors, hueRangeStart: 30, hueRangeEnd: 285 });
    expect(colors).toStrictEqual(['#f0b375', '#c5f075', '#75f08e', '#75eaf0', '#7582f0']);
  });

  test('it should return the correct array of 1 color', () => {
    const numColors = 1;
    const colors = colorHelpers.generateHexColors({ numColors });
    expect(colors).toHaveLength(1);
  });

  test('it should return the empty array if 0 colors', () => {
    const numColors = 0;
    const colors = colorHelpers.generateHexColors({ numColors });
    expect(colors).toStrictEqual([]);
  });
});
