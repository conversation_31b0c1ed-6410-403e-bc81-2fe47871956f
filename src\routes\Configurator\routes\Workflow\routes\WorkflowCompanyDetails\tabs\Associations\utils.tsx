import * as yup from 'yup';

import {
  CompanyDefinitionsForAssociation,
  CompanyGroupsForTaskAssociation,
  CompanyTasksForAssociation,
  DefinitionTaskAssociations,
  WfListOfConvalidationAssociation,
  WorkflowDefinitionTaskGroupsAssociations,
  WorkflowDefinitionTaskGroupsAssociationsExtended,
} from 'models/response';
import { OriginalRow } from 'components/Table';

export const relationListFilters = [
  { value: 0, label: 'NOT SELECTED' },
  { value: 1, label: 'AND' },
  { value: 2, label: 'OR' },
];

export const relationList = [
  { value: 1, label: 'AND' },
  { value: 2, label: 'OR' },
];

export const transformedRows = (
  rows: WorkflowDefinitionTaskGroupsAssociations[] | WorkflowDefinitionTaskGroupsAssociationsExtended[],
) => {
  let id = -1;
  const newRows: WorkflowDefinitionTaskGroupsAssociationsExtended[] = [];
  rows?.forEach((row) => {
    const { definitionName, idDefinition, definitionTaskAssociations } = row;
    if (definitionTaskAssociations.length === 1) {
      const firstRecord = definitionTaskAssociations[0];
      const newList = {
        id,
        subRows: [],
        definitionName,
        idDefinition,
        definitionTaskAssociations,
        ...firstRecord,
      };
      newRows.push(newList);
    } else {
      const newList = {
        id,
        definitionName,
        idDefinition,
        definitionTaskAssociations,
        subRows:
          definitionTaskAssociations.length > 1
            ? definitionTaskAssociations.map((el) => ({
                id: el.idTask === -2 ? -2 : ++id,
                isSubRow: true,
                ...el,
                definitionName,
              }))
            : [],
      };
      newRows.push(newList);
    }
    id++;
  });
  return newRows;
};

export const formValidation = yup.object().shape({
  definitionName: yup.string().required(),
  taskName: yup.string().required(),
  idConvalidationAction: yup.number().required(),
});

export const formValidationWhenSubRowEdited = (
  t: (value: string) => string,
  oldRow?: DefinitionTaskAssociations,
  selectedRow?: WorkflowDefinitionTaskGroupsAssociationsExtended | null,
) => {
  return yup
    .object()
    .shape({
      taskName: yup.string().required(),
      idConvalidationAction: yup.number().required(),
    })
    .test(
      'is-same-row',
      t('The row is not modified'),
      (value: { escalation: boolean; idConvalidationAction: number; taskName: string; weight: number }) => {
        if (oldRow === undefined) return true;
        if (value === undefined) return false;
        const { escalation, idConvalidationAction, taskName, weight } = value;

        const taskGroupOfOldRow = JSON.stringify(oldRow.taskGroupAssociations?.filter((el) => el.assigned));
        const taskGroupSelectedRow = JSON.stringify(selectedRow?.taskGroupAssociations?.filter((el) => el.assigned));

        if (
          escalation === oldRow.escalation &&
          idConvalidationAction === oldRow.idConvalidationAction &&
          taskName === oldRow.taskName &&
          weight === oldRow.weight &&
          taskGroupOfOldRow === taskGroupSelectedRow
        ) {
          return false;
        }
        return true;
      },
    );
};

export const buildPayload = (
  row: OriginalRow,
  listOfDefinitionsForAssociations: CompanyDefinitionsForAssociation[] | undefined,
  listOfTasksForAssociations: CompanyTasksForAssociation[] | undefined,
  listOfConvalidation: WfListOfConvalidationAssociation[],
  latestGroupsList: React.MutableRefObject<CompanyGroupsForTaskAssociation[]>,
  idDefinitionSelected?: number,
  selectedAssociation?: WorkflowDefinitionTaskGroupsAssociationsExtended,
) => {
  return {
    idDefinition: idDefinitionSelected ? idDefinitionSelected : row.definitionName,
    definitionName: selectedAssociation
      ? selectedAssociation.definitionName
      : listOfDefinitionsForAssociations?.find((el) => el.idDefinition === row.definitionName)?.wfName || '',
    definitionTaskAssociations: [
      {
        idTaskDefinition: 0,
        idTask: row.taskName,
        taskName: listOfTasksForAssociations?.find((el) => el.idTask === row.taskName)?.name || '',
        escalation: row.escalation,
        weight: row.weight,
        position: 1,
        nextPosition: 0,
        idConvalidationAction: row.idConvalidationAction,
        convalidationDescription:
          listOfConvalidation.find((el) => el.idAction === row.convalidationAction)?.description || '',
        taskGroupAssociations: latestGroupsList.current
          .filter((el) => el.assigned)
          .map((el) => {
            return {
              idGroup: el.idGroup,
              groupName: el.groupName,
              relation: el.relation,
              amountLimit: el.amountLimit,
              currency: el.currency,
              assigned: true,
            };
          }),
      },
    ],
  };
};
