import React from 'react';
import { useSelector } from 'react-redux';
import { actionType } from 'utils/constants';

import utils from 'utils';

import CommonButton from 'components/Buttons/CommonButton';
import { useHistory, useRouteMatch } from 'react-router';
import selectors from 'state/selectors';

const BackListVendor = () => {
  const t = utils.intl.useTranslator();
  const history = useHistory();
  const { path } = useRouteMatch();

  const isEdited = useSelector(selectors.configurator.getIsEditedVendorDetail);
  const selectedCompany = useSelector(selectors.configurator.getSelectedCompanyVendorConfig);

  const companyName = selectedCompany?.label;
  const hasPermission = utils.user.isActionByCompanyActive(actionType.BACK_LIST_VENDOR, companyName);

  return (
    <CommonButton
      action={() => {
        history.push(`${path}/VMD`);
      }}
      scope="tertiary"
      value={t(actionType.BACK_LIST_VENDOR)}
      icon="circle"
      disabled={!hasPermission || isEdited}
    />
  );
};

export default BackListVendor;
