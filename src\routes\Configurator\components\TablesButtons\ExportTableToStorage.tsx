import React from 'react';

import { actionType } from 'utils/constants';
import { useSelector } from 'react-redux';

import utils from 'utils';
import services from 'services';
import selectors from 'state/selectors';

import CommonButton from 'components/Buttons/CommonButton';

const ExportTableToStorage = () => {
  const t = utils.intl.useTranslator();
  const idProgram = useSelector(selectors.app.getProgramCode) || -1;
  const activeTable = useSelector(selectors.configurator.selectActiveTable);
  const table = activeTable?.value;

  const exportTableToStorage = async () => {
    try {
      if (table) {
        await services.exportTableToBucket(table, idProgram);
        utils.app.notify('success', t('export-table-to-storage-success'));
      }
    } catch (e) {
      console.error(e);
    }
  };

  return (
    <CommonButton
      action={exportTableToStorage}
      disabled={!table || !utils.user.isActionByCompanyActive(actionType.EXPORT_TABLE_TO_STORAGE)}
      scope="tertiary"
      value={t('ExportTableToStorage')}
    />
  );
};

export default ExportTableToStorage;
