import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import actions from 'state/actions';
import selectors from 'state/selectors';

import Table, { OriginalRow, TableColumn } from 'components/Table';
import utils from 'utils';
import styled from 'styled-components/macro';

interface DetailsTable {
  saveDetails: (rows: OriginalRow[]) => void;
  columns: TableColumn[];
}

const Loading = styled.div`
  margin-top: 20px;
  color: ${({ theme }) => theme.colorPalette.grey.dark};
  font-size: 15px;
`;

const DetailsTable = ({ columns, saveDetails }: DetailsTable) => {
  const dispatch = useDispatch();

  const t = utils.intl.useTranslator();

  const editedRowId = useSelector(selectors.configurator.getEditedRowIdVendorDetail);
  const tableRows = useSelector(selectors.configurator.getSubjectDetailsTableRows);

  const onSave = (editedRow: OriginalRow) => {
    const updatedRows = tableRows.map((row) => parseInt(row.supplierCode) === editedRow.supplierCode
      ? { ...editedRow, supplierCode: row.supplierCode }
      : { ...row }
    );

    dispatch(actions.configurator.setSubjectDetailsTableRows(updatedRows));
    dispatch(actions.configurator.setEditedRowIdSubjectDetails(''));

    saveDetails(updatedRows);
  };

  const onCancel = () => {
    dispatch(actions.configurator.setSubjectDetailsTableRows(tableRows as OriginalRow[]));
    dispatch(actions.configurator.setEditedRowIdSubjectDetails(''));
  };

  const onSelection = (row: OriginalRow) => {
    dispatch(actions.configurator.setSelectedRowIdSubjectDetails(row?.[0]?.supplierCode));
  };

  const isEditedVendorDetailView = useSelector(selectors.configurator.getIsEditedVendorDetail);
  const editedDetailRowId = useSelector(selectors.configurator.getEditedRowIdVendorDetail);

  return (
    <>
      { columns.length
        ? <Table
          rowId="supplierCode"
          columns={columns || []}
          rows={tableRows}
          hasResize
          hasSelection={isEditedVendorDetailView}
          editRowID={editedRowId}
          hasPagination
          onSelection={(row: OriginalRow) => onSelection(row)}
          initialSelection={editedDetailRowId ? [editedDetailRowId] : []}
          onSave={(row) => onSave(row)}
          onCancel={onCancel}
        />
      : <Loading>{t('loading')}</Loading> }
    </>
  )
};

export default DetailsTable;
