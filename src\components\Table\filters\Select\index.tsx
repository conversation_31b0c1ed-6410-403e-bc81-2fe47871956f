import React, { useMemo } from 'react';
import utils from 'utils';
import DropDown from 'components/Input/Dropdown';
import { Props } from '..';

const all = document.getElementById('react-select-portal');
const Select = (props: Props) => {
  const column = props;
  const { filterValue, setFilter, id: accessor, preFilteredRows, selectFilterOptions } = column;
  const options = useMemo(() => {
    if (!selectFilterOptions) return utils.table.buildOptionsByRowsWithCount(preFilteredRows, accessor);
    return selectFilterOptions;
  }, [preFilteredRows, accessor, selectFilterOptions]);

  const value = useMemo(() => {
    // eslint-disable-next-line eqeqeq
    return options.filter(({ value }) => value == filterValue) || [];
  }, [options, filterValue]);
  return (
    <DropDown
      noBorder
      placeholder=""
      small
      isClearable
      value={value}
      onChange={(option) => {
        setFilter(`${option?.value ?? ''}`);
      }}
      options={options}
      menuWidth="fit-content"
      closeMenuOnScroll={(e) => {
        if (all?.contains(e.target as Node)) return false;
        return true;
      }}
    />
  );
};

export default Select;
