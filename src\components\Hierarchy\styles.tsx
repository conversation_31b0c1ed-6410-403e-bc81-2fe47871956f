import styled from 'styled-components/macro';

export const Wrapper = styled.div`
  display: flex;
  flex-direction: row;
`;

export const Left = styled.div`
  width: 220px;
`;

export const Right = styled.div`
  width: 100%;
  max-height: 350px;
  overflow: auto;
  margin-left: 10px;
  padding-right: 10px;
`;

export const Title = styled.h4`
  font-size: ${({ theme }) => theme.fontSizePalette.heading.H4};
  color: ${({ theme }) => theme.colorPalette.grey.grey7};
  font-weight: 300;
  letter-spacing: -0.66px;
  line-height: 24px;
  margin-bottom: 15px;
`;

export const CardContainer = styled.div`
  max-height: 350px;
  overflow: auto;
  padding-right: 15px;
`;

export const CheckBoxesContainer = styled.div`
  margin-top: 15px;
`;
