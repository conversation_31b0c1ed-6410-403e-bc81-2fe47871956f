import { createGlobalStyle, DefaultTheme } from 'styled-components';
import {
  ZIndexPalette,
  BoxShadowPalette,
  ColorPalette,
  FontSizePalette,
  FontWeightPalette,
  BreakpointsPalette,
} from 'models/style';

import {
  colorPalette,
  fontFamily,
  fontSizePalette,
  fontWeightPalette,
  breakpointsPalette,
  zIndexPalette,
  boxShadowPalette,
  colorPaletteDark,
} from './utils/styleConstants';

declare module 'styled-components' {
  export interface DefaultTheme {
    colorPalette: ColorPalette;
    fontSizePalette: FontSizePalette;
    fontWeightPalette: FontWeightPalette;
    fontFamily: string;
    zIndexPalette: ZIndexPalette;
    breakpointsPalette: BreakpointsPalette;
    boxShadowPalette: BoxShadowPalette;
  }
}

export const theme: DefaultTheme = {
  colorPalette,
  fontSizePalette,
  fontWeightPalette,
  fontFamily,
  zIndexPalette,
  breakpointsPalette,
  boxShadowPalette,
};

export const lightTheme: DefaultTheme = {
  colorPalette,
  fontSizePalette,
  fontWeightPalette,
  fontFamily,
  zIndexPalette,
  breakpointsPalette,
  boxShadowPalette,
};

export const darkTheme: DefaultTheme = {
  ...lightTheme,
  colorPalette: colorPaletteDark,
};

export const StylesGlobal = createGlobalStyle`
  * {
    box-sizing: border-box;
    scrollbar-width: thin;
    scrollbar-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode
        ? `${theme.colorPalette.grey.grey7} ${theme.colorPalette.grey.grey3}`
        : `${theme.colorPalette.grey.grey4} ${theme.colorPalette.grey.grey1}`};
  }

  body {
    background-color: ${({ theme }) => theme.colorPalette.white};
    font-family: ${fontFamily};
    overflow: hidden;
  }

  #root {
    background-color: ${({ theme }) => theme.colorPalette.white};
    height: 100vh;
    min-height: 100vh;
    width: 100vw;
    min-width: 100vw;
  }

  h1 {
    font-size: ${({ theme }) => theme.fontSizePalette.heading.H1};
  }

  h2 {
    font-size: ${({ theme }) => theme.fontSizePalette.heading.H2};
    color: ${({ theme }) => theme.colorPalette.grey.grey7};
    font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  }

  h3 {
    font-size: ${({ theme }) => theme.fontSizePalette.heading.H3};
  }

  h4 {
    font-size: ${({ theme }) => theme.fontSizePalette.heading.H4};
    color: ${({ theme }) => theme.colorPalette.grey.grey7};
    font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  }

  p {
    font-weight: ${({ theme }) => theme.fontWeightPalette.light};
    color: ${({ theme }) => theme.colorPalette.grey.grey9};
    }
  label {

    font-weight: ${({ theme }) => theme.fontWeightPalette.light};
    color: ${({ theme }) => theme.colorPalette.grey.grey9};
  }
  ::-webkit-scrollbar {
    height: 8px;
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode
        ? theme.colorPalette.grey.grey3
        : theme.colorPalette.grey.grey3};
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode
        ? theme.colorPalette.grey.grey7
        : theme.colorPalette.grey.grey7};
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode
        ? theme.colorPalette.grey.grey8
        : theme.colorPalette.grey.grey8};
  }

  .datePicker-input-hover:hover, .datePicker-input-hover:focus-visible {
    outline: 0;
    border-width: 1px;
    border-style: solid;
    border-color: ${colorPalette.turquoise.normal};
  }

  .datePicker-input-hover-noBorder:hover, .datePicker-input-hover-noBorder:focus-visible {
    outline: 0;
    border-width: 1px;
    border-style: solid;
    border-color: none;
  }
  .react-datepicker-wrapper {
    width: unset !important;
  }

  #react-select-portal > div {
    z-index: ${zIndexPalette.highest} !important;
  }

  .react-select input {
    font-family: 'Roboto';
  }

  #notification-portal {
    display: flex;
    flex-direction: column;
    position: fixed;
    right: 24px;
    bottom: 21px;
    z-index: ${zIndexPalette.highest};
  }
`;

