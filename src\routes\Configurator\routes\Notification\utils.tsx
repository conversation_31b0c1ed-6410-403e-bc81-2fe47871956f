export const getDaysOfWeekList = (t: (key: string) => string) => [
  { label: t('Monday'), value: 1 },
  { label: t('Tuesday'), value: 2 },
  { label: t('Wednesday'), value: 3 },
  { label: t('Thursday'), value: 4 },
  { label: t('Friday'), value: 5 },
  { label: t('Saturday'), value: 6 },
  { label: t('Sunday'), value: 0 },
];

export const minutesOptions = [
  { value: 0, label: '00' },
  { value: 30, label: '30' },
];

export const getHoursOptions = () =>
  Array.from({ length: 12 }, (_, i) => ({
    label: `${i === 0 ? 12 : i} AM`,
    value: i.toString().padStart(2, '0'),
  })).concat(
    Array.from({ length: 12 }, (_, i) => ({
      label: `${i === 0 ? 12 : i} PM`,
      value: (i + 12).toString().padStart(2, '0'),
    })),
  );

export function generateCronExpression(
  hour: { label: string; value: string } | null,
  minute: { label: string; value: number } | null,
  allDays: boolean,
  daysOfWeek: { label: string; value: number }[],
): string {
  // Default to "0" if no minute is selected
  const m = minute?.value ?? 0;
  // Default to "*" if no hour is selected
  const h = hour?.value ?? '*';

  // Check if all days are selected manually
  const allDaysSelected =
    daysOfWeek.length === 7 ||
    daysOfWeek
      .map((d) => d.value)
      .sort()
      .join(',') === '0,1,2,3,4,5,6';

  // if all days are selected use "*"
  const dow = allDays || allDaysSelected ? '*' : daysOfWeek.map((d) => d.value).join(',');

  return `${m} ${h} * * ${dow}`;
}

export const parserfrequency = (frequency: string | undefined, t: (key: string) => string) => {
  if (!frequency) return { validHour: null, validMinute: null, allDays: true, daysOfWeek: [] };

  const frequencyParts = frequency.split(' ');

  if (frequencyParts.length < 5) {
    return { validHour: null, validMinute: null, allDays: true, daysOfWeek: [] };
  }

  // Extract minute and hour
  const minute = frequencyParts[0];
  const hour = frequencyParts[1];

  const validHour = getHoursOptions().find((h) => h.value === hour) || null;
  const validMinute = minutesOptions.find((m) => Number(m.value) === Number(minute)) || null;

  // Extract days of the week
  const daysOfWeekStr = frequencyParts[4] ?? '*';
  const allDays = daysOfWeekStr === '*';

  const daysOfWeek = allDays
    ? []
    : (daysOfWeekStr
        .split(',')
        .map((day) => getDaysOfWeekList(t).find((d) => d.value === Number(day)) || null)
        .filter(Boolean) as { label: string; value: number }[]);

  return { validHour, validMinute, allDays, daysOfWeek };
};
