import React from 'react';
import { useSelector } from 'react-redux';
import selectors from 'state/selectors';
import Toolbar from 'components/Toolbar';
import ToolbarButton from './ToolbarButton';
import DynamicDropdowns from './DinamicDropdowns';

const ToolBar = () => {
  const programName = useSelector(selectors.app.getProgramName);
  const toolbarActions = useSelector(selectors.configurator.selectTemplateActions);
  const actionList = useSelector(selectors.configurator.selectTemplateActionsList);
  const actionListDoubleSubView = useSelector(selectors.configurator.selectTemplateActionsListDoubleSubView);
  const actionDetailOpen = useSelector(selectors.configurator.selectTemplateActionsOpenDetail);

  return (
    <>
      <Toolbar.BlueBar>
        <Toolbar.Left>
          <Toolbar.DropContainer>
            <DynamicDropdowns />
          </Toolbar.DropContainer>
        </Toolbar.Left>
        <Toolbar.Right>
          <Toolbar.ModuleName>{programName}</Toolbar.ModuleName>
        </Toolbar.Right>
      </Toolbar.BlueBar>
      <Toolbar.ActionBar>
        <Toolbar.ButtonsContainer>
          {toolbarActions?.map((action, i) => (
            <ToolbarButton action={action} key={`${action}-${i}`} />
          ))}
          {actionList?.map((action, i) => (
            <ToolbarButton action={action} key={`${action}-${i}`} />
          ))}
          {actionListDoubleSubView?.map((action, i) => (
            <ToolbarButton action={action} key={`${action}-${i}`} />
          ))}
          {actionDetailOpen?.map((action, i) => (
            <ToolbarButton action={action} key={`${action}-${i}`} />
          ))}
        </Toolbar.ButtonsContainer>
      </Toolbar.ActionBar>
    </>
  );
};

export default ToolBar;
