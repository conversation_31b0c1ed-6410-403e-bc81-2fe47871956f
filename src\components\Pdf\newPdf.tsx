import React from 'react';
import { Canvas, ContentDiv, Div, Wrapper } from './newPdf.style';
import { usePdfRenderer } from './utils';
import PdfToolbar, { PdfButtonProps } from './PdfToolbar';
import PdfMovableArea from './movableArea';
import { BoundingBox } from 'models/response';
import { Area } from 'models/configurator';
import Preloader from 'components/Preloader';

export type PdfMapperProps = {
  onAreaChange: (area: Area) => void;
  isSelected: boolean;
  isEditing: boolean;
};

export type PdfRendererProps = {
  base64: string;
  height?: string;
  mapperProps?: PdfMapperProps;
} & PdfButtonProps;

const PdfRenderer: React.FC<PdfRendererProps> = ({
  base64,
  height,
  showDownloadButton = true,
  showFitButtons = true,
  showMappingButtons = false,
  showOcrButtons = true,
  showPageNavigation = true,
  showRotateButtons = true,
  showZoomButtons = true,
  showTranslateButtons = true,
  mapperProps,
}) => {
  const { onAreaChange, isSelected, isEditing } = mapperProps || {};
  const {
    canvasRef,
    wrapperRef,
    contentDivRef,
    pageNumber,
    numPages,
    handlePrevPage,
    handleNextPage,
    rotateClockwise,
    rotateCounterClockwise,
    zoomIn,
    zoomOut,
    canZoomIn,
    canZoomOut,
    fitToWidth,
    fitToHeight,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    boundingBox,
    setPageNumber,
    fitMode,
    handleLastPage,
    handleFirstPage,
    dragging,
    areas,
    updateArea,
    toggleMappingMode,
    handleMouseDoubleClick,
    ocrMode,
    setOcrMode,
    containerRef,
    isOcrActive,
    translateDocument,
    translationTooltip,
  } = usePdfRenderer({ base64, onAreaChange });

  return (
    <Div>
      <Preloader area="pdf" />
      <PdfToolbar
        base64={base64}
        fitMode={fitMode}
        numPages={numPages}
        onFirstPage={handleFirstPage}
        onFitToHeight={fitToHeight}
        onFitToWidth={fitToWidth}
        onLastPage={handleLastPage}
        onNextPage={handleNextPage}
        onPrevPage={handlePrevPage}
        onRotateClockwise={rotateClockwise}
        onRotateCounterClockwise={rotateCounterClockwise}
        onZoomIn={zoomIn}
        onZoomOut={zoomOut}
        canZoomIn={canZoomIn}
        canZoomOut={canZoomOut}
        pageNumber={pageNumber}
        setPageNumber={setPageNumber}
        ocrMode={isOcrActive}
        onToggleMappingMode={toggleMappingMode}
        ocrModeType={ocrMode}
        setOcrModeType={setOcrMode}
        showDownloadButton={showDownloadButton}
        showFitButtons={showFitButtons}
        showMappingButtons={showMappingButtons}
        showOcrButtons={showOcrButtons}
        showPageNavigation={showPageNavigation}
        showRotateButtons={showRotateButtons}
        showZoomButtons={showZoomButtons}
        showTranslateButtons={showTranslateButtons}
        translateDoc={translateDocument}
        translationTooltip={translationTooltip}
      />

      <Wrapper
        ref={wrapperRef}
        onMouseDown={handleMouseDown}
        onMouseMove={handleMouseMove}
        onMouseUp={handleMouseUp}
        onMouseLeave={handleMouseUp}
        onDoubleClick={handleMouseDoubleClick}
        cursor={dragging ? 'grab' : 'auto'}
        fixedHeight={height}
      >
        <div ref={containerRef} style={{ display: 'inline-block', position: 'relative', backgroundColor: 'grey' }}>
          <Canvas ref={canvasRef} />
          <ContentDiv ref={contentDivRef} />

          {areas.map((area) => (
            <PdfMovableArea
              key={`${area.id}-${area.width}-${area.height}-${area.left}-${area.top}-${isEditing && 'active'}`}
              id={area.id}
              pageNumber={pageNumber}
              rectangleArea={area}
              restrictedArea={{
                width: canvasRef.current?.clientWidth || 0,
                height: canvasRef.current?.clientHeight || 0,
                top: canvasRef.current?.offsetTop || 0,
                left: canvasRef.current?.offsetLeft || 0,
              }}
              onAreaChange={(newBox: BoundingBox) => {
                updateArea(area.id, newBox);
              }}
              currentPage={pageNumber}
              isEditing={isEditing ?? false}
              isSelected={isSelected ?? false}
              activeColor="#eb5648"
              notActiveColor="blue"
              name="name"
            />
          ))}

          {boundingBox && boundingBox.pageNumber === pageNumber && (
            <div
              style={{
                position: 'absolute',
                border: '1px dotted black',
                backgroundColor: 'yellow',
                opacity: 0.3,
                left: `${Math.min(boundingBox.x1, boundingBox.x2)}px`,
                top: `${Math.min(boundingBox.y1, boundingBox.y2)}px`,
                width: `${Math.abs(boundingBox.x2 - boundingBox.x1)}px`,
                height: `${Math.abs(boundingBox.y2 - boundingBox.y1)}px`,
                pointerEvents: 'none',
              }}
            ></div>
          )}
        </div>
      </Wrapper>
    </Div>
  );
};

export default PdfRenderer;
