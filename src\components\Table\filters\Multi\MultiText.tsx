import React, { useEffect } from 'react';
import { Props } from '..';
import Search from '../../../Input/SearchInput';
import MultiField from './multiField';
import { createPortal } from 'react-dom';
import utils from 'utils';

const portalId = 'multifilter-portal';

const MultiText = (column: Props) => {
  const { filterValue, setFilter, id, Header, preFilteredRows, multiFilterOptions, hasLocalKeys } = column;
  const t = utils.intl.useTranslator();
  const allColumnValues = utils.table.buildOptionsByRows(
    preFilteredRows,
    id,
    multiFilterOptions,
    t,
    hasLocalKeys,
    Header,
  );

  const [isOpen, setIsOpen] = React.useState(false);
  const [selected, setSelected] = React.useState<string[]>((filterValue ?? '')?.split?.(',') ?? []);
  const ref = React.useRef<HTMLDivElement>(null);
  let timeoutId = 0;

  useEffect(() => {
    const handleClose = (e: any) => {
      if (!ref.current?.contains(e.target)) {
        setIsOpen(false);
      }
    };
    if (isOpen) {
      window.addEventListener('mousewheel', handleClose);
      window.addEventListener('mousedown', handleClose);
    }
    return () => {
      window.removeEventListener('mousewheel', handleClose);
      window.removeEventListener('mousedown', handleClose);
    };
  }, [isOpen]);

  const setDebounceFilter = (e: React.ChangeEvent<HTMLInputElement>) => {
    clearTimeout(timeoutId);
    const { value } = e.target;
    timeoutId = setTimeout(() => {
      setFilter(value);
      document.getElementById(`${id}-${Header}`)?.focus();
    }, 750);
  };

  const onChangeFilter = (filter: string) => {
    setFilter(filter);
  };

  useEffect(() => {
    if (typeof filterValue === 'string') {
      setSelected(filterValue ? filterValue.split(',') : []);
    }
  }, [filterValue]);

  const transformValue = (value: string | undefined) => {
    if (!value) return '';
    const splitedValue = value?.split(',');
    let newValue = '';
    splitedValue.forEach((item) => {
      const val = multiFilterOptions?.get(item);
      newValue += `${val ?? item},`;
    });
    return newValue.slice(0, -1);
  };

  return (
    <div>
      <Search
        id={`${id}-${Header}`}
        placeholder=""
        fullWidth
        small
        autocomplete={false}
        onChange={setDebounceFilter}
        value={multiFilterOptions ? transformValue(filterValue) : filterValue}
        onClick={(e: React.MouseEvent<HTMLInputElement, MouseEvent>) => {
          const { x, y } = e.currentTarget.getClientRects()[0];
          const outOfView = 32 * 3 + 31 * Math.min(5, allColumnValues.length);

          if (window.innerHeight < y + 30 + outOfView) {
            ref.current?.style.setProperty('left', `${x}px`);
            ref.current?.style.setProperty('top', `${y - 30 - outOfView}px`);
          } else {
            ref.current?.style.setProperty('left', `${x}px`);
            ref.current?.style.setProperty('top', `${y + 30}px`);
          }
          setIsOpen(true);
        }}
      />
      {createPortal(
        <div ref={ref} style={{ position: 'absolute' }}>
          {isOpen && (
            <MultiField
              options={allColumnValues}
              open={isOpen}
              selected={selected}
              setSelected={setSelected}
              onChangeFilter={onChangeFilter}
            />
          )}
        </div>,
        document.getElementById(portalId) || document.body,
      )}
    </div>
  );
};

export default MultiText;
