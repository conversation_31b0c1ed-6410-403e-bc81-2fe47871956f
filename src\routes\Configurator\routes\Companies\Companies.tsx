/* eslint-disable max-lines */
import React, { useEffect, useCallback, useState } from 'react';
import { Formik } from 'formik';

import { TableColumn } from 'components/Table';
import { Company, CompanyCountryParams, DocumentType, HTableValues } from 'models/response';

import { useSelector, useDispatch } from 'react-redux';

import utils from 'utils';
import services from 'services';
import selectors from 'state/selectors';
import actions from 'state/actions';

import * as Yup from 'yup';

import { Wrapper, Title, Container } from '../../styles';

import Table from 'components/Table';
import FormCrud from './components/FormCrud';
import { FormValuesCompany, Option, OptionAT } from 'models/configurator';
import styled from 'styled-components/macro';
import { useAsync } from 'react-use';

interface LeftProps {
  reduce: boolean;
}

const Left = styled.div<LeftProps>(
  ({ reduce }) => `
  flex-basis: ${reduce ? '68%' : '100%'};
  width: 68%;
  padding-right: 15px;
`,
);

const defaultValues: FormValuesCompany = {
  name: '',
  label: '',
  vatNumber: '',
  socialReason: '',
  fiscalCode: '',
  phone: '',
  fax: '',
  email: '',
  url: '',
  onlineCode: '',
  referenceUser: undefined,
  counterPrefix: '',
  country: '',
  ocrDoc: false,
  documentTypeList: [],
  currency: '',
  countryCurrency: '',
};

const Companies = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();

  const idProgram = useSelector(selectors.app.getProgramCode) || -1;
  const idUser = useSelector(selectors.user.getIdUser) || -1;

  // Redux selectors companies
  const companies = useSelector(selectors.configurator.getCompanies);
  const selectedCompany = useSelector(selectors.configurator.getSelectedCompany);
  const formModeCompany = useSelector(selectors.configurator.getFormModeCompany);
  const users = useSelector(selectors.configurator.getUsers);

  const [documentTypes, setDocumentTypes] = useState<DocumentType[]>([]);
  const [optionsDocument, setOptionsDocument] = useState<Option[]>([]);
  const [optionsUser, setOptionsUser] = useState<Option[]>([]);
  const [optionsCurrency, setOptionsCurrency] = useState<OptionAT[]>([]);
  const [optionsCountries, setOptionsCountries] = useState<OptionAT[]>([]);

  const companyColumns: TableColumn[] = [
    { accessor: 'name', Header: t('companyCode'), filterType: 'free' },
    { accessor: 'label', Header: t('label'), filterType: 'free' },
    { accessor: 'vatNumber', Header: t('vatNumber'), filterType: 'free' },
    { accessor: 'socialReason', Header: t('socialReason'), filterType: 'free' },
    { accessor: 'fiscalCode', Header: t('fiscalCode'), filterType: 'free' },
    { accessor: 'phone', Header: t('phone'), filterType: 'free' },
    { accessor: 'fax', Header: t('fax'), filterType: 'free' },
    { accessor: 'email', Header: t('email'), filterType: 'free' },
    { accessor: 'url', Header: t('url'), filterType: 'free' },
    {
      accessor: 'currency',
      Header: t('currency'),
      filterType: 'free',
      Cell: ({ value, row }: { value: any; row: any }) => {
        return value ? value : row.original.countryCurrency;
      },
    },
  ];

  const setAllCompaniesFunction = async () => {
    try {
      const { data } = await services.getDocuments({ idUser, idProgram, idTemplate: 15, allAuthorized: false });
      dispatch(actions.configurator.setCompanies(utils.input.removeDefaultValue(data, 'idCompany')));
      const optionUser = users.map((user) => ({
        value: user.idUser,
        label: user.name,
      }));
      setOptionsUser(optionUser);
    } catch (e) {
      console.error(e);
    }
  };

  const getDocument = async () => {
    try {
      const { data } = await services.getDocumentTypes();
      setDocumentTypes(data);

      const optionDoc = data.map((doc: DocumentType) => ({
        value: doc?.idDocType,
        label: doc?.docName,
      }));
      setOptionsDocument(optionDoc);
    } catch (e) {
      console.error(e);
    }
  };

  const stableSetAllCompaniesFunction = useCallback(setAllCompaniesFunction, []);

  useEffect(() => {
    stableSetAllCompaniesFunction();
    getDocument();
  }, [stableSetAllCompaniesFunction]);

  useAsync(async () => {
    if (formModeCompany) {
      const { data } = await services.getAP2HTable('h_currency', selectedCompany?.idCompany || 0);
      const currencyList = data.map((currency: HTableValues) => ({
        value: currency.code,
        label: currency.description,
      }));
      setOptionsCurrency(currencyList);

      const { data: countriesReponse } = await services.getCountryParams();
      const listOfCountriesMapped = countriesReponse.map((country: CompanyCountryParams) => ({
        value: country.country,
        label: country.description,
      }));

      setOptionsCountries(listOfCountriesMapped);
    }
  }, [formModeCompany]);

  const onCompanySelection = (rows: Company[]) => {
    dispatch(actions.configurator.setSelectedCompany(rows[0]));
    dispatch(actions.configurator.setFormModeCompany(null));
  };

  const setInitialValues = (): FormValuesCompany => {
    if (selectedCompany && formModeCompany === 'edit') {
      return {
        name: selectedCompany.name,
        label: selectedCompany.label,
        vatNumber: selectedCompany.vatNumber,
        socialReason: selectedCompany.socialReason,
        fiscalCode: selectedCompany.fiscalCode,
        phone: selectedCompany.phone,
        fax: selectedCompany.fax,
        email: selectedCompany.email,
        url: selectedCompany.url,
        onlineCode: selectedCompany.onlineCode,
        referenceUser: selectedCompany.referenceUser,
        counterPrefix: selectedCompany.counterPrefix,
        country: selectedCompany.country,
        ocrDoc: selectedCompany?.ocrDoc ? selectedCompany.ocrDoc : false,
        documentTypeList: selectedCompany.documentTypeList
          ? selectedCompany.documentTypeList.map((el) => ({
              value: el.idDocType,
              label: el.docName,
            }))
          : [],
        currency: selectedCompany.currency,
        countryCurrency: selectedCompany.countryCurrency,
      };
    }
    return defaultValues;
  };

  const validationLogic = () => {
    return Yup.object({
      name: Yup.string()
        .required(t('required'))
        .matches(/^(?=.*\S).+$/, t('required')),
    });
  };

  function documentConstruction(allDocuments: DocumentType[], selectedDocuments: Option[]) {
    const commonElement = [];
    for (let i = 0; i < allDocuments.length; i++) {
      for (let j = 0; j < selectedDocuments.length; j++) {
        if (allDocuments[i].idDocType === selectedDocuments[j].value) {
          commonElement.push(allDocuments[i]);
        }
      }
    }

    return commonElement;
  }

  const onSubmit = async (values: FormValuesCompany) => {
    const { name, documentTypeList } = values;

    try {
      if (formModeCompany === 'clone') {
        if (selectedCompany) {
          await services.cloneCompany({
            idCompanyToClone: selectedCompany.idCompany,
            nameNewCompany: name,
          });
          utils.app.notify('success', t('company-cloned'));
          setAllCompaniesFunction();
        }
      }
      if (formModeCompany === 'new') {
        const body = {
          ...values,
          documentTypeList: documentConstruction(documentTypes, documentTypeList),
        };
        await services.createCompany(body);
        utils.app.notify('success', t('company-created'));
        dispatch(actions.configurator.setFormModeCompany(null));
        setAllCompaniesFunction();
      }

      if (formModeCompany === 'edit' && selectedCompany) {
        const bodyCopy = {
          ...values,
          idCompany: selectedCompany?.idCompany,
          documentTypeList: documentConstruction(documentTypes, documentTypeList),
        };
        const { data } = await services.saveCompany(bodyCopy);
        const newCompanies = companies.map((e) => (e.idCompany === data.idCompany ? data : e));
        dispatch(actions.configurator.setCompanies(newCompanies));
        dispatch(actions.configurator.setSelectedCompany(data));
        utils.app.notify('success', t('company-saved'));
      }
    } catch (e) {
      console.error(e);
    } finally {
      const { data } = await services.getConfigurationsByCompanies(idUser, idProgram);
      dispatch(actions.app.setCompanies(utils.input.removeDefaultValue(data, 'idCompany')));
    }
  };

  return (
    <>
      <Wrapper>
        <Title>
          <h2>{t('companies')}</h2>
        </Title>
        <Formik
          initialValues={setInitialValues()}
          onSubmit={(values, { resetForm }) => {
            onSubmit(values);
            resetForm();
          }}
          enableReinitialize
          validationSchema={validationLogic()}
        >
          {({
            values,
            errors,
            touched,
            isValid,
            isValidating,
            dirty,
            handleChange,
            setFieldValue,
            handleBlur,
            submitForm,
            resetForm,
          }) => (
            <Container>
              <Left reduce={formModeCompany !== null}>
                {companies.length > 0 ? (
                  <Table
                    rowId="idCompany"
                    onSelection={(rows: Company[]) => onCompanySelection(rows)}
                    hasToolbar
                    columns={companyColumns}
                    rows={companies}
                    hasSelection
                    hasPagination
                    hasResize
                    hasSort
                    hasFilter
                    initialSelection={selectedCompany ? [selectedCompany.idCompany] : []}
                  />
                ) : null}
              </Left>
              {formModeCompany !== null ? (
                <FormCrud
                  values={values}
                  optionsDocuments={optionsDocument}
                  optionsUsers={optionsUser}
                  handleChange={handleChange}
                  setFieldValue={setFieldValue}
                  handleBlur={handleBlur}
                  errors={errors}
                  touched={touched}
                  dirty={dirty}
                  isValid={isValid}
                  isValidating={isValidating}
                  submitForm={submitForm}
                  resetForm={resetForm}
                  optionsCurrency={optionsCurrency}
                  optionsCountries={optionsCountries}
                />
              ) : null}
            </Container>
          )}
        </Formik>
      </Wrapper>
    </>
  );
};

export default Companies;
