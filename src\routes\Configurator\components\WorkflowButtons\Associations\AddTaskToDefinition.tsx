import React from 'react';

import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';
import { actionType } from 'utils/constants';
import { useDispatch, useSelector } from 'react-redux';
import selectors from 'state/selectors';
import actions from 'state/actions';
import services from 'services';
import { transformedRows } from 'routes/Configurator/routes/Workflow/routes/WorkflowCompanyDetails/tabs/Associations/utils';
import { useSWRConfig } from 'swr';

const AddTaskToDefinition = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const editedRowId = useSelector(selectors.configurator.getEditedRowIdWfAssociations);
  const selectedAssociation = useSelector(selectors.configurator.getSelectedDefinitionTaskGroupsAssociations);
  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  const { listOfGroupsForAssociations } = services.useGetGroupsForTaskAssociations(
    'getCompanyGroupsForAssociationsKey',
    companiesDefinition?.idCompany,
  );

  const { associations } = services.useGetDefinitionTaskGroupsAssociations(
    'getDefinitionTaskGroupsAssociationsKey',
    companiesDefinition?.idCompany,
  );

  const { mutate } = useSWRConfig();

  const addTask = () => {
    if (selectedAssociation) {
      const obj = {
        definitionName: selectedAssociation.definitionName || '',
        id: selectedAssociation.id,
        idDefinition: selectedAssociation.idDefinition || 0,
        definitionTaskAssociations: [
          {
            convalidationDescription: '',
            escalation: false,
            idConvalidationAction: 0,
            idTask: -2,
            idTaskDefinition: 0,
            nextPosition: null,
            position: 0,
            taskGroupAssociations: [],
            taskName: '',
            weight: 0,
          },
          ...selectedAssociation.definitionTaskAssociations,
        ],
        subRows: [
          {
            convalidationDescription: '',
            escalation: false,
            id: -2,
            idConvalidationAction: 0,
            idTask: -2,
            idTaskDefinition: 0,
            isSubRow: true,
            nextPosition: null,
            position: 0,
            taskGroupAssociations: [],
            taskName: '',
            weight: 0,
          },
          ...selectedAssociation.subRows,
        ],
      };

      const newListAssociation = associations?.map((el) => {
        if (el.idDefinition === selectedAssociation.idDefinition) {
          return obj;
        }
        return el;
      });

      const transformedList = transformedRows(newListAssociation || []);

      mutate('getDefinitionTaskGroupsAssociationsKey', transformedList, {
        revalidate: false,
      });
      dispatch(actions.configurator.setEditedRowIdWfAssocations(-2));
      dispatch(actions.configurator.setIsActiveAddTaskToAssociation(true));
      // non serve piu lo stato per l'open row
      // dispatch(actions.configurator.openRowIdAssociationTable(selectedAssociation.id));
      if (companiesDefinition?.idCompany) {
        dispatch(actions.configurator.setListOfGroupsForTaskAssociations(listOfGroupsForAssociations || []));
      }
      dispatch(actions.configurator.setIsActiveAddTaskToAssociation(true));

      // each definition has a list of available tasks for association,
      // find the id definition of the selected association
      // for update the API that returns list of available tasks
      dispatch(actions.configurator.setSelectedIdDefFromAssociations(selectedAssociation.idDefinition));
    }
  };

  return (
    <CommonButton
      action={() => addTask()}
      scope="tertiary"
      value={t(actionType.ADD_TASK_TO_DEFINITION)}
      icon="circle"
      disabled={
        editedRowId !== null ||
        selectedAssociation === null ||
        !!selectedAssociation.isSubRow ||
        !utils.user.isActionByCompanyActive(actionType.ADD_TASK_TO_DEFINITION, companiesDefinition?.companyName)
      }
    />
  );
};

export default AddTaskToDefinition;
