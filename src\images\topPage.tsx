import { useTheme } from 'providers/ThemeProvider';
import React from 'react';

interface ArrowProps {
  rotation?: number;
}

const TopPage: React.FC<ArrowProps> = ({ rotation = 0 }) => {
  const { theme } = useTheme();
  const color = theme.colorPalette.isDarkMode
    ? theme.colorPalette.turquoise.light
    : theme.colorPalette.turquoise.normal;

  return (
    <>
      <svg
        width="26px"
        height="26px"
        viewBox="0 0 48 48"
        xmlns="http://www.w3.org/2000/svg"
        style={{ transform: `rotate(${rotation}deg)` }}
      >
        <title>page-top</title>
        <g id="Layer_2" data-name="Layer 2">
          <g id="invisible_box" data-name="invisible box">
            <rect width="48" height="48" fill="none" />
          </g>
          <g id="icons_Q2" data-name="icons Q2">
            <path fill={color} d="M6,6H42a2,2,0,0,1,0,4H6A2,2,0,0,1,6,6Z" />
            <path
              fill={color}
              // eslint-disable-next-line max-len
              d="M35.4,24.5l-10-9.9a1.9,1.9,0,0,0-2.8,0l-10,9.9a2.1,2.1,0,0,0-.2,2.7,1.9,1.9,0,0,0,3,.2L22,20.8V38a2,2,0,0,0,4,0V20.8l6.6,6.6a1.9,1.9,0,0,0,3-.2A2.1,2.1,0,0,0,35.4,24.5Z"
            />
          </g>
        </g>
      </svg>
    </>
  );
};

export default TopPage;
