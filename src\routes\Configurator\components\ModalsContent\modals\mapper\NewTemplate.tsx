/* eslint-disable max-lines */
import React from 'react';
import Modal from 'components/Modal';
import { useSelector } from 'react-redux';
import service from 'services';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import IntlHelper from 'utils/helpers/intl.helper';
import DropFileInput from 'components/Input/DropFileInput';
import { useFormik } from 'formik';
import { Dropdown, TextInput } from 'components/Input';
import { colorPalette } from 'utils/styleConstants';
import services from 'services';
import { useDispatch } from 'react-redux';
import { useAsync } from 'react-use';
import actions from 'state/actions';
import { InputLabel } from 'components/Input/TextInput';
import utils from 'utils';
import { OTemplate } from 'models/configurator';

const Subtitle = styled.h5`
  font-size: ${({ theme }) => theme.fontSizePalette.body.M};
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-weight: 300;
  letter-spacing: -0.5px;
  line-height: 19px;
  margin-bottom: 10px;
`;

const Content = styled.div`
  display: flex;
  flex-direction: column;
  align-items: baseline;
  justify-content: center;
  width: 300px;
  margin: auto;
  margin-top: 25px;
  row-gap: 25px;
`;

const Item = styled.div`
  display: inline-flex;
  justify-content: space-between;
  align-items: center;
  width: 300px;
  display: inline-block;
`;
// react drag and drop files
const NewTemplate = () => {
  const t = IntlHelper.useTranslator();
  const { selectAllIdDocTypeNames } = selectors.configurator;
  const dispatch = useDispatch();
  const docTypes = useSelector(selectors.configurator.selectDocTypes);
  const { createNewTemplate } = service;

  useAsync(async () => {
    if (docTypes.length === 0) {
      try {
        const { data } = await services.getTemplatesWithDocType();
        dispatch(actions.configurator.setDocTypes(data));
      } catch (error) {
        console.error(error);
      }
    }
  }, [dispatch]);

  const formikInitialValue: {
    name: string;
    docType: number | null;
    file: File | null;
    fileJson: File | null;
  } = {
    name: '',
    file: null,
    docType: null,
    fileJson: null,
  };

  const onSaveAction = async ({ docType, file, fileJson, name }: typeof formikInitialValue) => {
    try {
      if (!(docType && file && name)) {
        throw new Error(t('some params are not valid'));
      }
      const fileToBase64 = await utils.file.toBase64(file);
      if (typeof fileToBase64 !== 'string') {
        throw new Error(t('fileToBase64 is not string'));
      }
      const fileJsonToBase64 = fileJson ? await utils.file.toBase64(fileJson) : undefined;
      const templateNumber = await createNewTemplate({
        idDocType: docType,
        templateName: name,
        pdfTemplatePath: `data:application/pdf;base64,${fileToBase64}`,
        metadataJsonPath:
          typeof fileJsonToBase64 === 'string' ? `application/json;base64,${fileJsonToBase64}` : undefined,
      });
      const OtemplateElement: OTemplate = {
        idTemplate: templateNumber.data,
        idDocType: docType,
        templateName: name,
        pdfNrPages: file.size,
        pdfTemplatePath: `lucy4-template-mapper-bucket/${name}.pdf`,
        metadataJsonPath: null,
      };
      dispatch(actions.configurator.setNewOtemplate(OtemplateElement));
      utils.app.notify('success', t('template created'));
    } catch (error) {
      utils.app.notify('fail', error as string);
    }
  };
  const formik = useFormik({
    initialValues: formikInitialValue,
    onSubmit: (values) => onSaveAction(values),
  });

  // formik state
  const { values, setFieldValue, handleSubmit, handleChange, dirty } = formik;
  const docTypesOption = useSelector(selectAllIdDocTypeNames('value', 'label'));
  const activeDocTypeOption = docTypesOption.find((option) => option.value === values.docType);

  const onFileAction = (file: File | null, field: string) => {
    setFieldValue(field, file);
  };
  const disabled = values.name === '' || values.name === null || values.docType === null || values.file === null;

  return (
    <div>
      <Modal.Header title={t('New Template')} />
      <Modal.Content>
        <Subtitle>{t('The template will be available after the saving process is completed')}</Subtitle>
        <form onSubmit={handleSubmit}>
          <Content>
            <Item>
              <TextInput
                name="name"
                value={values.name}
                onChange={handleChange}
                label={t('Template-name')}
                width="168px"
                labelMarginRight="auto"
                labelFontSize="16px"
                labelWidth="auto"
                labelColor={colorPalette.grey.grey9}
                borderRadius="4px"
              />
            </Item>
            <Item>
              <InputLabel
                marginRight="auto"
                labelFontSize="16px"
                labelWidth={'calc(100% - 168px)'}
                textAlign="left"
                labelColor={colorPalette.grey.grey9}
              >
                {t('document type')}
              </InputLabel>
              <Dropdown
                placeholder={t('Select document type')}
                onChange={({ value }) => {
                  setFieldValue('docType', value);
                }}
                options={docTypesOption}
                value={activeDocTypeOption}
                margin="0 0 0 auto"
                width="168px"
                displayWrapper="inline-block"
              />
            </Item>
            <Item>
              <DropFileInput
                onFileChange={(file) => onFileAction(file, 'file')}
                fileTypes={['application/pdf']}
                label={
                  <>
                    {t('Upload pdf')}
                    <strong>{t('browse')}</strong>
                  </>
                }
              />
            </Item>
            <Item>
              <DropFileInput
                onFileChange={(file) => onFileAction(file, 'fileJson')}
                fileTypes={['application/json']}
                label={
                  <>
                    {t('Upload json')}
                    <strong>{t('browse')}</strong>
                  </>
                }
              />
            </Item>
          </Content>
        </form>
      </Modal.Content>
      <Modal.Footer
        confirmText={t('Save')}
        confirmAction={formik.submitForm}
        withCloseButton
        confirmDisabled={!dirty || disabled}
      />
    </div>
  );
};

export default NewTemplate;
