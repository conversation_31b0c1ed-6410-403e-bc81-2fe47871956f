import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import utils from 'utils';
import actions from 'state/actions';
import { actionType } from 'utils/constants';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';

const AddOpenWfNotification = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const selectedRow = useSelector(selectors.configurator.getSelectedOpenWfSettings);
  const formMode = useSelector(selectors.configurator.getFormModeOpenWf);
  return (
    <CommonButton
      action={() => dispatch(actions.configurator.setFormModeOpenWf('add'))}
      scope="tertiary"
      value={t(actionType.ADD_OPEN_WF_NOTIFICATIONS)}
      icon="circle"
      disabled={
        !utils.user.isActionByCompanyActive(actionType.ADD_OPEN_WF_NOTIFICATIONS) || !!selectedRow || !!formMode
      }
    />
  );
};

export default AddOpenWfNotification;
