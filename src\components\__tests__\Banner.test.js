import React from 'react';
import { screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom/extend-expect';
import BannerComponent from '../BannerComponent/index';
import { renderWithStyle } from 'utils/helpers/test.helpers';

describe('BannerComponent', () => {
  let openSpy;

  beforeEach(() => {
    openSpy = jest.spyOn(window, 'open').mockImplementation();
  });

  afterEach(() => {
    openSpy.mockRestore();
  });

  test('renders the banner title', () => {
    renderWithStyle(<BannerComponent imageUrl="https://upload.wikimedia.org/wikipedia/commons/thumb/d/d1/Ray-Ban_logo.svg/1200px-Ray-Ban_logo.svg.png" bannerTitle="Test Banner" linkTo="https://www.ray-ban.com/italy" />);
    expect(screen.getByText('Test Banner')).toBeInTheDocument();
  });

  test('renders the image container', () => {
    renderWithStyle(<BannerComponent imageUrl="https://upload.wikimedia.org/wikipedia/commons/thumb/d/d1/Ray-Ban_logo.svg/1200px-Ray-Ban_logo.svg.png" bannerTitle="Test Banner" linkTo="https://www.ray-ban.com/italy" />);
    expect(screen.getByTestId('image-container')).toBeInTheDocument();
  });

  test('opens link in new tab when icon is clicked and linkTo is provided', () => {
    renderWithStyle(<BannerComponent imageUrl="https://upload.wikimedia.org/wikipedia/commons/thumb/d/d1/Ray-Ban_logo.svg/1200px-Ray-Ban_logo.svg.png" bannerTitle="Test Banner" linkTo="https://www.ray-ban.com/italy" />);
    fireEvent.click(screen.getByTestId('linkToWebSite'));
    expect(openSpy).toHaveBeenCalledWith('https://www.ray-ban.com/italy', '_blank');
  });

  test('does not open link if linkTo is empty', () => {
    renderWithStyle(<BannerComponent imageUrl="https://upload.wikimedia.org/wikipedia/commons/thumb/d/d1/Ray-Ban_logo.svg/1200px-Ray-Ban_logo.svg.png" bannerTitle="Test Banner" linkTo="" />);
    fireEvent.click(screen.getByTestId('linkToWebSite'));
    expect(openSpy).not.toHaveBeenCalled();
  });
});
