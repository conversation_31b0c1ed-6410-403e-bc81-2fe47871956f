import { CommonButton } from 'components/Buttons';
import React from 'react';
import utils from 'utils';
import { useSelector } from 'react-redux';
import { actionType } from 'utils/constants';
import selectors from 'state/selectors';
import services from 'services';
import useCheckBeforeAction from '../hooks';
import { lucyModuleKey } from 'utils/configs';
import { useFormikContext } from 'formik';
import { FormValues, Q1Value } from 'models/documents';

const GenerateDEM = () => {
  const records = useSelector(selectors.documents.selectAllActiveDocuments);
  const t = utils.intl.useTranslator();
  const saveDocument = utils.hooks.useSaveDocument();
  const { checkDocuments } = useCheckBeforeAction(actionType.SANITY_CHECK);
  const protocol = useSelector(selectors.documents.selectActiveDocumentID);
  const documentHeader = useSelector(selectors.documents.selectDocumentHeader);
  const { setStatus } = useFormikContext<{ [key: string]: Q1Value | FormValues }>() || {};
  const programCode = useSelector(selectors.app.getProgramCode);
  const getProgramCode = (idProgram: number) => {
    return lucyModuleKey[idProgram as keyof typeof lucyModuleKey] || '';
  };

  const generateDem = async () => {
    const timestamp = utils.date.convertDate(new Date());

    try {
      const response = await services.generateDEM(records[0].protocol);

      utils.file.downloadFileFromBase64Source(
        `${records[0].protocol}_DEM_${timestamp}`,
        response.data,
        'application/pdf',
        'pdf',
      );
      utils.app.notify('success', t('dem-generated-successfully'), 5000);
    } catch (e) {
      console.error(e);
      utils.app.notify('fail', t('error-generating-dem'), 5000);
    }
  };

  const applyRules = async () => {
    try {
      if (protocol && documentHeader?.actualDocumentType?.idDocType) {
        const lucyModule = getProgramCode(programCode);
        const { data } = await services.checkMandatoryFields({ protocol, rulesGroup: lucyModule });
        const status = data.fields.map((el: any) => ({ idField: el.idField, status: el.fieldStatus }));
        setStatus(status);
        if (data.docStatus === 1) {
          utils.app.notify('fail', data.docMessageStatus || t('error-submitting-the-document'), 10000);
        } else if (data.docStatus === 2 && data.docMessageStatus) {
          utils.app.notify('warning', data.docMessageStatus, 10000);
        }
        generateDem();
      }
    } catch (e) {
      console.error(e);
    }
  };

  const onClick = async () => {
    const docFree = await checkDocuments();
    if (!docFree) return;
    const val = await saveDocument();
    if (val === null || val === undefined) {
      return;
    }
    applyRules();
  };

  return (
    <CommonButton
      action={onClick}
      scope="tertiary"
      value={t('generate-dem')}
      icon="file-pdf"
      disabled={!utils.user.isActionEnabled(records, actionType.GENERATE_DEM)}
    />
  );
};

export default GenerateDEM;
