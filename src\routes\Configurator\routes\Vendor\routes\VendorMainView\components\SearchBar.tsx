import CommonButton from 'components/Buttons/CommonButton';
import { Dropdown, TextInput } from 'components/Input';
import { AppliedFilters } from 'models/configurator';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import selectors from 'state/selectors';
import utils from 'utils';
import { Formik } from 'formik';
import styled from 'styled-components/macro';
import services from 'services';
import { SearchSubject } from 'models/response';
import { convertDateValues, getFilterColumn, filterOperator } from '../../utils';

const FormWrapper = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 704px;

  & > span {
    color: ${({ theme }) => theme.colorPalette.grey.grey5};
  }
`;

const plusSign = <span> + </span>;

const SearchBar = () => {
  const selectedCompany = useSelector(selectors.configurator.getSelectedCompanyVendorConfig);
  const appliedFilters = useSelector(selectors.configurator.getAppliedFilters);
  const editedVendorId = useSelector(selectors.configurator.getIdEditedVendorFromList);
  const subjectTableColumns = useSelector(selectors.configurator.getSubjectTableColumns);

  const { timeZone } = useSelector(selectors.user.getUserPreference);

  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const onSubmit = async (values: AppliedFilters) => {
    dispatch(actions.configurator.setAppliedFilters(values));
    const { data } = await services.getSubjects(
      {
        key: values.column?.value || '',
        value: values.value,
        searchType: values.operator?.value || 0,
        idCompany: selectedCompany?.value || -1,
      },
      'module',
    );

    const dataMod = data.map((subject) => {
      const subjectMod = convertDateValues(subject, subjectTableColumns || [], timeZone);
      return subjectMod as SearchSubject;
    });

    dispatch(actions.configurator.setSubjectList(dataMod as SearchSubject[]));
  };

  useEffect(() => {
    dispatch(
      actions.configurator.setAppliedFilters({
        column: getFilterColumn(t)[0],
        operator: filterOperator[1],
        value: '',
      }),
    );
  }, [dispatch, t]);

  return (
    <>
      <Formik enableReinitialize initialValues={appliedFilters} onSubmit={onSubmit}>
        {({ values, handleBlur, handleChange, handleSubmit, setFieldValue }) => (
          <form onSubmit={handleSubmit}>
            <FormWrapper>
              <Dropdown
                name="column"
                value={values['column']}
                onChange={(value) => {
                  setFieldValue('column', value);
                }}
                options={getFilterColumn(t)}
                disabled={!selectedCompany || !!editedVendorId}
                placeholder={t('select_column')}
                width="170px"
              />
              {plusSign}
              <Dropdown
                name="operator"
                placeholder={t('select_operator')}
                value={values['operator']}
                onChange={(value) => {
                  setFieldValue('operator', value);
                }}
                options={filterOperator}
                disabled={!selectedCompany || !!editedVendorId}
                width="170px"
              />
              {plusSign}
              <TextInput
                id="value"
                value={values['value']}
                placeholder={t('valoreRicerca')}
                onChange={handleChange}
                onBlur={handleBlur}
                disabled={!selectedCompany || !!editedVendorId}
                borderRadius="5px"
                width="170px"
              />
              <CommonButton
                scope="primary"
                value={t('search')}
                type="submit"
                disabled={!values['value'] || !!editedVendorId}
              />
            </FormWrapper>
          </form>
        )}
      </Formik>
    </>
  );
};

export default SearchBar;
