import React from 'react';
import styled from 'styled-components/macro';
import { headerHeight } from 'utils/styleConstants';

const Home = styled.div`
  height: calc(100vh - ${headerHeight}px);
  justify-content: center;
  display: flex;
  align-items: center;
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.white : theme.colorPalette.grey.grey11};

  h2 {
    color: ${({ theme }) => theme.colorPalette.grey.grey9};
    font-size: ${({ theme }) => theme.fontSizePalette.heading.H2};
    font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  }
`;

export default () => (
  <Home>
    <h2>Welcome</h2>
  </Home>
);
