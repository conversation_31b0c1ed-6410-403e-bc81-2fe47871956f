import React, { useState } from 'react';
import { useAsync } from 'react-use';
import services from 'services';
import { useField } from 'formik';
import styled from 'styled-components/macro';
import IntlHelper from 'utils/helpers/intl.helper';
import Header from 'components/Modal/Header';
import CommonButton from 'components/Buttons/CommonButton';
import { FatherContracts } from 'models/response';
import { useDispatch, useSelector } from 'react-redux';
import Table from 'components/Table';
import actions from 'state/actions';
import selectors from 'state/selectors';
import Modal from 'components/Modal';

const TableContainer = styled.div`
  width: 1200px;
  .table-pagination {
    justify-content: center;
  }
`;

const ButtonsContainer = styled.div`
  display: flex;
  justify-content: center;
  button {
    margin: 0 5px;
  }
`;

const WarningContainer = styled.div`
  text-align: center;
  padding: 10px 0;
`;

const ChildOf = () => {
  const t = IntlHelper.useTranslator();
  const dispatch = useDispatch();
  const [fatherContracts, setFatherContracts] = useState<FatherContracts[]>([]);
  const [selectedRow, setSelectedRow] = useState<FatherContracts | null>(null);
  const [fieldCompany] = useField<{ label: string; value: number }>('companyHeader');
  const [fieldSubject] = useField<{ name: string; id: number }>('subjectHeader');
  const protocol = useSelector(selectors.documents.selectActiveDocumentID);
  const idProgram = useSelector(selectors.app.getProgramCode);
  const [isOpen, setIsOpen] = useState(false);
  const idTemplate = useSelector(selectors.documents.selectActiveTemplateId);

  const columns = [
    {
      Header: t('subject'),
      accessor: 'subject',
    },
    {
      Header: t('mallAddress'),
      accessor: 'mallAddress',
    },
    {
      Header: t('mallCity'),
      accessor: 'mallCity',
    },
    {
      Header: t('mallName'),
      accessor: 'mallName',
    },
    {
      Header: t('mallPostalCode'),
      accessor: 'mallPostalCode',
    },
    {
      Header: t('mallProvince'),
      accessor: 'mallProvince',
    },
    {
      Header: t('startDate'),
      accessor: 'startDate',
    },
    {
      Header: t('endDate'),
      accessor: 'endDate',
    },
  ];

  useAsync(async () => {
    if (fieldCompany.value && fieldSubject.value) {
      try {
        const { data } = await services.getFatherContracts(fieldCompany.value.value, fieldSubject.value.id);
        setFatherContracts(data);
      } catch (e) {
        console.error(e);
      }
    }
  }, []);

  const onSelection = (rows: any) => {
    setSelectedRow(rows[0]);
  };

  const reloadPage = () => {
    if (protocol && idTemplate) {
      dispatch(actions.documents.setDocumentHeader(protocol, idProgram, idTemplate));
      dispatch(actions.documents.setDocumentFields(protocol, idProgram));
    }
  };

  const setFather = async (protocolFather: number) => {
    try {
      if (protocol) {
        await services.mergeFather(protocolFather, protocol);
        reloadPage();
        dispatch(actions.modal.closeModal());
      }
    } catch (e) {
      console.error(e);
    }
  };

  const saveRecord = () => {
    if (selectedRow) {
      setFather(selectedRow.protocol);
    }
  };

  return (
    <>
      <Modal onClose={() => setIsOpen(false)} open={isOpen}>
        <div>
          <Header title={t('Are you sure?')} subtitle={t('are you sure you want to confirm?')} />
          <ButtonsContainer>
            <CommonButton
              scope="primary"
              value={t('confirm')}
              type="button"
              action={() => {
                saveRecord();
                setIsOpen(false);
              }}
            />
            <CommonButton scope="secondary" value={t('cancel')} type="button" action={() => setIsOpen(false)} />
          </ButtonsContainer>
        </div>
      </Modal>
      <Header title={t('fatherContractsTitle')} subtitle={t('fatherContractsSubTitle')} />
      <TableContainer>
        {fatherContracts ? (
          <Table
            rowId="idWSubject"
            hasSort
            columns={columns}
            rows={fatherContracts}
            onSelection={onSelection}
            onRowDoubleClick={saveRecord}
            hasSelection
            hasPagination
            hasResize
          />
        ) : null}
      </TableContainer>
      <WarningContainer>{t('warning_father_contract')}</WarningContainer>
      <ButtonsContainer>
        <CommonButton
          scope="primary"
          value={t('confirm')}
          type="button"
          disabled={!selectedRow}
          action={() => setIsOpen(true)}
        />
        <CommonButton
          scope="secondary"
          value={t('cancel')}
          type="button"
          action={() => dispatch(actions.modal.closeModal())}
        />
      </ButtonsContainer>
    </>
  );
};

export default ChildOf;
