import React from 'react';
import NumberInput from '../Input/NumberInput';
import { act } from 'react-dom/test-utils';
import { renderWithStyle } from 'utils/helpers/test.helpers';

describe('Number input', () => {
  test('Number input render right text/number', () => {
    renderWithStyle(<NumberInput id="test" value={2} />);

    const input = document.getElementById('test');

    expect(input.value).toBe('2');
  });

  test('Number input change value', () => {
    renderWithStyle(<NumberInput id="test" value={2} />);

    const input = document.getElementById('test');

    act(() => {
      input.value = 3;
    });
    expect(input.value).toBe('3');
  });

  test('Number input feedback color check', () => {
    renderWithStyle(<NumberInput feedbackColor="red" hasFeedback={true} id="test" value={2} />);

    const input = document.getElementById('test');

    const styles = getComputedStyle(input);
    expect(styles.borderColor).toBe('red');
  });
});
