import Table, { OriginalRow, TableColumn } from 'components/Table';
import { GetCompanyWithTasks } from 'models/response';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import services from 'services';
import actions from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import { mutate } from 'swr';
import utils from 'utils';
import * as yup from 'yup';

const CustomTitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.M};
  font-weight: bold;
`;
const Wrapper = styled.div`
  margin-top: 20px;
`;

const Tasks = () => {
  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);

  const { tasks, revalidate } = services.useGetCompanyWithTasks('getCompanyWithTasksKey');
  const { associations } = services.useGetDefinitionTaskGroupsAssociations(
    'getDefinitionTaskGroupsAssociationsKey',
    companiesDefinition?.idCompany,
  );
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const editedRowId = useSelector(selectors.configurator.getEditedWfTask);
  const columns: TableColumn[] = [
    {
      id: '1',
      accessor: 'idTask',
      Header: 'Task ID',
      readOnly: true,
      inputType: 'text',
      Cell: ({ value }: { value: number }) => {
        return value === -1 ? '' : value;
      },
    },
    {
      id: '2',
      accessor: 'name',
      Header: 'Task Name',
      inputType: 'text',
    },
    {
      id: '3',
      accessor: 'desc',
      Header: 'Task Description',
      inputType: 'text',
    },
  ];

  const clearState = () => {
    dispatch(actions.configurator.setSelectedWfTask(null));
    dispatch(actions.configurator.setEditedTaskWfRow(null));
    dispatch(actions.configurator.setIsWfTableEdit(false));
  };

  useEffect(() => {
    if (tasks === undefined || !tasks.length || editedRowId !== null) return;
    const newTasks = tasks.filter((task) => task.idTask !== -1);
    mutate('getCompanyWithTasksKey', newTasks, {
      revalidate: false,
    });
  }, [tasks, editedRowId]);

  useEffect(() => {
    revalidate();
  }, [companiesDefinition, revalidate]);

  const onSaveNewRow = async (newRow: GetCompanyWithTasks) => {
    try {
      const { name, desc } = newRow;
      const { data: idTask } = await services.createWfTask(name, desc);
      const newTasks = tasks?.map((task) => {
        if (task.idTask === -1) {
          return { ...task, idTask, name, desc };
        }
        return task;
      });
      mutate('getCompanyWithTasksKey', newTasks, {
        revalidate: false,
      });
      utils.app.notify('success', t('task_created'));
    } catch (e) {
      console.error(e);
      mutate(
        'getCompanyWithTasksKey',
        tasks?.filter((task) => task.idTask !== -1),
        {
          revalidate: false,
        },
      );
    } finally {
      clearState();
    }
  };
  const schema = yup.object().shape({
    name: yup.string().required(),
    desc: yup.string(),
  });
  const onSaveEditedRow = async (editedRow: GetCompanyWithTasks) => {
    try {
      const { idTask, name, desc } = editedRow;
      await services.editWfTask(idTask, name, desc);
      const newTasks = tasks?.map((task) => {
        if (task.idTask === idTask) {
          return { ...task, name, desc };
        }
        return task;
      });

      const newAssociatiosList = associations?.map((association) => {
        return {
          ...association,
          definitionTaskAssociations: association.definitionTaskAssociations.map((el) => {
            if (el.idTask === editedRowId) {
              return {
                ...el,
                taskName: name,
              };
            }
            return el;
          }),
        };
      });

      mutate('getCompanyWithTasksKey', newTasks, {
        revalidate: false,
      });
      mutate('getCompanyTasksForAssociationsKey', newTasks, {
        revalidate: false,
      });
      mutate('getDefinitionTaskGroupsAssociationsKey', newAssociatiosList, {
        revalidate: false,
      });
      utils.app.notify('success', t('task_edited'));
    } catch (e) {
      console.error(e);
      mutate('getCompanyWithTasksKey', tasks, {
        revalidate: false,
      });
    } finally {
      clearState();
    }
  };

  const onSave = async (row: OriginalRow) => {
    const typedRow = row as GetCompanyWithTasks;
    if (typedRow.idTask === -1) {
      await onSaveNewRow(typedRow);
    } else {
      await onSaveEditedRow(typedRow);
    }
  };

  const onCancel = () => {
    if (editedRowId === -1) {
      const newTasks = tasks?.filter((task) => task.idTask !== -1);
      mutate('getCompanyWithTasksKey', newTasks, {
        revalidate: false,
      });
    }
    clearState();
  };

  return (
    <Wrapper>
      {tasks?.length === 0 ? (
        <div>{t('no_data')}</div>
      ) : (
        <>
          <CustomTitle>{t('tasks')}</CustomTitle>
          <Table
            columns={columns}
            rows={tasks ?? []}
            hasToolbar
            hasSelection
            onSelection={(rows: GetCompanyWithTasks[]) => {
              if (rows.length === 0) {
                dispatch(actions.configurator.setSelectedWfTask(null));
                return;
              }
              dispatch(actions.configurator.setSelectedWfTask(rows[0].idTask));
            }}
            onSave={onSave}
            onCancel={onCancel}
            hasPagination
            hasResize
            hasSort
            hasFilter
            isEditable
            rowId="idTask"
            editRowID={editedRowId?.toString()}
            initialSelection={editedRowId !== null ? [editedRowId] : []}
            onSaveValidation={schema}
          />
        </>
      )}
    </Wrapper>
  );
};

export default Tasks;
