// fieldInputTtype 2
import React from 'react';
import styled from 'styled-components/macro';
import NumberInput from 'components/Input/NumberInput';
import { UserDecimalSep } from 'models/user';

const InputWrapper = styled.div<{
  feedbackColor: string | undefined;
  width?: string;
  display?: string;
  margin?: string;
}>`
  display: ${({ display }) => display ?? 'flex'};
  justify-content: flex-start;
  align-items: center;
  input {
    border-radius: 3px;
    font-family: Roboto;
    font-weight: ${({ theme }) => theme.fontWeightPalette.regular};
    height: 25px;
    width: ${({ width }) => width ?? '165px'};
    border: 1px solid ${({ theme, feedbackColor }) =>
      feedbackColor ? feedbackColor :
      theme.colorPalette.isDarkMode ?
        theme.colorPalette.grey.grey3 :
        theme.colorPalette.grey.grey5
    };
    border-radius: 3px;
    padding-right: 0;
    font-size: 12px;
    margin: ${({ margin }) => margin ?? `${margin}`};
    background-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ?
        theme.colorPalette.grey.grey3 :
        theme.colorPalette.white
    };
    color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ?
        theme.colorPalette.grey.grey10 :
        theme.colorPalette.black
    };
  }
  input:focus {
    outline: 0 !important;
    border: 1px solid
      ${({ theme, feedbackColor }) =>
        feedbackColor ? feedbackColor :
        theme.colorPalette.isDarkMode ?
          theme.colorPalette.turquoise.dark :
          theme.colorPalette.turquoise.normal
      } !important;
  }
`;

const InputLabel = styled.span`
  display: inline-block;
  margin-right: 15px;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  width: 100px;
  text-align: right;
  overflow: hidden;
  letter-spacing: -0.31px;
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ?
      theme.colorPalette.grey.grey10 :
      theme.colorPalette.grey.grey9
  };
`;

export interface Props {
  label: string;
  disabled?: boolean;
  onChange: (value?: number) => void;
  onFocus: (value?: number) => void;
  onBlur: (value?: number) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLDivElement>) => void;
  readonly: boolean;
  value: number;
  decimalSeparator: UserDecimalSep;
  hasFixedDecimals?: boolean;
  decimalScale?: number;
  feedbackColor?: string;
  hasFeedback?: boolean;
  feedbackMessage?: string;
  multi?: boolean;
  width?: string;
  display?: string;
  margin?: string;
}

const NumberInputType = (props: Props) => {
  const {
    label,
    readonly,
    onChange,
    onBlur,
    onFocus,
    onKeyDown,
    decimalSeparator,
    hasFixedDecimals = false,
    decimalScale,
    hasFeedback = false,
    feedbackColor,
    feedbackMessage,
    multi,
    value,
    width,
    display,
    margin,
  } = props;

  return (
    <InputWrapper feedbackColor={feedbackColor} width={width} display={display} margin={margin}>
      {!multi ? <InputLabel>{label}</InputLabel> : null}
      <NumberInput
        hasFeedback={hasFeedback}
        feedbackMessage={feedbackMessage}
        feedbackColor={feedbackColor}
        decimalScale={decimalScale}
        readOnly={readonly}
        value={value}
        decimalSeparator={decimalSeparator}
        hasFixedDecimals={hasFixedDecimals}
        onKeyDown={onKeyDown}
        onFocus={onFocus}
        onBlur={onBlur}
        onChange={onChange}

      />
    </InputWrapper>
  );
};

export default NumberInputType;
