import React, { useRef } from 'react';
import { Pie } from 'react-chartjs-2';
import { colorPalette } from 'utils/styleConstants';

type Value = { x: string; y: number | string };

export interface PieChartProps {
  data: Value[];

  percentage?: boolean;
  colors?: string[];
}

const PieChart = ({ data: dataValues, percentage, colors }: PieChartProps) => {
  const chartRef = useRef<any>(null);

  const data = {
    labels: dataValues.map((item) => item.x),
    datasets: [
      {
        data: dataValues.map((item) => item.y),
        backgroundColor: colors,
        borderColor: 'white',
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    barThickness: 17,
    borderWidth: 2,
    borderColor: 'transparent',
    borderRadius: {
      topLeft: 4,
      topRight: 4,
    },
    plugins: {
      legend: {
        align: 'end',
        labels: {
          boxWidth: 20,
          boxHeight: 10,
          padding: 15,
          font: {
            size: 13,
            family: 'Roboto',
            weight: 'lighter',
          },
        },
      },
      title: {
        display: false,
      },
      tooltip: {
        callbacks: {
          title: () => '',
          label: (tooltipItem: { raw: { y: number | string } }) => {
            const value = tooltipItem.raw;
            return percentage ? value + ' %' : value;
          },
        },
        backgroundColor: colorPalette.white,
        titleColor: colorPalette.turquoise.dark,
        bodyColor: colorPalette.turquoise.dark,
        displayColors: false,
        bodyFont: {
          size: 12,
          weight: 400,
        },
        caretPadding: 10,
        borderColor: colorPalette.grey.grey3,
        borderWidth: 1,
        padding: 7,
      },
    },
  };

  return (
    // @ts-ignore
    <Pie ref={chartRef} data={data} options={options} />
  );
};

export default PieChart;
