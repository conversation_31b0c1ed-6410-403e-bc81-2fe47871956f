import React, { useRef, useEffect, useState } from 'react';
import { Tabs } from './Tabs';
import { <PERSON><PERSON><PERSON>, <PERSON>Arrow, RightArrow, Tab<PERSON>ist, TabPanels } from './style';
import 'resize-observer-polyfill';
import LeftArrowIcon from 'images/arrow-left.svg';
import RightArrowIcon from 'images/arrow-right.svg';

export interface Props {
  tabs: Array<Tab>;
  maxHeight?: string;
  hasBorder?: boolean;
  setActiveTabExternal?: (index: number) => void;
}

interface Tab {
  name: string;
  content: any;
  id: number;
  disabled?: boolean;
}
export interface Props {
  tabs: Array<Tab>;
  maxHeight?: string;
  hasBorder?: boolean;
  tabWidth?: string;
  tabPanelPadding?: string;
  singleTabWidth?: string;
  defaultTab?: number;
}

const MultiTabs = ({
  tabs,
  maxHeight = '100%',
  hasBorder = true,
  setActiveTabExternal,
  tabWidth,
  tabPanelPadding,
  singleTabWidth,
  defaultTab,
}: Props) => {
  const tabsRef = useRef<HTMLDivElement>(null);
  const [hasArrows, setHasArrows] = useState(false);
  const [leftDisabled, setLeftDisabled] = useState(true);
  const [rightDisabled, setRightDisabled] = useState(false);
  const containerRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (tabsRef?.current) {
      tabsRef.current.scrollWidth > tabsRef.current.clientWidth && setHasArrows(true);
    }
  }, [tabsRef]);

  const handleResize = () => {
    if (tabsRef?.current) {
      tabsRef.current.scrollWidth > tabsRef.current.clientWidth ? setHasArrows(true) : setHasArrows(false);
    }
  };

  useEffect(() => {
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const checkArrows = () => {
    if (tabsRef?.current) {
      tabsRef.current.scrollLeft === 0 ? setLeftDisabled(true) : setLeftDisabled(false);
      if (tabsRef.current.scrollLeft >= tabsRef.current.scrollWidth - tabsRef.current.clientWidth) {
        setRightDisabled(true);
      } else {
        setRightDisabled(false);
      }
    }
  };

  const scrollRight = () => {
    if (tabsRef?.current) {
      tabsRef.current.scrollLeft += 100;
      checkArrows();
    }
  };

  const scrollLeft = () => {
    if (tabsRef?.current) {
      tabsRef.current.scrollLeft -= 100;
      checkArrows();
    }
  };
  const getFirstEnabledTab = () => {
    for (let i = 0; i < tabs.length; i++) {
      if (!tabs[i].disabled) {
        return i;
      }
    }
    return 0;
  };

  useEffect(() => {
    const tabsRefCurrent = containerRef.current;
    const resizeObserver = new ResizeObserver((entries: ResizeObserverEntry[]) => {
      for (const entry of entries) {
        const { width } = entry.contentRect;
        const totalTabsWidth = Array.from(tabsRef.current?.children ?? []).reduce((total, current) => {
          if (current instanceof HTMLElement) {
            return total + current.offsetWidth;
          }
          return total;
        }, 0);
        const hasOverflow = totalTabsWidth > width;
        setHasArrows(hasOverflow);
        checkArrows();
      }
    });

    if (tabsRefCurrent) {
      resizeObserver.observe(tabsRefCurrent);
    }

    return () => {
      if (tabsRefCurrent) {
        resizeObserver.unobserve(tabsRefCurrent);
      }
    };
  }, [tabs]);
  return (
    <Tabs
      nTabs={tabs.length}
      defaultTab={defaultTab ? defaultTab : getFirstEnabledTab()}
      setActiveTabExternal={setActiveTabExternal}
    >
      <Container ref={containerRef}>
        <TabList ref={tabsRef} width={tabWidth}>
          {hasArrows && tabsRef && (
            <>
              <LeftArrow disabled={leftDisabled} onClick={() => scrollLeft()} src={LeftArrowIcon} />
              <RightArrow disabled={rightDisabled} onClick={() => scrollRight()} src={RightArrowIcon} />
            </>
          )}
          {tabs.map((tab, i) => (
            <Tabs.Tab id={i} key={tab.name} label={tab.name} disabled={tab.disabled} singleTabWidth={singleTabWidth}>
              {tab.name}
            </Tabs.Tab>
          ))}
        </TabList>
        <TabPanels maxHeight={maxHeight} hasBorder={hasBorder} padding={tabPanelPadding}>
          {tabs.map((tab, i) => (
            <Tabs.Panel id={i} key={tab.name} label={tab.name}>
              {tab.content}
            </Tabs.Panel>
          ))}
        </TabPanels>
      </Container>
    </Tabs>
  );
};

export default MultiTabs;
