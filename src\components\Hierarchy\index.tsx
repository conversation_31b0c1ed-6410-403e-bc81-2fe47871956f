/* eslint-disable max-lines */
import React, { useState, useEffect } from 'react';
import { useDebounce } from 'react-use';

import utils from 'utils';

import { Wrapper, Left, Right, Title, CardContainer, CheckBoxesContainer } from './styles';

import Dropdown from 'components/Input/Dropdown';
import TextInput from 'components/Input/TextInput';
import StaticCard from 'components/Draggable/StaticCard';
import CheckGrouper from 'components/Grouper/CheckGrouper';
import Table from 'components/Table';

interface HierarchyProps {
  title: string;

  hasSearch?: boolean;
  searchTypeValues?: { value: string; label: string; parent: 'one' | 'two' | 'three' | 'table' | 'table-two' }[];

  parentsOne?: any[];
  parentOneName?: string;
  parentOneId?: string | number;
  parentOneSelection?: (value: any) => void;
  preSelectedParentOne?: any;
  parentOneChild?: string;

  parentsTwo?: any[];
  parentTwoName?: string;
  parentTwoId?: string | number;
  parentTwoSelection?: (isActive?: boolean, value?: any) => void;
  parentTwoChild?: string;
  preSelectedParentTwo?: any;
  parentTwoHasTable?: boolean;

  parentThreeName?: string;
  parentThreeId?: string | number;
  parentThreeSelection?: (value: any) => void;
  preSelectedParentThree?: any;
  parentThreeChild?: string;
  parentThreeMulti?: boolean;

  idRowTable?: string;
  columns?: { accessor: string; Header: string }[];
  tableValueName?: string;
  tableValueNameTwo?: string;

  toCleanSelections?: any;
}

const Hierarchy = (props: HierarchyProps) => {
  const {
    title,

    searchTypeValues,
    hasSearch,

    parentsOne,
    parentOneName,
    parentOneId,
    parentOneSelection,
    preSelectedParentOne,
    parentOneChild,

    parentsTwo,
    parentTwoName,
    parentTwoId,
    parentTwoSelection,
    preSelectedParentTwo,
    parentTwoChild,
    parentTwoHasTable,

    parentThreeName,
    parentThreeId,
    parentThreeSelection,
    parentThreeChild,
    parentThreeMulti,

    idRowTable,
    columns,
    tableValueName,
    tableValueNameTwo,

    toCleanSelections,
    preSelectedParentThree,
  } = props;

  const t = utils.intl.useTranslator();
  const [selectedParentOne, setSelectedParentOne] = useState<any>(null);
  const [selectedParentTwo, setSelectedParentTwo] = useState<any>(null);
  const [selectedParentThree, setSelectedParentThree] = useState<any[]>([]);

  const [searchValue, setSearchValue] = useState<string>('');
  const [searchType, setSearchType] = useState<{
    value: string;
    label: string;
    parent: 'one' | 'two' | 'three' | 'table' | 'table-two';
  } | null>(searchTypeValues ? searchTypeValues[0] : null);

  const [filteredParentOne, setFilteredParentOne] = useState<any[] | null>(null);

  // Check if there are pre selections
  useEffect(() => {
    if (parentsOne?.length && preSelectedParentOne) {
      setSelectedParentOne(preSelectedParentOne);
      if (preSelectedParentOne[`${parentOneChild}`] && preSelectedParentTwo) {
        setSelectedParentTwo(preSelectedParentTwo);
        if (preSelectedParentTwo[`${parentTwoChild}`] && preSelectedParentThree) {
          setSelectedParentThree(preSelectedParentThree);
        }
      }
    }
  }, [parentsOne, preSelectedParentOne, parentOneChild, preSelectedParentTwo, parentTwoChild, preSelectedParentThree]);

  // Search
  const clearSelections = () => {
    setSelectedParentOne(null);
    setSelectedParentTwo(null);
    setSelectedParentThree([]);
  };

  useEffect(() => {
    if (!preSelectedParentOne) {
      clearSelections();
      setSearchValue('');
    }
  }, [searchType, toCleanSelections, preSelectedParentOne]);

  // Manage filteder data
  const filterWorkFlow = (searchValue: string) => {
    const findChildThree = (parentThreeNameParam: string) => {
      if (parentsOne && parentOneChild && parentTwoChild && parentThreeNameParam) {
        const parentsOneFilter = parentsOne.filter((parentOne) =>
          parentOne[parentOneChild].some((parentTwo: any) =>
            parentTwo[parentTwoChild].find((parentThree: any) =>
              parentThree[parentThreeNameParam].toLowerCase().includes(searchValue.toLowerCase()),
            ),
          ),
        );
        if (parentsOneFilter) {
          const parentsOneTwoFilter: any[] = parentsOneFilter.map((parentOne) => {
            return {
              ...parentOne,
              [parentOneChild]: parentOne[parentOneChild].filter((parentTwo: any) =>
                parentTwo[parentTwoChild].find((parentThree: any) =>
                  parentThree[parentThreeNameParam].toLowerCase().includes(searchValue.toLowerCase()),
                ),
              ),
            };
          });
          if (parentsOneTwoFilter) {
            const parentsOneTwoThreeFilter: any[] = parentsOneTwoFilter.map((parentOne: any) => {
              return {
                ...parentOne,
                [parentOneChild]: parentOne[parentOneChild].map((parentTwo: any) => {
                  return {
                    ...parentTwo,
                    [parentTwoChild]: parentTwo[parentTwoChild].filter((parentThree: any) =>
                      parentThree[parentThreeNameParam].toLowerCase().includes(searchValue.toLowerCase()),
                    ),
                  };
                }),
              };
            });
            setFilteredParentOne(parentsOneTwoThreeFilter);
            !preSelectedParentOne && clearSelections();
          }
        }
      }
    };

    const searchTableValue = (tableValueName: string) => {
      if (parentOneChild && tableValueName && parentTwoChild) {
        if (parentThreeChild) {
          const parentsOneFilter = parentsOne?.filter((parentOne) =>
            parentOne[parentOneChild].some((parentTwo: any) =>
              parentTwo[parentTwoChild].some((parentThree: any) =>
                parentThree[parentThreeChild].find((tableValue: any) =>
                  tableValue[tableValueName].toLowerCase().includes(searchValue.toLowerCase()),
                ),
              ),
            ),
          );
          if (parentsOneFilter) {
            const parentsOneTwoFilter: any[] = parentsOneFilter.map((parentOne) => {
              return {
                ...parentOne,
                [parentOneChild]: parentOne[parentOneChild].filter((parentTwo: any) =>
                  parentTwo[parentTwoChild].some((parentThree: any) =>
                    parentThree[parentThreeChild].find((tableValue: any) =>
                      tableValue[tableValueName].toLowerCase().includes(searchValue.toLowerCase()),
                    ),
                  ),
                ),
              };
            });
            if (parentsOneTwoFilter) {
              const parentsOneTwoThreeFilter: any[] = parentsOneTwoFilter.map((parentOne) => {
                return {
                  ...parentOne,
                  [parentOneChild]: parentOne[parentOneChild].map((parentTwo: any) => {
                    return {
                      ...parentTwo,
                      [parentTwoChild]: parentTwo[parentTwoChild].filter((parentThree: any) =>
                        parentThree[parentThreeChild].find((tableValue: any) =>
                          tableValue[tableValueName].toLowerCase().includes(searchValue.toLowerCase()),
                        ),
                      ),
                    };
                  }),
                };
              });
              if (parentsOneTwoThreeFilter) {
                const parentsOneTwoThreeTableFilter: any[] = parentsOneTwoThreeFilter.map((parentOne) => {
                  return {
                    ...parentOne,
                    [parentOneChild]: parentOne[parentOneChild].map((parentTwo: any) => {
                      return {
                        ...parentTwo,
                        [parentTwoChild]: parentTwo[parentTwoChild].map((parentThree: any) => {
                          return {
                            ...parentThree,
                            [parentThreeChild]: parentThree[parentThreeChild].filter((tableValue: any) =>
                              tableValue[tableValueName].toLowerCase().includes(searchValue.toLowerCase()),
                            ),
                          };
                        }),
                      };
                    }),
                  };
                });
                setFilteredParentOne(parentsOneTwoThreeTableFilter);
                !preSelectedParentOne && clearSelections();
              }
            }
          }
        } else {
          if (tableValueName) {
            findChildThree(tableValueName);
          }
        }
      }
    }

    if (hasSearch && searchType && parentsOne && parentOneName) {
      if (searchValue.length >= 3) {
        switch (searchType.parent) {
          case 'one': {
            const parentsOneFilter = parentsOne.filter((parentOne) =>
              parentOne[parentOneName].toLowerCase().includes(searchValue.toLowerCase()),
            );
            if (parentsOneFilter) {
              setFilteredParentOne(parentsOneFilter);
              !preSelectedParentOne && clearSelections();
            }
            break;
          }
          case 'two': {
            if (parentOneChild && parentTwoName) {
              const parentsOneFilter = parentsOne.filter((parentOne) =>
                parentOne[parentOneChild].find((parentTwo: any) =>
                  parentTwo[parentTwoName].toLowerCase().includes(searchValue.toLowerCase()),
                ),
              );
              if (parentsOneFilter) {
                const parentsOneTwoFilter: any[] = parentsOneFilter.map((parentOne) => {
                  return {
                    ...parentOne,
                    [parentOneChild]: parentOne[parentOneChild].filter((parentTwo: any) =>
                      parentTwo[parentTwoName].toLowerCase().includes(searchValue.toLowerCase()),
                    ),
                  };
                });
                setFilteredParentOne(parentsOneTwoFilter);
                !preSelectedParentOne && clearSelections();
              }
            }
            break;
          }
          case 'three': {
            if (parentThreeName) {
              findChildThree(parentThreeName);
            }
            break;
          }
          case 'table': {
            tableValueName && searchTableValue(tableValueName)
            break;
          }
          case 'table-two': {
            tableValueNameTwo && searchTableValue(tableValueNameTwo)
            break;
          }
          default:
            setFilteredParentOne(null);
            !preSelectedParentOne && clearSelections();
        }
      } else {
        setFilteredParentOne(null);
        !preSelectedParentOne && clearSelections();
      }
    }
  };

  useDebounce(
    () => {
      filterWorkFlow(searchValue);
    },
    1000,
    [searchValue],
  );

  const getTooltip = (value: any) => {
    if (parentOneChild && parentTwoChild && parentTwoId && parentThreeId) {
      const relatedParent = selectedParentOne[parentOneChild].find((parentTwo: any) =>
        parentTwo[parentTwoChild].find((parentThree: any) => parentThree[parentThreeId] === value[parentThreeId]),
      );
      if (relatedParent) {
        if (selectedParentOne.suspendType === 0) {
          return t('group_not_selectable_because_task_is_not_selectable');
        } else if (
          selectedParentOne.suspendType !== 0 &&
          !(selectedParentTwo?.[parentTwoId] === relatedParent?.[parentTwoId]) &&
          relatedParent[parentTwoChild].every((ele: any) => ele.relation === 2)
        ) {
          return t('relation_or_select_task_first');
        } else if (
          selectedParentOne.suspendType !== 0 &&
          relatedParent?.escalation === false &&
          !relatedParent?.[parentTwoChild].every((ele: any) => ele.relation === 2)
        ) {
          return t('relation_and_group_not_selectable');
        }
      }
    }
  };

  const renderTable = (values: any, idRow: string) => {
    return <Table columns={columns || []} rows={values} rowId={idRow} hasSort light />;
  };

  const parentThreeIsEditable = (parentThree: any) => {
    if (preSelectedParentThree) {
      return false;
    } else {
      if (parentTwoChild && parentThreeId) {
        if (selectedParentTwo?.[parentTwoChild].find((e: any) => e[parentThreeId] === parentThree[parentThreeId])) {
          if (selectedParentTwo.escalation === false) {
            if (selectedParentTwo[parentTwoChild].every((ele: any) => ele.relation === 2)) {
              return true;
            } else {
              return false;
            }
          } else {
            return true;
          }
        } else {
          return false;
        }
      }
    }
  };

  // Update parentThree external state
  useEffect(() => {
    parentThreeSelection && parentThreeSelection(selectedParentThree);
  }, [selectedParentThree, parentThreeSelection]);

  const renderParentsTwo = () => {
    // Render Parent Two
    if (((parentsOne && selectedParentOne) || parentsTwo) && parentTwoName && parentTwoId) {
      const contentTasks = (
        parentsOne && selectedParentOne && parentOneChild ? selectedParentOne[parentOneChild] : parentsTwo
      ).map((parentTwo: any, parentTwoIndex: number) => {
        return (
          <CheckGrouper
            customMargin={'0 0 10px 0'}
            title={
              parentTwo.taskWeight !== undefined
                ? `${parentTwoName && parentTwo[parentTwoName]} (${t('task_weight')}: ${parentTwo.taskWeight})`
                : parentTwo.amountLimit !== undefined
                ? `${parentTwoName && parentTwo[parentTwoName]} (${t('amount-limit')}: ${parentTwo.amountLimit})`
                : parentTwoName && parentTwo[parentTwoName]
            }
            key={`${parentOneId ? selectedParentOne[parentOneId] : '0'}-${parentTwoIndex}-${parentTwo[parentTwoId]}`}
            selectable={true}
            onChange={(isActive) => {
              if (parentTwoHasTable) {
                if (isActive) {
                  setSelectedParentTwo([parentTwo]);
                  parentTwoSelection && parentTwoSelection(undefined, [parentTwo]);
                } else {
                  setSelectedParentTwo([]);
                  parentTwoSelection && parentTwoSelection(undefined, []);
                }
              } else {
                setSelectedParentTwo(isActive ? parentTwo : null);
                parentTwoSelection && parentTwoSelection(isActive, parentTwo);
                setSelectedParentThree([]);
              }
            }}
            isSelected={selectedParentTwo && parentTwo[parentTwoId] === selectedParentTwo[parentTwoId] ? true : false}
            // TODO set condition to disable checkbox if preselected
            // isEditable={parentsOne ? selectedParentOne.suspendType !== 0 : true}
            isEditable={parentsOne ? selectedParentOne.suspendType !== 0 && !preSelectedParentTwo : true}
          >
            {
              // Render Parent Three
              parentTwoHasTable && parentTwoChild && idRowTable
                ? renderTable(parentTwo[parentTwoChild], idRowTable)
                : parentTwoChild && parentThreeName && parentThreeId
                ? parentTwo[parentTwoChild].map((parentThree: any, parentThreeIndex: number) => {
                    return (
                      <CheckGrouper
                        customMargin={'0 0 10px 0'}
                        title={
                          parentThree.amountLimit !== undefined
                            ? `${parentThree[parentThreeName]} (${t('amount-limit')}: ${parentThree.amountLimit})`
                            : parentThree[parentThreeName]
                        }
                        key={`${
                          parentOneId ? selectedParentOne[parentOneId] : '0'
                        }-${parentTwoIndex}-${parentThreeIndex}-${parentThree[parentThreeId]}`}
                        selectable={true}
                        onChange={(isActive: boolean) => {
                          if (parentThreeMulti) {
                            if (isActive) {
                              if (selectedParentTwo?.escalation === true) {
                                const parentThreeGroup = selectedParentTwo?.[parentTwoChild].filter(
                                  (e: any) => parentThree.amountLimit === e.amountLimit,
                                );
                                setSelectedParentThree(parentThreeGroup);
                              } else {
                                setSelectedParentThree((pre: any) => [...pre, parentThree]);
                              }
                            } else {
                              if (selectedParentTwo?.escalation === true) {
                                setSelectedParentThree([]);
                              } else {
                                setSelectedParentThree((pre: any) =>
                                  pre.filter((obj: any) => obj[parentThreeId] !== parentThree[parentThreeId]),
                                );
                              }
                            }
                          } else {
                            if (isActive) {
                              setSelectedParentThree([parentThree]);
                            } else {
                              setSelectedParentThree([]);
                            }
                          }
                        }}
                        isSelected={
                          selectedParentThree.find((e) => e[parentThreeId] === parentThree[parentThreeId])
                            ? true
                            : false
                        }
                        isEditable={parentThreeIsEditable(parentThree)}
                        tooltip={getTooltip(parentThree)}
                      >
                        {parentThreeChild && idRowTable ? renderTable(parentThree[parentThreeChild], idRowTable) : null}
                      </CheckGrouper>
                    );
                  })
                : null
            }
          </CheckGrouper>
        );
      });
      return contentTasks;
    } else {
      return null;
    }
  };

  return (
    <>
      <Wrapper>
        <Left>
          {hasSearch && searchTypeValues ? (
            <Dropdown
              disabled={preSelectedParentOne}
              options={searchTypeValues}
              value={searchType}
              onChange={(option) => {
                setSearchType(option);
              }}
              margin="0 0 5px 0"
            />
          ) : null}
          <TextInput
            disabled={preSelectedParentOne}
            borderRadius="5px"
            value={searchValue}
            onChange={(e: React.FocusEvent<HTMLInputElement>) => {
              setSearchValue && setSearchValue(e.target.value);
            }}
            fullWidth
          />
        </Left>
        <Right>
          <Title>{t(`${title}`)}</Title>
          {!parentsOne && parentTwoName && parentTwoId ? renderParentsTwo() : null}
        </Right>
      </Wrapper>
      <Wrapper>
        <Left>
          <CardContainer>
            {parentsOne && parentOneName && parentOneId
              ? (filteredParentOne || parentsOne).map((parentOne, index) => {
                  return (
                    <StaticCard
                      disabled={preSelectedParentOne}
                      key={index + 1}
                      name={parentOne[parentOneName]}
                      index={index + 1}
                      onClick={() => {
                        setSelectedParentOne(
                          selectedParentOne && selectedParentOne[parentOneId] === parentOne[parentOneId]
                            ? null
                            : parentOne,
                        );
                        parentOneSelection &&
                          parentOneSelection(
                            selectedParentOne && selectedParentOne[parentOneId] === parentOne[parentOneId]
                              ? null
                              : parentOne,
                          );
                        setSelectedParentTwo(null);
                        setSelectedParentThree([]);
                      }}
                      isActive={selectedParentOne && selectedParentOne[parentOneId] === parentOne[parentOneId]}
                    />
                  );
                })
              : null}
          </CardContainer>
        </Left>
        <Right>
          <CheckBoxesContainer>
            {parentsOne && parentTwoName && parentTwoId ? renderParentsTwo() : null}
          </CheckBoxesContainer>
        </Right>
      </Wrapper>
    </>
  );
};

export default Hierarchy;
