import { RootState } from 'models';

const getNotificationActiveView = (state: RootState) => state.configurator.notifications.activeView;
const getTemplatesMailNotification = (state: RootState) => state.configurator.notifications.templatesList;
// OPEN WORKFLOW SETTINGS
const getFormModeOpenWf = (state: RootState) => state.configurator.notifications.openWf.formModeOpenWF;
const getSelectedOpenWfSettings = (state: RootState) => state.configurator.notifications.openWf.selectedOpenWfSettings;
const getListOfOpenWfSettings = (state: RootState) => state.configurator.notifications.openWf.listOfOpenWfSettings;

// WF REMINDER SETTINGS
const getListOfWfReminderSettings = (state: RootState) =>
  state.configurator.notifications.wfReminder.listOfWfReminderSettings;
const getSelectedWfReminderSettings = (state: RootState) =>
  state.configurator.notifications.wfReminder.selectedWfReminderSettings;
const getFormModeWfReminder = (state: RootState) => state.configurator.notifications.wfReminder.formModeWfReminder;

// OVERDUE SETTINGS
const getListOfOverdueSettings = (state: RootState) => state.configurator.notifications.overdue.listOfOverdueSettings;
const getSelectedOverdueSettings = (state: RootState) =>
  state.configurator.notifications.overdue.selectedOverdueSettings;
const getFormModeOverdue = (state: RootState) => state.configurator.notifications.overdue.formModeOverdue;

export default {
  getNotificationActiveView,
  getFormModeOpenWf,
  getSelectedOpenWfSettings,
  getListOfOpenWfSettings,
  getListOfWfReminderSettings,
  getSelectedWfReminderSettings,
  getFormModeWfReminder,
  getListOfOverdueSettings,
  getSelectedOverdueSettings,
  getFormModeOverdue,
  getTemplatesMailNotification,
};
