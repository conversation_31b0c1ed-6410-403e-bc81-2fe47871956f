import React from 'react';

import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';
import { actionType, modalActionType } from 'utils/constants';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import selectors from 'state/selectors';
import axios from 'axios';
import services from 'services';
import { WorkflowDefinitionTaskGroupsAssociationsExtended } from 'models/response';
import { transformedRows } from 'routes/Configurator/routes/Workflow/routes/WorkflowCompanyDetails/tabs/Associations/utils';
import { useSWRConfig } from 'swr';

const DeleteAssociation = () => {
  const t = utils.intl.useTranslator();

  const dispatch = useDispatch();

  const selectedAssociation = useSelector(selectors.configurator.getSelectedDefinitionTaskGroupsAssociations);
  const editedRowId = useSelector(selectors.configurator.getEditedRowIdWfAssociations);
  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  const listOfDefinitions = useSelector(selectors.configurator.getListOfDefinitionsForCompanySelected);

  const { listOfDefinitionsForAssociations } = services.useGetCompanyDefinitionsForAssociation(
    'getCompanyDefinitionForAssociationsKey',
    companiesDefinition?.idCompany,
  );

  const { associations } = services.useGetDefinitionTaskGroupsAssociations(
    'getDefinitionTaskGroupsAssociationsKey',
    companiesDefinition?.idCompany,
  );

  const { mutate } = useSWRConfig();

  const deleteRow = async () => {
    try {
      await services.deleteDefinitionTaskAssociations(
        selectedAssociation?.idDefinition,
        selectedAssociation?.idTaskDefinition,
      );

      if (selectedAssociation) {
        let list: WorkflowDefinitionTaskGroupsAssociationsExtended[] = [];

        if (selectedAssociation?.isSubRow) {
          list =
            associations?.map((el) => {
              return {
                ...el,
                subRows: el.subRows.filter((el) => el.idTask !== selectedAssociation.idTask),
                definitionTaskAssociations: el.definitionTaskAssociations.filter(
                  (el) => el.idTask !== selectedAssociation.idTask,
                ),
              };
            }) ?? [];
        } else {
          list = associations?.filter((el) => el.idDefinition !== selectedAssociation.idDefinition) ?? [];
        }

        const newList = transformedRows(list);
        const findDefinitionInList = listOfDefinitions.find(
          (el) => el.idDefinition === selectedAssociation?.idDefinition,
        );

        if (findDefinitionInList) {
          const {
            idDefinition,
            wfName,
            suspendType,
            noteMandatory,
            currencyExchange,
            takeNextSingleTask,
            taskOnMinCausalWeight,
          } = findDefinitionInList;
          mutate(
            'getCompanyDefinitionForAssociationsKey',
            [
              {
                idDefinition: idDefinition,
                wfName: wfName,
                wfDesc: wfName,
                idCompany: companiesDefinition?.idCompany || undefined,
                suspendType: suspendType,
                noteMandatory: noteMandatory,
                currencyExchange: currencyExchange,
                takeNextSingleTask: takeNextSingleTask,
                taskOnMinCausalWeight: taskOnMinCausalWeight,
              },
              ...(listOfDefinitionsForAssociations || []),
            ],
            {
              revalidate: false,
            },
          );
        }

        mutate('getDefinitionTaskGroupsAssociationsKey', newList, {
          revalidate: false,
        });

        utils.app.notify(
          'success',
          t(selectedAssociation?.idDefinition ? 'wf-definition-task-association-deleted' : 'task-associated-deleted'),
        );
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        utils.app.notify('fail', `${t('error')} ${error.response}`);
        return;
      }
      console.error(error);
    } finally {
      dispatch(actions.modal.closeModal());
    }
  };
  return (
    <CommonButton
      action={() => {
        dispatch(
          actions.modal.setModal({
            actionType: modalActionType.configurator.DELETE_CONFIRMATION,
            props: {
              title: t('delete-wf-associaion-row-title'),
              subtitle: t('delete-wf-associaion-subtitle'),
              func: () => deleteRow(),
            },
          }),
        );
      }}
      scope="tertiary"
      value={t(actionType.DELETE_WF_ASSOCIATION)}
      icon="circle"
      disabled={
        editedRowId !== null ||
        !selectedAssociation ||
        !utils.user.isActionByCompanyActive(actionType.DELETE_WF_ASSOCIATION, companiesDefinition?.companyName)
      }
    />
  );
};

export default DeleteAssociation;
