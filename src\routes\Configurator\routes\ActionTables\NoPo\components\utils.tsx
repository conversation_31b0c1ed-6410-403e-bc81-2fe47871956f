/* eslint-disable max-lines */
import { CommonButton } from 'components/Buttons';
import { TableColumn } from 'components/Table';
import { OptionAT } from 'models/configurator';
import { CompanyActionTable, NopoDefaultRow } from 'models/response';
import React, { Dispatch } from 'react';
import services from 'services';
import actions from 'state/actions';
import { modalActionType } from 'utils/constants';
import * as yup from 'yup';

type Options = {
  label: string;
  value: string | number;
  [key: string]: any;
};

export const nopoSchemaNewRow = (t: (value: string) => string, selectedTable: string) => {
  return yup.object().shape({
    companyName: yup.mixed().required().notOneOf([undefined, null, ''], t('missing company')),
    wfCausal: yup.mixed().required().notOneOf([undefined, null, ''], t('missing wfCausal')),
    workflowDefinition: yup.mixed().required().notOneOf([undefined, null, ''], t('missing workflowDefinition')),
    fluxName: yup.string().required().notOneOf([undefined, null, ''], t('missing fluxName')),
    microcategoryDescription:
      selectedTable === 'microcategory'
        ? yup.mixed().required().notOneOf([undefined, null, ''], t('missing microcategoryDescription'))
        : yup.mixed(),
    subjectName:
      selectedTable === 'vendor'
        ? yup.mixed().required().notOneOf([undefined, null, ''], t('missing vendorName'))
        : yup.mixed(),
  });
};

export const nopoSchema = (t: (value: string) => string, oldRow: NopoDefaultRow | undefined) =>
  yup
    .object()
    .shape({
      wfCasual: yup.mixed(),
      workflowDefinition: yup.mixed(),
    })
    .test(
      'is-same-row',
      t('The row is not modified'),
      (value: {
        companyName: string;
        fluxName: string;
        id: number | string;
        microcategoryCode: number | string;
        microcategoryDescription: number | string;
        wfCausal: number | string;
        workflowDefinition: number | string;
      }) => {
        if (oldRow === undefined) return true;
        if (value === undefined) return false;
        const { wfCausal, workflowDefinition } = value;
        if (
          (wfCausal === oldRow.wfCausal || wfCausal === oldRow.wfIdCausal) &&
          (workflowDefinition === oldRow.wfIdWorkflowDefinition || workflowDefinition === oldRow.workflowDefinition)
        )
          return false;
        return true;
      },
    );

export const microcategoryNoPoColumns = (
  companies: Options[],
  microcategories: Options[],
  casuals: Options[],
  definitions: Options[],
  fluxes: Options[],
  isANewRow: boolean,
  t: (key: string) => string,
): TableColumn[] => [
  {
    id: '1',
    accessor: 'companyName',
    Header: t('companyName'),
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: companies,
    readOnly: !isANewRow,
  },
  {
    id: '2',
    accessor: 'microcategoryCode',
    Header: t('microcategoryCode'),
    readOnly: true,
  },
  {
    id: '3',
    accessor: 'microcategoryDescription',
    Header: t('microcategoryDescription'),
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: microcategories,
    readOnly: !isANewRow,
  },
  {
    id: '4',
    accessor: 'wfIdCausal',
    Header: t('wfIdCausal'),
    isVisible: false,
  },
  {
    id: '5',
    accessor: 'wfCausal',
    Header: t('wfCausal'),
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: casuals,
    Cell: ({ value }: { value: number | string }) => {
      return casuals.find((el) => el.value === value)?.label ?? value;
    },
  },
  {
    id: '7',
    accessor: 'idAction',
    Header: t('idAction'),
    isVisible: false,
  },
  {
    id: '8',
    accessor: 'idCompany',
    Header: t('idCompany'),
    isVisible: false,
  },
  {
    id: '10',
    accessor: 'wfIdWorkflowDefinition',
    Header: t('wfIdWorkflowDefinition'),
    isVisible: false,
  },
  {
    id: '11',
    accessor: 'workflowDefinition',
    Header: t('workflowDefinition'),
    isVisible: true,
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: definitions,
    Cell: ({ value }: { value: number | string }) => {
      return definitions.find((el) => el.value === value)?.label ?? value;
    },
  },
  {
    id: '6',
    accessor: 'ap2Template',
    Header: t('ap2Template'),
    readOnly: true,
  },
  {
    id: '9',
    accessor: 'fluxName',
    Header: t('fluxName'),
    filterType: 'select',
    inputType: 'select',
    selectOptionsTypes: fluxes,
    readOnly: !isANewRow,
  },
];

export const vendorNoPoColumns = (
  companies: Options[],
  subjects: Options[],
  casuals: Options[],
  definitions: Options[],
  fluxes: Options[],
  isANewRow: boolean,
  t: (key: string) => string,
  dispatch: Dispatch<any>,
): TableColumn[] => [
  {
    id: '1',
    accessor: 'idAction',
    Header: t('idAction'),
    isVisible: false,
  },
  {
    id: '2',
    accessor: 'idCompany',
    Header: t('idCompany'),
    isVisible: false,
  },
  {
    id: '3',
    accessor: 'companyName',
    Header: t('companyName'),
    isVisible: true,
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: companies,
    readOnly: !isANewRow,
  },
  {
    id: '4',
    accessor: 'supplierCode',
    Header: t('supplierCode'),
    filterType: 'free',
    inputType: 'text',
    readOnly: true,
  },
  {
    id: '5',
    accessor: 'subjectName',
    Header: t('subjectName'),
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: subjects,
    readOnly: !isANewRow,
  },
  { id: '6', accessor: 'wfIdCausal', Header: t('wfIdCausal'), isVisible: false },
  {
    id: '7',
    accessor: 'wfCausal',
    Header: t('wfCausal'),
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: casuals,
    Cell: ({ value }: { value: number | string }) => {
      return casuals.find((el) => el.value === value)?.label ?? value;
    },
  },
  {
    id: '8',
    accessor: 'ap2Template',
    Header: t('ap2Template'),
    filterType: 'free',
    Cell: ({ value, row }: any) => {
      return (
        <div style={{ display: 'flex', height: '100%' }}>
          <CommonButton
            action={() =>
              dispatch(
                actions.modal.openModal(modalActionType.configurator.SELECT_AP2_TEMPLATE, {
                  vendorCode: row?.original?.vendorCode,
                  companyName: row?.original?.companyName,
                  idCompany: row?.original?.idCompany,
                  idDocType: row?.original?.idDocType,
                  row: row?.original,
                }),
              )
            }
            scope="tertiary"
            value={t(value ? 'filled' : 'not filled')}
          />
        </div>
      );
    },
  },
  {
    id: '9',
    accessor: 'wfIdWorkflowDefinition',
    Header: t('wfIdWorkflowDefinition'),
    isVisible: false,
  },
  {
    id: '10',
    accessor: 'workflowDefinition',
    Header: t('workflowDefinition'),
    filterType: 'free',
    inputType: 'select',
    selectOptionsTypes: definitions,
    Cell: ({ value }: { value: number | string }) => {
      return definitions.find((el) => el.value === value)?.label ?? value;
    },
  },
  {
    id: '11',
    accessor: 'fluxName',
    Header: t('fluxName'),
    filterType: 'select',
    inputType: 'select',
    selectOptionsTypes: fluxes,
    readOnly: !isANewRow,
  },
];

export function searchFunc(
  selectedTable: string,
  companiesSelected: CompanyActionTable[],
  checkMicroCategory: () => string[],
  dispatch: Dispatch<any>,
  checkVendorList: () => string[],
) {
  return async () => {
    if (selectedTable === 'microcategory') {
      const request = {
        companyNames: companiesSelected.map((el) => el.companyName),
        microcategoryCodes: checkMicroCategory(),
      };
      if (!request.microcategoryCodes.length) return;
      const { data } = await services.getNopoMicrocategories(request);
      data.length > 0 &&
        dispatch(actions.configurator.setNoPoLeftTableRows(data.map((el, idx) => ({ id: `${idx}`, ...el }))));
      return;
    }
    if (selectedTable === 'vendor') {
      const request = {
        companyNames: companiesSelected.map((el) => el.companyName),
        supplierCodes: checkVendorList(),
      };
      if (!request.supplierCodes.length) return;
      const { data } = await services.getNopoVendor(request);
      dispatch(actions.configurator.setNoPoLeftTableRows(data.map((el, idx) => ({ id: `${idx}`, ...el }))));
    }
  };
}

export function checkMicrocategoryFunc(selectedOptionValue: OptionAT[]) {
  return () => {
    if (selectedOptionValue?.length) {
      return selectedOptionValue.map((el) => el.value.toString());
    }
    return [];
  };
}

export function checkVendorListFunc(selectedOptionValue: OptionAT[]) {
  return () => {
    if (selectedOptionValue.length) {
      return selectedOptionValue.map((el) => el.value.toString());
    }
    return [];
  };
}
