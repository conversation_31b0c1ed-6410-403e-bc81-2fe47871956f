import * as React from 'react';
import { actionType } from 'utils/constants';
import { colorPalette } from 'utils/styleConstants';
import { useTabs } from './Tabs';

export interface ITabProps {
  /**
   * Unique label of Tab to show when clicked.
   */
  label: string;
  onClickExtend?: any;
  children?: React.ReactNode;
  disabled?: boolean;
  id: number;
  singleTabWidth?: string;
}

export const Tab: React.FC<ITabProps> = (props: ITabProps) => {
  /**
   * This component allows changing of the active Tab.
   */
  const { activeTab, setActiveTab } = useTabs();
  const buttonId = (() => {
    if (props.id - activeTab === 1) return actionType.NEXT_Q1_TAB;
    if (props.id - activeTab === -1) return actionType.PREV_Q1_TAB;
    return '';
  })();

  return (
    <div
      style={{
        ...(props.disabled
          ? { opacity: 0.2, color: 'grey', borderBottom: `5px solid ${colorPalette.grey.grey4}` }
          : { opacity: 1 }),
        ...(props.singleTabWidth && { width: `${props.singleTabWidth}%` }),
      }}
      className={activeTab === props.id ? 'tab is-selected' : 'tab'}
      data-testid={`tab${props.id}`}
    >
      <button
        id={buttonId}
        type="button"
        disabled={props.disabled}
        onClick={() => {
          if (props.onClickExtend) {
            props.onClickExtend(props.id);
          }
          setActiveTab(props.id);
        }}
      >
        {props.children}
      </button>
    </div>
  );
};
