import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import utils from 'utils';
import { actionType } from 'utils/constants';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';
import actions from 'state/actions';

const EditOpenWfNotification = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const selectedRow = useSelector(selectors.configurator.getSelectedOpenWfSettings);
  const formMode = useSelector(selectors.configurator.getFormModeOpenWf);

  return (
    <CommonButton
      action={() => dispatch(actions.configurator.setFormModeOpenWf('edit'))}
      scope="tertiary"
      value={t(actionType.EDIT_OPEN_WF_NOTIFICATIONS)}
      icon="circle"
      disabled={
        !utils.user.isActionByCompanyActive(actionType.EDIT_OPEN_WF_NOTIFICATIONS) || !selectedRow || !!formMode
      }
    />
  );
};

export default EditOpenWfNotification;
