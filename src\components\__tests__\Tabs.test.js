import MultiTabs from '../Tabs/MultiTabs';
import React from 'react';
import '@testing-library/jest-dom';
import { fireEvent, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom/extend-expect';
import { colorPalette } from 'utils/styleConstants';
import { renderWithStyle } from 'utils/helpers/test.helpers';

const tabs = [
  { name: 'Tab0', content: <p>hi0</p>, disabled: true, id: 0 },
  { name: 'Tab1', content: <p>hi1</p>, id: 1 },
  { name: 'Tab2', content: <p>hi2</p>, id: 2 },
  { name: 'Tab3', content: <p>hi3</p>, id: 3 },
  { name: 'Tab4', content: <p>hi4</p>, id: 4 },
  { name: 'Tab5', content: <p>hi5</p>, id: 5 },
];

describe('Tabs', () => {
  test('gets rendered in DOM', async () => {
    renderWithStyle(<MultiTabs tabs={tabs} />);
    const TabsElement = screen.getAllByRole('button');
    expect(TabsElement[0]).toBeInTheDocument();
  });
  test('renders tabs with correct names', async () => {
    renderWithStyle(<MultiTabs tabs={tabs} />);
    const TabsElement = screen.getAllByRole('button');
    TabsElement.forEach((tab, index) => {
      expect(tab).toHaveTextContent('Tab' + index);
    });
  });
  test('renders tabs with correct content', async () => {
    const { container } = renderWithStyle(<MultiTabs tabs={tabs} />);
    const TabContent = container.querySelectorAll('p');
    // First tab is disabled, so it should render the second tab
    expect(TabContent[0]).toHaveTextContent('hi1');
  });

  it('first button should be disabled', async () => {
    renderWithStyle(<MultiTabs tabs={tabs} />);
    const TabsElement = screen.getAllByRole('button');
    expect(TabsElement[0]).toHaveAttribute('disabled');
  });
  it('all other button should be enabled', async () => {
    renderWithStyle(<MultiTabs tabs={tabs} />);
    const TabsElement = screen.getAllByRole('button');
    for (let i = 1; i < TabsElement.length; i++) {
      expect(TabsElement[i]).not.toHaveAttribute('disabled');
    }
  });
  it('disabled Button`s bottom border should be grey and have low opacity', async () => {
    const { container } = renderWithStyle(<MultiTabs tabs={tabs} />);
    const TabsDivs = container.getElementsByClassName('tab');
    expect(TabsDivs[0]).toHaveStyle({
      borderBottomColor: `${colorPalette.grey.grey4}`,
      opacity: '0.2',
    });
  });
  it('enabled Button`s bottom border should be white and have high opacity', async () => {
    const { container } = renderWithStyle(<MultiTabs tabs={tabs} />);
    const TabsDivs = container.getElementsByClassName('tab');
    expect(TabsDivs.item(2)).toHaveStyle({
      borderBottomColor: `${colorPalette.turquoise.light}`,
      opacity: '1',
    });
    for (let i = 2; i < TabsDivs.length; i++) {
      expect(TabsDivs.item(i)).toHaveStyle({
        borderBottomColor: `${colorPalette.turquoise.light}`,
        opacity: '1',
      });
    }
  });
  it('clicks on tab should change active tab', async () => {
    const { container } = renderWithStyle(<MultiTabs tabs={tabs} />);
    const TabsElement = container.getElementsByClassName('tab');
    const buttons = screen.getAllByRole('button');
    fireEvent.click(buttons[1]);
    for (let i = 1; i < TabsElement.length; i++) {
      userEvent.click(buttons[i]);
      expect(TabsElement[i]).toHaveClass('is-selected');
      expect(TabsElement[i - 1]).not.toHaveClass('is-selected');
      expect(screen.getByText('hi' + i)).toBeInTheDocument;
    }
  });
  it('tab content has no Border and maxHeight equal 100%', async () => {
    const { container } = renderWithStyle(<MultiTabs tabs={tabs} hasBorder />);
    const TabContent = container.querySelectorAll('p');
    expect(TabContent[0]).toHaveStyle({
      borderBottom: 'none',
    });
  });

  it('disable tab should not be clicked ', () => {
    const { container } = renderWithStyle(<MultiTabs tabs={tabs} />);
    const buttons = screen.getAllByRole('button');
    const TabsElement = container.getElementsByClassName('tab');
    fireEvent.click(buttons[0]);
    expect(buttons[0]).toHaveAttribute('disabled');
    expect(TabsElement[0]).not.toHaveClass('is-selected');
  });
  it('default active button color should be medium turquoise', () => {
    const { container } = renderWithStyle(<MultiTabs tabs={tabs} />);
    const TabsElement = container.getElementsByClassName('tab is-selected');
    expect(TabsElement[0]).toHaveStyle({
      borderBottomColor: `${colorPalette.turquoise.medium}`,
    });
  });
  it('if first tab is disabled then he should select the second as default', () => {
    const { container } = renderWithStyle(<MultiTabs tabs={tabs} />);
    const TabsElement = container.getElementsByClassName('tab');
    expect(TabsElement[0]).not.toHaveClass('is-selected');
  });
});
