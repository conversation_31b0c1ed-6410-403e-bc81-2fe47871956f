import Modal from 'components/Modal';
import React from 'react';
import utils from 'utils';
import selectors from 'state/selectors';
import services from 'services';
import { useDispatch, useSelector } from 'react-redux';
import RadioButton from 'components/Buttons/RadioButton';
import { ExpActionsTable } from 'models/request';
import actions from 'state/actions';
import { GetNopoMicrocategoriesResponse, GetNopoVendorResponse } from 'models/response';
import {
  checkMicrocategoryFunc,
  checkVendorListFunc,
  searchFunc,
} from 'routes/Configurator/routes/ActionTables/NoPo/components/utils';

type Row = {
  companyName: string;
  workflowDefinition: number | string;
  wfCausal: number | string;
  fullOcr: boolean;
  id: number;
  microcategoryCode: string;
  microcategoryDescription: string;
};

function SelectCompanies() {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const rows: any[] = useSelector(selectors.configurator.getNOPOLeftTableRows);
  const { row }: { row: Row } = useSelector(selectors.modal.getModalProps);
  const [allCompaniesSelected, setAllCompaniesSelected] = React.useState<string | null>(null);
  const selectedCompanies = useSelector(selectors.configurator.getSelectedCompanies);
  const selectedTable = useSelector(selectors.configurator.selectTableToSearch);
  const companiesSelected = useSelector(selectors.configurator.getSelectedCompanies);
  const selectedOptionValue = useSelector(selectors.configurator.getSelectedOptionValue);
  const notModifiedRow = useSelector(selectors.configurator.getNOPONotModifiedRow);
  const checkMicroCategory = checkMicrocategoryFunc(selectedOptionValue);
  const checkVendorList = checkVendorListFunc(selectedOptionValue);
  const search = searchFunc(selectedTable, companiesSelected, checkMicroCategory, dispatch, checkVendorList);

  const onSaveAction = async () => {
    try {
      if (notModifiedRow === undefined) throw new Error('notModifiedRow not found');
      const idAction: number = rows?.find((originalRow) => originalRow?.id === String(row.id))?.idAction;
      if (idAction === undefined) throw new Error('idAction not found');
      let wfIdWorkflowDefinition: number | null = row.workflowDefinition as number;
      let wfIdCausal: number | null= row.wfCausal as number;
      if (typeof row.workflowDefinition === 'string') {
          wfIdWorkflowDefinition = notModifiedRow.wfIdWorkflowDefinition;
      }
      if (typeof row.wfCausal === 'string') {
          wfIdCausal = notModifiedRow.wfIdCausal;
      }
      const expActionsTable: ExpActionsTable = {
        idAction: idAction,
        fullOcr: false,
        wfIdCausal: wfIdCausal,
        wfIdWorkflowDefinition: wfIdWorkflowDefinition,
        corporateApproval: false,
      };
      let response;
      if (allCompaniesSelected === 'false') {
        response = await services.editExpActionTable({
          companyNames: [row.companyName],
          expActionsTable,
        });
      } else {
        response = await services.editExpActionTable({
          companyNames: selectedCompanies.map((company) => company.companyName),
          expActionsTable: expActionsTable,
        });
      }
      if (response.data.length !== 0) {
        utils.app.notify('warning', t('company-warn') + response.data.join(', '));
      }
      await search();
      dispatch(actions.app.setDisabledDropdown(false));
      utils.app.notify('success', t('editSuccess'));
    } catch (error) {
      console.error(error);
      utils.app.notify('fail', t('editError'));
      // set the old row in the table
      const allRowsCopy = [...rows] as GetNopoVendorResponse[] | GetNopoMicrocategoriesResponse[];
      const index = allRowsCopy.findIndex((originalRow: any) => originalRow.id === String(row.id));
      if (index === -1 || notModifiedRow === undefined) return;
      allRowsCopy[index] = notModifiedRow;
      dispatch(actions.configurator.setNoPoLeftTableRows(allRowsCopy));
      dispatch(actions.app.setDisabledDropdown(false));
    }
  };

  return (
    <div>
      <Modal.Header title={t('applyChangesTitle')} />
      <Modal.Content>
        <h4>{t('applyChanges')}</h4>
        <RadioButton
          options={[
            { value: 'false', label: t('single company') },
            { value: 'true', label: t('multiple companies') },
          ]}
          onChange={(e) => {
            setAllCompaniesSelected(e);
          }}
        ></RadioButton>
      </Modal.Content>
      <Modal.Footer
        confirmText={t('confirm')}
        confirmAction={onSaveAction}
        confirmDisabled={allCompaniesSelected === null}
        withCloseButton={false}
      />
    </div>
  );
}

export default SelectCompanies;
