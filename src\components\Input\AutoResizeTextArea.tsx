import styled from 'styled-components/macro';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { statusColors } from 'utils/styleConstants';

const Container = styled.div<{ height?: number }>`
  position: relative;
  height: ${({ height }) => `${height}px`};
`;

export const TextArea = styled.textarea<{
  hasFeedBack?: boolean;
  feedbackColor?: string;
  minHeight?: number;
  maxHeight?: number;
  fontSize?: number;
}>`
  font-family: Roboto;
  box-sizing: border-box;
  min-width: 165px;
  width: 165px;
  height: 25px;
  overflow-y: hidden;
  min-height: ${({ minHeight }) => `${minHeight}px`};
  max-height: ${({ maxHeight }) => `${maxHeight}px`};
  border: 1px solid
    ${({ hasFeedBack, feedbackColor, theme }) =>
      hasFeedBack
        ? feedbackColor || theme.colorPalette.red.error
        : theme.colorPalette.isDarkMode
        ? theme.colorPalette.grey.grey3
        : theme.colorPalette.grey.grey5};
  border-radius: 3px;
  padding: 4px 5px;
  line-height: 1.4;
  margin: 0;
  resize: none;
  font-size: ${({ fontSize, theme }) => (fontSize ? `${fontSize}px` : theme.fontSizePalette.body.XS)};
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.white};
  color: ${({ theme }) => theme.colorPalette.grey.grey9};

  &::placeholder {
    color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey8 : theme.colorPalette.grey.grey9};
  }

  &:focus {
    outline: 0;
    border: 1px solid
      ${({ hasFeedBack, feedbackColor, theme }) =>
        hasFeedBack ? feedbackColor || theme.colorPalette.red.error : theme.colorPalette.turquoise.normal};
  }

  ::-webkit-scrollbar {
    width: 3px;
  }
  ::-webkit-scrollbar-track {
    background: ${({ theme }) => theme.colorPalette.grey.grey5};
  }
  ::-webkit-scrollbar-thumb {
    background: ${({ theme }) => theme.colorPalette.turquoise.normal};
  }
  ::-webkit-scrollbar-thumb:hover {
    background: ${({ theme }) => theme.colorPalette.turquoise.dark};
  }
  &:disabled {
    background-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey8 : theme.colorPalette.grey.grey1};
    color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey6 : theme.colorPalette.grey.grey5};
  }
`;

export const FeedbackText = styled.p<{ feedbackColor?: string }>`
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  color: ${({ feedbackColor }) => feedbackColor || statusColors.error};
  text-align: right;
  position: absolute;
  right: 0;
  padding-top: 3px;
  text-wrap: nowrap;
`;

export type Props = {
  onChange?: (event: React.ChangeEvent<HTMLTextAreaElement>) => void;
  onBlur?: (event: React.FocusEvent<HTMLTextAreaElement>) => void;
  onFocus?: (event: React.FocusEvent<HTMLTextAreaElement>) => void;
  hasFeedBack?: boolean;
  feedbackColor?: string;
  feedBackMessage?: string;
  value?: string;
  name?: string;
  fontSize?: number;
  placeholder?: string;
  maxHeight?: number;
  minHeight?: number;
};

function AutoResizeTextArea({
  hasFeedBack,
  feedBackMessage,
  feedbackColor,
  value = '',
  onChange,
  onBlur,
  onFocus,
  name,
  fontSize,
  placeholder,
  minHeight = 25,
  maxHeight = 60,
}: Props) {
  const [currentValue, setCurrentValue] = useState(value);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  const resizeTextArea = useCallback(() => {
    if (textareaRef.current && containerRef.current) {
      textareaRef.current.style.height = `${minHeight}px`; // Reset height to base
      containerRef.current.style.height = `${minHeight}px`;
      const height = `${Math.max(minHeight, Math.min(textareaRef.current.scrollHeight, maxHeight))}px`;
      textareaRef.current.style.height = height;
      containerRef.current.style.height = height;
      textareaRef.current.style.overflowY = textareaRef.current.scrollHeight > maxHeight ? 'scroll' : 'hidden';
    }
  }, [minHeight, maxHeight]);

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setCurrentValue(e.target.value);
    resizeTextArea();
    onChange && onChange(e);
  };
  useEffect(() => {
    setCurrentValue(value);
    resizeTextArea();
  }, [value, resizeTextArea]);

  return (
    <Container ref={containerRef} height={minHeight}>
      <TextArea
        id={name}
        name={name}
        ref={textareaRef}
        value={currentValue}
        onChange={handleChange}
        onBlur={onBlur}
        onFocus={onFocus}
        hasFeedBack={hasFeedBack}
        feedbackColor={feedbackColor}
        minHeight={minHeight}
        maxHeight={maxHeight}
        fontSize={fontSize}
        placeholder={placeholder}
      />
      {hasFeedBack && feedBackMessage && <FeedbackText feedbackColor={feedbackColor}>{feedBackMessage}</FeedbackText>}
    </Container>
  );
}

export default AutoResizeTextArea;
