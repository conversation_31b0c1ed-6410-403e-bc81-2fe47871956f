/* eslint max-lines: off */
import { BoundingBox, GetInfoFor<PERSON>uery, Subject } from './response';
import { FieldStatus, FieldType } from './documents';
import { UserCsvSep, UserDate, UserDecimalSep } from './user';
import { ReportFilter } from './report';

// <PERSON><PERSON> requests
export interface Login {
  username: string | null;
  password: string | null;
  code?: string; // OAuth Login
  accessToken?: string; // OAuth Login
}

export interface LoginOAuth {
  code: string; // OAuth Login
  accessToken: string; // OAuth Login
}

export interface LockOrUnlockDocument {
  idUser: number;
  idProgram: number;
  protocol: number;
}

export interface SetDocumentGridColumnsBody {
  idUser: number;
  idTemplate: number;
  fixedColumns: number;
  documentGridColumns: {
    width: number;
    position: number;
    idTemplateColumn: number;
  }[];
}

// wDocBatch
export interface SetMemo {
  protocols: number[];
  idUser: number;
  memo: string;
}

export interface DeleteDocuments {
  protocols: number[];
  idCompany: number;
  idUser: number;
  idProgram: number;
}

export interface ChangePriority {
  protocols: number[] | string[];
  idCompany: number;
  idUser: number;
  idProgram: number;
  priority: number;
}

export interface GetAllowedUsers {
  idUser: number;
  idProgram: number;
  idCompany: number;
}

export interface Reassign {
  protocols: number[] | string[];
  idUser: number;
  idUserTo: number;
  idProgram: number;
}

export interface InsertDocumentAttach {
  idUser: number;
  protocol: number;
  base64Attach: string;
  extension: string;
  fileName: string;
}

export interface DeleteDocumentAttach {
  idDocAttach: number;
  idUserDisable: number;
}

export interface GetDocumentAttach {
  idDocAttach: number;
}

export interface GetArchivedAttachImage {
  idDocAttach: number;
  protocol: number;
}

export interface GetZoneText {
  protocol: number;
  pageNumber: number;
  fieldType: FieldType;
  x1: string;
  y1: string;
  x2?: string;
  y2?: string;
}

export interface GetDocumentAttachments {
  protocol: number;
}

export interface SendMail {
  protocols: number[];
  idUser: number;
  idProgram: number;
  to: string;
  cc?: string;
  bcc?: string;
  subject?: string;
  body?: string;
}

export interface ChangeUserSettings {
  idUser: number;
  base64Image: string | null;
  flagUpdateImage: 0 | 1;
  preferredDateFormat: UserDate;
  decimalSeparator: UserDecimalSep;
  sendMail: 0 | 1;
  csvSeparator: UserCsvSep;
  timeZone: string;
  openNextDocument: boolean;
  openInvoiceLogAutomatically: boolean;
}

export interface GetDocumentFields {
  protocol: number;
  idProgram: number;
}

export interface GetDocumentFieldsArchive {
  protocol: number;
  idProgram: number;
  idUser: number;
}

export interface GetDocumentHeader {
  protocol: number;
  idProgram: number;
  idTemplate: number;
}

export interface GetSubjects {
  key: string;
  value: string;
  searchType: number;
  distance?: number;
  idCompany: number;
  idDocType?: number;
  fromClient?: boolean;
}

export interface InsertSubjects {
  subject: string;
  vatNumber: string;
  fiscalCode: string;
  validationType: 0;
  tel?: string;
  fax?: string;
  email?: string;
  url?: string;
  status?: number;
  typology?: number;
  documentsType?: number;
  dateType?: number;
  blocked?: number;
  idCompany: number | null;
  iban?: string;
  address?: string;
  debitCredit: number;
}

export interface ChangeDocumentType {
  idDocType: number;
  idCompany: number;
  protocols: number[];
  idProgram: number;
}

export interface ChangeCompanyDocument {
  idDocType: number;
  idCompany: number;
  protocols: number[];
  idProgram: number;
}

export interface SetSubject {
  idSubject: number;
  protocols: number[];
}

export interface SaveDocument {
  protocol: number;
  idUser: number;
  idProgram: number;
  docStatus: number | null; // null for contracts
  movementTypeCode: string | null; // null for contracts
  export1Code: string | null; // null for contracts
  export2Code: string | null; // null for contracts
  subject: Subject | null; // null for contracts
  supplierCode: string | null; // null for contracts
  documentFields: SaveDocumentFields[];
  accountFields: SaveDocumentAccountFields;
}

export interface ValidateDocument {
  protocols: number[];
  idProgram: number;
  idUserQualityCheck: number;
  forceSubmit: boolean;
}

export interface SaveDocumentFields {
  idField: number;
  values: SaveDocumentQ1FieldValue[];
  position: number;
}

export interface ApplyRules {
  protocol: number;
  idDocType: number;
  supplierCode: string;
  fields: {
    // q1 id
    idField: number;
    // q1 fields
    fieldValues: string[];
    // q3 fields
    accountFields: Q3Values | null;
    // position
    num: number;
  };
}

export interface Q3Values {
  [idFieldGroup: string]: Array<{ [idColumn: string]: string[] }>;
}

export interface SaveDocumentQ1FieldValue {
  content: string;
  boundingBox: BoundingBox;
  pageNumber: number;
  confidence: number;
  status: FieldStatus;
}

export interface SaveDocumentQ3FieldValue {
  content: string;
  boundingBox: BoundingBox;
  pageNumber: number;
  confidence: number;
}

// key = bodyId from idFieldGroup property
// SaveDocumentAccountFieldBody
export interface SaveDocumentAccountFields {
  [key: string]: SaveDocumentAccountFieldBodyRow[];
}
// key = columnId from fieldColum property of FieldGroup
export interface SaveDocumentAccountFieldBodyRow {
  [key: string]: SaveDocumentQ3FieldValue;
}

export interface UploadFile {
  idUser: number;
  idCompany: number;
  idDocType: number;
  pdfBase64: string;
  splitString: string;
  filename: string;
  uploaderFields: any;
  pageAction: PageAction;
  originalProtocol?: number;
}

export interface PageAction {
  [key: number]: {
    delete: boolean;
    rotation: 0 | 90 | 180 | 270 | null;
  };
}

export interface GetDiskMonitor {
  dateFrom: string;
  dateTo: string;
}

// REPORT
export interface ExecReportPayload {
  idReport: number;
  idUser: number;
  queryReportInfo: GetInfoForQuery;
  outputFormat: string;
  async: boolean;
}

export interface DownloadExecutedReport {
  idUser: number;
  pathToFile: string;
}

export interface SetScanDateBody {
  protocol: number[] | null;
  date: string;
  idUser?: number;
}

export interface SplitAndFix {
  documentToDistributeList: number[];
}

export interface BulkInsertBody {
  idCompany: number;
  base64Csv: string;
}

export interface CloseReject extends BaseCloseOperation {
  idWfCausal: number;
}

export type CloseParkDocument = BaseCloseOperation;
interface BaseCloseOperation {
  protocol: number;
  note: string;
}
export interface WfInfoBody {
  idCompany: number;
  idUser: number;
}

export interface OpenWfBody {
  idCausal: number;
  idWorkflowDefinition: number;
  protocols: number[];
  idCompany: number;
  idTaskDefinition: number | undefined;
  idGroups: number[];
  note: string;
}

export interface OpenWfInfoBody {
  protocols: number[];
  idUser: number;
  idGroup: number;
  idWorkflowDefinition: number;
  idCompany: number;
  note: string;
}

export interface CloseRejectPrevious {
  protocol: number;
  idPreviousTask: number;
  note: string;
}

export interface WfTaskGroups {
  idGroup: number[];
  idTaskDefinition: number | null | undefined;
}

export interface ReferenceNextTaskId {
  isLastTask: boolean;
  wfTaskGroups: WfTaskGroups;
}

export interface EscalationId {
  wfTaskGroups: WfTaskGroups;
}

export interface ReferenceId {
  isLastTask: boolean;
  wfTaskGroups: WfTaskGroups;
  idCompany: any;
  idDefinition: number | null;
}

export interface CloseConvalidateDocumentBody {
  protocol: number;
  note: string;
  referenceNextTaskId: ReferenceNextTaskId | null;
  referenceId: ReferenceId | null;
  escalationId: EscalationId | null;
  definitionClosable: boolean;
  finalDestinationUser: number | null;
}

export interface CloseConvalidateInfoDocumentBody {
  protocol: number;
  note: string;
  finalDestinationUser: number | null;
}

export interface OpenLitigationBody {
  idCompany: number;
  protocols: number[];
}

export interface CloseLitigationBody {
  protocols: number[];
  idCausal: number;
  moveType: string;
  mailAttach: boolean;
  pdfAttach: boolean;
  idCompany: number;
  mailSupplier: string | null;
  note: string;
  mailCc: string | null;
  mailBody?: string;
  park: boolean;
}

export interface PrintPdfLitigationRequest {
  idCausal: number;
  idCompany: number;
  protocols: number[];
  text: string | null;
  text2: string | null;
}

export interface CreateAndSaveUserBody {
  idUser?: number;
  name: string;
  username: string;
  email: string;
  sendMail: number;
  groupDistribution: number;
  loginType: string;
  changePassword?: number;
  blocked: number;
  expirationDate: Date | null;
}

export interface CloneUserBody {
  idUserToCopy: number;
  emailNewUser: string;
  usernameNewUser: string;
  nameNewUser: string;
}

export type CreateNewTemplate = {
  idDocType: number;
  templateName: string;
  pdfTemplatePath: string;
  metadataJsonPath?: string;
};

interface BaseTemplate extends BoundingBox {
  idTemplate: number;
  page: number;
  content: string;
}

export type AddRow = BaseTemplate & {
  idField: number | null;
  idColumn: null | number;
};

export type EditRow = AddRow & {
  rowNumber: number;
};
export type DeleteRow = EditRow;

export type AddTemplateFields = AddRow[];
export type UpdateTemplateFields = EditRow[];
export type DeleteTemplateFields = DeleteRow[];

export type AddDeleteUserCompanyRole = {
  idUser: number;
  idCompany: number;
  idRole: number;
};

export type EditUserCompanyRole = {
  idUser: number;
  idCompany: number;
  oldIdRole: number;
  newIdRole: number;
};

type ProgramPrivilegeRequest = {
  idProgram: number;
  idPrivilege: number | null;
};

export type ConfigureNewRole = {
  roleName: string;
  programPrivilegeRequests: ProgramPrivilegeRequest[];
};

export type EditConfiguredRole = {
  idProgram: number;
  idRole: number | null;
  idPrivilege: number | null;
  invisible: boolean;
};

export type CloneUserRoleConfiguration = {
  idCompany: number;
  idCloningUser: number;
  idClonedUser: number;
};

export interface CloneCompanyBody {
  idCompanyToClone: number;
  nameNewCompany: string;
}

export interface CreateCompanyBody {
  name: string;
  label: string;
  vatNumber: string;
  socialReason: string;
  fiscalCode: string;
  phone: string;
  fax: string;
  email: string;
  url: string;
  onlineCode: string;
  referenceUser: number | undefined;
  counterPrefix: string;
  country: string;
  currency: string;
  countryCurrency: string;
}

export interface EditCompanyBody extends CreateCompanyBody {
  idCompany: number;
}

export interface GetPoMicroCategoryRequest {
  companyNames: string[];
  microcategoryCodes: string[];
}

export interface GetPoVendorRequest {
  companyNames: string[];
  supplierCodes: string[];
}

export type RoleType = {
  idRole: number;
  roleName: string;
  roleDescription: string;
};

export type RoleList = {
  idCompany: number;
  name: string;
  oRolesList: RoleType[];
  socialReason: string;
};
export interface ReverseRepost {
  protocol: number;
  documentAction: string;
  automaticDistribute: boolean;
  idProgram: number;
}

export type EditExpActionTable = {
  expActionsTable: ExpActionsTable;
  companyNames: string[];
};

export type ExpActionsTable = {
  idAction: number; // id of the row
  wfIdCausal: number | null;
  wfIdWorkflowDefinition: number | null;
  fullOcr: boolean;
  corporateApproval: boolean;
};

export type CreateExpActionTable = {
  companyName: string; // dalla row
  costCenterCode: string; // passare riga vuota
  vendorCode: string; // caso microcategory passare microcategoryCode
  vendorCategoryCode: string; // caso microcategory passare microcategorydescription
  fluxName: string; // recuperate dal fluxtype
  wfName: string | null; // wfdefinition.wfname
  wfIdGroups: string | null; // non presente quindi null
  wfIdCausal: number | null; // idWfCausal
  wfIdTaskDefinition: number | null; // null
  wfIdWorkflowDefinition: number | null; // null
  wfNote: string | null; // null
  fullOcr: boolean | null; // fullOcr
  corporateApproval: boolean; // non presente quindi false
};

export type WfDefinition = {
  currencyExchange: boolean;
  idCompany: number;
  idDefinition: number;
  noteMandatory: boolean;
  suspendType: string;
  takeNextSingleTask: boolean;
  taskOnMinCausalWeight: boolean;
  wfDesc: string;
  wfName: string;
};

export type WfCasual = {
  causalDesc: string;
  causalName: string;
  idCompany: number;
  idWfCausal: number;
  weight: number;
};

export interface MicroCategory {
  idCompany: number;
  code: string;
  description: string;
  language: string;
}
export interface EditCorporateApproval {
  microcategoryCode: string;
  corporateApproval: boolean;
}
export interface GetNopoMicrocategories {
  companyNames: string[];
  microcategoryCodes: string[];
}
export interface GetNopoVendor {
  companyNames: string[];
  supplierCodes: string[];
}

export type CommonDocumentAttributes = {
  idProgram: number;
  idUser: number;
  companyEquals?: number;
  socialReasonIn?: string[];
  socialReasonLike?: string;
  socialReasonEquals?: string;
  vatNumberIn?: string[];
  vatNumberLike?: string;
  vatNumberEquals?: string;
  protocolInIn?: string[];
  protocolInLike?: string;
  protocolInEquals?: string;
  registrationProtocolIn?: string[];
  registrationProtocolLike?: string;
  registrationProtocolEquals?: string;
  registrationProtocolRange?: [string | undefined, string | undefined];
  protocolInRange?: [string | undefined, string | undefined];
  vendorNameIn?: string[];
  vendorNameLike?: string;
  vendorNameEquals?: string;
  supplierCodeIn?: string[];
  supplierCodeLike?: string;
  supplierCodeEquals?: string;
  documentTypeEquals?: number;
  documentTypeLike?: string;
  userNameLike?: string;
  userEquals?: number;
  documentNameLike?: string;
  fromDateEquals?: string;
  fromDateLess?: string;
  fromDateGreater?: string;
  toDateEquals?: string;
  toDateLess?: string;
  toDateGreater?: string;
  invoiceNumberIn?: string[];
  invoiceNumberLike?: string;
  invoiceNumberEquals?: string;
  idGroupIn?: number[];
  idGroupEquals?: number;
  priorityIn?: number[];
  memoEquals?: string;
  memoLike?: string;
};

export type GetDocumentFromDataRepository = CommonDocumentAttributes & {
  channelTypeEquals?: string;

  positionEquals?: string;
  positionIn?: string[];

  fileNameEquals?: string;
  fileNameLike?: string;
  fileNameIn?: string[];

  movementTypeIn?: string[];
  movementTypeLike?: string;
  movementTypeEquals?: string;

  fromRegistrationDateEquals?: string;
  fromRegistrationDateLess?: string;
  fromRegistrationDateGreater?: string;

  toRegistrationDateEquals?: string;
  toRegistrationDateLess?: string;
  toRegistrationDateGreater?: string;

  fromArchiveDateEquals?: string;
  fromArchiveDateLess?: string;
  fromArchiveDateGreater?: string;

  toArchiveDateEquals?: string;
  toArchiveDateLess?: string;
  toArchiveDateGreater?: string;

  fromAcquisitionDateEquals?: string;
  fromAcquisitionDateLess?: string;
  fromAcquisitionDateGreater?: string;

  toAcquisitionDateEquals?: string;
  toAcquisitionDateLess?: string;
  toAcquisitionDateGreater?: string;

  fromAdrEquals?: string;
  fromAdrLike?: string;
};

export type GetDocument = CommonDocumentAttributes & {
  allAuthorized: boolean;
  idTemplate: number;
};

export type CountReportRows = {
  table: string;
  source: string;
  selectItems: {
    columnHeader: string;
    columnName: string;
  }[];
  filters: ReportFilter[];
};

export interface SummaryKpiFilters {
  dateFrom: string;
  dateTo: string;
  idCompanyList?: number[];
  idDocTypeList?: number[];
  incChannelList?: number[];
}

export interface ChartFilters {
  dateFrom: string;
  dateTo: string;
  idCompanyList?: number[];
  idDocTypeList?: number[];
  incChannelList?: number[];
  idCharts: number[];
}

export interface CategoryDetailsFilters {
  dateFrom: string;
  dateTo: string;
  idCompanyList?: number[];
  idDocTypeList?: number[];
  incChannelList?: number[];
  idCategory: number;
  allInOneView?: boolean;
}

export interface ReportFilters {
  dateFrom: string;
  dateTo: string;
  idCompanyList?: number[];
  idDocTypeList?: number[];
  incChannelList?: number[];
  idReport: number;
}

export interface SaveUpdateReportSettings {
  idReport: number;
  idFolder: number;
  settingType: string;
  filtersJson: ReportFilter[];
  selectItemsJson: {
    columnHeader: string;
    columnName: string;
  }[];
}

export interface ReassignDocumentLatency {
  protocols: number[] | string[];
  idProgram: number;
}

export interface IOptionValue {
  [key: string]: any;
}

export interface IFilterValue {
  idFilter: number;
  options: IOptionValue[];
}

export interface GetDocumentsDynamic {
  idProgram: number;
  idUser: number;
  idTemplate: number;
  allAuthorized: boolean;
  filters: IFilterValue[];
  protocolsToFilter?: number[];
  idGroupEquals?: number | null;
  idGroupIn?: number[];
}

export interface GetDocumentsFromDataRepositoryDynamic {
  idProgram: number;
  idUser: number;
  idTemplate: number;
  filters: IFilterValue[];
}

export interface AddOpenWfNotificationSettings {
  idUser: number;
  openWfSendMail: boolean;
  openWfSendNotificationImmediately: boolean;
  openWfMaxDocuments: number;
  openWfMaxDocumentDetails: number;
  idTemplateImmediateOpenWorkflow: number;
  idTemplateScheduledOpenWorkflow: number;
  openWfFrequency: string;
}

export interface AddWfReminderNotificationSettings {
  idUser: number;
  reminderSendMail: boolean;
  startDeltaReminder: number;
  endDeltaReminder: number;
  reminderDays: number;
  maxReminder: number;
  reminderMaxDocuments: number;
  reminderMaxDocumentDetails: number;
  idTemplateScheduledReminder: number;
  reminderFrequency: string;
}

export interface AddOverDueNotificationSettings {
  idUser: number;
  overDueSendMail: boolean;
  overDueMaxDocuments: number;
  idTemplateScheduledOverdue: number;
  overDueFrequency: string;
}
