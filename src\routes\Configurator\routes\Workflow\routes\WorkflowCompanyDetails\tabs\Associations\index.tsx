/* eslint-disable max-lines */
import axios from 'axios';
import { CommonButton } from 'components/Buttons';
import Checkbox from 'components/Buttons/CheckboxButton';
import MultiSwitch from 'components/MultiSwitch';
import Table, { OriginalRow, TableProps } from 'components/Table';
import {
  CompanyGroupsForTaskAssociation,
  CreateAssociation,
  WorkflowDefinitionTaskGroupsAssociations,
  WorkflowDefinitionTaskGroupsAssociationsExtended,
} from 'models/response';
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import services from 'services';
import actions from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import utils from 'utils';
import { modalActionType } from 'utils/constants';
import { buildPayload, formValidation, formValidationWhenSubRowEdited, relationList, transformedRows } from './utils';
import { useSWRConfig } from 'swr';
import { NumberInput } from 'components/Input';
import { RowAccessor } from 'components/Table/interfaces';

const CustomTitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.M};
  font-weight: bold;
`;
const Wrapper = styled.div`
  margin-top: 20px;
`;
const WrapperTables = styled.div`
  display: flex;
  flex-direction: row;
  overflow-x: auto;
`;
const FirstTable = styled.div`
  padding-right: 15px;
`;

const GridInput = styled.input`
  max-width: 100%;
  font-family: Roboto;
  font-weight: ${({ theme }) => theme.fontWeightPalette.regular};
  border: none;
  border-radius: 3px;
  &:focus {
    outline: 0;
  }
  ::-ms-reveal,
  ::-ms-clear {
    display: none;
  }
`;

const AssociationsTab = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const [isTableGroupsVisible, setIsTableGroupsVisible] = useState<boolean>(false);

  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  const selectedAssociation = useSelector(selectors.configurator.getSelectedDefinitionTaskGroupsAssociations);
  const editedRowId = useSelector(selectors.configurator.getEditedRowIdWfAssociations);
  const listOfGroups = useSelector(selectors.configurator.getListOfGroupsForTaskAssociations);
  const isActiveCreateNewAssociation = useSelector(selectors.configurator.getIsActiveCreateNewAssociation);
  const isActiveEditAssociation = useSelector(selectors.configurator.getIsActiveEditAssociation);
  const isActiveAddTaskToAssociation = useSelector(selectors.configurator.getIsActiveAddTaskToAssociation);
  const openRowIdTable = useSelector(selectors.configurator.getOpenRowIdAssociation);
  const selectedTab = useSelector(selectors.configurator.getSelectedTab);

  // each definition has a list of available tasks for association,
  // this is the id definition of the selected association
  // for update the API that returns list of available tasks
  const idDefinitionSelected = useSelector(selectors.configurator.getSelectedIdDefFromAssociations);
  // this is the idTaskDefinition of the selected association
  // for update the API that returns list of available tasks
  const idTaskDefinitionSelected = useSelector(selectors.configurator.getSelectedIdTaskDefFromAssociations);

  // list of all associations
  const { associations } = services.useGetDefinitionTaskGroupsAssociations(
    'getDefinitionTaskGroupsAssociationsKey',
    companiesDefinition?.idCompany,
  );
  const { listOfTasksForAssociations, revalidate } = services.useGetCompanyTaskForAssociation(
    'getCompanyTasksForAssociationsKey',
    companiesDefinition?.idCompany,
    idDefinitionSelected,
    idTaskDefinitionSelected,
  );

  const { listOfDefinitionsForAssociations } = services.useGetCompanyDefinitionsForAssociation(
    'getCompanyDefinitionForAssociationsKey',
    companiesDefinition?.idCompany,
  );
  const listOfConvalidation = services.useGetWfOConvalidationTaskActionAssociation('getWfConvalidationKey');
  const { listOfGroupsForAssociations } = services.useGetGroupsForTaskAssociations(
    'getCompanyGroupsForAssociationsKey',
    companiesDefinition?.idCompany,
  );

  const latestGroupsList = useRef<CompanyGroupsForTaskAssociation[]>([]);
  const latesSelectedAssociation = useRef<WorkflowDefinitionTaskGroupsAssociationsExtended | null>(null);
  const { mutate } = useSWRConfig();

  const [openRow, setOpenRow] = useState<number | null | undefined>(null);
  const [rowNewAssociation, setRowNewAssociation] = useState<OriginalRow | null>(null);

  const getCurrentAssociation =
    associations?.find((item) => item.definitionTaskAssociations.find((subItem) => subItem.idTask === editedRowId))
      ?.id ?? null;

  useEffect(() => {
    setOpenRow(getCurrentAssociation);
  }, [getCurrentAssociation]);

  useEffect(() => {
    latestGroupsList.current = listOfGroups;
  }, [listOfGroups]);

  useEffect(() => {
    latesSelectedAssociation.current = selectedAssociation;
  }, [selectedAssociation]);

  const setCheckValue = (
    value: number | boolean | string,
    row: OriginalRow,
    accessor: 'assigned' | 'relation' | 'amountLimit',
  ) => {
    if (isActiveAddTaskToAssociation || isActiveCreateNewAssociation) {
      const copyOf = listOfGroups.map((el) => {
        if (el.idGroup === row.original.idGroup) {
          return {
            ...el,
            [accessor]: value,
          };
        }
        return el;
      });
      dispatch(actions.configurator.setListOfGroupsForTaskAssociations(copyOf));
    } else {
      if (selectedAssociation && selectedAssociation.taskGroupAssociations) {
        const newSelected = {
          ...selectedAssociation,
          taskGroupAssociations: selectedAssociation.taskGroupAssociations.map((el) => {
            if (el.idGroup === row.original.idGroup) {
              return {
                ...el,
                [accessor]: value,
              };
            }
            return el;
          }),
        };
        dispatch(actions.configurator.setSelectedDefinitionTaskGroupsAssociations(newSelected));
      }
    }
  };

  const columns: TableProps['columns'] = [
    {
      id: '1',
      accessor: 'definitionName',
      Header: t('definitionName'),
      inputType: 'select',
      readOnly: isActiveEditAssociation || isActiveAddTaskToAssociation,
      selectOptionsTypes: utils.input.buildOptions(listOfDefinitionsForAssociations || [], 'wfName', 'idDefinition'),
      Cell: 'withCounter',
    },
    {
      id: '2',
      accessor: 'taskName',
      Header: t('taskName'),
      inputType: 'select',
      selectOptionsTypes: utils.input.buildOptions(listOfTasksForAssociations || [], 'name', 'idTask'),
      Cell: ({ value }: { value: any }) => {
        return <>{value ? value : '-'}</>;
      },
    },
    {
      id: '3',
      accessor: 'escalation',
      Header: t('reminder_escalation'),
      inputType: 'checkbox',
      sortType: 'boolean',
      Cell: ({ value }: any) => {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox isActive={editedRowId === null && value} isEditable={!!editedRowId} />
          </div>
        );
      },
    },
    {
      id: '4',
      accessor: 'weight',
      Header: t('weight'),
      inputType: 'number',

      hasLocalKeys: ['LocalizableStringFe', 'workflowAssociationMain'],

    },
    {
      id: '5',
      accessor: 'idConvalidationAction',
      Header: t('convalidationAction'),
      inputType: 'select',
      selectOptionsTypes: utils.input.buildOptions(listOfConvalidation, 'name', 'idAction'),
      hasLocalKeys: ['LocalizableStringFe', 'workflowAssociation'],
    },
  ];

  const taskGroupsAssociationsColumns: TableProps['columns'] = [
    {
      accessor: 'assigned',
      Header: t('assigned'),
      sortType: 'boolean',

      Cell: ({ value, row }: any) => {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox
              isActive={value}
              isEditable={isActiveAddTaskToAssociation || isActiveCreateNewAssociation || isActiveEditAssociation}
              action={(value: boolean) => setCheckValue(value, row, 'assigned')}
            />
          </div>
        );
      },
    },
    {
      accessor: 'groupName',
      Header: t('groupName'),
      Cell: ({ value, row }: { value: any; row: any }) => {
        return (
          <CommonButton
            action={() => {
              dispatch(actions.configurator.setSelectedGroupUserAssociation(row.original));
              dispatch(
                actions.modal.openModal(modalActionType.configurator.GROUPS_USERS_ASSOCIATIONS_MODAL, {
                  rowNewAssociation,
                }),
              );
            }}
            scope="tertiary"
            value={value ? value : '-'}
          />
        );
      },
    },
    {
      accessor: 'relation',
      Header: t('relation'),
      Cell: ({ value, row }: any) => {
        return (
          <>
            {value !== null ? (
              <div style={{ display: 'flex', height: '100%', alignItems: 'center' }}>
                <MultiSwitch
                  elements={relationList}
                  value={value}
                  disabled={
                    (!isActiveEditAssociation && !isActiveAddTaskToAssociation && !isActiveCreateNewAssociation) ||
                    !row.values.assigned
                  }
                  onChange={(value) => setCheckValue(value, row, 'relation')}
                />
              </div>
            ) : (
              '-'
            )}
          </>
        );
      },
      hasLocalKeys: ['LocalizableStringFe', 'workflowAssociation'],
    },
    {
      accessor: 'amountLimit',
      Header: t('amountLimit'),
      width: 220,
      Cell: ({ value, row }: { value: any; row: any }) => {
        if (
          (isActiveAddTaskToAssociation || isActiveCreateNewAssociation || isActiveEditAssociation) &&
          row.values.assigned
        ) {
          return (
            <NumberInput
              width="165px"
              customInput={GridInput}
              value={value}
              saveAction={(val) => setCheckValue(val || 0, row, 'amountLimit')}
            />
          );
        }
        return <>{value}</>;
      },
    },
    {
      accessor: 'currency',
      Header: t('currency'),
      Cell: ({ value }: { value: any }) => {
        return <>{value ? value : '-'}</>;
      },
    },
  ];

  useEffect(() => {
    dispatch(actions.configurator.setSelectedDefinitionTaskGroupsAssociations(null));
  }, [selectedTab, dispatch]);

  // if is selected the id of definition, I update the API to returns the list of available tasks for associations
  // if it is not present the list will be empty
  useEffect(() => {
    if (idDefinitionSelected !== undefined) {
      revalidate();
    } else {
      mutate('getCompanyTasksForAssociationsKey', [], {
        revalidate: false,
      });
    }
  }, [idDefinitionSelected, revalidate, mutate]);

  const onCancel = async () => {
    // update the list of associations, delete the record with idDefinition === -1
    if (isActiveCreateNewAssociation) {
      const newRows = associations?.filter((el) => el.idDefinition !== -1);
      mutate('getDefinitionTaskGroupsAssociationsKey', newRows, {
        revalidate: false,
      });
    }

    // update the list of associations, delete the subrow with idTask === -2
    if (isActiveAddTaskToAssociation) {
      const newlist = associations?.map((el) => {
        const subRows = el.subRows.filter((subRow) => subRow.idTask !== -2);
        return {
          ...el,
          definitionTaskAssociations: el.definitionTaskAssociations.filter((def) => def.idTask !== -2),
          subRows: subRows.length === 1 ? [] : subRows,
        };
      });

      mutate('getDefinitionTaskGroupsAssociationsKey', newlist, {
        revalidate: false,
      });

      dispatch(actions.configurator.setIsActiveAddTaskToAssociation(false));
    }

    if (isActiveCreateNewAssociation) dispatch(actions.configurator.setIsActiveCreateNewAssociation(false));
    if (isActiveEditAssociation) dispatch(actions.configurator.setIsActiveEditAssociation(false));

    dispatch(actions.configurator.setSelectedIdDefFromAssociations(undefined));
    dispatch(actions.configurator.setSelectedIdTaskDefFromAssociations(undefined));
    // update the list of groups for new association
    dispatch(actions.configurator.setListOfGroupsForTaskAssociations(listOfGroupsForAssociations || []));

    dispatch(actions.configurator.setEditedRowIdWfAssocations(null));
    dispatch(actions.configurator.setSelectedDefinitionTaskGroupsAssociations(null));
  };

  const confirmCreate = async (row: OriginalRow) => {
    if (latestGroupsList.current.find((el) => el.assigned)) {
      const payloadApi: CreateAssociation = buildPayload(
        row,
        listOfDefinitionsForAssociations,
        listOfTasksForAssociations,
        listOfConvalidation,
        latestGroupsList,
      );

      const { data } = await services.addDefinitionTaskAssociations(payloadApi);
      utils.app.notify('success', t('wf-definition-task-association-created'));

      const payloadTable: WorkflowDefinitionTaskGroupsAssociations = {
        ...payloadApi,
        definitionTaskAssociations: [
          {
            idTaskDefinition: data[0],
            idTask: row.taskName,
            taskName: listOfTasksForAssociations?.find((el) => el.idTask === row.taskName)?.name || '',
            escalation: row.escalation,
            weight: row.weight,
            idConvalidationAction: row.idConvalidationAction,
            taskGroupAssociations: latestGroupsList.current.map((el) => {
              return {
                ...el,
                assigned: el.assigned === true ? true : false,
              };
            }),
          },
        ],
      };

      mutate(
        'getCompanyDefinitionForAssociationsKey',
        (listOfDefinitionsForAssociations || []).filter((def) => def.idDefinition !== row.definitionName),
        {
          revalidate: false,
        },
      );

      const newListAssociations = [payloadTable, ...(associations?.filter((el) => el.idDefinition !== -1) || [])];

      mutate('getDefinitionTaskGroupsAssociationsKey', transformedRows(newListAssociations), {
        revalidate: false,
      });

      setIsTableGroupsVisible(false);
      dispatch(actions.configurator.setEditedRowIdWfAssocations(null));
      dispatch(actions.configurator.setSelectedDefinitionTaskGroupsAssociations(null));
      dispatch(actions.configurator.setIsActiveCreateNewAssociation(false));
      dispatch(actions.configurator.setSelectedIdDefFromAssociations(undefined));
    } else {
      utils.app.notify('warning', t('select_group_for_task_association'));
    }
  };

  const confirmAddTaskAssociation = async (row: OriginalRow) => {
    if (latestGroupsList.current.find((el) => el.assigned)) {
      if (selectedAssociation && idDefinitionSelected) {
        const definitionFinded = associations?.find((el) => el.idDefinition === idDefinitionSelected);

        const payloadApi: CreateAssociation = buildPayload(
          row,
          listOfDefinitionsForAssociations,
          listOfTasksForAssociations,
          listOfConvalidation,
          latestGroupsList,
          idDefinitionSelected,
          selectedAssociation,
        );

        const { data } = await services.addDefinitionTaskAssociations(payloadApi);
        dispatch(actions.configurator.setIsActiveAddTaskToAssociation(false));
        const payloadTable: WorkflowDefinitionTaskGroupsAssociations = {
          ...payloadApi,
          definitionTaskAssociations: [
            {
              idTaskDefinition: data[0],
              idTask: row.taskName,
              taskName: listOfTasksForAssociations?.find((el) => el.idTask === row.taskName)?.name || '',
              escalation: row.escalation,
              weight: row.weight,
              idConvalidationAction: row.idConvalidationAction,
              taskGroupAssociations: latestGroupsList.current.map((el) => {
                return {
                  ...el,
                  assigned: el.assigned === true ? true : false,
                };
              }),
            },
            ...(definitionFinded?.definitionTaskAssociations.filter((el) => el.idTask !== -2) || []),
          ],
        };

        const newList = associations?.map((el) => {
          if (el.idDefinition === idDefinitionSelected) {
            return {
              ...payloadTable,
            };
          }
          return el;
        });
        setIsTableGroupsVisible(false);
        dispatch(actions.configurator.setEditedRowIdWfAssocations(null));
        dispatch(actions.configurator.setSelectedIdDefFromAssociations(undefined));

        mutate('getDefinitionTaskGroupsAssociationsKey', transformedRows(newList || []), {
          revalidate: false,
        });

        utils.app.notify('success', t('wf-task-association-added'));
      }
    } else {
      utils.app.notify('warning', t('select_group_for_task_association'));
    }
  };

  const confirmEditSubRow = async (row: OriginalRow) => {
    const selectedRow = latesSelectedAssociation.current;
    const findedRowListAssociation = associations?.find(
      (el) => !!el.definitionTaskAssociations.find((e) => e.idTaskDefinition === selectedRow?.idTaskDefinition),
    );

    if (selectedRow && findedRowListAssociation) {
      const definitionTaskAssociations = {
        escalation: row.escalation,
        idConvalidationAction: row.idConvalidationAction,
        idTask: typeof row.taskName === 'string' ? selectedRow.idTask : row.taskName,
        taskName:
          typeof row.taskName === 'string'
            ? row.taskName
            : listOfTasksForAssociations?.find((el) => el.idTask === row.taskName)?.name || '',
        weight: row.weight,
        taskGroupAssociations: selectedRow.taskGroupAssociations
          ? selectedRow.taskGroupAssociations.filter((el) => el.assigned)
          : [],
        convalidationDescription:
          listOfConvalidation.find((el) => el.idAction === row.idConvalidationAction)?.description || '',
        idTaskDefinition: selectedRow.idTaskDefinition,
        nextPosition: selectedRow.nextPosition,
        position: selectedRow.position,
      };

      if (selectedRow.taskGroupAssociations?.find((el) => el.assigned)) {
        const payloadApi: CreateAssociation = {
          idDefinition: findedRowListAssociation.idDefinition,
          definitionName: findedRowListAssociation.definitionName,
          definitionTaskAssociations: [definitionTaskAssociations],
        };

        await services.editDefinitionTaskAssociations(payloadApi);
        const payloadTable: WorkflowDefinitionTaskGroupsAssociations = {
          ...payloadApi,
          definitionTaskAssociations: findedRowListAssociation.definitionTaskAssociations?.map((task) => {
            if (task.idTask === selectedRow?.idTask) {
              return {
                ...definitionTaskAssociations,
                taskGroupAssociations:
                  selectedRow.taskGroupAssociations?.map((group) => {
                    return {
                      ...group,
                      assigned: group.assigned === true ? true : false,
                    };
                  }) || [],
              };
            }
            return task;
          }),
        };

        const newList = associations?.map((el) => {
          if (el.idDefinition === payloadTable?.idDefinition) {
            return {
              ...payloadTable,
            };
          }
          return el;
        });
        const transformedList = transformedRows(newList || []);
        mutate('getDefinitionTaskGroupsAssociationsKey', transformedList, {
          revalidate: false,
        });
        setIsTableGroupsVisible(false);
        dispatch(actions.configurator.setIsActiveEditAssociation(false));
        dispatch(actions.configurator.setEditedRowIdWfAssocations(null));
        dispatch(actions.configurator.setSelectedIdDefFromAssociations(undefined));
        dispatch(actions.configurator.setSelectedIdTaskDefFromAssociations(undefined));

        utils.app.notify('success', t('wf-task-association-edited'));
      } else {
        utils.app.notify('warning', t('select_group_for_task_association'));
      }
    }
  };

  const onMainTableChange = (row: OriginalRow, accessor: RowAccessor) => {
    // when the definition is changed we update the list of available tasks for create associations
    if (accessor === 'definitionName') {
      const currentAssociations = [...(associations || [])];
      const index = currentAssociations.findIndex((el) => el.id === editedRowId);

      const newRow = {
        ...currentAssociations[index],
        [accessor as string]: row[accessor as string],
      };

      currentAssociations[0] = newRow;
      mutate('getDefinitionTaskGroupsAssociationsKey', currentAssociations, {
        revalidate: false,
      });
      dispatch(actions.configurator.setSelectedIdDefFromAssociations(row.definitionName));
    }

    let isValid = false;

    if (isActiveAddTaskToAssociation && Object.keys(row).length !== 0 && row.taskName !== '') {
      setRowNewAssociation({
        idDefinition: null,
        definitionName: selectedAssociation?.definitionName,
        idTask: row.taskName,
      });
      isValid = true;
    }
    if (
      isActiveCreateNewAssociation &&
      Object.keys(row).length !== 0 &&
      row.definitionName !== '' &&
      row.taskName !== ''
    ) {
      setRowNewAssociation({
        idDefinition: row.definitionName,
        idTask: row.taskName,
      });
      isValid = true;
    }
    setIsTableGroupsVisible(isValid);
  };

  const saveConfirm = async (row: OriginalRow) => {
    try {
      if (isActiveAddTaskToAssociation) await confirmAddTaskAssociation(row);
      if (isActiveCreateNewAssociation) await confirmCreate(row);
      if (isActiveEditAssociation) await confirmEditSubRow(row);
    } catch (error) {
      if (axios.isAxiosError(error)) {
        utils.app.notify('fail', `${t('error')} ${error.response}`);
        return;
      }
      console.error(error);
    }
  };

  const oldRow = associations
    ?.find(
      (el) =>
        !!el.definitionTaskAssociations.find(
          (e) => e.idTaskDefinition === latesSelectedAssociation.current?.idTaskDefinition,
        ),
    )
    ?.definitionTaskAssociations.find(
      (el) => el.idTaskDefinition === latesSelectedAssociation.current?.idTaskDefinition,
      selectedAssociation,
    );

  const definitionTaskTable = React.useMemo(
    () => (
      <FirstTable>
        <CustomTitle>{t('definitions_tasks_associations')}</CustomTitle>
        <Table
          columns={columns}
          rows={associations || []}
          hasSelection
          hasPagination
          hasResize
          hasSort
          hasFilter
          useExpandibleRows
          onSelection={(rows: WorkflowDefinitionTaskGroupsAssociationsExtended[]) => {
            dispatch(actions.configurator.setSelectedDefinitionTaskGroupsAssociations(rows?.length ? rows[0] : null));
          }}
          isEditable
          editRowID={editedRowId?.toString()}
          initialSelection={editedRowId ? [editedRowId] : undefined}
          onCancel={() => onCancel()}
          onSave={(row) => saveConfirm(row)}
          onEditRowChange={(row: OriginalRow, accessor?: RowAccessor) =>
            isActiveAddTaskToAssociation || isActiveCreateNewAssociation ? onMainTableChange(row, accessor) : null
          }
          onSaveValidation={
            isActiveEditAssociation ? formValidationWhenSubRowEdited(t, oldRow, selectedAssociation) : formValidation
          }
          openRowId={openRow}
          hasToolbar
        />
      </FirstTable>
    ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [
      associations,
      editedRowId,
      listOfDefinitionsForAssociations,
      openRowIdTable,
      listOfTasksForAssociations,
      openRow,
      selectedAssociation,
      listOfConvalidation,
    ],
  );

  useEffect(() => {
    if (isActiveAddTaskToAssociation || isActiveCreateNewAssociation) setIsTableGroupsVisible(false);
  }, [isActiveCreateNewAssociation, isActiveAddTaskToAssociation]);

  const groupsTable =
    ((isActiveCreateNewAssociation || isActiveAddTaskToAssociation || isActiveEditAssociation) &&
      isTableGroupsVisible) ||
    (selectedAssociation && selectedAssociation.taskGroupAssociations?.length) ? (
      <div>
        <CustomTitle>{t('groups')}</CustomTitle>
        <Table
          hasToolbar
          columns={taskGroupsAssociationsColumns}
          rows={
            (isActiveCreateNewAssociation || isActiveAddTaskToAssociation || isActiveEditAssociation) &&
            isTableGroupsVisible
              ? listOfGroups
              : selectedAssociation
              ? selectedAssociation.taskGroupAssociations
                ? selectedAssociation.taskGroupAssociations
                : []
              : []
          }
          hasPagination
          hasResize
          hasSort
          hasFilter
        />
      </div>
    ) : null;

  return (
    <Wrapper>
      <WrapperTables>
        {definitionTaskTable}
        {groupsTable}
      </WrapperTables>
    </Wrapper>
  );
};
export default AssociationsTab;
