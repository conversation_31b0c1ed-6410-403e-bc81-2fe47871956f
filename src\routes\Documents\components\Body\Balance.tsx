import React from 'react';
import styled from 'styled-components/macro';
import selectors from 'state/selectors';
import utils from 'utils';
import intlHelper from 'utils/helpers/intl.helper';
import { useSelector } from 'react-redux';

const AmountBalanceContainer = styled.div`
  display: flex;
  margin-top: 5px;
`;

const AmountContainer = styled.div`
  border-right: 1px solid ${({ theme }) => theme.colorPalette.grey.grey7};
  display: flex;
  padding: 10px;
  > div {
    padding-right: 5px;
  }
  color: ${({ theme }) => (theme.colorPalette.grey.grey10)};
`;

const BalanceContainer = styled.div`
  display: flex;
  padding: 10px;
  > div {
    padding-right: 5px;
  }
  color: ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey10: 'inherit')};
`;

const AmountValue = styled.div`
  font-weight: bold;
`;

export const BalanceValue = styled.div<{ balance: number }>`
  color: ${({ balance, theme }) => (balance === 0 ? theme.colorPalette.green.medium : theme.colorPalette.red.dark)};
  font-weight: bold;
`;

interface Props {
  amountValue: number;
  balanceValue: number;
}

const Balance = (props: Props) => {
  const { amountValue, balanceValue } = props;
  const t = utils.intl.useTranslator();
  const userNumberPreferences = useSelector(selectors.user.getUserPreference);
  const decimalScale = useSelector(selectors.documents.selectDocumentDecimal);

  const amountValueFormatted = intlHelper.formatNumber(amountValue, decimalScale, userNumberPreferences.decimalSep);
  const balanceValueFormatted = intlHelper.formatNumber(balanceValue, decimalScale, userNumberPreferences.decimalSep);

  return (
    <AmountBalanceContainer>
      <AmountContainer>
        <div>{t('amount')}:</div>
        <AmountValue>{amountValueFormatted}</AmountValue>
      </AmountContainer>
      <BalanceContainer>
        <div>{t('balance')}:</div>
        <BalanceValue balance={balanceValue}>{balanceValueFormatted}</BalanceValue>
      </BalanceContainer>
    </AmountBalanceContainer>
  );
};

export default Balance;
