import React, { useEffect, useState } from 'react';
import styled from 'styled-components/macro';
import { useDispatch, useSelector } from 'react-redux';
import CommonButton from 'components/Buttons/CommonButton';
import Dropdown from 'components/Input/Dropdown';
import utils from 'utils';
import selectors from 'state/selectors';
import services from 'services';
import actions from 'state/actions';
import { Header } from 'components/Modal';
import { ButtonContainer } from 'components/Modal/DefaultModal';

const DropDownContainer = styled.div`
  display: flex;
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  padding: 15px;
  align-items: center;
  label {
    padding-right: 15px;
    font-size: ${({ theme }) => theme.fontSizePalette.xSmall};
  }
`;

interface Option {
  value: number;
  label: string;
}

const MassiveDocTypeChange = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();

  const [docTypes, setDocTypes] = useState<Option[]>([]);
  const [selectedDocType, setSelectedDocType] = useState<Option | null>(null);

  const selectedDocuments = useSelector(selectors.documents.selectAllActiveDocuments);
  const protocols = useSelector(selectors.documents.selectAllActiveDocumentsID);
  const idProgram = useSelector(selectors.app.getProgramCode);

  useEffect(() => {
    const fetchDocTypes = async () => {
      if (selectedDocuments.length > 0) {
        const idCompany = selectedDocuments[0].idCompany;
        try {
          const { data } = await services.getDocTypes(idCompany);
          const options = data.map((docType) => ({
            value: docType.idDocType,
            label: docType.docName,
          }));
          setDocTypes(options);
        } catch (error) {
          console.error('Error fetching doc types:', error);
          utils.app.notify('fail', t('error_fetching_doc_types'));
        }
      }
    };
    fetchDocTypes();
  }, [selectedDocuments, t]);

  const handleDocTypeChange = (opt: Option | null) => {
    setSelectedDocType(opt);
  };

  const handleConfirm = async () => {
    if (!selectedDocType) return;

    try {
      const { data } = await services.changeDocumentType({
        idDocType: selectedDocType.value,
        idCompany: selectedDocuments[0].idCompany,
        protocols,
        idProgram,
      });
      if (data.success) {
        utils.app.notify('success', t('doc_type_changed_correctly'));
        dispatch(
          actions.documents.updateTableRows({
            protocols,
            property: 'documentName',
            value: selectedDocType.label,
          }),
        );
        dispatch(actions.modal.closeModal());
        return;
      }
      dispatch(
        actions.documents.updateTableRows({
          protocols: protocols.filter((protocol) => !data.protocols.includes(protocol)),
          property: 'documentName',
          value: selectedDocType.label,
        }),
      );
      utils.app.notify('warning', t('doc_type_not_changed_correctly: ') + data.protocols.join(', '));
      dispatch(actions.modal.closeModal());
    } catch (error) {
      console.error('Error changing document type:', error);
      utils.app.notify('fail', t('error_changing_doc_type'));
    }
  };

  return (
    <>
      <Header title={t('change_doc_type_title')} subtitle={t('change_doc_type_subtitle')} />
      <DropDownContainer>
        <label>{t('document-type')}</label>
        <Dropdown value={selectedDocType} onChange={handleDocTypeChange} options={docTypes} name="document-type" />
      </DropDownContainer>
      <ButtonContainer>
        <CommonButton value={t('confirm')} disabled={!selectedDocType} action={handleConfirm} />
        <CommonButton value={t('cancel')} action={() => dispatch(actions.modal.closeModal())} />
      </ButtonContainer>
    </>
  );
};

export default MassiveDocTypeChange;
