// import AdvancedPdf from 'components/Pdf/AdvancedPdf';
import Pdf from 'components/Pdf/newPdf';
import { Formik } from 'formik';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector, shallowEqual } from 'react-redux';
import actions from 'state/actions';
import selectors from 'state/selectors';
import services from 'services';
import styled from 'styled-components/macro';
import DocHeader from '../components/DocumentDetails/DocumentHeader';
import Q1 from '../components/DocumentDetails/Q1';
import ToolBarDetails from '../components/Toolbar/Details';
import Body from '../components/Body';
import Modal from 'components/Modal';
import DetailModalsContent from '../components/ModalsContent/DetailModalsContent';
import { RootState } from 'models';
import { actionType, modalActionType } from 'utils/constants';
import utils from 'utils';
import { FilePosition, FilePositionTypes } from '../components/DocumentSearch/index.types';
import ResizableTwoRowsLayout from 'components/Layout/ResizableTwoRowsLayout';
import ResizableTwoColumnsLayout from 'components/Layout/ResizableTwoColumnLayout';

const RightColWrapper = styled.div`
  padding: 5px;
  width: 100%;
`;

const LeftColWrapper = styled.div``;

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const DocumentDetails = (props: {}) => {
  const dispatch = useDispatch();
  const initialValues = utils.hooks.useInitialValues();
  const saveAction = utils.hooks.useSaveAction();
  const rows = useSelector(selectors.documents.selectTableRows);
  const resetManualDirty = utils.hooks.useResetManualDirty();
  const protocol = useSelector(selectors.documents.selectActiveDocumentID);
  const idProgram = useSelector(selectors.app.getProgramCode);
  const documentFields = useSelector(selectors.documents.selectDocumentFields);
  const documentImage = useSelector(selectors.documents.selectDocumentImage);
  const documentImageId = useSelector(selectors.documents.selectDocumentImageId);
  const isArchived = useSelector(selectors.documents.selectIsTemplateArchived);
  const config = useSelector((store: RootState) => selectors.app.getConfig(store, actionType.Q3_LEFT), shallowEqual);
  const isModalVisible = useSelector(selectors.modal.getVisibility);
  const currentModal = useSelector(selectors.modal.getActionType);
  const isReadOnly = useSelector(selectors.documents.selectIsTemplateReadOnly);
  const isDocReadOnly = useSelector(selectors.documents.selectIsDocReadOnly);
  const [hasAvailableQ1, setHasAvailableQ1] = useState<boolean>(true);
  const q3Left = config.val === 1;
  const configAutofocus = useSelector((store: RootState) =>
    selectors.app.getConfig(store, actionType.AUTOFOCUS_Q3_FIELD),
  );
  const documentHeader = useSelector(selectors.documents.selectDocumentHeader);
  const [hasRegProtocol, setHasRegProtocol] = useState<boolean>(false);
  const isQ3Active = useSelector(selectors.documents.selectShowBody);
  const openInvoiceLogAuto = useSelector(selectors.user.getUserPreference).openInvoiceLogAutomatically;
  const templateActionsOpen = useSelector(selectors.documents.selectTemplateActionsOpen);
  const idTemplate = useSelector(selectors.documents.selectActiveTemplateId);

  useEffect(() => {
    if (documentFields?.length) {
      const defaultQ3Open = documentFields
        ?.map((doc) => doc.sectionFields)
        .reduce((result, current) => {
          return result.concat(current);
        })
        .find((field) => field.fixedName === configAutofocus?.val);

      const actualHMoveTypeQ3 = documentHeader?.actualHMoveType?.q3;

      if (defaultQ3Open?.fieldGroup && actualHMoveTypeQ3) {
        dispatch(actions.documents.setBodyColumns(defaultQ3Open.fieldGroup.fieldColumn));
        dispatch(actions.documents.setBalanceField(defaultQ3Open.fieldGroup.balanceField));
        dispatch(actions.documents.setHeaderNameQ3(defaultQ3Open.fieldGroup.name));
        dispatch(actions.documents.setBodyName(defaultQ3Open.fieldName));
        dispatch(actions.documents.setShowBody(true));
        dispatch(actions.documents.setFieldGroupId(defaultQ3Open.fieldGroup.idFieldGroup));
        dispatch(actions.documents.setPredictionsData(defaultQ3Open.fieldGroup.predictionsValues));
      }
    }
  }, [documentFields, configAutofocus, dispatch, documentHeader]);

  useEffect(() => {
    if (protocol) {
      const registrationProtocol = rows.find((ele) => ele.protocol === protocol)?.registrationProtocol;
      const barcodeLast = rows.find((ele) => ele.protocol === protocol)?.protocolIn.slice(-1);
      setHasRegProtocol(registrationProtocol || barcodeLast === 'S' ? true : false);
    }
  }, [protocol, rows]);

  useEffect(() => {
    if (protocol && idTemplate) {
      dispatch(actions.documents.setDocumentHeader(protocol, idProgram, idTemplate));
      dispatch(actions.documents.setDocumentFields(protocol, idProgram));
    }
  }, [idProgram, protocol, dispatch, idTemplate]);

  useEffect(() => {
    const isActionLogActive = templateActionsOpen?.find((el) => el === actionType.LOG);
    if (isActionLogActive && openInvoiceLogAuto) {
      dispatch(actions.modal.openModal(modalActionType.documents.LOG));
    }
  }, [openInvoiceLogAuto, templateActionsOpen, dispatch]);

  useEffect(() => {
    const updateDocumentImage = async (protocol: number) => {
      try {
        const filePosition = rows.find((ele) => ele.protocol === protocol)?.position as FilePosition;
        const { data } =
          isArchived || filePosition === FilePositionTypes.archived
            ? await services.getArchivedDocumentImage(protocol)
            : await services.getDocumentImage(protocol);
        dispatch(actions.documents.setDocumentImage(data));
        dispatch(actions.documents.setDocumentImageId(protocol));
      } catch (e) {
        dispatch(actions.documents.setDocumentImage(''));
        console.error(e);
      }
    };
    if (protocol !== documentImageId) {
      protocol && updateDocumentImage(protocol);
    }
  }, [documentImageId, isArchived, protocol, dispatch, rows]);

  const RightColumn = useMemo(
    () => (
      <RightColWrapper>
        <ResizableTwoRowsLayout
          upperRow={!q3Left ? <Body hasRegProtocol={hasRegProtocol} /> : null}
          bottomRow={<Pdf  base64={documentImage} showTranslateButtons />}
          isUpperRowActive={isQ3Active}
        />
      </RightColWrapper>
    ),
    [documentImage, q3Left, hasRegProtocol, isQ3Active],
  );

  const LeftColumn = useMemo(
    () => (
      <LeftColWrapper>
        {q3Left && <Body hasRegProtocol={hasRegProtocol} />}
        <Q1 isReadOnly={isReadOnly || hasRegProtocol || isDocReadOnly} />
      </LeftColWrapper>
    ),
    [q3Left, isReadOnly, hasRegProtocol, isDocReadOnly],
  );

  useEffect(() => {
    documentFields && setHasAvailableQ1(documentFields?.length !== 0);
  }, [documentFields]);

  return protocol && documentFields && initialValues ? (
    <Formik
      initialValues={initialValues}
      enableReinitialize
      validate={utils.documents.validate}
      onSubmit={(values) => {
        saveAction(values);
        resetManualDirty(values);
      }}
    >
      <>
        <ToolBarDetails />
        <DocHeader isReadOnly={isReadOnly || hasRegProtocol || isDocReadOnly} />
        {hasAvailableQ1 ? (
          <ResizableTwoColumnsLayout initialLeftWidth={40} leftColumn={LeftColumn} rightColumn={RightColumn} />
        ) : (
          RightColumn
        )}
        {currentModal !== 'userPassword' && currentModal !== 'userSettings' && (
          <Modal
            onClose={() => dispatch(actions.modal.closeModal())}
            open={isModalVisible}
            isDraggable={true}
            isClosable={currentModal !== modalActionType.documents.SHOW_SAP_DATA}
          >
            <DetailModalsContent />
          </Modal>
        )}
      </>
    </Formik>
  ) : null;
};

export default DocumentDetails;
