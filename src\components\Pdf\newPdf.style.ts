import styled from 'styled-components/macro';
const PageInput = styled.input`
  font-family: Roboto;
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  width: 25px;
  height: 17px;
  border-radius: 3px;
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey5 : theme.colorPalette.turquoise.background};
  border: none;
  outline: 0;
  margin-right: 3px;

  &:focus {
    outline: 1px solid ${({ theme }) => theme.colorPalette.grey.grey5};
  }
`;

const PdfButton = styled.div<{ disabled?: boolean }>`
  color: ${({ theme }) => theme.colorPalette.turquoise.normal};

  &:hover {
    cursor: pointer;
  }

  ${({ disabled }) =>
    disabled &&
    `
    opacity: 0.3;
    pointer-events: none;
  `}
`;

const Center = styled.div`
  display: flex;
  align-items: center;
  > * {
    margin-right: 10px;
  }
`;

const Div = styled.div`
  min-width: 500px;
`;
const Wrapper = styled.div<{ cursor?: string; fixedHeight?: string }>`
  width: 100%;
  height: ${({ fixedHeight }) => fixedHeight || '70vh'};
  border: 1px solid ${({ theme }) => theme.colorPalette.grey.grey5};
  overflow: auto;
  position: relative;
  cursor: ${({ cursor }) => cursor};
  text-align: center;
  background-color: ${({ theme }) => theme.colorPalette.grey.grey2};
`;
const Canvas = styled.canvas`
  display: block;
`;
const NumPages = styled.p`
  font-family: Roboto;
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  font-size: ${({ theme }) => theme.fontSizePalette.body.XS};
`;

const PdfToolbar = styled.div`
  width: 100%;
  padding: 10px 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid ${({ theme }) => theme.colorPalette.grey.grey5};
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.white};
  border-radius: 5px 5px 0 0;
  height: 50px;
  box-sizing: border-box;
  border: 1px solid ${({ theme }) => theme.colorPalette.grey.grey5};
  border-bottom: 0;
`;

const InputContainer = styled.div`
  margin: 0 10px;
  display: flex;
  align-items: center;
`;

const Left = styled.div`
  display: flex;
  align-items: center;
`;

const ContentDiv = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  color: transparent;
  pointer-events: none;
`;
const Right = styled.div`
  display: flex;
  align-items: center;

  > * {
    margin-left: 15px;
  }
`;
const VerticalBar = styled.div`
  background-color: ${({ theme }) => theme.colorPalette.grey.grey5};
  height: 26px;
  width: 1px;
  border-radius: 0.5px;
  margin-left: 10px;
`;

export {
  PdfButton,
  Center,
  NumPages,
  PdfToolbar,
  Canvas,
  InputContainer,
  Left,
  PageInput,
  ContentDiv,
  Wrapper,
  Right,
  VerticalBar,
  Div
};
