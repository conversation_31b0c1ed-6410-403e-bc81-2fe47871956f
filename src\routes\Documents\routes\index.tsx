import React, { useMemo } from 'react';
import { Route, Switch, useRouteMatch, useHistory } from 'react-router-dom';
import styled from 'styled-components/macro';
import withLock from 'components/HOC/withLock';
import withCheckID from 'components/HOC/withCheckID';
import DocumentDetails from './documentDetails';
import DocumentList from './documentList';
import { useSelector } from 'react-redux';
import selectors from 'state/selectors';

const Wrapper = styled.div`
  display: flex;
  flex-direction: column;
  padding-top: 0;
  position: relative;
  height: calc(100vh - 70px);
  overflow: hidden;
  background-color: ${({ theme }) => theme.colorPalette.white};
`;

const Routes = () => {
  const { path } = useRouteMatch();
  const readOnly = useSelector(selectors.documents.selectIsTemplateReadOnly);
  const history = useHistory();

  const DecoratedDocumentDetails = useMemo(() => {
    return !readOnly
      ? withCheckID(withLock(DocumentDetails, 'documents'), () => history.push(path))
      : withCheckID(DocumentDetails, () => history.push(path));
  }, [readOnly, history, path]);

  return (
    <>
      <Switch>
        <Wrapper>
          <Route exact path={path} component={DocumentList} />
          <Route path={`${path}/edit`} component={DecoratedDocumentDetails} />
        </Wrapper>
      </Switch>
    </>
  );
};

export default Routes;
