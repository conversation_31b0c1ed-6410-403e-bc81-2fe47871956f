import { FormTypesNotifications } from 'models/configurator';
import { GetTemplatesMailNotification, IWfReminderNotifications } from 'models/response';
import * as Yup from 'yup';
import { parserfrequency } from '../utils';

export interface FormValues {
  user: { label: string; value: number } | null;
  reminderSendMail: boolean;
  startDeltaReminder: number;
  endDeltaReminder: number;
  reminderDays: number;
  maxReminder: number;
  reminderMaxDocuments: number;
  reminderMaxDocumentDetails: number;
  idTemplateScheduledReminder: { label: string; value: number } | null;
  hour: { label: string; value: string } | null;
  minute: { label: string; value: number } | null;
  allDays: boolean;
  daysOfWeek: { label: string; value: number }[];
}

export const defaultValues: FormValues = {
  user: null,
  reminderSendMail: false,
  startDeltaReminder: 1,
  endDeltaReminder: 1,
  reminderDays: 1,
  maxReminder: 1,
  reminderMaxDocuments: 1,
  reminderMaxDocumentDetails: 1,
  idTemplateScheduledReminder: null,
  hour: null,
  minute: null,
  allDays: false,
  daysOfWeek: [],
};

export const validationLogic = (t: (key: string) => string) => {
  return Yup.object({
    user: Yup.object()
      .shape({
        label: Yup.string().required(t('required')),
        value: Yup.number().required(t('required')),
      })
      .required(t('required')),

    hour: Yup.object()
      .shape({
        label: Yup.string().required(t('required')),
        value: Yup.number().required(t('required')),
      })
      .required(t('required')),
    minute: Yup.object()
      .shape({
        label: Yup.string().required(t('required')),
        value: Yup.number().required(t('required')),
      })
      .required(t('required')),
    allDays: Yup.boolean(),
    daysOfWeek: Yup.array()
      .of(
        Yup.object().shape({
          label: Yup.string().required(),
          value: Yup.number().required(),
        }),
      )
      .test('valid-days', t('required_days_of_week'), function (daysOfWeek) {
        const { allDays } = this.parent;
        if (!allDays && (!daysOfWeek || daysOfWeek.length === 0)) {
          return this.createError({ path: 'daysOfWeek', message: t('required_days_of_week') });
        }
        return true;
      }),
  });
};

export const setInitialValues = (
  selectedRow: IWfReminderNotifications | null,
  formMode: FormTypesNotifications,
  t: (key: string) => string,
  templatesList: GetTemplatesMailNotification[],
): FormValues => {
  if (selectedRow && formMode === 'edit') {
    const {
      idUser,
      name,
      reminderSendMail,
      startDeltaReminder,
      endDeltaReminder,
      reminderDays,
      maxReminder,
      reminderMaxDocuments,
      reminderMaxDocumentDetails,
      idTemplateScheduledReminder,
      reminderFrequency,
    } = selectedRow;

    const { validHour, validMinute, allDays, daysOfWeek } = parserfrequency(reminderFrequency, t);

    const templateScheduledReminder =
      templatesList.find((el) => el.idMailTemplate === idTemplateScheduledReminder) ?? null;

    return {
      user: { value: idUser, label: name },
      reminderSendMail,
      startDeltaReminder,
      endDeltaReminder,
      reminderDays,
      maxReminder,
      reminderMaxDocuments,
      reminderMaxDocumentDetails,
      idTemplateScheduledReminder: templateScheduledReminder
        ? {
            value: templateScheduledReminder.idMailTemplate,
            label: `${templateScheduledReminder.idMailTemplate} - ${templateScheduledReminder.templateName}`,
          }
        : null,
      hour: validHour,
      minute: validMinute,
      allDays,
      daysOfWeek,
    };
  }

  return defaultValues;
};
