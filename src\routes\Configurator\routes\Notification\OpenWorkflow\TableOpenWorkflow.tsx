import Checkbox from 'components/Buttons/CheckboxButton';
import Table, { TableColumn } from 'components/Table';
import { IOpenWorkflowNotifications } from 'models/response';
import React, { useState } from 'react';
import utils from 'utils';
import CronCell from '../components/CronCell';
import cronstrue from 'cronstrue';
import { useDispatch, useSelector } from 'react-redux';
import selectors from 'state/selectors';
import actions from 'state/actions';

const TableOpenWorkflow = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const openWorkflowList = useSelector(selectors.configurator.getListOfOpenWfSettings);
  const formMode = useSelector(selectors.configurator.getFormModeOpenWf);

  const notificationColumns: TableColumn[] = [
    { accessor: 'idUser', Header: t('idUser'), filterType: 'free' },
    { accessor: 'name', Header: t('name'), filterType: 'free' },
    {
      accessor: 'openWfSendMail',
      Header: t('openWfSendMail'),
      sortType: 'boolean',
      Cell: ({ value }: any) => {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox isActive={value} isEditable={false} />
          </div>
        );
      },
    },
    {
      accessor: 'openWfFrequency',
      Header: t('openWfFrequency'),
      Cell: ({ value }: any) => {
        const [tooltip, setTooltip] = useState('');

        return (
          <div
            onMouseEnter={() =>
              setTooltip(cronstrue.toString(value, { verbose: true, locale: navigator.language.split('-')[0] || 'en' }))
            }
            onMouseLeave={() => setTooltip('')}
            title={tooltip}
          >
            <CronCell value={value} />
          </div>
        );
      },
    },
    { accessor: 'openWfMaxDocumentDetails', Header: t('openWfMaxDocumentDetails'), filterType: 'free' },
    { accessor: 'openWfMaxDocuments', Header: t('openWfMaxDocuments'), filterType: 'free' },
    {
      accessor: 'openWfSendNotificationImmediately',
      Header: t('openWfSendNotificationImmediately'),
      sortType: 'boolean',
      Cell: ({ value }: any) => {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox isActive={value} isEditable={false} />
          </div>
        );
      },
    },
    { accessor: 'idTemplateImmediateOpenWorkflow', Header: t('idTemplateImmediateOpenWorkflow'), filterType: 'free' },
    { accessor: 'idTemplateScheduledOpenWorkflow', Header: t('idTemplateScheduledOpenWorkflow'), filterType: 'free' },
  ];

  const onUserSelection = (rows: IOpenWorkflowNotifications[]) => {
    dispatch(actions.configurator.setSelectedOpenWfSettings(rows[0]));
  };

  return (
    <Table
      rowId="idUser"
      hasToolbar
      columns={notificationColumns}
      rows={openWorkflowList}
      hasSelection={!formMode}
      hasPagination
      hasResize
      hasSort
      hasFilter
      onSelection={(rows: IOpenWorkflowNotifications[]) => onUserSelection(rows)}
    />
  );
};
export default TableOpenWorkflow;
