import React, { useEffect, useState } from 'react';
import styled from 'styled-components/macro';

const Button = styled.span`
  content: '';
  position: absolute;
  top: 1.4px;
  left: 1.4px;
  width: 12px;
  height: 12px;
  border-radius: 20px;
  transition: 0.2s;
  background: #fff;
  box-shadow: 0 0 2px 0 rgba(10, 10, 10, 0.29);
`;

const Label = styled.label<{ isActive: boolean; disabled: boolean }>`
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};
  width: 30px;
  height: 15px;
  border-radius: 100px;
  position: relative;
  transition: background-color 0.2s;
  &:active ${Button} {
    width: 15px;
  }
  background: ${({ isActive, theme }) =>
    isActive ? theme.colorPalette.turquoise.normal : theme.colorPalette.grey.grey4};
  margin-right: 5px;
`;

const Input = styled.input`
  height: 0;
  width: 0;
  display: none;
  &:checked + ${Label} ${Button} {
    left: calc(100% - 1.4px);
    transform: translateX(-100%);
  }
`;

export interface ToggleProps {
  checked?: boolean;
  onChange?: Function;
  disabled?: boolean;
  tooltip?: string;
}

const Toggle = ({ checked = false, onChange, disabled = false, tooltip = 'Toggle' }: ToggleProps) => {
  const [isActive, setIsActive] = useState(checked);

  useEffect(() => {
    setIsActive(checked);
  }, [checked]);

  return (
    <>
      <Input
        checked={isActive}
        onChange={(e) => {
          if (!disabled) {
            onChange && onChange(e);
            setIsActive(!isActive);
          }
        }}
        className="react-switch-checkbox"
        id={'react-switch-new'}
        type="checkbox"
      />
      <Label disabled={disabled} isActive={isActive} className="react-switch-label" htmlFor={'react-switch-new'}>
        <Button title={tooltip} className={'react-switch-button'} />
      </Label>
    </>
  );
};

export default Toggle;
