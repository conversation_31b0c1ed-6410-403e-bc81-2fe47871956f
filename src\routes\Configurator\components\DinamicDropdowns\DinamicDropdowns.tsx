import React from 'react';
import { Switch, Route } from 'react-router-dom';

import { MapperDropdowns } from './MapperDropdowns';
import { TablesDropdowns } from './TablesDropdowns';
import { ActionTablesDropdowns } from './ActionTablesDropdowns';
import { ConfiguratorDropdowns } from './ConfiguratorDropdowns';
import { WorkflowDropDowns } from './WorkflowDropdowns';
import { VendorDropDowns } from './VendorDropdowns';
import NotificationsDropdowns from './NotificationsDropdowns';

export const routes = [
  {
    index: 1,
    path: '/configurator/MAPPER',
    Dropdowns: MapperDropdowns,
  },
  {
    index: 2,
    path: '/configurator/TABLES',
    Dropdowns: TablesDropdowns,
  },
  {
    index: 3,
    path: '/configurator/ACTIONS',
    Dropdowns: ActionTablesDropdowns,
  },
  {
    index: 4,
    path: '/configurator/WORKFLOW',
    Dropdowns: WorkflowDropDowns,
  },
  {
    index: 5,
    path: '/configurator/VMD',
    Dropdowns: VendorDropDowns,
  },
  {
    index: 6,
    path: '/configurator/NOTIFICATIONS',
    Dropdowns: NotificationsDropdowns,
  },
];

export const DynamicDropdowns = () => {
  return (
    <>
      <ConfiguratorDropdowns />
      <Switch>
        {routes.map((route) => (
          <Route key={route.index} path={route.path}>
            <route.Dropdowns />
          </Route>
        ))}
      </Switch>
    </>
  );
};
