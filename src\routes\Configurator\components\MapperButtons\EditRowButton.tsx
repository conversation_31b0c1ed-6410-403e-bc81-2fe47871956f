import React from 'react';
import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';
import actions from 'state/actions';
import { useSelector, useDispatch } from 'react-redux';
import { actionType } from 'utils/constants';

const EditRowButton = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const templateSelectedRowId = useSelector(selectors.configurator.selectMapperSelectedRowId);
  const itHasSubRows = useSelector(selectors.configurator.itHasSubRows);
  const rows = useSelector(selectors.configurator.selectMapperRows);
  const canEdit = !utils.user.isActionByCompanyActive('MapperSaveRows') || !templateSelectedRowId || itHasSubRows;
  const rowCurrentPage = rows.findIndex((r) => {
    if (r.id === templateSelectedRowId) return true;
    if (r.subRows) {
      return r.subRows.findIndex((sr: any) => sr.id === templateSelectedRowId) > -1;
    }
    return false;
  });

  const setEditedRow = () => {
    if (rowCurrentPage > -1) {
      dispatch(actions.configurator.setMapperEditRowId(templateSelectedRowId));
      return;
    }
    utils.app.notify('fail', t('RowNotFound'));
  };

  return (
    <CommonButton
      action={setEditedRow}
      scope="tertiary"
      value={t('EditRow')}
      disabled={canEdit || !utils.user.isActionByCompanyActive(actionType.MAPPER_EDIT_ROW)}
    />
  );
};
export default EditRowButton;
