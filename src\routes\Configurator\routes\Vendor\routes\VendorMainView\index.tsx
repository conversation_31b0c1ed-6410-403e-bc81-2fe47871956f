import React from 'react';
import Table, { OriginalRow, TableColumn } from 'components/Table';
import { useAsync } from 'react-use';
import { useDispatch, useSelector } from 'react-redux';
import { SearchSubject } from 'models/response';
import { Title, Wrapper } from 'routes/Configurator/styles';
import actions from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import SearchBar from './components/SearchBar';
import service from 'services';
import { convertDateValues, getInputType } from '../utils';
import utils from 'utils';
import Checkbox from 'components/Buttons/CheckboxButton';

const SubTitle = styled.div`
  color: ${({ theme }) => theme.colorPalette.grey.grey7};
  margin: 25px 0px;
`;

const VendorMainView = () => {
  const selectedCompany = useSelector(selectors.configurator.getSelectedCompanyVendorConfig);
  const subjectList = useSelector(selectors.configurator.getSubjectList);
  const appliedFilters = useSelector(selectors.configurator.getAppliedFilters);
  const editedVendorId = useSelector(selectors.configurator.getIdEditedVendorFromList);
  const selectedVendorId = useSelector(selectors.configurator.getSelectedVendorIdFromList);
  const subjectTableColumns = useSelector(selectors.configurator.getSubjectTableColumns);
  const { dateFormat, timeZone } = useSelector(selectors.user.getUserPreference);

  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  useAsync(async () => {
    if (!subjectTableColumns) {
      const { data: columns } = await service.getSubjectConfiguration();
      const columnsSorted = columns.sort((a, b) => a.position - b.position);

      const cell = (type: string) => {
        switch (type.toLowerCase()) {
          case 'date':
            return ({ value }: { value: number }) => utils.date.convertDate(value, timeZone ?? 'UTC');

          case 'boolean':
            return ({ value }: { value: any; row: any }) => (
              <div style={{ display: 'flex', height: '100%' }}>
                <Checkbox isActive={value} isEditable={false} />
              </div>
            );

          default:
            return ({ value, row: { depth } }: { value: any; row: any }) => (depth > 0 ? '-' : value);
        }
      };

      const columnsMapped = columnsSorted.map(({ size, property, columnHeader, type, hidden, readOnly, position }) => {
        return {
          Header: t(columnHeader),
          accessor: `${property}`,
          id: `${position}`,
          width: size,
          readOnly,
          inputType: getInputType(type),
          isVisible: !hidden,
          dateFormat: dateFormat,
          filterType: type === 'Date' ? ('date' as TableColumn['filterType']) : undefined,
          sortType: type === 'Date' ? utils.table.dateSort : undefined,
          filter: type === 'Date' ? utils.date.epochFilterFunction(timeZone, true) : undefined,
          Cell: cell(type),
        };
      });

      columnsMapped.length && dispatch(actions.configurator.setSubjectTableColumns(columnsMapped));
    }
  }, [subjectTableColumns]);

  const updateSelectedVendor = (editedRow: OriginalRow) => {
    const newRow = { ...(editedRow as SearchSubject) };
    if (selectedVendorId) {
      dispatch(actions.configurator.updateVendorTableRow({ newRow, idWSubject: selectedVendorId }));
      dispatch(actions.configurator.setIdEditedVendorFromList(null));
    }
  };

  return (
    <Wrapper>
      <Title>
        <h2>
          {(selectedCompany && selectedCompany.label ? selectedCompany.label + ' ' : '') +
            t('master_data_configurator')}
        </h2>
      </Title>
      <SubTitle>{t('select_vendor')}</SubTitle>
      <SubTitle>{t('vendor_configurator_description')}</SubTitle>
      <SearchBar />
      {subjectList.length ? (
        <Table
          rowId="idWSubject"
          hasSort
          columns={subjectTableColumns || []}
          rows={subjectList}
          hasFilter
          hasSelection
          hasPagination
          hasResize
          onSelection={(rows: SearchSubject[]) =>
            dispatch(actions.configurator.setSelectedVendorIdFromList(rows?.length ? rows[0].idWSubject : null))
          }
          isEditable
          editRowID={editedVendorId ? editedVendorId.toString() : undefined}
          onSave={async (editedRow) => {
            try {
              const editedRowMod = convertDateValues(editedRow, subjectTableColumns || [], timeZone);
              await service.saveSubjectAndDetails({ ...editedRowMod });
              updateSelectedVendor(editedRow);
              utils.app.notify('success', t('subject-saved'));
            } catch (err) {
              console.error(err);
            }
          }}
          onCancel={() => {
            dispatch(actions.configurator.setIdEditedVendorFromList(null));
          }}
          initialSelection={selectedVendorId ? [selectedVendorId] : undefined}
        />
      ) : appliedFilters.value ? (
        <div style={{ textAlign: 'center', margin: '100px 0px' }}>{t('no_result')}</div>
      ) : null}
    </Wrapper>
  );
};
export default VendorMainView;
