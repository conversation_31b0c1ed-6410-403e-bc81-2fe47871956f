import React, { useEffect, useState } from 'react';
import cronstrue from 'cronstrue';

const loadLocale = async (locale: string) => {
  try {
    await import(`cronstrue/locales/${locale}`);
  } catch (error) {
    console.error(error);
  }
};

const CronCell = ({ value }: { value: string }) => {
  const language = navigator.language.split('-')[0] || 'en';
  const [cronText, setCronText] = useState(cronstrue.toString(value, { verbose: true, locale: language }));

  useEffect(() => {
    const fetchTranslation = async () => {
      await loadLocale(language);
      await setCronText(cronstrue.toString(value, { verbose: true, locale: language }));
    };

    fetchTranslation();
  }, [value, language]);

  return <div style={{ display: 'flex', height: '100%' }}>{cronText}</div>;
};

export default CronCell;
