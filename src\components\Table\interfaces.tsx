import { UseTableRowProps, TableState, Column, Accessor, SortByFn } from 'react-table';
import { UserDate } from 'models/user';
import * as yup from 'yup';
import { SetStateAction } from 'react';
import { localizationString } from 'models/response';
import { prefixesNameList } from 'utils/constants';

export interface OriginalRow {
  [index: string]: any;
  id?: string | number;
  disabled?: boolean;
}

export type LocalKeys = [localizationString | undefined, prefixesNameList | undefined];
export interface TableColumn {
  Header: string;
  accessor: Column['accessor'];
  id?: Column['id'];
  isVisible?: boolean;
  disableResizing?: Column['disableResizing'];
  width?: number;
  minWidth?: number;
  maxWidth?: number;
  Cell?: Function | 'withCounter';
  className?: string;
  Filter?: Function;
  /**
   * used to custom the filter function
   */
  filter?: (rows: any[], columnIds: string[], filterValue: any) => any[];
  /**
   * used to value the type of the column filter input
   */
  filterType?: 'free' | 'select' | 'date' | 'none';
  dateFormat?: UserDate;
  /**
   * used to value the type of the editable cell
   */
  inputType?: 'number' | 'text' | 'date' | 'select' | 'checkbox';
  /**
   * used to value the possible options of type select editable cell
   */
  selectOptionsTypes?: { value: string | number; label: string | JSX.Element }[];
  /**
   * used to value the possible options of type select column filter input
   */
  selectFilterOptions?: { value: string | number; label: string | JSX.Element }[];
  /**
   * it is used to poulate dinamically the label of the multi filter
   */
  multiFilterOptions?: Map<string, string>;
  disableSortBy?: boolean;
  readOnly?: boolean;
  sortType?: SortByFn<{}> | 'boolean' | 'alphanumeric' | 'datetime' | 'basic' | 'string' | 'number';
  /**
   * used to find keys in o_local
   */
  hasLocalKeys?: LocalKeys;
  cellEditValueRequired?: boolean;
  columnHeader?: string;
  columnName?: string;
}

export interface TableProps
  extends ExpandableRowProps,
    SortProps,
    FilterProps,
    ResizeProps,
    EditableRowProps,
    ExportProps,
    PaginationProps,
    RowSelectionProps,
    StyledTableProps {
  id?: string;
  columns: TableColumn[];
  rows: OriginalRow[];
  onSelection?: Function;
  onUnmount?: (state: TableState, row?: any) => void;
  onRowsChange?: Function;
  onChange?: Function;
  fixedColumns?: number;
  hasToolbar?: boolean;
  hideHeader?: boolean;
}
interface FilterProps {
  hasFilter?: boolean;
  /**
   * @note this is used to set the filter value from outside the table
   * if the column has an id, it will be used to set the filter value, otherwise the accessor will be used
   */
  filters?: TableState['filters'];
  onFilterOrSelectionChange?: Function;
  resetFiltersDependency?: any;
}

interface StyledTableProps {
  light?: boolean;
  maxHeight?: number;
  toolBarWidth?: string;
}

interface ExpandableRowProps {
  useExpandibleRows?: boolean;
  paginateExpandedRows?: boolean;
  openRowId?: number | null;
}
interface SortProps {
  hasSort?: boolean;
  sortBy?: TableState['sortBy'];
  onSortingChange?: Function;
}

interface ResizeProps {
  hasResize?: boolean;
  onResizeEnd?: (columnId: string, columnWidth: number) => void;
}
export type RowAccessor = string | Accessor<{}> | (string & Accessor<{}>) | undefined;
interface EditableRowProps {
  isEditable?: boolean;
  onEditRowChange?: (row: OriginalRow, accessor?: RowAccessor) => void;
  editRowID?: string;
  editRow?: OriginalRow;
  onSaveValidation?: yup.ObjectSchema<object>;
  onSave?: (row: OriginalRow) => void;
  onCancel?: (row?: OriginalRow) => void;
}

interface ExportProps {
  hasExport?: boolean;
  specialColumns?: { [key: string]: ((row: any) => string) | null };
}

interface PaginationProps {
  hasPagination?: boolean;
  pageSize?: number;
  // @note this maybe do the same thing as externalPageIndex, so we could remove externalPageIndex
  pageIndex?: number;
  onPageSizeChange?: Function;
  /**
   * used to externally change page index,
   * we should use this when we want to change the page index from outside the table
   */
  externalPageIndex?: number;
  /**
   * used to externally change page index
   * this is used internally just to set external to undefined, so we can trigger the useEffect
   */
  setExternalPageIndex?: (pageIndex?: number) => void | SetStateAction<number | undefined>;
}

interface RowSelectionProps {
  hasSelection?: boolean;
  hasMultiSelection?: boolean;
  onSelection?: Function;
  rowId?: string;
  onRowClick?: (row: UseTableRowProps<{}>['original']) => void;
  onRowDoubleClick?: Function;
  initialSelection?: (number | string)[];
}
