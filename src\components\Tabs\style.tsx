import styled from 'styled-components/macro';

export const LeftArrow = styled.img<{ disabled: boolean }>`
  position: absolute;
  left: 0px;
  top: 10px;
  cursor: pointer;
  pointer-events: ${(props) => (props.disabled ? 'none' : 'auto')};
  color: ${({ disabled, theme }) => (disabled ? theme.colorPalette.grey.grey4 : theme.colorPalette.turquoise.normal)};
  font-family: monospace;
  font-weight: ${({ theme }) => theme.fontWeightPalette.bold};
`;

export const RightArrow = styled.img<{ disabled: boolean }>`
  position: absolute;
  right: 0px;
  top: 10px;
  cursor: pointer;
  pointer-events: ${({ disabled }) => (disabled ? 'none' : 'auto')};
  color: ${({ disabled, theme }) => (disabled ? theme.colorPalette.grey.grey4 : theme.colorPalette.turquoise.normal)};
  font-family: monospace;
  font-weight: ${({ theme }) => theme.fontWeightPalette.bold};
`;

export const Container = styled.div<{ hasBorder?: boolean }>`
  position: relative;
`;

export const TabList = styled.div<{ width?: string }>`
  display: flex;
  overflow: hidden;
  box-sizing: border-box;

  .tab {
    width: ${({ width }) => (width ? width : '100%')};
    button {
      font-family: Roboto;
      color: ${({ theme }) => theme.colorPalette.grey.grey9};
      font-size: ${({ theme }) => theme.fontSizePalette.xxSmall};
    }
    margin-right: 4px;
    margin-bottom: 5px;
    padding: 0 20px;
    border-radius: 5px 5px 0 0;
    border-bottom: 5px solid
      ${({ theme }) =>
        theme.colorPalette.isDarkMode ? theme.colorPalette.turquoise.normal : theme.colorPalette.turquoise.light};

    &.is-selected {
      button {
        color: ${({ theme }) =>
          theme.colorPalette.isDarkMode ? theme.colorPalette.turquoise.light : theme.colorPalette.turquoise.dark};
        font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
      }
      border-bottom: 1px solid ${({ theme }) => theme.colorPalette.white};
      background-color: ${({ theme }) => theme.colorPalette.white};
      border-bottom: 5px solid
        ${({ theme }) =>
          theme.colorPalette.isDarkMode ? theme.colorPalette.turquoise.light : theme.colorPalette.turquoise.normal};
      border-radius: 0;
    }

    button {
      cursor: pointer;
      width: 100%;
      background: transparent;
      border: none;
      &:focus {
        outline: none;
        border: none;
      }
    }
  }
`;

export const TabPanels = styled.div<{ maxHeight?: string; hasBorder?: boolean; padding?: string; minWidth?: string }>`
  ${({ minWidth }) => minWidth && `min-width:${minWidth}`};
  border: ${({ theme, hasBorder }) => (hasBorder ? `1px solid ${theme.colorPalette.grey.mediumDark}` : '')};
  border-radius: 5px;
  padding: ${({ padding }) => padding || '10px 5px'};
  margin-top: -1px;
  width: 100%;
  overflow-y: auto;
  max-height: ${(props) => props.maxHeight};
  overflow-x: hidden;
`;
