import { UserCompany } from 'models/response';
import services from 'services';
import selectors from 'state/selectors';
import store from 'state/store';
import { ACTIONTYPE, actionType, modalActionType, WFTYPE_INFO, WFTYPE_INTEGR } from 'utils/constants';
import { Row } from 'models/table';

const isUserLoggedIn = async () => {
  try {
    const { status, data } = await services.checkLogin();
    if (status === 200) return data;
    return false;
  } catch (e) {
    console.error(e);
    return false;
  }
};

const getUserPlaceHolder = (input: string) => {
  if (input) {
    const convert = input.split(' ');
    if (convert.length === 1) {
      return convert[0].substring(0, 1);
    } else {
      const firstLetter = convert[0].substring(0, 1);
      const secondLetter = convert[1].substring(0, 1);
      return firstLetter.concat(secondLetter);
    }
  }
  return '';
};

const getCompany = (records: Row[]) => records[0]['companyName'] || records[0]['company'];

const getConfig = (type: string, company?: string) => {
  try {
    const config = selectors.app.getConfig(store.getState(), type);
    const { isForCompany, val, CompanyConf } = config;
    return (!isForCompany || !company ? val : CompanyConf[company]) === 1;
  } catch (error) {
    console.warn(`type ${type} not found`);
    return false;
  }
};
/**
 * this fucntion return a boolean to allow an user to do a specific action by a company
 * when no company is valued a DEFAULT one is used
 * @param {string} action
 * @param {string} companyName
 * @return {boolean}
 */
const isActionByCompanyActive = (action: string, companyName?: string) => {
  try {
    return selectors.app.isActionByCompanyActive(store?.getState(), companyName ?? 'DEFAULT', action);
  } catch (error) {
    console.error(error);
    console.error(`Action type ${action} not found`);
    return false;
  }
};

const isActionEnabled = (records: any[], type: string, notCheckOnCompany?: boolean): boolean => {
  // azioni non dipendenti da selezione
  switch (type) {
    case actionType.USER:
    case actionType.USERGROUP: {
      const userCompanies = selectors.app.getCompanies(store.getState());
      return userCompanies.some(
        (company: UserCompany) => company.actions.find((action) => action.type === 'OTHER_USERS_DOCUMENTS')?.active,
      );
    }
    case actionType.COMPANY_DOC_TYPE: {
      if (records.length) {
        const company = records[0].idCompany;
        return records.some((ele) => ele.idCompany !== company);
      }
    }
  }
  if (!records.length) return false;

  const companyName = getCompany(records);
  // capire se è meglio spostare nei case queste costanti
  const HAVE_INFO = records.some(({ wfType }) => wfType === WFTYPE_INFO);
  const IS_REFERENTE = records.every(({ funzione }) => funzione === 1);
  const IS_INTEGRATIVO = records.every(({ wfType }) => wfType === WFTYPE_INTEGR);
  const IS_SAME_COMPANY = records.every(({ idCompany }) => idCompany === records[0]['idCompany']);
  const IS_SAME_DOCTYPE = records.every(({ idDocType }) => idDocType === records[0]['idDocType']);
  const IS_SAME_CLUSTER = records.every(({ codCluster }) => codCluster === records[0]['codCluster']);
  const IS_SAME_CAUSALE = records.every(({ causale }) => causale === records[0]['causale']);

  const LUCY_BATCH_PRIV = IS_SAME_COMPANY
    ? isActionByCompanyActive('ENABLE_LUCYBATCH_PRIV', companyName) ||
      records.some((record) => record.username !== 'lucybatch')
    : false;

  let esito = true;
  switch (type) {
    case actionType.MASSIVE_DOC_TYPE_CHANGE:
      return records.length > 0 && records.every((doc) => doc.idCompany === records[0].idCompany);
    case actionType.REJECT:
    case actionType.MULTIPLE_REJECT:
    case actionType.FORZACONVALIDA:
      esito = records.length > 0;
      break;
    case actionType.CHECK_DUPLICATE_DOCUMENT:
    case actionType.APPLY_VAL_LOGICS:
    case actionType.DOWNLOAD_JIES:
    case actionType.USERGROUP:
    case actionType.CONNECT_JIES:
      esito = true;
      break;
    case actionType.SPLIT_FIX:
    case actionType.CHANGE_PDF_DATE:
    case actionType.RETURN_TO_BATCH:
    case actionType.CONFIRM:
    case actionType.DOWNLOAD_PDF:
      esito = records.length >= 1;
      break;
    case actionType.CONVALIDA:
      if (records.length === 1) esito = true;
      if (IS_REFERENTE) {
        esito = !HAVE_INFO && IS_SAME_COMPANY && IS_SAME_CLUSTER && IS_SAME_CAUSALE;
      }
      esito = true;
      break;
    case actionType.DOCINFO:
    case actionType.PARK:
    case actionType.CLOSEPARK:
    case actionType.ASK_INFO:
    case actionType.RESPONSE_INFO:
    case actionType.CLOSE_LITIGATION:
    case actionType.SHOW_Q3:
    case actionType.ALLEGA:
    case actionType.SENDMAIL:
    case actionType.LOG:
    case actionType.DOCUMENT_HISTORY:
    case actionType.REVERSE_REPOST:
    case actionType.UNLOCK_DOCUMENT:
    case actionType.LITIGATION_MAIL:
    case actionType.LITIGATION_PARK_MAIL:
      esito = records.length === 1;
      break;
    case actionType.REJECT_PREVIOUS:
      esito = records.length === 1 && !HAVE_INFO;
      break;
    case actionType.SENDLEVEL: {
      const SENDLEVEL_VAL = getConfig(type, companyName);
      if (records.length === 1) {
        return !HAVE_INFO && SENDLEVEL_VAL;
      }
      return !HAVE_INFO && IS_SAME_COMPANY && IS_SAME_CLUSTER && IS_SAME_CAUSALE && SENDLEVEL_VAL;
    }
    /*
      NO_MORE_WF -> true se property senderType ==4, false in tutti gli altri casi
      HAVE_INFO -> true se uno dei documenti selezionati è un INFO
      (NO_MORE_WF ? true : !HAVE_INFO) && (/getConfiguration(SENDDIRECT) x COMPANY == 1)
    */
    case actionType.DIRECT:
      if (records.length === 1) {
        const NO_MORE_WF = records[0]['senderType'] === 4;
        const DIRECT_VAL = getConfig(type, companyName);
        esito = (NO_MORE_WF || !HAVE_INFO) && DIRECT_VAL;
      }
      esito = false;
      break;
    case actionType.VALIDA_REFERENTE: {
      const VALIDA_REFERENTE_VAL = getConfig(type, companyName);
      esito = !HAVE_INFO && VALIDA_REFERENTE_VAL && IS_REFERENTE && IS_INTEGRATIVO;
      break;
    }

    case actionType.OPEN_WORKFLOW:
    case actionType.LITIGATION:
    case actionType.LITIGATION_PRINT:
    case actionType.LITIGATION_ARCHIVE:
    case actionType.VALIDATE:
    case modalActionType.documents.SELF_VALIDATE:
      esito = IS_SAME_COMPANY && records.length > 0;
      break;
    case actionType.DELETE_DOCUMENT:
    case actionType.ASSIGNDOCUMENT:
      esito = IS_SAME_COMPANY && LUCY_BATCH_PRIV;
      break;
    case actionType.AUTONOMOUSLY_VALIDATE:
    case actionType.REASSIGN_PRIORITY:
    case actionType.NOTES:
      esito = IS_SAME_COMPANY;
      break;
    case actionType.ACCOUNTANT_REJECT:
      esito =
        IS_SAME_COMPANY &&
        records.every((record) => record.subjectId === records[0].subjectId) &&
        (isActionByCompanyActive('rdc_showComunicazioneDaMail', companyName) ||
          isActionByCompanyActive('rdc_showArchivia', companyName) ||
          isActionByCompanyActive('rdc_showStampaDaInviare', companyName));
      break;
    case 'SetSubject':
      esito = records.length > 0 && IS_SAME_COMPANY && IS_SAME_DOCTYPE;
      break;
    case actionType.ASSIGN_DOCUMENT_LATENCY:
      esito = IS_SAME_COMPANY;
      break;
    default: {
      console.warn(`Action ${type} not found, default case used`);
      return records.length > 0;
    }
  }
  if (!notCheckOnCompany) {
    return esito && isActionByCompanyActive(type, companyName);
  } else {
    return esito;
  }
};

const isAllActionsEnabled = (records: any[], types: string[]) => {
  const companyName = records.length ? getCompany(records) : undefined;
  let result = false;

  for (let i = 0; i < types.length; i++) {
    if (isActionByCompanyActive(types[i], companyName)) {
      result = true;
      break;
    }
  }

  return result;
};

const actionNeedToCheck = (actionTypes: ACTIONTYPE) => {
  const userCompanies = selectors.app.getCompanies(store.getState());
  const protocolsSelected = selectors.documents.selectAllProtocolsOfRowSelected(store.getState());

  const getAllCompaniesSelected = protocolsSelected.filter((protocol) => {
    const company = userCompanies.find((company) => company.idCompany === protocol.idCompany);
    return company?.actions.find((action) => action.type === actionTypes)?.checkBeforeAction;
  });
  return getAllCompaniesSelected;
};

export default {
  isActionByCompanyActive,
  getConfig,
  isActionEnabled,
  isUserLoggedIn,
  getUserPlaceHolder,
  isAllActionsEnabled,
  actionNeedToCheck,
};
