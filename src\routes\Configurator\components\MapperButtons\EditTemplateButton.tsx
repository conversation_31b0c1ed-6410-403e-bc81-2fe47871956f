import React from 'react';
import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';
import actions from 'state/actions';
import { useSelector, useDispatch } from 'react-redux';
import { actionType } from 'utils/constants';

const EditTemplateButton = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const activeDocTypeId = useSelector(selectors.configurator.selectMapperIdDocType);
  const templateId = useSelector(selectors.configurator.selectActiveOTemplateId);
  const onEditTemplate = async () => {
    dispatch(actions.modal.setModal({ actionType: actionType.EDIT_TEMPLATE, props: { templateId, activeDocTypeId } }));
  };
  return (
    <CommonButton
      action={onEditTemplate}
      scope="tertiary"
      value={t('EditTemplate')}
      disabled={templateId === null || !utils.user.isActionByCompanyActive(actionType.EDIT_TEMPLATE)}
      icon="circle"
    />
  );
};
export default EditTemplateButton;
