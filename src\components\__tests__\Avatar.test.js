import React from 'react';
import Avatar from '../avatar';
import '@testing-library/jest-dom';
import { fireEvent } from '@testing-library/react';
import { renderWithStyle, createSnapshot, snapshotConfig } from 'utils/helpers/test.helpers';
import { toMatchImageSnapshot } from 'jest-image-snapshot';

expect.extend({ toMatchImageSnapshot });

describe('Avatar', () => {
  test('Avatar check onClick', () => {
    const onClick = jest.fn();
    renderWithStyle(<Avatar id="test" onClick={onClick} size={100} />);

    const avatar = document.getElementById('test');

    fireEvent.click(avatar);
    expect(onClick).toHaveBeenCalledTimes(1);
  });

  test('Avatar size check', () => {
    renderWithStyle(<Avatar id="test" size={100} />);

    const avatar = document.querySelector('#test');
    const styles = getComputedStyle(avatar);

    expect(styles.width).toBe('100px');
  });

  test('matches snapshot', async () => {
    jest.setTimeout(30000);
    const image = await createSnapshot(<Avatar userName="Pippo Baudo" size={100} />, { width: 100, heigth: 100 });
    expect(image).toMatchImageSnapshot(snapshotConfig);
  });
});
