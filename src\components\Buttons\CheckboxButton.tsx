import React, { useEffect, useState } from 'react';

import { statusColors } from 'utils/styleConstants';
import styled from 'styled-components/macro';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faCheck } from '@fortawesome/free-solid-svg-icons';
import { useTheme } from 'providers/ThemeProvider';

const StyledCheckbox = styled.input<React.ComponentProps<'input'>>`
  position: absolute;
  top: -5px;
  left: -5px;
  width: 20px;
  height: 20px;
  opacity: 0;
`;

const CheckContainer = styled.div<{
  isChecked: boolean;
  disabled: boolean;
  isEditable: boolean;
  color: string | undefined;
}>`
  transition: all 150ms;
  cursor: ${({ isEditable }) => (isEditable ? 'pointer' : 'not-allowed')};
  min-width: 18px;
  height: 18px;

  background-color: ${({ disabled, theme: { colorPalette } }) =>
    disabled ? colorPalette.grey.grey1 : colorPalette.isDarkMode ? colorPalette.grey.grey6 : colorPalette.white};

  border: ${(props) =>
    props.isChecked
      ? `1px solid ${props.theme.colorPalette.turquoise.normal}`
      : `1px solid ${props.theme.colorPalette.grey.grey5}`};

  border: ${({ isChecked, disabled, theme: { colorPalette } }) =>
    isChecked && disabled && `1px solid ${colorPalette.grey.grey5}`};

  border-radius: 3px;
  border: ${(props) => props.color && `1px solid ${props.color}`};

  &:focus-within {
    border-color: ${({ color, isEditable, theme }) =>
      !color && isEditable ? theme.colorPalette.turquoise.normal : theme.colorPalette.grey.grey5};
    outline: none;
    box-shadow: 1px 1px 5px 0 ${({ theme }) => theme.colorPalette.turquoise.normal};
  }

  ${({ isChecked, theme, color, disabled }) =>
    isChecked &&
    !disabled &&
    `border-color: ${!color ? theme.colorPalette.turquoise.normal : color};
     outline: none;`}

  > input {
    cursor: ${({ isEditable }) => (isEditable ? 'pointer' : 'not-allowed')};
  }
`;

const CheckWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  width: max-content;
`;

const CheckLabel = styled.div<{ hasLabel: boolean; isEditable: boolean }>`
  opacity: ${({ isEditable }) => (isEditable ? '1' : '0.4')};
  display: ${(props) => (props.hasLabel ? 'block' : 'none')};
  margin-left: 10px;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XS};
  color: ${({ theme }) => theme.colorPalette.grey.grey10};
`;

const FeedbackText = styled.p<{
  feedbackColor: string | undefined;
  displayText?: string;
  textPositioning?: string;
}>`
  font-family: Roboto;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  color: ${({ feedbackColor }) => (feedbackColor ? feedbackColor : statusColors.error)} !important;

  margin: 1px 0 0 1px;
  position: absolute;
  text-wrap: nowrap;
  ${({ displayText }) => displayText && `display: ${displayText}`};
  ${({ textPositioning }) => textPositioning && `position: ${textPositioning}`};

  text-align: left;
  left: -1px;
  bottom: -13px;
`;

export interface ICheckboxProps {
  name?: string;
  label?: string;
  isActive?: boolean;
  action?: (isActive: boolean, e?: React.MouseEvent<HTMLInputElement, MouseEvent>) => void;
  isEditable?: boolean;
  style?: object;
  statusColor?: string;
  feedbackMessage?: string;
  wrapperStyle?: object;
  id?: string;
}

const Checkbox = (props: ICheckboxProps) => {
  const {
    name,
    label,
    action,
    isActive = false,
    isEditable = true,
    style,
    statusColor,
    feedbackMessage = '',
    wrapperStyle,
    id,
  } = props;
  const [checked, setChecked] = useState(isActive);

  useEffect(() => {
    setChecked(isActive);
  }, [isActive]);
  const { theme } = useTheme();
  const actionEvent = (e: React.MouseEvent<HTMLInputElement, MouseEvent>) => {
    action && action(!checked, e);
    setChecked(!checked);
  };

  return (
    <CheckWrapper style={wrapperStyle}>
      <CheckContainer
        color={statusColor}
        isEditable={isEditable}
        onClick={(e: React.MouseEvent<HTMLInputElement, MouseEvent>) => {
          isEditable ? actionEvent(e) : e.stopPropagation();
        }}
        isChecked={checked}
        disabled={!isEditable}
      >
        {checked && (
          <FontAwesomeIcon
            style={{
              width: '16px',
              height: '16px',
              marginBottom: 3,
              marginLeft: 1,
              color: isEditable ? theme.colorPalette.turquoise.normal : theme.colorPalette.grey.grey5,
            }}
            icon={faCheck}
          />
        )}
        <StyledCheckbox
          id={id}
          style={style}
          tabIndex={!isEditable ? -1 : 0}
          readOnly={!isEditable}
          type="checkbox"
          name={name}
          value={name}
          checked={checked}
          disabled={!isEditable}
          onChange={() => null}
        />
      </CheckContainer>
      <CheckLabel isEditable={isEditable} hasLabel={Boolean(label)}>
        {label}
      </CheckLabel>
      {feedbackMessage.length > 0 && statusColor && (
        <FeedbackText feedbackColor={statusColor}>{feedbackMessage}</FeedbackText>
      )}
    </CheckWrapper>
  );
};

export default Checkbox;
