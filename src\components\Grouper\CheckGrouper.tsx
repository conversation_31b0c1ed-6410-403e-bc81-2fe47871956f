import React, { useEffect, useState } from 'react';
import styled from 'styled-components/macro';

import Checkbox from 'components/Buttons/CheckboxButton';

import { ReactComponent as arrow } from 'images/arrow-right.svg';

export interface CheckGrouperProps {
  isOpen?: boolean;
  children: React.ReactNode;
  title: string;
  customMargin?: string;
  selectable?: boolean;
  isSelected?: boolean;
  onChange?: (isActive: boolean) => void;
  isEditable?: boolean;
  tooltip?: string;
}

//  #region Styled components
const Container = styled.div<{ isOpen: boolean; customMargin?: string }>`
  overflow: ${({ isOpen }) => (isOpen ? 'auto' : '')};
  margin: ${({ customMargin }) => (customMargin ? customMargin : '')};
  position: relative;
  background: white;
  padding-right: 10px;
`;
const Arrow = styled(arrow)<{ isOpen: boolean }>`
  cursor: pointer;
  width: 15px;
  height: 15px;
  padding: 2px;
  transform: ${({ isOpen }) => (isOpen ? 'rotate(90deg)' : 'rotate(0deg)')};
  g {
    fill: ${({ isOpen }) => (isOpen ? '#919191' : '#4D4D4D')};
    stroke: transparent;
  }
`;
const Title = styled.div`
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
`;
const TitleContainer = styled.div`
  display: flex;
  align-items: center;
  font-size: ${({ theme }) => theme.fontSizePalette.body.S};
  font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  letter-spacing: -0.44px;
  line-height: 16px;
  & > * {
    margin-left: 7px;
  }
`;
const ChildrenContainer = styled.div`
  margin: 8px 0 0 10px;
`;
//  #endregion

const CheckGrouper = ({
  isOpen = false,
  isEditable = true,
  children,
  title,
  customMargin,
  selectable,
  isSelected = false,
  onChange,
  tooltip = '',
}: CheckGrouperProps) => {
  const [open, setOpen] = useState<boolean>(isOpen);
  const [selected, setSelected] = useState<boolean>(isSelected);

  const handleChange = (isActive: boolean) => {
    if (selectable) {
      setSelected(isActive);
      onChange && onChange(isActive);
    }
  };

  useEffect(() => {
    setSelected(isSelected);
  }, [isSelected]);

  return (
    <Container isOpen={open} customMargin={customMargin} title={tooltip}>
      <TitleContainer>
        <Arrow onClick={() => setOpen(!open)} isOpen={open} />
        {selectable ? (
          <Checkbox name={title} isActive={selected} action={handleChange} isEditable={isEditable} />
        ) : null}
        <Title>{title}</Title>
      </TitleContainer>
      <ChildrenContainer>{open && children}</ChildrenContainer>
    </Container>
  );
};

export default CheckGrouper;
