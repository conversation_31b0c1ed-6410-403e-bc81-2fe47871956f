import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import utils from 'utils';
import { actionType } from 'utils/constants';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';
import actions from 'state/actions';

const EditOverdueNotification = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const selectedRow = useSelector(selectors.configurator.getSelectedOverdueSettings);
  const formMode = useSelector(selectors.configurator.getFormModeOverdue);

  return (
    <CommonButton
      action={() => dispatch(actions.configurator.setFormModeOverdue('edit'))}
      scope="tertiary"
      value={t(actionType.EDIT_OVERDUE_NOTIFICATION)}
      icon="circle"
      disabled={!utils.user.isActionByCompanyActive(actionType.EDIT_OVERDUE_NOTIFICATION) || !selectedRow || !!formMode}
    />
  );
};

export default EditOverdueNotification;
