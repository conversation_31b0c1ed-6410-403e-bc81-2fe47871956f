import { FormTypesNotifications } from 'models/configurator';
import { GetTemplatesMailNotification, IOverdueNotifications } from 'models/response';
import * as Yup from 'yup';
import { parserfrequency } from '../utils';

export interface FormValues {
  user: { label: string; value: number } | null;
  overDueSendMail: boolean;
  overDueMaxDocuments: number;
  idTemplateScheduledOverdue: { label: string; value: number } | null;
  hour: { label: string; value: string } | null;
  minute: { label: string; value: number } | null;
  allDays: boolean;
  daysOfWeek: { label: string; value: number }[];
}

export const defaultValues: FormValues = {
  user: null,
  overDueSendMail: false,
  overDueMaxDocuments: 1,
  idTemplateScheduledOverdue: null,
  hour: null,
  minute: null,
  allDays: false,
  daysOfWeek: [],
};

export const validationLogic = (t: (key: string) => string) => {
  return Yup.object({
    user: Yup.object()
      .shape({
        label: Yup.string().required(t('required')),
        value: Yup.number().required(t('required')),
      })
      .required(t('required')),
    idTemplateScheduledOverdue: Yup.object()
      .shape({
        label: Yup.string().required(t('required')),
        value: Yup.number().required(t('required')),
      })
      .required(t('required')),
    hour: Yup.object()
      .shape({
        label: Yup.string().required(t('required')),
        value: Yup.number().required(t('required')),
      })
      .required(t('required')),
    minute: Yup.object()
      .shape({
        label: Yup.string().required(t('required')),
        value: Yup.number().required(t('required')),
      })
      .required(t('required')),
    allDays: Yup.boolean(),
    daysOfWeek: Yup.array()
      .of(
        Yup.object().shape({
          label: Yup.string().required(),
          value: Yup.number().required(),
        }),
      )
      .test('valid-days', t('required_days_of_week'), function (daysOfWeek) {
        const { allDays } = this.parent;
        if (!allDays && (!daysOfWeek || daysOfWeek.length === 0)) {
          return this.createError({ path: 'daysOfWeek', message: t('required_days_of_week') });
        }
        return true;
      }),
  });
};
export const setInitialValues = (
  selectedRow: IOverdueNotifications | null,
  formMode: FormTypesNotifications,
  t: (key: string) => string,
  templatesList: GetTemplatesMailNotification[],
): FormValues => {
  if (selectedRow && formMode === 'edit') {
    const { idUser, name, overDueMaxDocuments, overDueSendMail, overDueFrequency, idTemplateScheduledOverdue } =
      selectedRow;

    const { validHour, validMinute, allDays, daysOfWeek } = parserfrequency(overDueFrequency, t);

    const templateScheduled = templatesList.find((el) => el.idMailTemplate === idTemplateScheduledOverdue) ?? null;

    return {
      user: { value: idUser, label: name },
      overDueSendMail,
      overDueMaxDocuments,
      idTemplateScheduledOverdue: templateScheduled
        ? {
            value: templateScheduled.idMailTemplate,
            label: `${templateScheduled.idMailTemplate} - ${templateScheduled.templateName}`,
          }
        : null,
      hour: validHour,
      minute: validMinute,
      allDays,
      daysOfWeek,
    };
  }

  return defaultValues;
};
