import React from 'react';
import { Meta, Story } from '@storybook/react';

import styled from 'styled-components/macro';
import ThemeSwitch from 'components/ThemeSwitch';
import { colorPalette } from 'utils/styleConstants';

export default {
  title: 'ThemeSwitch',
  component: ThemeSwitch,
  decorators: [
    (Story) => (
      <DemoContainer>
        <Story />
        <DemoContent>
          <h1>Theme Switch Demo</h1>
          <p>Click the sun/moon icon in the top-right corner to toggle between light and dark modes.</p>
          <DemoCard>
            <h2>Sample Card</h2>
            <p>This is a sample card to demonstrate theme switching.</p>
          </DemoCard>
        </DemoContent>
      </DemoContainer>
    ),
  ],
} as Meta;

const DemoContainer = styled.div`
  position: relative;
  min-height: 400px;
  padding: 20px;
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.white};
  transition: background-color 0.3s ease;
`;

const DemoContent = styled.div`
  padding: 20px;

  h1 {
    font-size: ${({ theme }) => theme.fontSizePalette.heading.H1};
    color: ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.white : theme.colorPalette.grey.grey9)};
    margin-bottom: 16px;
  }

  p {
    font-size: ${({ theme }) => theme.fontSizePalette.body.M};
    color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.grey.grey8};
    margin-bottom: 24px;
  }
`;

const DemoCard = styled.div`
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.grey.grey1};
  border: 1px solid
    ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey4 : theme.colorPalette.grey.grey3)};
  border-radius: 8px;
  padding: 20px;
  max-width: 400px;

  h2 {
    font-size: ${({ theme }) => theme.fontSizePalette.heading.H2};
    color: ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.white : theme.colorPalette.grey.grey9)};
    margin-bottom: 12px;
  }

  p {
    font-size: ${({ theme }) => theme.fontSizePalette.body.S};
    color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.grey.grey8};
    margin-bottom: 0;
  }
`;

const Template: Story = () => (
  <div
    style={{
      backgroundColor: colorPalette.turquoise.normal
    }}
  >
    <ThemeSwitch />
  </div>
);

export const Default = Template.bind({});
