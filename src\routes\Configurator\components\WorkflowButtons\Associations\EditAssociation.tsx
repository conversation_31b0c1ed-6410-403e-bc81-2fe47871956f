import React from 'react';

import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';
import { actionType } from 'utils/constants';
import { useDispatch, useSelector } from 'react-redux';
import selectors from 'state/selectors';
import actions from 'state/actions';

const EditAssociation = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const selectedAssociation = useSelector(selectors.configurator.getSelectedDefinitionTaskGroupsAssociations);
  const editedRowId = useSelector(selectors.configurator.getEditedRowIdWfAssociations);
  const listOfDefinitions = useSelector(selectors.configurator.getListOfDefinitionsForCompanySelected);
  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);

  return (
    <CommonButton
      action={() => {
        if (selectedAssociation) {
          dispatch(actions.configurator.setEditedRowIdWfAssocations(selectedAssociation.id));

          // each definition has a list of available tasks for association,
          // find the id definition and the idTaskDefinition of the selected association
          // for update the API that returns list of available tasks
          const idDef = listOfDefinitions.find((el) => el.wfName === selectedAssociation.definitionName)?.idDefinition;
          dispatch(actions.configurator.setSelectedIdDefFromAssociations(idDef));
          if (selectedAssociation.idTaskDefinition !== null)
            dispatch(actions.configurator.setSelectedIdTaskDefFromAssociations(selectedAssociation.idTaskDefinition));
        }

        dispatch(actions.configurator.setIsActiveEditAssociation(true));
      }}
      scope="tertiary"
      value={t(actionType.EDIT_WF_ASSOCIATION)}
      icon="circle"
      disabled={
        editedRowId !== null ||
        !selectedAssociation ||
        !selectedAssociation.idTask ||
        !utils.user.isActionByCompanyActive(actionType.EDIT_WF_ASSOCIATION, companiesDefinition?.companyName)
      }
    />
  );
};

export default EditAssociation;
