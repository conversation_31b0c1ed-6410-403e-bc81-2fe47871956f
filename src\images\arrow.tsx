import { useTheme } from 'providers/ThemeProvider';
import React from 'react';

interface ArrowProps {
  rotation?: number;
}

const ArrowLeft: React.FC<ArrowProps> = ({ rotation = 0 }) => {
  const { theme } = useTheme();
  const color = theme.colorPalette.turquoise.normal;

  return (
    <>
      <svg
        width="9px"
        height="13px"
        viewBox="0 0 9 13"
        version="1.1"
        xmlns="http://www.w3.org/2000/svg"
        xmlnsXlink="http://www.w3.org/1999/xlink"
        style={{ transform: `rotate(${rotation}deg)` }}
      >
        <title>Icon/arrow right</title>
        <g id="Normalizer" stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
          <g id="Modules/Normalizer/Print-Copy" transform="translate(-207.000000, -77.000000)" fill={color}>
            <g id="Icon/arrow-right" transform="translate(203.000000, 76.000000)">
              <path
                d="M12.5964694,3.96051191 C12.9396183,3.5669385 13.5368497,3.52606136 13.9304231,3.86921026 C14.2846392,4.17804427 14.3531713,4.69268514 14.11318,5.07943999 L14.0217248,5.20316401 L8.94896667,11.0213458 C8.60722238,11.4133082 8.02268649,11.4499191 7.6356553,11.1304005 L7.52591138,11.0238602 L2.41685128,5.20567843 C2.07231371,4.8133201 2.11108008,4.21594794 2.50343841,3.87141037 C2.8565609,3.56132655 3.3757446,3.56171893 3.72721801,3.85092746 L3.83770647,3.95799749 L8.23273342,8.96438341 L12.5964694,3.96051191 Z"
                id="Path-2-Copy-8"
                transform="translate(8.218189, 7.490922) rotate(-270.000000) translate(-8.218189, -7.490922) "
              ></path>
            </g>
          </g>
        </g>
      </svg>
    </>
  );
};

export default ArrowLeft;
