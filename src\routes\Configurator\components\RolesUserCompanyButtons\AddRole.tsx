import React from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { actionType } from 'utils/constants';

import { UserForRoleRow } from 'models/configurator';

import selectors from 'state/selectors';
import utils from 'utils';
import actions from 'state/actions';
import services from 'services';

import CommonButton from 'components/Buttons/CommonButton';

const AddRole = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const selectedCompanyId = useSelector(selectors.configurator.getSelectedCompanyForRoles);
  const companies = useSelector(selectors.configurator.getCompaniesForRoles);
  const selectedUser = useSelector(selectors.configurator.getSelectedUserForRoles);
  const transformedUserRows = useSelector(selectors.configurator.getTransformedUserRows);
  const isEditingUserRows = useSelector(selectors.configurator.getIsEditingUserRows);

  const addRoleAction = async () => {
    if (selectedCompanyId !== null) {
      const idSelected = selectedUser[0].idUser;
      try {
        const { data } = await services.getUserAvailableRoles(selectedCompanyId, idSelected);
        dispatch(actions.configurator.setUserAvailableRoles(data));
        const idParent = selectedUser[0].id;
        // I create the new sub row
        const newSubRow = {
          id: -2,
          idUser: selectedUser[0].idUser,
          idRole: null,
          username: selectedUser[0].username,
          name: selectedUser[0].name,
          email: selectedUser[0].email,
          role: '',
          programResponseList: [],
          subRows: [],
        };
        // I create the new row
        const newRow: UserForRoleRow = {
          ...selectedUser[0],
          idRole: null,
          role: '',
          subRows: [...selectedUser[0].subRows, newSubRow],
        };
        const newRows = transformedUserRows.map((e) => {
          return e.id === newRow.id ? newRow : e;
        });
        dispatch(actions.configurator.setIsEditingUserRows(newRows));
        dispatch(actions.configurator.setRowIdUserRoles(String(-2)));
        dispatch(actions.configurator.setExternalSelectedUserId([newSubRow.id]));
        dispatch(actions.configurator.setOpenRowId(idParent));
      } catch (e) {
        console.error(e);
      }
    }
  };

  return (
    <CommonButton
      action={addRoleAction}
      disabled={
        !selectedUser.length ||
        selectedUser[0]?.isSubRow === true ||
        isEditingUserRows.length > 0 ||
        !utils.user.isActionByCompanyActive(
          actionType.ADD_ROLE,
          companies.find((e) => e.idCompany === selectedCompanyId).name,
        )
      }
      scope="tertiary"
      value={t('AddRole')}
      icon="circle"
    />
  );
};

export default AddRole;
