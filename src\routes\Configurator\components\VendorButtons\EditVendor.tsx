import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actionType } from 'utils/constants';

import utils from 'utils';
import actions from 'state/actions';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';

const EditVendor = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();

  const selectedVendorId = useSelector(selectors.configurator.getSelectedVendorIdFromList);
  const editedVendorId = useSelector(selectors.configurator.getIdEditedVendorFromList);
  const selectedCompany = useSelector(selectors.configurator.getSelectedCompanyVendorConfig);

  const companyName = selectedCompany?.label;
  const hasPermission = utils.user.isActionByCompanyActive(actionType.EDIT_VENDOR, companyName);

  return (
    <CommonButton
      action={() => {
        dispatch(actions.configurator.setIdEditedVendorFromList(selectedVendorId));
      }}
      scope="tertiary"
      value={t(actionType.EDIT_VENDOR)}
      icon="circle"
      disabled={!hasPermission || !selectedVendorId || !!editedVendorId}
    />
  );
};

export default EditVendor;
