import { useTheme } from 'providers/ThemeProvider';
import React from 'react';
import { Bar } from 'react-chartjs-2';

export interface ChartProps {
  dataValues: Data[];
  title?: string;
  height?: string;
}

interface Data {
  label: string;
  count: number;
}

const Chart = (props: ChartProps) => {
  const { dataValues, title } = props;
  const { height = dataValues.length > 1 ? `${dataValues.length * 50}px` : '100px' } = props;
  const { theme } = useTheme();
  const isDarkMode = theme.colorPalette.isDarkMode;

  const data = {
    labels: dataValues.map((el) => el.label),
    datasets: [
      {
        label: '',
        data: dataValues.map((el) => el.count),
        backgroundColor: [isDarkMode ? theme.colorPalette.turquoise.dark : theme.colorPalette.turquoise.light],
        borderColor: [isDarkMode ? theme.colorPalette.turquoise.dark : theme.colorPalette.turquoise.light],
        borderRadius: 50,
        barThickness: 11,
      },
    ],
  };

  const options = {
    indexAxis: 'y' as const,
    elements: {
      bar: {
        borderWidth: 2,
      },
    },
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: {
        grid: {
          display: false,
          color: isDarkMode ? theme.colorPalette.grey.grey4 : theme.colorPalette.grey.grey3,
        },
        ticks: {
          color: isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.grey.grey8,
        },
      },
      x: {
        grid: {
          color: isDarkMode ? theme.colorPalette.grey.grey4 : theme.colorPalette.grey.grey3,
        },
        ticks: {
          color: isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.grey.grey8,
        },
      },
    },
    plugins: {
      legend: {
        display: false,
        labels: {
          color: isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.grey.grey8,
        },
      },
      title: {
        display: !!title,
        text: title,
        color: isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.grey.grey8,
        font: {
          size: 14,
          family: theme.fontFamily,
        },
      },
    },
  };

  return (
    <div
      style={{
        position: 'relative',
        height,
        backgroundColor: isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.white,
        padding: '10px',
        borderRadius: '5px',
      }}
    >
      {dataValues.length ? <Bar data={data} options={options} /> : null}
    </div>
  );
};

export default Chart;
