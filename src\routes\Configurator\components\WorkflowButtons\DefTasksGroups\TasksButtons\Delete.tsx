import CommonButton from 'components/Buttons/CommonButton';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import services from 'services';
import selectors from 'state/selectors';
import utils from 'utils';
import { mutate } from 'swr';
import actions from 'state/actions';
import { modalActionType } from 'utils/constants';
import { actionType } from 'utils/constants';

const Delete = () => {
  const t = utils.intl.useTranslator();
  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  const { tasks } = services.useGetCompanyWithTasks('getCompanyWithTasksKey');

  const { revalidateAssociations } = services.useGetDefinitionTaskGroupsAssociations(
    'getDefinitionTaskGroupsAssociationsKey',
    companiesDefinition?.idCompany,
  );

  const selectedRowId = useSelector(selectors.configurator.getSelectedWfTask);
  const isWfDefinitionInEdit = useSelector(selectors.configurator.getIsWfTableEdit);
  const dispatch = useDispatch();

  const deleteConfirmation = async () => {
    try {
      if (selectedRowId === null) return;
      await services.deleteWfTask(selectedRowId);
      mutate(
        'getCompanyWithTasksKey',
        (tasks ?? []).filter((task) => task.idTask !== selectedRowId),
        {
          revalidate: false,
        },
      );

      // update api get associations
      revalidateAssociations();

      utils.app.notify('success', t('task_deleted'));
      dispatch(actions.modal.closeModal());
    } catch (e) {
      utils.app.notify('fail', t('task_not_deleted'));
    } finally {
      dispatch(actions.configurator.setSelectedWfTask(null));
      dispatch(actions.configurator.setEditedTaskWfRow(null));
    }
  };

  const onClick = () => {
    dispatch(
      actions.modal.setModal({
        actionType: modalActionType.configurator.DELETE_CONFIRMATION,
        props: {
          func: deleteConfirmation,
          subtitle: t('delete_task_confirmation'),
          title: t('delete_task'),
        },
      }),
    );
  };
  return (
    <CommonButton
      action={onClick}
      scope="tertiary"
      value={t('delete-task')}
      disabled={
        selectedRowId === null ||
        isWfDefinitionInEdit ||
        !utils.user.isActionByCompanyActive(actionType.DELETE_WF_TASK, companiesDefinition?.companyName)
      }
    />
  );
};

export default Delete;
