import React from 'react';
import { useSelector, useDispatch } from 'react-redux';

import utils from 'utils';
import actions from 'state/actions';
import selectors from 'state/selectors';

import Dropdown from 'components/Input/Dropdown';

export const TablesDropdowns = () => {
  const dispatch = useDispatch();

  const tablesSelectValues = useSelector(selectors.configurator.selectTablesSelectValues);
  const activeTable = useSelector(selectors.configurator.selectActiveTable);

  return (
    <>
      {tablesSelectValues.length && (
        <Dropdown
          onChange={(value) => {
            dispatch(actions.configurator.setActiveTable(value));
          }}
          options={utils.input.buildOptions(tablesSelectValues, 'tableDesc', 'table')}
          value={activeTable}
          margin="0px 15px"
        />
      )}
    </>
  );
};
