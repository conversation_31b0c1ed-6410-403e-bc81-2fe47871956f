import CommonButton from 'components/Buttons/CommonButton';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import selectors from 'state/selectors';
import utils from 'utils';
import { actionType } from 'utils/constants';

const Edit = () => {
  const t = utils.intl.useTranslator();
  const editedRowId = useSelector(selectors.configurator.getEditedWfTask);
  const selectedRowId = useSelector(selectors.configurator.getSelectedWfTask);
  const isWfDefinitionInEdit = useSelector(selectors.configurator.getIsWfTableEdit);
  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  const dispatch = useDispatch();
  const onClick = () => {
    dispatch(actions.configurator.setEditedTaskWfRow(selectedRowId));
    dispatch(actions.configurator.setIsWfTableEdit(true));
  };
  return (
    <CommonButton
      action={onClick}
      scope="tertiary"
      value={t('edit-task')}
      disabled={
        editedRowId !== null ||
        selectedRowId === null ||
        isWfDefinitionInEdit ||
        !utils.user.isActionByCompanyActive(actionType.EDIT_WF_TASK, companiesDefinition?.companyName)
      }
    />
  );
};

export default Edit;
