import React from 'react';
import { actionType } from 'utils/constants';

import utils from 'utils';

import CommonButton from 'components/Buttons/CommonButton';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import selectors from 'state/selectors';

const EditVendorDetail = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
 
  const selectedRowId = useSelector(selectors.configurator.getSelectedRowIdVendorDetail);
  const editedRowId = useSelector(selectors.configurator.getEditedRowIdVendorDetail);

  const selectedCompany = useSelector(selectors.configurator.getSelectedCompanyVendorConfig);
  const companyName = selectedCompany?.label;
  const hasPermission = utils.user.isActionByCompanyActive(actionType.EDIT_DETAIL_VENDOR, companyName);

  return (
    <CommonButton
      action={() => {
        if (selectedRowId) {
          dispatch(actions.configurator.setEditedRowIdSubjectDetails(selectedRowId));
        }
      }}
      scope="tertiary"
      value={t(actionType.EDIT_DETAIL_VENDOR)}
      icon="circle"
      disabled={!hasPermission || !selectedRowId || !!editedRowId}
    />
  );
};

export default EditVendorDetail;
