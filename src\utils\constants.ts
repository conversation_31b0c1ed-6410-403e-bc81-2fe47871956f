/* eslint max-lines: 0 */
import { AIsuggestion } from 'models/response';
import { UserCsvSep, UserDate, UserDecimalSep } from 'models/user';
import moment from 'moment-timezone';

// LUCY4 SERVICES
export const USERSERVICE = 'user';
export const LOGINSERVICE = 'login';
export const LOCALSERVICE = 'local';
export const CINISERVICE = 'cIni';
export const CINIDISKSERVICE = 'cIniDisk';
export const PROGRAMSERVICE = 'program';
export const GRIDCOLUMNSERVICE = 'gridColumn';
export const WDOCBATCHSERVICE = 'wDocBatch';
export const SUBJECT = 'subject';
export const MAILSERVICE = 'mail';
export const FILESERVICE = 'file';
export const HUBMAILSERVICE = 'wHubPackageMail';
export const MAILSTATUS = 'mailStatus';
export const MONITORSERVICE = 'monitor';
export const REPORTS = 'reports';
export const WORKFLOW = 'workflow';
export const INTERACTIVE = 'interactive';
export const INTEGRATION = 'integration';
export const CONFIGURATOR = 'config';
export const VENDOR = 'vendor';
export const TABLE_FIELDS = 'tableFields';
export const KPI = 'kpi';
export const GBS = 'gbs';
export const CHARTS = 'charts';
export const USERREPORTSETTINGS = 'userReportSettings';
export const NOTIFICATIONS = 'notifications';

// MODAL TYPES
export const modalActionType = {
  USER_SETTINGS: 'userSettings',
  USER_PASSWORD: 'userPassword',
  CHANGELOG: 'changelog',
  RESET_PASSWORD: 'resetPassword',
  documents: {
    FILE_IN_ACQUISITION: 'documents/fileInAcquisition',
    REVERSE_REPOST: 'documents/Reverse/Re-Post',
    CHANGE_LAYOUT: 'documents/changeLayout',
    CHILD_OF: 'documents/ChildOf',
    SEARCH_SUBJECT: 'documents/searchSubject',
    ADD_MEMO: 'documents/addMemo',
    DELETE_DOCUMENTS: 'documents/deleteDocuments',
    EDIT_COLUMNS: 'documents/editColumns',
    SAVE: 'documents/save',
    GO_BACK: 'documents/goBack',
    GO_AHEAD: 'documents/goAhead',
    REASSIGN_USER: 'documents/reassign',
    SEND_MAIL: 'documents/sendMail',
    ALLEGA: 'documents/allega',
    NO_SUBJECT_DETAIL: 'documents/noSubjectDetail',
    REASSIGN_PRIORITY: 'documents/reassignPriority',
    CONTINUE_WITHOUT_SAVING: 'documents/continueWithoutSaving',
    LOG: 'documents/log',
    NEW_STORE: 'documents/newStore',
    SET_SCAN_DATE: 'documents/setDataScan',
    SPLIT_FIX: 'documents/splitAndFix',

    HOT_KEYS: 'documents/hotKEYS',
    MASSIVE_DOCUMENTS: 'documents/massiveDocuments',
    COMPANY_DOC_TYPE: 'documents/companyDocType',
    SELF_VALIDATE: 'documents/selfValidate',
    WORKFLOW_LOG: 'documents/workflowLog',
    SEND_TO_WORKFLOW: 'documents/sendToWorkflow',
    CONFIRM: 'documents/confirm',
    SELECTION: 'documents/selection',
    RESPONSE_INFO: 'documents/responseInfo',
    LITIGATION: 'documents/litigation',
    CLOSE_LITIGATION: 'documents/CloseLitigation',
    VALIDATION_CHECK: 'documents/ValidationCheck',
    SUBJECT_DETAILS: 'documents/subjectDetails',
    DOCUMENT_HISTORY: 'documentHistoryAndWf',
    DOWNLOAD_PDF: 'documents/downloadPdf',
    SAP_USER_LOGIN: 'documents/sapUserLogin',
    SHOW_SAP_DATA: 'documents/showSapData',
    SAP_MESSAGES: 'documents/SapMessages',
    SUPPLIER_IBAN_LIST: 'documents/SupplierIbanList',
    MULTIPLE_CONFIRM: 'documents/MultipleConfirm',
    MULTIPLE_CONFIRM_ERRORS: 'documents/MultipleConfirmErrors',
    DOWNLOAD_ARCHIVE_PDF: 'documents/downloadArchivePDF',
  },
  pdfUploader: {
    PDF_PREVIEW: 'pdfUploader/pdfPreview',
    SEARCH_SUBJECT: 'pdfUploader/searchSubject',
    CHILD_OF: 'pdfUploader/ChildOf',
    UPLOAD_PROGRESS: 'pdfUploader/uploadProgress',
  },
  report: {
    EXPORT_REPORT: 'report/export',
    SAVE_OR_UPDATE_FILTERS: 'report/SaveOrUpdateFilters',
    DELETE_PREFERENCE_FILTER: 'report/DeletePreferenceFilter',
  },
  configurator: {
    NEW_TEMPLATE: 'AddTemplate',
    EDIT_TEMPLATE: 'EditTemplate',
    DELETE_TEMPLATE: 'DeleteTemplate',
    DELETE_ROWS: 'MapperDeleteRow',
    // General
    DELETE_CONFIRMATION: 'configurator/DeleteConfirmation',
    CLEAR_CONFIRMATION: 'configurator/ClearConfirmation',
    // Users
    IMPORTED_DUPLICATE_USERS: 'configurator/importedDuplicateUsers',
    // Roles
    ADD_USER_MODAL: 'configurator/addUserModal',
    CONFIGURE_ROLES_MODAL: 'configurator/configureRolesModal',
    CLONE_ROLE_MODAL: 'configurator/cloneRoleModal',
    // Action table
    SELECT_AP2_TEMPLATE: 'configurator/selectAP2Template',
    SELECT_COMPANIES: 'configurator/selectCompanies',
    SELECT_COMPANIES_NOPO: 'configurator/selectCompaniesNoPO',
    // Workflow
    GROUPS_USERS_MODAL: 'configurator/groupsUsersModal',
    CLONE_WF_TABLE_MODAL: 'configurator/cloneWfTableModal',
    GROUPS_USERS_ASSOCIATIONS_MODAL: 'configurator/groupsUsersAssociationsModal',
    CLONE_WF_ASSOCIATION: 'configurator/cloneWfAssociationModal',
    ADD_REPLACE_MASSIVE_USER: 'configurator/addReplaceMassiveUser',
    REMOVE_MASSIVE_USER: 'configurator/removeMassiveUser',
  },
  normalizer: {
    LOADING_ERROR: 'normalizer/loadingError',
    // General
    DELETE_CONFIRMATION: 'normalizer/DeleteConfirmation',
    RETRY_ERRORS: 'normalizer/retryErrors',
  },
  controlTower: {
    EXPORT_KPI_REPORT: 'controlTower/exportKpiReport',
    DRILL_DOWN_KPI: 'controlTower/drillDownKpi',
  },
};

// BUTTONS
// ATTENZIONE! per alcune azioni
// il valore della costante viene utilizzato
// per accedere alla configurazione corrisponedente
// vedi redux/modules/app/model->config
export const actionType = {
  CLOSE_DOCUMENT: 'close-document',
  SAVE_DOCUMENT: 'save-document',
  EXPORT_SAP: 'export-sap',
  SEND_TO_WORKFLOW: 'SendToWorkflow',
  DOWNLOAD_PDF: 'downloadArchivePDF',
  SET_ALL_DOCS_AS_DEFAULT: 'SET_ALL_DOCS_AS_DEFAULT',
  REVERSE_REPOST: 'Reverse/Re-Post',
  ASSIGNDOCUMENT: 'AssignDocument',
  REASSIGN_PRIORITY: 'ActionArchiveSetPriorityDocument',
  DELETE_DOCUMENT: 'ActionArchiveDeleteDocuments',
  COPY_CELL: 'copy-cell',
  AUTONOMOUSLY_VALIDATE: 'autonomously-validate',
  OPEN_WORKFLOW: 'SendToWorkflow',
  RETURN_TO_BATCH: 'ActionArchiveReassignDocumentsToBatch',
  REMOVE_BRAIN_TRAINING: 'remove-brain-training',
  SEND_TO_SAP: 'send-to-sap',
  INVOKE_CUSTOM_PROCEDURE: 'invoke-custom-procedure',
  ADDNEWSTORE: 'AddNewStore',
  SAVE: 'save',
  SAVE_DISK: 'save_disk',
  BACK: 'back',
  UPDATE: 'update',
  USER: 'user',
  USERGROUP: 'user-group',
  VIEWS: 'views',
  SEARCH: 'search',
  CONVALIDA: 'CONVALIDA',
  DOCINFO: 'INFO',
  CLOSEINFO: 'CLOSEINFO',
  REJECT: 'Reject',
  MULTIPLE_REJECT: 'MultipleReject',
  CONFIRM: 'Confirm',
  PARK: 'Park',
  CLOSEPARK: 'ClosePark',
  SENDLEVEL: 'SENDLEVEL',
  DIRECT: 'SENDDIRECT',
  REJECT_PREVIOUS: 'RejectToPrevious',
  VALIDA_REFERENTE: 'VALIDATEREFERENCEUSER',
  INFO: 'info',
  ALLEGA: 'ManageAttachment',
  NOTES: 'Notes',
  SENDMAIL: 'SendMail',
  LOG: 'WfHistory',
  DOCUMENT_HISTORY: 'documentHistoryAndWf',
  DEQUEUE: 'dequeue',
  GRID: 'grid',
  GRIDSWAP: 'grid-swap',
  SHOW_Q3: 'show-q3',
  CLOSE_Q3: 'close-q3',
  MOVE_IN_Q3: 'move-in-q3',
  CAN_EDIT_Q3: 'CAN_EDIT_Q3',
  PRINT: 'print',
  USERSWAP: 'user-swap',
  TABLELAYOUT: 'table-layout',
  ROWSLAYOUT: 'rowslayout',
  COLSLAYOUT: 'colslayout',
  FULLLAYOUT: 'fulllayout',
  LAYOUT: 'layout',
  CSV: 'csv',
  PDF: 'pdf',
  EXCEL: 'excel',
  HTML: 'html',
  MORE: 'more',
  PLUS: 'plus',
  MINUS: 'minus',
  ADDROW: 'add-row',
  REMOVEROW: 'remove-row',
  REMOVEROWS: 'remove-rows',
  DUPLICATEROW: 'duplicate-row',
  ADDPREF: 'add-pref',
  ADDPREFS: 'add-prefs',
  DEFAULT: 'default',
  ACCOUNTANT_REJECT: 'accountant-reject',
  ACCOUNTANT_REJECT_MAIL: 'accountant-reject-mail',
  ACCOUNTANT_REJECT_ARCHIVE: 'accountant-reject-archive',
  ACCOUNTANT_REJECT_PRINT: 'accountant-reject-print',
  JIES_STATUS: 'interactive/jies-status',
  JIES_STATUS_RED: 'interactive/jies-status-red',
  JIES_STATUS_ORANGE: 'interactive/jies-status-orange',
  JIES_STATUS_GREEN: 'interactive/jies-status-green',
  TRASH: 'trash',
  TRASH_RED: 'trash-red',
  LEFT_ARROW: 'left-arrow',
  RIGHT_ARROW: 'right-arrow',
  UP_ARROW: 'up-arrow',
  DOWN_ARROW: 'down-arrow',
  HORIZONTAL_EXPAND: 'horizontal-expand',
  VERTICAL_EXPAND: 'vertical-expand',
  ZOOM_MINUS: 'zoom-minus',
  ZOOM_PLUS: 'zoom-plus',
  ROTATION: 'rotation-arrow',
  ROTATE_RIGHT: 'rotation-arrow',
  ROTATE_LEFT: 'rotation-arrow-flip',
  DOCUMENTS: 'documents/convalida',
  FORZACONVALIDA: 'ActionDocumentForceQC',
  APPLICAREGOLE: 'documents/applicaregole',
  DOCSTATUS: 'document-status',
  LEFT_ARROW_LINE: 'left-arrow-line',
  RIGHT_ARROW_LINE: 'right-arrow-line',
  ICON_SWAP: 'swap',
  MANUAL: 'manual',
  STOP: 'stop',
  SCARICA: 'scarica',
  OPEN: 'open',
  EDIT: 'edit',
  DUPLICATE: 'duplicate',
  EXPORT: 'export',
  RESUBMIT: 'resubmit',
  SPLIT_FIX: 'SplitFix',
  SET_RECEIPT_DATE: 'SetReceiptDate',
  CHANGE_PDF_DATE: 'change-pdf-date',
  MASSIVE_VALIDATION: 'massiveValidation',
  COMPANY_DOC_TYPE: 'ChangeCompanyAndDocType',
  AUTO_VIEW_IN_PAGE: 'AUTO_VIEW_IN_PAGE',
  PRIORITY_WARNING: 'PRIORITY_WARNING',
  CLEAR_ZERO_VALUE_Q1_CURRENCY_FIELD: 'CLEAR_ZERO_VALUE_Q1_CURRENCY_FIELD',
  CLEAR_ZERO_VALUE_Q3_CURRENCY_FIELD: 'CLEAR_ZERO_VALUE_Q3_CURRENCY_FIELD',
  CHECK_DUPLICATE_DOCUMENT: 'CheckDuplicateDocument',
  SELF_VALIDATE: 'SelfValidate',
  MANUAL_REASSIGN_DOCUMENTS: 'ManualReassignDocuments',
  MANUAL_REASSIGN_DOCUMENTS_NO_OCR: 'ManualReassignNonOcrDocuments',
  MANUAL_REASSIGN_DOCUMENTS_OCR: 'ManualReassignOcrDocuments',
  VALIDATE: 'Validate',
  APPLYRULES: 'ApplyRules',
  PREV_Q1_TAB: 'PrevQ1Tab',
  NEXT_Q1_TAB: 'NextQ1Tab',
  AUTOSAVE_DOCUMENT: 'autosave',
  Q3_LEFT: 'q3-left',
  LITIGATION: 'Litigation',
  LITIGATION_MAIL: 'Litigation_Mail',
  LITIGATION_ARCHIVE: 'Litigation_Archive',
  LITIGATION_PRINT: 'Litigation_Print',
  CLOSE_LITIGATION: 'CloseLitigation',
  ASK_INFO: 'AskInfo',
  RESPONSE_INFO: 'ResponseInfo',
  DOWNLOAD_JIES: 'DownloadJIES',
  CONNECT_JIES: 'ConnectJIES',
  DUPLICATED_PROTOCOL: 'duplicatedProtocol',
  DOC_STATUS_STOPLIGHT: 'docStatusStopLight',
  DOC_PRIORITY: 'docPriority',
  EXPORT_TO_SAP: 'ExportToSap',
  DEFAULT_HIDE_FIELD_BY_CONFIDENCE: 'DEFAULT_HIDE_FIELD_BY_CONFIDENCE',
  DEFAULT_SELECT_Q1_PLACEHOLDER: 'DEFAULT_SELECT_Q1_PLACEHOLDER',
  ENABLE_ALL_IN_ONE_VIEW: 'ENABLE_ALL_IN_ONE_VIEW',
  NO_SUBJECT: 'noSubject',
  NEW_TEMPLATE: 'AddTemplate',
  EDIT_TEMPLATE: 'EditTemplate',
  DELETE_TEMPLATE: 'DeleteTemplate',
  MAPPER_ADD_ROW: 'MapperAddRow',
  DELETE_ROWS: 'MapperDeleteRow',
  MAPPER_EDIT_ROW: 'MapperEditRow',
  SHOW_SUBJECT_DETAILS: 'SHOW_SUBJECT_DETAILS',
  NO_INFO_SENDTOWF: 'noInfoSendToWorkflow',
  APPLY_VAL_LOGICS: 'ActionFieldsUpdateRules',
  ACTION_TABLE_ADD_ROW: 'AddExpAction',
  ACTION_TABLE_DELETE_ROW: 'DeleteExpAction',
  ACTION_TABLE_EDIT_ROW: 'EditExpAction',
  HIDE_MOVE_TYPE: 'HideMovType',
  DELETE_AP2_TEMPLATE: 'DeleteExpAP2Template',
  ADD_AP2_TEMPLATE: 'AddExpAP2Template',
  EDIT_AP2_TEMPLATE: 'EditExpAP2Template',
  AUTOFOCUS_Q3_FIELD: 'AUTOFOCUS_Q3_FIELD',
  EDIT_CORPORATE_APPROVAL: 'EditCorporateApproval',
  HIDE_ADD_STORE_BUTTON: 'HIDE_ADD_STORE_BUTTON',
  SHOW_FILTER_FOR_DOCUMENTS_LIST: 'SHOW_FILTER_FOR_DOCUMENTS_LIST',
  DISABLE_LITIGATION_WITH_REGISTRATION: 'DISABLE_LITIGATION_WITH_REGISTRATION',
  CHECK_VENDOR_ON_FORCE_VALIDATE: 'CHECK_VENDOR_ON_FORCE_VALIDATE',
  ADD_NOTE: 'AddNote',
  DELETE_NOTE: 'DeleteNote',
  LITIGATION_PARK_PRINT: 'Litigation_Park_Print',
  LITIGATION_PARK_MAIL: 'Litigation_Park_Mail',
  LITIGATION_PARK_ARCHIVE: 'Litigation_Park_Archive',
  // Configurator
  // Users
  ADD_USER: 'AddUser',
  EDIT_USER: 'EditUser',
  IMPORT_USER: 'ImportUser',
  EXPORT_USER: 'ExportUser',
  BLOCK_USER: 'DeleteUser',
  CLONE_USER: 'CloneUser',
  USER_ROLES: 'UserRoles',
  // Tables
  ADD_TABLE_ROW: 'AddRow',
  DELETE_TABLE_ROW: 'DeleteRow',
  EDIT_TABLE_ROW: 'EditRow',
  EXPORT_TABLE_TO_STORAGE: 'ExportTableToStorage',
  // Companies
  ADD_COMPANY: 'AddCompany',
  EDIT_COMPANY: 'EditCompany',
  CLONE_COMPANY: 'CloneCompany',
  // Roles
  ROLES_BACK_TO_COMPANY_LIST: 'RolesBackToCompanyList',
  ADD_USER_ROLE: 'AddUserRole',
  DELETE_ROLE: 'DeleteRole',
  CONFIGURE_ROLE: 'ConfigureRoles',
  ADD_ROLE: 'AddRole',
  CLONE_ROLE: 'CloneRole',
  EDIT_ROLE: 'EditUserRole',
  // Workflow
  VIEW_COMPANY_WORKFLOW: 'ViewCompanyWf',
  BACK_TO_VIEW_MODE: 'BackToViewMode',
  // Workflow definition
  DELETE_WF_DEFINITION: 'DeleteWfDefinition',
  CLONE_WF_DEFINITION: 'CloneWfDefinition',
  EDIT_WF_DEFINITION: 'EditWfDefinition',
  ADD_WF_DEFINITION: 'AddWfDefinition',
  // Workflow task
  DELETE_WF_TASK: 'DeleteWfTask',
  EDIT_WF_TASK: 'EditWfTask',
  ADD_WF_TASK: 'AddWfTask',
  // Workflow group
  DELETE_WF_GROUP: 'DeleteWfGroup',
  CLONE_WF_GROUP: 'CloneWfGroup',
  EDIT_WF_GROUP: 'EditWfGroup',
  ADD_WF_GROUP: 'AddWfGroup',
  // Workflow Association
  DELETE_WF_ASSOCIATION: 'DeleteWfAssociation',
  CLONE_WF_ASSOCIATION: 'CloneWfAssociation',
  EDIT_WF_ASSOCIATION: 'EditWfAssociation',
  ADD_WF_ASSOCIATION: 'AddWfAssociation',
  ADD_GROUP_TO_TASK: 'AddGroupToTask',
  ADD_TASK_TO_DEFINITION: 'AddTaskToDefinition',
  UNLOCK_DOCUMENT: 'UnlockDocument',
  ASSIGN_DOCUMENT_LATENCY: 'AssignDocumentLatency',
  // Vendor Configurator
  RESET_SEARCH_VENDOR: 'ResetSearchVendor',
  EDIT_VENDOR: 'EditVendor',
  VIEW_DETAIL_VENDOR: 'ViewDetailVendor',
  BACK_LIST_VENDOR: 'BackListVendor',
  BACK_VIEW_MODE_VENDOR: 'BackViewModeVendor',
  EDIT_VENDOR_DETAILS: 'EditVendorDetails',
  EDIT_DETAIL_VENDOR: 'EditDetailVendor',
  // Workflow Configurator
  ADD_MASSIVE_USER: 'AddUserMassivelyWfGroup',
  REMOVE_MASSIVE_USER: 'RemoveUserMassivelyWfGroup',
  REPLACE_MASSIVE_USER: 'ReplaceUserMassivelyWfGroup',
  RETRIVE_DOC_FROM_LITIGATION: 'RetriveDocFromLitigationView',
  RETRIEVE_FROM_DOCUMENTS: 'RetrieveFromDocuments',
  MASSIVE_DOC_TYPE_CHANGE: 'MassiveDocTypeChange',
  SANITY_CHECK: 'CheckMandatoryFields',
  GENERATE_DEM: 'GenerateDEM',
  // Notification Configurator
  ADD_OPEN_WF_NOTIFICATIONS: 'AddOpenWfNotificationSettings',
  EDIT_OPEN_WF_NOTIFICATIONS: 'EditOpenWfNotificationSettings',
  DELETE_OPEN_WF_NOTIFICATIONS: 'DeleteOpenWfNotificationSettings',
  ADD_WF_REMINDER_NOTIFICATIONS: 'AddWfReminderNotificationSettings',
  EDIT_WF_REMINDER_NOTIFICATIONS: 'EditWfReminderNotificationSettings',
  DELETE_WF_REMINDER_NOTIFICATIONS: 'DeleteWfReminderNotificationSettings',
  ADD_OVERDUE_NOTIFICATION: 'AddOverdueNotificationSettings',
  EDIT_OVERDUE_NOTIFICATION: 'EditOverdueNotificationSettings',
  DELETE_OVERDUE_NOTIFICATION: 'DeleteOverdueNotificationSettings',
};

export type ACTIONTYPE = (typeof actionType)[keyof typeof actionType];

// WFTYPE
export const WFTYPE_INFO = 0;
export const WFTYPE_INTEGR = 1;

export const dateOptions: UserDate[] = [
  'dd-MM-yyyy',
  'dd/MM/yyyy',
  'dd.MM.yyyy',
  'MM-dd-yyyy',
  'MM/dd/yyyy',
  'MM.dd.yyyy',
  'yyyy-MM-dd',
  'yyyy/MM/dd',
  'yyyy.MM.dd',
  'MMM-dd-yyyy',
  'MMM/dd/yyyy',
];

export const notDraggable = [modalActionType.documents.EDIT_COLUMNS];

export type PdfRotation = 0 | 90 | 180 | 270 | null;

export const defaultProgramCode = 1;
export const defaultProgramName = '';
export const defaultUserDateFormat: UserDate = 'dd-MM-yyyy';
export const defaultUserTimeZoneFormat = moment.tz.guess();
export const idAccessor = 'idRow';
export const defaultUserDecimalSeparator: UserDecimalSep = '.';
export const defaultUserCsvSeparator: UserCsvSep = ',';
export const defaultAIsuggestion: AIsuggestion = {
  content: '',
  confidence: -1,
  pageNumber: -1,
  boundingBox: { x1: -1, x2: -1, y1: -1, y2: -1 },
  status: 0,
  number: 0,
};
export const searchTypes1 = [
  {
    label: 'Equal',
    value: 0,
  },
  {
    label: 'Like',
    value: 1,
  },
];
export const searchTypes = searchTypes1.concat({
  label: 'Value',
  value: 2,
});

export const searchTypes2 = searchTypes.concat({
  label: 'Range',
  value: 3,
});

type moduleProgramCode = {
  invoiceInteractiveExport: number;
  claimValidation: number;
  pdfUploader: number;
  monitor: number;
  rxValidation: number;
  configurator: number;
  report: number;
  contractValidation: number;
  invoiceWorkflow: number;
  controlTower: number;
};

export const moduleProgramCode: moduleProgramCode = {
  invoiceInteractiveExport: 3,
  claimValidation: 109,
  pdfUploader: 56,
  monitor: 112,
  rxValidation: 110,
  configurator: 111,
  report: 10,
  contractValidation: 108,
  invoiceWorkflow: 50,
  controlTower: 113,
};

export const docHeaderExcluded = [
  'documentTypeHeader',
  'companyHeader',
  'subjectHeader',
  'isChildOf',
  'movTypeHeader',
  'supplierCode',
  'vatNumber',
];

export const definitionTaskGroupTabs = {
  WORKFLOW_DEFINITION: 'WORKFLOW_DEFINITION',
  WORKFLOW_TASK: 'WORKFLOW_TASK',
  WORKFLOW_GROUP: 'WORKFLOW_GROUP',
};

export const EVENTSTATUS = {
  deleted: -9,
};

export const EXPORTSTATUS = {
  workingQueue: 4,
};
export const prefixes = {
  documentList: 'documentList.',
  workflowAssociation: 'workflow.association.',
  workflowDefinition: 'workflow.definition.',
  workflowMain: 'workflow.main.',
  configuratorTables: 'configurator.tables.',
  documentHistory: 'document.history.',
  workflowAssociationMain: 'workflow.association.main.',
  undefined: '',
  monitorTimers: 'monitor.timers.',
};

export type prefixesNameList = keyof typeof prefixes;

export const CHANNELS = [
  { label: 'MAIL', value: 1 },
  { label: 'SPLITTER', value: 5 },
  { label: 'FILEACQUIRE', value: 3 },
  { label: 'PAPER', value: 0 },
  { label: 'EINVOICE', value: 6 },
  { label: 'VENDORPORTAL', value: 7 },
  { label: 'DISK', value: 4 },
  { label: 'AP15', value: 8 },
];

export const CHANNELS_MAP = {
  MAIL: 1,
  SPLITTER: 5,
  FILEACQUIRE: 3,
  PAPER: 0,
  EINVOICE: 6,
  VENDORPORTAL: 7,
  DISK: 4,
  AP15: 8,
};

export const URLS = {
  controlTower: {
    detailCategories: 'detail-categories',
    chart: 'chart',
  },
};

export const CHART_TYPE = {
  histogram: 'histogram',
  line: 'line',
  bar: 'bar',
  pie: 'pie',
};

export const AXIS_TYPE = {
  date: 'DATE',
  dateMonth: 'DATE_MONTH',
  dateTime: 'DATETIME',
};

export const ACTION_TRIGGER_CONFIG = {
  callApi: 'callApi',
  removeField: 'removeField',
};
