import React from 'react';

import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import { definitionTaskGroupTabs, modalActionType } from 'utils/constants';
import axios from 'axios';
import selectors from 'state/selectors';
import services from 'services';
import { mutate } from 'swr';
import { actionType } from 'utils/constants';

const Delete = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const selectedDefinitionTaskGroup = useSelector(selectors.configurator.getSelectedDefinitionTaskGroup);
  const selectedDefinitionFromCompanySelected = useSelector(
    selectors.configurator.getSelectedDefinitionFromCompanySelected,
  );
  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);

  const { revalidateAssociations } = services.useGetDefinitionTaskGroupsAssociations(
    'getDefinitionTaskGroupsAssociationsKey',
    companiesDefinition?.idCompany,
  );

  // WF DEFINITION
  const listOfDefinitions = useSelector(selectors.configurator.getListOfDefinitionsForCompanySelected);
  const isWfDefinitionInEdit = useSelector(selectors.configurator.getIsWfTableEdit);
  const { listOfDefinitionsForAssociations } = services.useGetCompanyDefinitionsForAssociation(
    'getCompanyDefinitionForAssociationsKey',
    companiesDefinition?.idCompany,
  );

  const deleteWFDefinition = async () => {
    if (selectedDefinitionFromCompanySelected) {
      await services.deleteWfDefinition(selectedDefinitionFromCompanySelected.idDefinition).then(() => {
        const list = listOfDefinitions.filter(
          (el) => el.idDefinition !== selectedDefinitionFromCompanySelected.idDefinition,
        );
        dispatch(actions.configurator.setListOfDefinitionsForCompanySelected(list));
        mutate(
          'getCompanyDefinitionForAssociationsKey',
          (listOfDefinitionsForAssociations ?? []).filter(
            (def) => def.idDefinition !== selectedDefinitionFromCompanySelected.idDefinition,
          ),
          {
            revalidate: false,
          },
        );

        // update get associations API
        revalidateAssociations();

        utils.app.notify('success', t('deleteWfDefinitionSuccess'));
      });
    }
  };

  // WF GROUPS
  const selectedGroupFromCompanySelected = useSelector(selectors.configurator.getSelectedGroupFromCompanySelected);
  const { listOfGroupsForAssociations } = services.useGetGroupsForTaskAssociations(
    'getCompanyGroupsForAssociationsKey',
    companiesDefinition?.idCompany,
  );
  const { groups } = services.useGetCompanyGroups('getCompanyGroupsKey', companiesDefinition?.idCompany);

  const deleteWFGroup = async () => {
    if (selectedGroupFromCompanySelected) {
      await services.deleteWfGroup(selectedGroupFromCompanySelected.idGroup).then(() => {
        const list = groups?.filter((el) => el.idGroup !== selectedGroupFromCompanySelected.idGroup);

        mutate(
          'getCompanyGroupsForAssociationsKey',
          (listOfGroupsForAssociations ?? []).filter(
            (group) => group.idGroup !== selectedGroupFromCompanySelected.idGroup,
          ),
          {
            revalidate: false,
          },
        );

        if (companiesDefinition) {
          mutate('getCompanyGroupsKey', list, {
            revalidate: false,
          });
        }
        // update get associations API
        revalidateAssociations();
        utils.app.notify('success', t('deleteWfGroupSuccess'));
      });
    }
  };

  const deleteRow = () => {
    try {
      switch (selectedDefinitionTaskGroup) {
        case definitionTaskGroupTabs.WORKFLOW_DEFINITION:
          deleteWFDefinition();
          break;
        case definitionTaskGroupTabs.WORKFLOW_GROUP:
          deleteWFGroup();
      }
    } catch (error) {
      if (axios.isAxiosError(error)) {
        utils.app.notify('fail', `${t('error')} ${error.response}`);
        return;
      }
      console.error(error);
    } finally {
      dispatch(actions.modal.closeModal());
    }
  };

  const checkDisabledButton = () => {
    switch (selectedDefinitionTaskGroup) {
      case definitionTaskGroupTabs.WORKFLOW_DEFINITION:
        if (
          selectedDefinitionFromCompanySelected === null ||
          isWfDefinitionInEdit ||
          !utils.user.isActionByCompanyActive(actionType.DELETE_WF_DEFINITION, companiesDefinition?.companyName)
        )
          return true;
        break;
      case definitionTaskGroupTabs.WORKFLOW_GROUP:
        if (
          selectedGroupFromCompanySelected === null ||
          isWfDefinitionInEdit ||
          !utils.user.isActionByCompanyActive(actionType.DELETE_WF_GROUP, companiesDefinition?.companyName)
        )
          return true;
        break;

      default:
        return false;
    }
  };
  return (
    <CommonButton
      action={() => {
        dispatch(
          actions.modal.setModal({
            actionType: modalActionType.configurator.DELETE_CONFIRMATION,
            props: {
              title: t('delete-wf-row-title'),
              subtitle: t('delete-wf-row-subtitle'),
              func: () => deleteRow(),
            },
          }),
        );
      }}
      scope="tertiary"
      value={t('delete')}
      icon="circle"
      disabled={checkDisabledButton()}
    />
  );
};

export default Delete;
