import React from 'react';

import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';
import { actionType, modalActionType } from 'utils/constants';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import selectors from 'state/selectors';

const CloneAssociation = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const selectedAssociation = useSelector(selectors.configurator.getSelectedDefinitionTaskGroupsAssociations);
  const editedRowId = useSelector(selectors.configurator.getEditedRowIdWfAssociations);
  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);

  const cloneRow = async () => {
    dispatch(
      actions.modal.setModal({
        actionType: modalActionType.configurator.CLONE_WF_ASSOCIATION,
      }),
    );
  };
  return (
    <CommonButton
      action={() => cloneRow()}
      scope="tertiary"
      value={t(actionType.CLONE_WF_ASSOCIATION)}
      icon="circle"
      disabled={
        editedRowId !== null ||
        !selectedAssociation ||
        !!selectedAssociation.isSubRow ||
        !utils.user.isActionByCompanyActive(actionType.CLONE_WF_ASSOCIATION, companiesDefinition?.companyName)
      }
    />
  );
};

export default CloneAssociation;
