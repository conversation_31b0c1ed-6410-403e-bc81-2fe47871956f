import React from 'react';
import styled from 'styled-components/macro';

import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { IconProp } from '@fortawesome/fontawesome-svg-core';
import TableNotVerifiedIcon from 'images/table-not-verified.svg';
import TableVerifiedIcon from 'images/table-verified.svg';
import CheckedIcon from 'images/checked.svg';
import UncheckedIcon from 'images/unchecked.svg';
import ExclamationMark from 'images/001-exclamation-mark.png';
import ParagraphField from 'images/Paragraph.svg';
import ParagraphFieldConfirmed from 'images/ParagraphConfirmed.svg';
// doc status
import RedTriangle from 'images/red_triangle.svg';
import GreenCircle from 'images/green_circle.svg';
import RedCircle from 'images/red_circle.png';
import YellowSquare from 'images/yellow_square.svg';
import Warning from 'images/warning.svg';
import BlockIcon from 'images/icon_block_red.svg';
import AlreaAssignedIcon from 'images/already-assigned-icon.svg';
import YellowCircle from 'images/yellow_circle.svg';
import PriorityHigh from 'images/priorityhigh.svg';
import PriorityLow from 'images/prioritylow.svg';
import PriorityMedium from 'images/prioritymedium.svg';
import TopPage from 'images/top-page.svg';
import Chevron from 'images/chevron.svg';
import Acquisition from 'images/acquisition.svg';
import AI from 'images/AI.svg';
import Archive from 'images/archive.svg';
import Export from 'images/export.svg';
import Validation from 'images/validation.svg';
import Workflow from 'images/workflow.svg';
import CriticalPriority from 'images/criticalPriority.svg';
import DuplicatedIcon from 'images/duplicated.svg';

const IconContainer = styled.div<{ icon: string; disabled: boolean; confirmed: boolean; cursor?: string }>`
  color: ${({ confirmed, theme }) =>
    confirmed ? `${theme.colorPalette.turquoise.normal}` : `${theme.colorPalette.grey.grey5}`};
  cursor: ${({ disabled, cursor }) => (cursor ? cursor : disabled ? 'cursor' : 'pointer')};
  opacity: ${({ disabled }) => (disabled ? '0.3' : '1')};
`;

export interface IconProps {
  icon: string;
  custom?: boolean;
  disabled?: boolean;
  cursor?: string;
  confirmed?: boolean;
  onClick?: () => void;
  style?: React.CSSProperties;
  label?: string;
}

const Icon = ({ icon, custom, disabled, confirmed, onClick, cursor, style = {}, label }: IconProps) => {
  const renderCustomIcon = () => {
    switch (icon) {
      case 'acquisition':
        return Acquisition;
      case 'ai':
        return AI;
      case 'archive':
        return Archive;
      case 'validation':
        return Validation;
      case 'export':
        return Export;
      case 'workflow':
        return Workflow;
      case 'border-all':
        if (confirmed) {
          return TableVerifiedIcon;
        } else {
          return TableNotVerifiedIcon;
        }
      case 'chevron':
        return Chevron;
      case 'exclamation-circle':
        if (confirmed) {
          return CheckedIcon;
        } else {
          return UncheckedIcon;
        }
      case 'exclamation-mark':
        return ExclamationMark;
      case 'warning':
        return Warning;
      case 'red-triangle':
        return RedTriangle;
      case 'green-circle':
        return GreenCircle;
      case 'red-circle':
        return RedCircle;
      case 'yellow-square':
        return YellowSquare;
      case 'block-circle':
        return BlockIcon;
      case 'already-assigned':
        return AlreaAssignedIcon;
      case 'yellow-circle':
        return YellowCircle;
      case 'priority-high':
        return PriorityHigh;
      case 'priority-medium':
        return PriorityMedium;
      case 'priority-low':
        return PriorityLow;
      case 'top-page':
        return TopPage;
      case 'critical-priority':
        return CriticalPriority;
      case 'duplicated-icon':
        return DuplicatedIcon;
      case 'paragraph-field':
        if (confirmed) {
          return ParagraphFieldConfirmed;
        } else {
          return ParagraphField;
        }

      default:
        return undefined;
    }
  };

  return (
    <IconContainer
      onClick={onClick}
      icon={icon}
      disabled={disabled ? true : false}
      confirmed={confirmed ? true : false}
      cursor={cursor}
      title={label}
    >
      {icon && custom ? (
        <img style={style} alt="custom-icon" src={renderCustomIcon()} />
      ) : (
        <FontAwesomeIcon style={{}} icon={icon as IconProp} />
      )}
    </IconContainer>
  );
};

export default Icon;
