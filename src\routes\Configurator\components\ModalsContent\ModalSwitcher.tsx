import React from 'react';
import { useSelector } from 'react-redux';
import selectors from 'state/selectors';
import { modalActionType } from 'utils/constants';
import SelectAP2Template from './modals/actionTable/SelectAP2Template';
import ClearConfirmationModal from './modals/ClearConfirmation';
import DeleteConfirmation from './modals/DeleteConfirmation';
import EditTemplateModal from './modals/mapper/EditTemplateModal';
import NewTemplate from './modals/mapper/NewTemplate';
import RemoveTemplateModal from './modals/mapper/RemoveTemplateModal';
import AddUserModal from './modals/rolesUserCompany/AddUserModal';
import CloneRoleModal from './modals/rolesUserCompany/CloneRoleModal';
import ConfigureRolesModal from './modals/rolesUserCompany/ConfigureRolesModal';
import ImportedDuplicateUsers from './modals/users/ImportedDuplicateUsers';
import SelectCompanies from './modals/actionTable/selectCompanies';
import SelectCompaniesNopo from './modals/actionTable/selectCompaniesNopo';
import GroupsUsersModal from './modals/workflow/DefTaskGroup/GroupsUsersModal';
import CloneWfTableModal from './modals/workflow/DefTaskGroup/CloneWfTableModal';
import GroupsUsersAssociationModal from './modals/workflow/Associations/GroupsUsersAssociationsModal';
import CloneAssociationModal from './modals/workflow/Associations/CloneAssociationModal';
import AddOrReplaceMassiveUser from './modals/workflow/DefTaskGroup/addReplaceMassiveUser';
import DefaultModal from 'components/Modal/DefaultModal';
// modals

const ModalsSwitcher = () => {
  const action = useSelector(selectors.modal.getActionType);

  switch (action) {
    // General
    case modalActionType.configurator.DELETE_CONFIRMATION:
      return <DeleteConfirmation />;
    case modalActionType.configurator.CLEAR_CONFIRMATION:
      return <ClearConfirmationModal />;
    // Users
    case modalActionType.configurator.IMPORTED_DUPLICATE_USERS:
      return <ImportedDuplicateUsers />;
    case modalActionType.configurator.NEW_TEMPLATE:
      return <NewTemplate />;
    case modalActionType.configurator.EDIT_TEMPLATE:
      return <EditTemplateModal />;
    case modalActionType.configurator.DELETE_TEMPLATE:
      return <RemoveTemplateModal />;
    // Roles
    case modalActionType.configurator.ADD_USER_MODAL:
      return <AddUserModal />;
    case modalActionType.configurator.CONFIGURE_ROLES_MODAL:
      return <ConfigureRolesModal />;
    case modalActionType.configurator.CLONE_ROLE_MODAL:
      return <CloneRoleModal />;
    // Action Table
    case modalActionType.configurator.SELECT_AP2_TEMPLATE:
      return <SelectAP2Template />;
    case modalActionType.configurator.SELECT_COMPANIES:
      return <SelectCompanies />;
    case modalActionType.configurator.SELECT_COMPANIES_NOPO:
      return <SelectCompaniesNopo />;
    // Workflow
    case modalActionType.configurator.GROUPS_USERS_MODAL:
      return <GroupsUsersModal />;
    case modalActionType.configurator.CLONE_WF_TABLE_MODAL:
      return <CloneWfTableModal />;
    case modalActionType.configurator.GROUPS_USERS_ASSOCIATIONS_MODAL:
      return <GroupsUsersAssociationModal />;
    case modalActionType.configurator.CLONE_WF_ASSOCIATION:
      return <CloneAssociationModal />;
    case modalActionType.configurator.ADD_REPLACE_MASSIVE_USER:
      return <AddOrReplaceMassiveUser />;
    case modalActionType.configurator.REMOVE_MASSIVE_USER:
        return <DefaultModal />;

    default:
      return null;
  }
};

export default ModalsSwitcher;
