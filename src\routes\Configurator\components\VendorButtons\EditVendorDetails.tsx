import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actionType } from 'utils/constants';

import utils from 'utils';
import actions from 'state/actions';
import selectors from 'state/selectors';

import CommonButton from 'components/Buttons/CommonButton';

const EditVendorDetails = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();

  const selectedCompany = useSelector(selectors.configurator.getSelectedCompanyVendorConfig);
  const companyName = selectedCompany?.label;
  const hasPermission = utils.user.isActionByCompanyActive(actionType.EDIT_VENDOR_DETAILS, companyName);

  return (
    <CommonButton
      action={() => {
        dispatch(actions.configurator.setIsEditedVendorDetail(true));
      }}
      scope="tertiary"
      value={t(actionType.EDIT_VENDOR_DETAILS)}
      icon="circle"
      disabled={!hasPermission}
    />
  );
};

export default EditVendorDetails;
