import React from 'react';
import styled from 'styled-components/macro';

const Wrapper = styled.div<{ disabled: boolean }>`
  border-bottom: 1px solid ${({ theme }) => theme.colorPalette.grey.grey5};
  display: flex;
  align-items: baseline;
  cursor: ${({ disabled }) => (disabled ? 'not-allowed' : 'pointer')};
`;

const StyledDragElement = styled.div<{ isActive?: boolean }>`
  transition: all 0.5s ease-in;
  width: 100%;
  user-select: none;
  margin-top: 5px;
  margin-left: 10px;
  border-bottom: 5px solid
    ${({ isActive, theme }) => (isActive ? theme.colorPalette.turquoise.normal : theme.colorPalette.grey.grey4)};
  padding: 5px 0px;
  text-align: left;
`;

const Text = styled.p`
  font-family: Roboto;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  line-height: 20px;
`;

export interface CardProps {
  name: string;
  index: number;
  isActive: boolean;
  onClick: () => void;
  disabled?: boolean;
}

const StaticCard = ({ name, index, isActive, onClick, disabled }: CardProps) => {
  return (
    <Wrapper
      disabled={disabled || false}
      onClick={() => {
        !disabled && onClick();
      }}
    >
      <Text>{index}</Text>
      <StyledDragElement isActive={isActive}>
        <Text>{name}</Text>
      </StyledDragElement>
    </Wrapper>
  );
};

export default StaticCard;
