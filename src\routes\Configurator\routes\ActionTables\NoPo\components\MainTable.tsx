/* eslint-disable max-lines */
import Table, { OriginalRow, TableColumn } from 'components/Table';
import { RowAccessor } from 'components/Table/interfaces';
import { CreateExpActionTable } from 'models/request';
import { GetNopoMicrocategoriesResponse, GetNopoVendorResponse } from 'models/response';
import React, { useCallback, useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import services from 'services';
import actions from 'state/actions';
import selectors from 'state/selectors';
import utils from 'utils';
import {
  checkMicrocategoryFunc,
  checkVendorListFunc,
  microcategoryNoPoColumns,
  nopoSchema,
  nopoSchemaNewRow,
  searchFunc,
  vendorNoPoColumns,
} from './utils';
import SearchBar from './SearchBar';
import axios from 'axios';
import { modalActionType } from 'utils/constants';
import { Checkbox } from 'components/Buttons';
import styled from 'styled-components/macro';
import _ from 'lodash';
import Modal from 'components/Modal';
import ModalsContent from '../../../../components/ModalsContent/ModalSwitcher';
import { CustomTitle, TablesContainer } from 'routes/Configurator/styles';

const Content = styled.div`
  padding-right: 15px;
`;

const MainTable = () => {
  const t = utils.intl.useTranslator();
  const selectedRow = useSelector(selectors.configurator.getNOPOSelectedRow);
  const wfCasuals = useSelector(selectors.configurator.getWfCasuals);
  const allSubjects = useSelector(selectors.configurator.getAllSubjects);
  const notModifiedRow = useSelector(selectors.configurator.getNOPONotModifiedRow);
  const wfTaskDefinitions = useSelector(selectors.configurator.getWfDefinitions);
  const companies = useSelector(selectors.app.getCompanies);
  const rows = useSelector(selectors.configurator.getNOPOLeftTableRows);
  const editedRowId = useSelector(selectors.configurator.getNOPOEditedRowId);
  const isModalVisible = useSelector(selectors.modal.getVisibility);
  const selectedTable = useSelector(selectors.configurator.selectTableToSearch);
  const saveValidationSchema =
    editedRowId === '-1' ? nopoSchemaNewRow(t, selectedTable) : nopoSchema(t, notModifiedRow);
  const ATCompanies = useSelector(selectors.configurator.getATcompanies);
  const dispatch = useDispatch();
  const allAuthorizedCompanies = companies
    .filter((company) => {
      if (company.idCompany === 0) return false;
      const action = company.actions.find((action) => action.type === 'AddExpAction');
      return action && action.active;
    })
    .map(({ name: label, idCompany: value }) => {
      return { label, value };
    });

  const selectedCompanies = useSelector(selectors.configurator.getSelectedCompanies);
  const microcategories = useSelector(selectors.configurator.getAllMicrocategories);
  const optionsValue = useSelector(selectors.configurator.getOptionValueDropdown);
  const selectedOptionValue = useSelector(selectors.configurator.getSelectedOptionValue);
  const isEditAP2Tempalte = useSelector(selectors.configurator.getIsEditAP2Template);
  const isAddP2Tempalte = useSelector(selectors.configurator.getIsAddAP2Template);

  const fluxes = services.useFluxes('NOPO');

  const clearState = useCallback(() => {
    dispatch(actions.configurator.setNoPoEditedRowId(undefined));
    dispatch(actions.configurator.setNoPoSelectedRow(null));
    dispatch(actions.configurator.setWfDefinitions([]));
    dispatch(actions.configurator.setAllSubject([]));
    dispatch(actions.configurator.setWfCasuals([]));
    dispatch(actions.configurator.setAllMicroCategories([]));
    dispatch(actions.app.setDisabledDropdown(false));
  }, [dispatch]);

  const closeModal = useCallback(async () => {
    if (notModifiedRow) {
      const currentRows = [...rows];
      const idx = currentRows.findIndex((r: any) => r?.id === notModifiedRow?.id);
      if (idx !== -1) {
        currentRows[idx] = { ...notModifiedRow };
        dispatch(
          actions.configurator.setNoPoLeftTableRows(
            currentRows as GetNopoVendorResponse[] | GetNopoMicrocategoriesResponse[],
          ),
        );
      }
    }
    clearState();
    dispatch(actions.modal.closeModal());
  }, [clearState, dispatch, notModifiedRow, rows]);

  const columns = useCallback(
    () =>
      selectedTable === 'microcategory'
        ? microcategoryNoPoColumns(
            allAuthorizedCompanies,
            microcategories, // microcategories,
            wfCasuals,
            wfTaskDefinitions,
            fluxes, // flussi
            editedRowId === '-1',
            t,
          )
        : vendorNoPoColumns(
            allAuthorizedCompanies,
            allSubjects,
            wfCasuals,
            wfTaskDefinitions,
            fluxes,
            editedRowId === '-1',
            t,
            dispatch,
          ),
    [
      wfCasuals,
      wfTaskDefinitions,
      allAuthorizedCompanies,
      editedRowId,
      allSubjects,
      t,
      dispatch,
      fluxes,
      selectedTable,
      microcategories,
    ],
  );
  const miniTableMicrocategoryColumns: TableColumn[] = useMemo(
    () => [
      {
        id: '1',
        accessor: 'supplierName',
        Header: t('supplierName'),
      },
      {
        id: '2',
        accessor: 'supplierCode',
        Header: t('supplierCode'),
      },
      {
        id: '4',
        accessor: 'supplierInExpActionTable',
        Header: t('supplierInExpActionTable'),
        // sub true false with check box
        Cell: ({ value }: { value: boolean }) => {
          return (
            <div style={{ display: 'flex', height: '100%' }}>
              <Checkbox isActive={value} isEditable={false} />
            </div>
          );
        },
      },
    ],
    [t],
  );

  const miniTableVendorColumns: TableColumn[] = useMemo(
    () => [
      {
        id: '1',
        accessor: 'microcategoryCode',
        Header: t('microcategoryCode'),
      },
      {
        id: '2',
        accessor: 'microcategoryDescription',
        Header: t('microcategoryDescription'),
      },
    ],
    [t],
  );

  React.useEffect(() => {
    return () => {
      clearState();
      dispatch(actions.configurator.setNoPoLeftTableRows([]));
    };
  }, [clearState, dispatch]);

  useEffect(() => {
    if (selectedTable === 'vendor' && selectedCompanies.length > 0 && rows.length > 0) {
      dispatch(actions.configurator.setVendorListAp2Template(rows as GetNopoVendorResponse[]));
    }
  }, [selectedCompanies, selectedTable, dispatch, rows]);

  const onSelection = (rows: GetNopoVendorResponse[] | GetNopoMicrocategoriesResponse[]) => {
    dispatch(actions.configurator.setNoPoSelectedRow(rows[0]));
  };

  const onSaveEditedRow = async (modifiedRow: OriginalRow) => {
    try {
      if (editedRowId === '-1') {
        const selectedOption = [...selectedOptionValue];

        if (selectedTable === 'microcategory') {
          const row = modifiedRow as GetNopoMicrocategoriesResponse;
          const chosenCasual = wfCasuals.find((casual) => casual.value === Number(row.wfCausal));
          // try {
          const chosenMicrocategory = microcategories.find(
            (microcategory) => microcategory.value === row.microcategoryDescription,
          );
          const chosenDefinition = wfTaskDefinitions.find(
            (definition) => definition.value === Number(row.workflowDefinition),
          );
          const chosenCompany = companies.find(
            (company) => company.idCompany === (row.companyName as unknown as number),
          );
          if (!chosenMicrocategory || !chosenCompany) {
            throw new Error('Missing data');
          }
          // new row to add
          const newRow: CreateExpActionTable = {
            companyName: chosenCompany.name,
            costCenterCode: '',
            vendorCode: '',
            vendorCategoryCode: chosenMicrocategory.value,
            fluxName: row.fluxName as string,
            wfName: chosenDefinition?.wfName ?? null,
            wfIdGroups: null,
            wfIdCausal: chosenCasual?.idWfCausal ?? null,
            wfIdTaskDefinition: null,
            wfIdWorkflowDefinition: chosenDefinition?.idDefinition ?? null,
            fullOcr: null,
            corporateApproval: false,
            wfNote: null,
          };
          // the id action to add to the row
          const { data: idAction } = await services.createExpActionTable(newRow);
          utils.app.notify('success', `${t('newActionTableRowCreated')} ${idAction}`);
          //  verify if there is the selected company is the state
          if (
            selectedCompanies.findIndex((company) => company.companyName === chosenCompany?.name) !== -1 &&
            chosenMicrocategory
          ) {
            // adding microcategory to redux state
            const newOptionValue = {
              value: chosenMicrocategory.value,
              label: `${chosenMicrocategory.value} - ${chosenMicrocategory.description}`,
            };

            dispatch(actions.configurator.setOptionValueDropdown([...optionsValue, newOptionValue]));

            selectedOption.push(newOptionValue);
          }
        }
        if (selectedTable === 'vendor') {
          // vendor table
          const typedRow = modifiedRow as GetNopoVendorResponse;
          const chosenCasual = wfCasuals.find((casual) => casual.value === Number(typedRow.wfCausal));
          const chosenDefinition = wfTaskDefinitions.find(
            (definition) => definition.value === Number(typedRow.workflowDefinition),
          );
          const chosenSubject = allSubjects.find((subject) => subject.supplierCode === typedRow.subjectName);
          const chosenCompany = companies.find(
            (company) => company.idCompany === (typedRow.companyName as unknown as number),
          );
          if (chosenCompany === undefined) {
            throw new Error(t('companyNotSelected'));
          }
          const newRow: CreateExpActionTable = {
            companyName: chosenCompany?.name,
            costCenterCode: '',
            vendorCode: typedRow.subjectName,
            vendorCategoryCode: '',
            fluxName: typedRow.fluxName,
            wfName: chosenDefinition?.wfName ?? null,
            wfIdGroups: null,
            wfIdCausal: chosenCasual?.idWfCausal ?? null,
            wfIdTaskDefinition: null,
            wfIdWorkflowDefinition: chosenDefinition?.idDefinition ?? null,
            fullOcr: null,
            corporateApproval: false,
            wfNote: null,
          };
          const { data } = await services.createExpActionTable(newRow);
          utils.app.notify('success', `${t('newActionTableRowCreated')} ${data}`);
          if (
            selectedCompanies.findIndex((company) => company.companyName === chosenCompany?.name) !== -1 &&
            chosenSubject
          ) {
            const newOptionValue = {
              value: typedRow.subjectName,
              label: `${typedRow.subjectName} - ${chosenSubject.label}`,
            };

            dispatch(actions.configurator.setOptionValueDropdown([...optionsValue, newOptionValue]));

            selectedOption.push(newOptionValue);
          }
        }

        dispatch(actions.configurator.setSelectedOptionValue(selectedOption));
        const checkMicroCategory = checkMicrocategoryFunc(selectedOption);
        const checkVendorList = checkVendorListFunc(selectedOption);
        const search = searchFunc(selectedTable, selectedCompanies, checkMicroCategory, dispatch, checkVendorList);
        search();
        clearState();
        dispatch(
          actions.configurator.setNoPoLeftTableRows(
            (rows as GetNopoMicrocategoriesResponse[]).filter((row) => row.id !== '-1'),
          ),
        );
        return;
      }
      // edited row
      dispatch(actions.configurator.setNoPoEditedRowId(undefined));
      dispatch(actions.configurator.setNoPoSelectedRow(null));
      dispatch(
        actions.modal.setModal({
          actionType: modalActionType.configurator.SELECT_COMPANIES_NOPO,
          props: { row: modifiedRow },
        }),
      );
    } catch (error) {
      utils.app.notify('fail', t('editActionNopoTableError'));
      clearState();
      dispatch(actions.configurator.setNoPoLeftTableRows((rows as any[]).filter((row) => row.id !== '-1')));
      if (axios.isAxiosError(error)) {
        utils.app.notify('fail', error.response?.data);
        console.error(error.response?.data);
        return;
      }
      console.error(error);
    }
  };

  const onEditedRowChange = async (row: OriginalRow, accessor?: RowAccessor) => {
    if (selectedTable === 'microcategory') {
      const currentRows: GetNopoMicrocategoriesResponse[] = [...(rows as GetNopoMicrocategoriesResponse[])];
      const index = currentRows.findIndex((r: any) => r?.id === editedRowId);
      if (index !== -1 && editedRowId !== undefined) {
        currentRows[index] = { ...currentRows[index], [accessor as string]: row[accessor as string] };
      }
      if (accessor === 'companyName') {
        // reset other fields
        currentRows[index] = {
          ...currentRows[index],
          workflowDefinition: '',
          wfCausal: '',
          microcategoryDescription: '',
          fluxName: '',
        };
        const chosenCompany = companies.find((company) => company.idCompany === row.companyName);
        if (!chosenCompany) return;
        const { data: microcategories } = await services.getAllMicrocategories(chosenCompany.idCompany);
        const { data: wfCasuals } = await services.getWfCasuals(chosenCompany.idCompany);
        const { data: wfTaskDefinitions } = await services.getWfDefinitions(chosenCompany.idCompany);
        dispatch(actions.configurator.setAllMicroCategories(microcategories));
        dispatch(actions.configurator.setWfCasuals(wfCasuals));
        dispatch(actions.configurator.setWfDefinitions(wfTaskDefinitions));
      }
      dispatch(actions.configurator.setNoPoLeftTableRows(currentRows));
    } else {
      // vendor
      const currentRows: GetNopoVendorResponse[] = [...(rows as GetNopoVendorResponse[])];
      const index = currentRows.findIndex((r: any) => r?.id === editedRowId);
      if (index !== -1 && editedRowId !== undefined) {
        currentRows[index] = { ...currentRows[index], [accessor as string]: row[accessor as string] };
      }
      if (accessor === 'companyName') {
        // reset other fields
        currentRows[index] = {
          ...currentRows[index],
          workflowDefinition: '',
          wfCausal: '',
          subjectName: '',
          fluxName: '',
        };
        const chosenCompany = companies.find((company) => company.idCompany === row.companyName);
        if (!chosenCompany) return;
        const { data: subjects } = await services.getAllSubject(chosenCompany.idCompany);
        const { data: wfCasuals } = await services.getWfCasuals(chosenCompany.idCompany);
        const { data: wfTaskDefinitions } = await services.getWfDefinitions(chosenCompany.idCompany);
        dispatch(actions.configurator.setWfCasuals(wfCasuals));
        dispatch(actions.configurator.setWfDefinitions(wfTaskDefinitions));
        dispatch(actions.configurator.setAllSubject(subjects));
      }
      dispatch(actions.configurator.setNoPoLeftTableRows(currentRows));
    }
  };

  const onCancel = async () => {
    // @note async is a workaround to avoid that the table is not updated when the user cancels
    clearState();
    const currentRows: GetNopoVendorResponse[] | GetNopoMicrocategoriesResponse[] = await _.cloneDeep(rows);
    if (notModifiedRow) {
      const idx = currentRows.findIndex((r: any) => r?.id === editedRowId);
      if (idx !== -1) {
        currentRows[idx] = notModifiedRow;
        dispatch(actions.configurator.setNoPoLeftTableRows(currentRows));
        dispatch(actions.configurator.setNopoNotModifiedRow(undefined));
      }
      return;
    }
    // new row
    const newRows = (currentRows as any[]).filter((row) => row.id !== '-1');
    dispatch(
      actions.configurator.setNoPoLeftTableRows(newRows as GetNopoVendorResponse[] | GetNopoMicrocategoriesResponse[]),
    );
  };
  return (
    <>
      <Modal id="nopo" onClose={closeModal} open={isModalVisible} isClosable={!isEditAP2Tempalte && !isAddP2Tempalte}>
        <ModalsContent />
      </Modal>
      {selectedCompanies.length > 0 && ATCompanies.length > 0 && <SearchBar />}
      {rows.length > 0  ? (
        <TablesContainer>
          <Content>
            <CustomTitle>{selectedTable === 'microcategory' ? t('microcategory') : t('vendor_code')}</CustomTitle>
            <Table
              hasToolbar
              columns={columns()}
              rows={rows}
              onSelection={onSelection}
              hasSelection
              isEditable
              editRowID={editedRowId}
              hasPagination
              hasResize
              hasSort
              hasFilter
              onSave={onSaveEditedRow}
              onSaveValidation={saveValidationSchema}
              onCancel={() => {
                onCancel();
              }}
              onEditRowChange={onEditedRowChange}
              initialSelection={editedRowId !== undefined ? [editedRowId] : undefined}
            />
          </Content>
          {selectedRow && selectedRow.id !== '-1' && (
            <Content>
              <CustomTitle>{selectedTable === 'microcategory' ? t('vendor_code') : t('microcategory')}</CustomTitle>
              <Table
                hasToolbar
                columns={selectedTable === 'microcategory' ? miniTableMicrocategoryColumns : miniTableVendorColumns}
                rows={
                  selectedTable === 'microcategory'
                    ? (selectedRow as GetNopoMicrocategoriesResponse).microcategoryDetails
                    : (selectedRow as GetNopoVendorResponse).vendorDetails
                }
                hasPagination
                hasResize
                hasSort
                hasFilter
              />
            </Content>
          )}
        </TablesContainer>
      ) : null}
    </>
  );
};
export default MainTable;
