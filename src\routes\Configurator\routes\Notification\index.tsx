import Modal from 'components/Modal';
import { configuratorNotificationsViews } from 'models/configurator';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import selectors from 'state/selectors';
import { Wrapper } from '../../styles';
import OpenWorkflowNotifications from './OpenWorkflow';
import OverdueNotifications from './Overdue';
import WorkflowReminder from './WorkflowReminder';
import ModalsContent from 'routes/Configurator/components/ModalsContent/ModalSwitcher';
import actions from 'state/actions';
import services from 'services';
import { useAsync } from 'react-use';

const Notification = () => {
  const activeView = useSelector(selectors.configurator.getNotificationActiveView);
  const isModalVisible = useSelector(selectors.modal.getVisibility);
  const activeTemplateId = useSelector(selectors.configurator.selectActiveTemplateId);

  const dispatch = useDispatch();

  const returnView = () => {
    if (activeView) {
      switch (activeView) {
        case configuratorNotificationsViews.OPEN_WORKFLOW: {
          return <OpenWorkflowNotifications />;
        }
        case configuratorNotificationsViews.WORKFLOW_REMINDER: {
          return <WorkflowReminder />;
        }
        case configuratorNotificationsViews.OVERDUE: {
          return <OverdueNotifications />;
        }
        default:
          return <OpenWorkflowNotifications />;
      }
    }
  };

  useAsync(async () => {
    const { data } = await services.getTemplatesMailNotification();
    dispatch(actions.configurator.setTemplatesNotificationList(data));
  }, [activeTemplateId]);

  return (
    <Wrapper>
      <Modal onClose={() => dispatch(actions.modal.closeModal())} open={isModalVisible}>
        <ModalsContent />
      </Modal>
      {returnView()}
    </Wrapper>
  );
};
export default Notification;
