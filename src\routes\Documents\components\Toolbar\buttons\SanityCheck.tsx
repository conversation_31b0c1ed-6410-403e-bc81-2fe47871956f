import { useFormikContext } from 'formik';
import { RootState } from 'models';
import { DocumentFieldsSectionField } from 'models/response';
import { FormValues, Q1Value } from 'models/documents';
import React from 'react';
import { useSelector } from 'react-redux';
import { useUpdateEffect } from 'react-use';
import services from 'services';
import selectors from 'state/selectors';
import utils from 'utils';
import { actionType } from 'utils/constants';
import _ from 'lodash';
import CommonButton from 'components/Buttons/CommonButton';
import useCheckBeforeAction from '../hooks';
import { lucyModuleKey } from 'utils/configs';

let previousdocumentFlatFields: DocumentFieldsSectionField[] = [];
const flagApplyRules = false;

const SanityCheck = () => {
  const documentFlatFields = useSelector(selectors.documents.selectDocumentFlatFields);
  const protocol = useSelector(selectors.documents.selectActiveDocumentID);
  const documentHeader = useSelector(selectors.documents.selectDocumentHeader);
  const records = useSelector(selectors.documents.selectAllActiveDocuments);
  const t = utils.intl.useTranslator();
  const autosave =
    useSelector((store: RootState) => selectors.app.getConfig(store, actionType.AUTOSAVE_DOCUMENT))?.val === 1;
  const { setStatus, submitCount } = useFormikContext<{ [key: string]: Q1Value | FormValues }>() || {};
  const saveDocument = utils.hooks.useSaveDocument(true);
  const { checkDocuments } = useCheckBeforeAction(actionType.SANITY_CHECK);
  const programCode = useSelector(selectors.app.getProgramCode);
  const getProgramCode = (idProgram: number) => {
    return lucyModuleKey[idProgram as keyof typeof lucyModuleKey] || '';
  };

  const applyRules = async () => {
    try {
      if (protocol && documentHeader?.actualDocumentType?.idDocType) {
        const lucyModule = getProgramCode(programCode);
        const { data } = await services.checkMandatoryFields({ protocol, rulesGroup: lucyModule });
        const status = data.fields.map((el: any) => ({ idField: el.idField, status: el.fieldStatus }));
        setStatus(status);
        if (data.docStatus === 1) {
          utils.app.notify('fail', data.docMessageStatus || t('error-submitting-the-document'), 10000);
        } else if (data.docStatus === 2 && data.docMessageStatus) {
          utils.app.notify('warning', data.docMessageStatus, 10000);
        }
        return data;
      }
    } catch (e) {
      console.error(e);
    }
  };

  useUpdateEffect(() => {
    submitCount > 0 && autosave && flagApplyRules && applyRules();
  }, [!_.isEqual(documentFlatFields, previousdocumentFlatFields)]);

  React.useEffect(() => {
    previousdocumentFlatFields = documentFlatFields;
  }, [documentFlatFields]);

  const handleApplyLogics = async () => {
    const docFree = await checkDocuments();
    if (!docFree) return;
    const val = await saveDocument();
    if (val === null || val === undefined) {
      return;
    }
    applyRules();
  };

  return (
    <CommonButton
      id={actionType.SANITY_CHECK}
      action={handleApplyLogics}
      scope="tertiary"
      value={t('sanity-check')}
      icon="circle"
      disabled={!utils.user.isActionEnabled(records, actionType.SANITY_CHECK)}
    />
  );
};

export default SanityCheck;
