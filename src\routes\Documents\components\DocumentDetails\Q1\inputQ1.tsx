/* eslint max-lines: 0 */
// FieldType = 0 / 21
import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useField, useFormikContext } from 'formik';

import utils from 'utils';
import actions from 'state/actions';
import service from 'services';
import selectors from 'state/selectors';

import { AIsuggestion, FieldGroup, FieldTriggerConfig } from 'models/response';
import { ActiveBox, Q1Value } from 'models/documents';

import {
  InputWrapper,
  InputsContainer,
  InputQ1Wrapper,
  InputLabel,
  CheckboxContainer,
  CheckboxLabel,
  InputQ1TextAreaWrapper,
  IconTextAreaWrapper,
} from './styles';

import DatePicker from 'components/Input/Datepicker';
import Icon from 'components/Input/Icon';
import SuggestionBox from 'components/SuggestionBox';
import Checkbox from 'components/Buttons/CheckboxButton';
import { actionType } from 'utils/constants';
import CurrencyInputType from './FieldType/CurrencyInputType';
import { UserDate, UserDecimalSep } from 'models/user';
import { RootState } from 'models';
import CommonButton from 'components/Buttons/CommonButton';
import MultiLinearInput from './FieldType/MultiLinearInput';
import NumberInputType from './FieldType/NumberInputType';
import TextInput from 'components/Input/TextInput';

import { useDecimalUpdater } from './List';
import { useExecuteActionQ1 } from 'utils/helpers/toolbar.helpers';
import { AutoResizeTextArea } from 'components/Input';

interface Props {
  label: string;
  name: string;
  readonly?: boolean;
  hasQ3?: boolean;
  fieldGroup?: FieldGroup | null;
  fieldInputType: number;
  protocol: number | null;
  idField: number;
  hidden?: boolean;
  dateFormat: UserDate;
  separator: UserDecimalSep;
  multi?: boolean;
  index?: number;
  removeAction?: () => void;
  addAction?: () => void;
  triggerConfig: FieldTriggerConfig | null;
}

const InputQ1 = (props: Props) => {
  const {
    name,
    label,
    readonly = false,
    hasQ3 = false,
    fieldInputType,
    protocol,
    idField,
    dateFormat,
    separator,
    fieldGroup,
    multi,
    index,
    removeAction,
    addAction,
    hidden = false,
    triggerConfig,
  } = props;
  useDecimalUpdater(name);
  const [field, , helpers] = useField<Q1Value | undefined>(name);
  const t = utils.intl.useTranslator();
  const { status } = useFormikContext();
  const [suggestions, setSuggestion] = useState<AIsuggestion[] | null>(null);
  const [showSuggestionsBox, setShowSuggestionsBox] = useState(false);
  const icon = hasQ3 ? 'border-all' : 'exclamation-circle';
  const dispatch = useDispatch();
  const inputRef = useRef<any>();
  const isAutoVIewInPageActive = utils.user.getConfig(actionType.AUTO_VIEW_IN_PAGE);
  const isQ3visible = useSelector(selectors.documents.selectShowBody);
  const config = useSelector((store: RootState) =>
    selectors.app.getConfig(store, actionType.CLEAR_ZERO_VALUE_Q1_CURRENCY_FIELD),
  );
  const records = useSelector(selectors.documents.selectAllActiveDocuments);
  const decimalScale = useSelector(selectors.documents.selectDocumentDecimal);

  const [companyName, setCompanyName] = useState<string | null>(null);
  const executeActionQ1 = useExecuteActionQ1(triggerConfig);

  useEffect(() => {
    setCompanyName(records.length ? records[0]['companyName'] : null);
  }, [records]);

  const showQ3 = () => {
    if (fieldGroup) {
      dispatch(actions.documents.setBodyColumns(fieldGroup.fieldColumn));
      dispatch(actions.documents.setBalanceField(fieldGroup.balanceField));
      dispatch(actions.documents.setHeaderNameQ3(fieldGroup.name));
      dispatch(actions.documents.setBodyName(name));
      dispatch(actions.documents.setShowBody(true));
      dispatch(actions.documents.setFieldGroupId(fieldGroup.idFieldGroup));
      dispatch(actions.documents.setPredictionsData(fieldGroup.predictionsValues));
    }
  };

  const onKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    try {
      if (e.shiftKey && e.key === 'Tab' && hasQ3 && isQ3visible) {
        e.preventDefault();
        const lastQ3Cell = document.querySelectorAll<HTMLInputElement>('#q3-last-cell input');
        if (lastQ3Cell.length) lastQ3Cell[0].click();
        return;
      }
      if (e.key === 'Tab' && hasQ3 && isQ3visible) {
        e.preventDefault();
        const firstQ3Cell = document.querySelectorAll<HTMLInputElement>('#q3-first-cell input');
        if (firstQ3Cell.length) firstQ3Cell[0].click();
      }
    } catch (e) {
      console.error(e);
    }
  };

  const onSuggestionSelection = (suggestion: AIsuggestion) => {
    if (field.value) {
      const suggestionValues = { ...suggestion };
      const arrayValues = [...field.value.content];
      suggestionValues.status = 3;
      arrayValues[multi && (index || index === 0) ? index : 0] = suggestionValues;
      helpers.setValue({
        ...field.value,
        content: arrayValues,
      });
      setShowSuggestionsBox(false);
      dispatch(actions.documents.setActiveBox(null));
    }
  };

  const getSuggestions = async () => {
    try {
      if (protocol) {
        const { data } = await service.getAIsuggestions(protocol, idField);
        return data;
      }
    } catch (err) {
      console.error(err);
    }
  };

  const getDefaultSuggestion = () => {
    let defaultSuggestion = '';
    if (field.value && typeof field.value.content === 'object') {
      const {
        confidence,
        boundingBox: { x1, x2, y1, y2 },
        pageNumber,
      } = field.value.content[multi && (index || index === 0) ? index : 0];
      // suggestion as an id in this way
      defaultSuggestion = `${confidence}-${pageNumber}-${x1},${y1}-${x2},${y2}`;
    }
    return defaultSuggestion;
  };

  const defaultSuggestion = React.useMemo(getDefaultSuggestion, [field.value]);

  const setActiveBox = (value: ActiveBox | null) => {
    dispatch(actions.documents.setActiveBox(value));
  };

  const onIconClick = async () => {
    if (readonly && !hasQ3) return;

    dispatch(actions.documents.setActiveBox(null));

    // if suggestions already visible close it
    if (showSuggestionsBox) {
      return setShowSuggestionsBox(false);
    }
    dispatch(actions.documents.setAiSuggestions([]));

    const data = await getSuggestions();

    if (data?.length) {
      setSuggestion(data);
      dispatch(actions.documents.setAiSuggestions(data));
    } else {
      !hasQ3 && utils.app.notify('warning', t('no suggestions'), 5000, 'no-suggestions');
    }

    if (hasQ3 && fieldGroup) {
      showQ3();
    } else {
      setShowSuggestionsBox(true);
    }
  };

  const autoViewInPage = () => {
    try {
      if (!isAutoVIewInPageActive) return;
      if (!field.value) {
        utils.app.notify('warning', t('value is missing'));
        return;
      }
      dispatch(actions.documents.setActiveBox(null));
      if ((field.value.content[0].pageNumber ?? -1) !== -1) {
        const activeBox = {
          ...field.value.content[index ?? 0].boundingBox,
          pageNumber: field.value.content[index ?? 0].pageNumber,
        };
        dispatch(actions.documents.setActiveBox(activeBox));
      }
    } catch (e) {
      console.error(e);
    }
  };

  const renderInput = (fieldInputType: number) => {
    const disabledCheck = companyName
      ? !utils.user.isActionByCompanyActive(actionType.CAN_EDIT_Q3, companyName) || (readonly && !hasQ3)
      : readonly && !hasQ3;
    if (!field.value) return null;
    const InputIcon = (
      <Icon
        disabled={disabledCheck}
        icon={icon}
        custom={true}
        onClick={() => {
          !disabledCheck && onIconClick();
        }}
        confirmed={field.value.content[index ?? 0].status === 3}
      />
    );
    if (!name || !field) return <p>input not valid</p>;
    switch (fieldInputType) {
      // DATE
      case 1: {
        // default value if available
        const dateValue: Date | null =
          multi && (index || index === 0)
            ? field.value.content[index].content
              ? utils.date.convertToObjectDate(field.value.content[index].content)
              : null
            : field.value.content[0].content
            ? utils.date.convertToObjectDate(field.value.content[0].content)
            : null;

        const handleDateChange = (date: Date | null) => {
          if (field.value) {
            const formatDate = date ? utils.date.formatDateForDb(date) : '';
            const arrayValues = [...field.value.content];
            arrayValues[multi && (index || index === 0) ? index : 0] = {
              ...field.value.content[multi && (index || index === 0) ? index : 0],
              content: formatDate,
              status: 0,
            };
            helpers.setValue({
              ...field.value,
              content: arrayValues,
            });
            if (triggerConfig) executeActionQ1();
          }
        };

        const handleDateBlur = (e: React.ChangeEvent<HTMLInputElement>) => {
          // check if value is not empty
          if (e.target.value.length && field.value) {
            // Object date from datePicker before focus
            const date: Date | undefined = inputRef.current?.state?.preSelection;
            const formatDate = date && date instanceof Date ? utils.date.formatDateForDb(date) : '';
            const arrayValues = [...field.value.content];
            arrayValues[multi && (index || index === 0) ? index : 0] = {
              ...field.value.content[multi && (index || index === 0) ? index : 0],
              content: formatDate,
              status: 3,
            };
            helpers.setValue({
              ...field.value,
              content: arrayValues,
            });
          }
        };

        // This function prevents the infinite focus loop in calendar popper on 'tab' press
        const onKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
          if (e.key === 'Tab') inputRef.current.setOpen(false);
        };

        const onFocus = () => {
          autoViewInPage();
          field.value && dispatch(actions.documents.setFocussedQ1({ fieldInputType, name, multi, index }));
        };
        return (
          <>
            <InputQ1Wrapper multi={multi} margin="0 0 0 -4px">
              <InputLabel title={label}>{!multi ? label : ''}</InputLabel>
              <DatePicker
                feedbackColor={utils.input.getInputStatusColor(idField, status)}
                hasFeedback={Boolean(utils.input.getInputStatusColor(idField, status))}
                feedbackMessage={utils.input.getInputStatusMessage(idField, status)}
                ref={inputRef}
                onBlur={handleDateBlur}
                selected={dateValue}
                dateFormat={dateFormat}
                disabled={readonly}
                onFocus={onFocus}
                onChange={handleDateChange}
                portal
                shouldCloseOnSelect={false}
                onKeyDown={onKeyDown}
                inputWidth={multi ? '125px' : '165px'}
              />
            </InputQ1Wrapper>
            {InputIcon}
          </>
        );
      }
      // TEXT
      case 0: {
        let value = '';
        try {
          value = multi && (index || index === 0) ? field.value.content[index].content : field.value.content[0].content;
        } catch (e) {
          console.error(e);
          utils.app.notify(
            'warning',
            `Field ${name}:${label} received with a non valid value`,
            5000,
            'non-valid-value',
          );
        }

        const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
          if (field.value) {
            const arrayValues = [...field.value.content];

            arrayValues[multi && (index || index === 0) ? index : 0] = {
              ...field.value.content[multi && (index || index === 0) ? index : 0],
              content: e && e.target.value ? e.target.value : '',
              boundingBox: { x1: -1, x2: -1, y1: -1, y2: -1 },
              pageNumber: -1,
              confidence: -1,
              status: 0,
            };
            helpers.setValue({
              ...field.value,
              content: arrayValues,
            });
          }
        };

        const handleBlur = (e: React.ChangeEvent<HTMLInputElement>) => {
          if (e.target.value && field.value) {
            const arrayValues = [...field.value.content];
            arrayValues[multi && (index || index === 0) ? index : 0] = {
              ...field.value.content[multi && (index || index === 0) ? index : 0],
              content: e.target.value,
              status: 3,
            };
            helpers.setValue({
              ...field.value,
              content: arrayValues,
            });
          }
        };

        const onFocus = () => {
          autoViewInPage();
          field.value &&
            dispatch(
              actions.documents.setFocussedQ1({
                fieldInputType,
                name,
                multi,
                index,
              }),
            );
        };

        return (
          <>
            <InputQ1Wrapper multi={multi} margin={multi ? '0 0 0 -4px' : '0'}>
              <InputLabel title={label}>{!multi ? label : ''}</InputLabel>
              {multi ? (
                <MultiLinearInput
                  feedbackColor={utils.input.getInputStatusColor(idField, status)}
                  hasFeedback={Boolean(utils.input.getInputStatusColor(idField, status))}
                  feedbackMessage={utils.input.getInputStatusMessage(idField, status)}
                  disabled={readonly}
                  value={value}
                  name={name}
                  onFocus={onFocus}
                  onKeyDown={onKeyDown}
                  onBlur={(e) => handleBlur(e)}
                  onChange={handleChange}
                  width="125px"
                  margin="0px"
                  borderRadius="4px"
                  typeOfField="text"
                  index={index}
                  values={[...field.value.content]}
                  field={field}
                  helpers={helpers}
                />
              ) : (
                <TextInput
                  feedbackColor={utils.input.getInputStatusColor(idField, status)}
                  hasFeedback={Boolean(utils.input.getInputStatusColor(idField, status))}
                  feedbackMessage={utils.input.getInputStatusMessage(idField, status)}
                  disabled={readonly}
                  value={value}
                  name={name}
                  onFocus={onFocus}
                  onKeyDown={onKeyDown}
                  onBlur={(e) => handleBlur(e)}
                  onChange={handleChange}
                  width="165px"
                  margin="0px"
                  borderRadius="4px"
                />
              )}
            </InputQ1Wrapper>
            {InputIcon}
          </>
        );
      }
      // NUMBER;
      case 2: {
        const inputIndex = multi ? index || 0 : 0;
        const value =
          multi && (index || index === 0) ? field.value.content[index].content : field.value.content[0].content;

        // no needs to use utils.intl.formatNumber since
        // value is an integer here
        const handleChange = (value: number | undefined) => {
          if (field.value) {
            const arrayValues = [...field.value.content];
            arrayValues[multi ? index ?? 0 : 0] = {
              ...field.value.content[multi && (index || index === 0) ? index : 0],
              content: value ? `${value}` : '',
              confidence: -1,
              status: 0,
            };
            helpers.setValue({
              ...field.value,
              content: arrayValues,
            });
          }
        };

        const handleBlur = (value: number | undefined) => {
          if (value && field.value) {
            const values: Q1Value = {
              ...field.value,
            };
            values.content[inputIndex] = {
              ...values.content[inputIndex],
              status: 3,
            };
            helpers.setValue(values);
          }
        };

        const onFocus = () => {
          autoViewInPage();
          field.value && dispatch(actions.documents.setFocussedQ1({ fieldInputType, name, multi, index }));
        };

        const handleChangeMultiLinear = (e: React.ChangeEvent<HTMLInputElement>) => {
          if (field.value) {
            const arrayValues = [...field.value.content];

            arrayValues[multi && (index || index === 0) ? index : 0] = {
              ...field.value.content[multi && (index || index === 0) ? index : 0],
              content: e && e.target.value ? e.target.value : '',
              boundingBox: { x1: -1, x2: -1, y1: -1, y2: -1 },
              pageNumber: -1,
              confidence: -1,
              status: 0,
            };
            helpers.setValue({
              ...field.value,
              content: arrayValues,
            });
          }
        };

        const handleBlurMultiLinear = (e: React.ChangeEvent<HTMLInputElement>) => {
          if (e.target.value && field.value) {
            const arrayValues = [...field.value.content];
            arrayValues[multi && (index || index === 0) ? index : 0] = {
              ...field.value.content[multi && (index || index === 0) ? index : 0],
              content: e.target.value,
              status: 3,
            };
            helpers.setValue({
              ...field.value,
              content: arrayValues,
            });
          }
        };

        return (
          <>
            {multi ? (
              <>
                <InputQ1Wrapper multi={multi} margin="0 0 0 -4px">
                  <MultiLinearInput
                    feedbackColor={utils.input.getInputStatusColor(idField, status)}
                    hasFeedback={Boolean(utils.input.getInputStatusColor(idField, status))}
                    feedbackMessage={utils.input.getInputStatusMessage(idField, status)}
                    disabled={readonly}
                    value={value}
                    name={name}
                    onFocus={onFocus}
                    onKeyDown={onKeyDown}
                    onBlur={(e) => handleBlurMultiLinear(e)}
                    onChange={handleChangeMultiLinear}
                    width={'125px'}
                    margin="0px"
                    borderRadius="4px"
                    typeOfField="number"
                    index={index}
                    values={[...field.value.content]}
                    field={field}
                    helpers={helpers}
                  />
                </InputQ1Wrapper>
                {InputIcon}
              </>
            ) : (
              <InputQ1Wrapper multi={multi} margin="0px 6px 0 0">
                <NumberInputType
                  feedbackColor={utils.input.getInputStatusColor(idField, status)}
                  hasFeedback={Boolean(utils.input.getInputStatusColor(idField, status))}
                  feedbackMessage={utils.input.getInputStatusMessage(idField, status)}
                  decimalScale={0}
                  onFocus={onFocus}
                  value={utils.intl.convertToNumber(value)}
                  readonly={readonly}
                  onChange={handleChange}
                  onBlur={handleBlur}
                  onKeyDown={onKeyDown}
                  label={label}
                  multi={multi}
                  decimalSeparator={separator}
                  width="165px"
                  display="flex"
                  margin="0 4px 0 0"
                />
                {InputIcon}
              </InputQ1Wrapper>
            )}
          </>
        );
      }
      // currency (number)
      case 3: {
        const fieldValue =
          multi && (index || index === 0) ? field.value.content[index].content : field.value.content[0].content;

        const handleChange = (value: number | undefined) => {
          if (field.value) {
            const content = value === undefined ? '' : utils.intl.formatNumber(value, decimalScale, ',');
            const arrayValues = [...field.value.content];
            arrayValues[multi && (index || index === 0) ? index : 0] = {
              ...field.value.content[multi && (index || index === 0) ? index : 0],
              content,
              pageNumber: -1,
              status: 0,
            };
            helpers.setValue({
              ...field.value,
              content: arrayValues,
            });
          }
        };

        const handleBlur = (value: number | undefined) => {
          if (field.value) {
            const content =
              config.val === 1 && value === 0
                ? ''
                : value !== undefined
                ? utils.intl.formatNumber(value, decimalScale, ',')
                : '';
            const arrayValues = [...field.value.content];
            arrayValues[multi && (index || index === 0) ? index : 0] = {
              ...field.value.content[multi && (index || index === 0) ? index : 0],
              content: content,
              status: 3,
            };
            helpers.setValue({
              ...field.value,
              content: arrayValues,
            });
          }
        };

        const onFocus = () => {
          autoViewInPage();
          field.value && dispatch(actions.documents.setFocussedQ1({ fieldInputType, name, multi, index }));
        };

        return (
          <InputQ1Wrapper multi={multi} margin={multi ? '0px 6px 0 18px' : '0 5px'}>
            <CurrencyInputType
              feedbackColor={utils.input.getInputStatusColor(idField, status)}
              hasFeedback={Boolean(utils.input.getInputStatusColor(idField, status))}
              feedbackMessage={utils.input.getInputStatusMessage(idField, status)}
              onFocus={onFocus}
              value={utils.intl.convertToNumber(fieldValue)}
              readonly={readonly}
              onChange={handleChange}
              onBlur={handleBlur}
              label={label}
              multi={multi}
              decimalSeparator={separator}
              decimalScale={decimalScale}
              width={multi ? '125px' : '165px'}
              margin={multi ? '0 5px' : '0 4px 0 0'}
            />
            {InputIcon}
          </InputQ1Wrapper>
        );
      }
      // CHECK
      case 4: {
        let value = '';
        try {
          value = multi && (index || index === 0) ? field.value.content[index].content : field.value.content[0].content;
        } catch (e) {
          console.error(e);
          utils.app.notify(
            'warning',
            `Field ${name}:${label} received with a non valid value`,
            5000,
            'non-valid-value',
          );
        }

        const handleChange = () => {
          if (field.value) {
            const arrayValues = [...field.value.content];
            arrayValues[multi && (index || index === 0) ? index : 0] = {
              ...field.value.content[multi && (index || index === 0) ? index : 0],
              content: value === 'true' ? 'false' : 'true',
            };
            helpers.setValue({
              ...field.value,
              content: arrayValues,
            });
          }
        };

        return (
          <CheckboxContainer>
            <CheckboxLabel>{!multi ? label : ''}</CheckboxLabel>
            <Checkbox
              statusColor={utils.input.getInputStatusColor(idField, status)}
              feedbackMessage={utils.input.getInputStatusMessage(idField, status)}
              name={name}
              isActive={value ? JSON.parse(value) : false}
              action={handleChange}
            />
          </CheckboxContainer>
        );
      }
      // TEXT AREA
      case 5: {
        let value = '';
        try {
          value = multi && (index || index === 0) ? field.value.content[index].content : field.value.content[0].content;
        } catch (e) {
          console.error(e);
          utils.app.notify(
            'warning',
            `Field ${name}:${label} received with a non valid value`,
            5000,
            'non-valid-value',
          );
        }

        const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
          if (field.value) {
            const arrayValues = [...field.value.content];

            arrayValues[multi && (index || index === 0) ? index : 0] = {
              ...field.value.content[multi && (index || index === 0) ? index : 0],
              content: e && e.target.value ? e.target.value : '',
              boundingBox: { x1: -1, x2: -1, y1: -1, y2: -1 },
              pageNumber: -1,
              confidence: -1,
              status: 0,
            };
            helpers.setValue({
              ...field.value,
              content: arrayValues,
            });
          }
        };

        const handleBlur = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
          if (e.target.value && field.value) {
            const arrayValues = [...field.value.content];
            arrayValues[multi && (index || index === 0) ? index : 0] = {
              ...field.value.content[multi && (index || index === 0) ? index : 0],
              content: e.target.value,
              status: 3,
            };
            helpers.setValue({
              ...field.value,
              content: arrayValues,
            });
          }
        };

        const onFocus = () => {
          autoViewInPage();
          field.value &&
            dispatch(
              actions.documents.setFocussedQ1({
                fieldInputType,
                name,
                multi,
                index,
              }),
            );
        };

        return (
          <>
            <InputQ1TextAreaWrapper multi={multi} margin={multi ? '0 0 0 -4px' : '0'}>
              <InputLabel title={label} style={{ paddingTop: '7px' }}>
                {label}
              </InputLabel>
              <AutoResizeTextArea
                feedbackColor={utils.input.getInputStatusColor(idField, status)}
                hasFeedBack={Boolean(utils.input.getInputStatusColor(idField, status))}
                feedBackMessage={utils.input.getInputStatusMessage(idField, status)}
                value={value}
                onChange={handleChange}
                onBlur={(e) => handleBlur(e)}
                onFocus={onFocus}
                name={name}
              />
            </InputQ1TextAreaWrapper>
            <IconTextAreaWrapper>
              <Icon
                disabled={disabledCheck}
                custom={true}
                icon="paragraph-field"
                onClick={() => {
                  !disabledCheck && onIconClick();
                }}
                confirmed={field.value.content[index ?? 0].status === 3}
              />
            </IconTextAreaWrapper>
          </>
        );
      }
      default:
        return <p>input not valid</p>;
    }
  };
  return field.value ? (
    <InputWrapper>
      <InputsContainer hidden={hidden} multi={multi}>
        {multi && removeAction && (
          <CommonButton
            tooltip={t('add-input')}
            scope="secondary"
            action={removeAction}
            icon="minus"
            disabled={readonly}
          />
        )}
        {multi && addAction && (
          <CommonButton tooltip={t('add-input')} scope="secondary" action={addAction} icon="plus" disabled={readonly} />
        )}
        {renderInput(fieldInputType)}
      </InputsContainer>
      {suggestions?.length && showSuggestionsBox && !hasQ3 && !hidden ? (
        <SuggestionBox
          defaultValue={defaultSuggestion}
          suggestions={suggestions}
          onSelection={onSuggestionSelection}
          setActiveBox={setActiveBox}
          marginLeft={multi ? '-124px' : '0px'}
        />
      ) : null}
    </InputWrapper>
  ) : null;
};

export default InputQ1;
