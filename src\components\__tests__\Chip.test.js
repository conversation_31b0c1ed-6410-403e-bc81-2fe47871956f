import React from 'react';
import Chip from '../Chip/Chip';
import '@testing-library/jest-dom';
import { createSnapshot, renderWithStyle, snapshotConfig } from 'utils/helpers/test.helpers';
import { toMatchImageSnapshot } from 'jest-image-snapshot';
import { fireEvent, screen } from '@testing-library/react';

expect.extend({ toMatchImageSnapshot });

describe('Chip Component', () => {
  test('renders without crash', () => {
    renderWithStyle(<Chip text="banana" />);
  });

  test('matches snapshot', async () => {
    jest.setTimeout(30000);
    const image = await createSnapshot(<Chip text="placeholder" />, { heigth: 25 });
    expect(image).toMatchImageSnapshot(snapshotConfig);
  });

  test('excutes action clicking on x icon', () => {
    const action = jest.fn();
    renderWithStyle(<Chip text="banana" onClick={action} />);
    const X = screen.getByText('X');
    expect(action).not.toBeCalled();
    fireEvent.click(X);
    expect(action).toBeCalled();
  });
});
