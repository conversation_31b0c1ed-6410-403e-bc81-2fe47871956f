import utils from 'utils';
import { prefixes, prefixesNameList } from 'utils/constants';

export const suspendTypes = (prefix: prefixesNameList) => {
  const t = utils.intl.useTranslator();
  const currentPrefix = prefixes[prefix];
  return [
    {
      value: 0,
      label: t(`${currentPrefix}.suspendTypes.0`),
    },
    {
      value: 1,
      label: t(`${currentPrefix}.suspendTypes.1`),
    },
    {
      value: 2,
      label: t(`${currentPrefix}.suspendTypes.2`),
    },
  ];
};
