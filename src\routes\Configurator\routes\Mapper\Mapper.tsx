/* eslint-disable max-lines */
import React, { useCallback, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import selectors from 'state/selectors';
import Pdf from 'components/Pdf/newPdf';
import actions from 'state/actions';
import Table from 'components/Table';
import styled from 'styled-components/macro';
import intlHelper from 'utils/helpers/intl.helper';
import ErrorBoundary from 'components/ErrorBoundary';
import ModalsSwitcher from '../../components/ModalsContent/ModalSwitcher';
import Modal from 'components/Modal';
import {
  columns,
  flattenArray,
  addNewField,
  editField,
  rowSchema,
  SubRowType,
  SubRowTypeColumn,
  voidParam,
} from './utils';
import { useAsync } from 'react-use';
import services from 'services';
import _ from 'lodash';
import { EditRow, AddRow } from 'models/request';
import { Area } from 'models/configurator';
import { OriginalRow } from 'components/Table/interfaces';
import utils from 'utils';

const Box = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: center;
  width: 100%;
  height: 100%;
  overflow: auto;
  margin-top: 20px;
  gap: 20px;
  flex-wrap: wrap;
`;
const Item = styled.div`
  /* margin: 0 10px; */
  overflow: hidden;
`;

const Mapper = () => {
  const dispatch = useDispatch();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const t = intlHelper.useTranslator();
  const closeModal = () => dispatch(actions.modal.closeModal());
  const isModalVisible = useSelector(selectors.modal.getVisibility);
  const templateID = useSelector(selectors.configurator.selectActiveOTemplateId);
  const mapperFields = useSelector(selectors.configurator.selectMapperFields);
  const mapperEditRowID = useSelector(selectors.configurator.selectMapperEditRowId);
  const mapperSelectedRow = useSelector(selectors.configurator.selectMapperSingleRowOrSubRow);
  const [mapperEditRow, setMapperEditRow] = useState<Area | undefined>(undefined);
  const areas = useSelector(selectors.configurator.selectMapperAreas);
  const selectCurrentPdfPage = useSelector(selectors.configurator.selectCurrentPdfPage);
  const fieldNameTypes = useSelector(selectors.configurator.selectFieldNames);
  const rows = useSelector(selectors.configurator.selectMapperRows);
  const allColumns = useCallback(
    () => columns(fieldNameTypes, mapperEditRowID === '-1'),
    [fieldNameTypes, mapperEditRowID],
  );
  const [PdfContent, setPDF] = useState('');
  const idFieldIdColumnFromRows = useSelector(selectors.configurator.selectIdFieldIdColumnFromRows);

  useAsync(async () => {
    try {
      if (templateID !== null) {
        const response = await services.getTemplatePdf(templateID);
        setPDF(response.data.pdfBase64);
      }
    } catch (e) {
      console.error(e);
      utils.app.notify('fail', e as string);
    }
  }, [templateID]);

  // here we load all available templates grouped by DocType
  useAsync(async () => {
    try {
      const { data } = await services.getTemplatesWithDocType();
      const sortedData = _.sortBy(data, (val) => val.docName);
      dispatch(actions.configurator.setDocTypes(sortedData));
    } catch (error) {
      console.error(error);
    }
  }, [dispatch]);

  const fetchFieldsData = async () => {
    try {
      if (templateID === null) return;
      const { data } = await services.getTemplateFields(templateID);
      dispatch(actions.configurator.setMapperFields(data));
      dispatch(actions.configurator.setMapperRows([]));
      dispatch(actions.configurator.setMapperRows(flattenArray(data)));
      dispatch(actions.configurator.setMapperEditRowId(''));
      dispatch(actions.configurator.setMapperSelectedRowId(''));
    } catch (error) {
      console.error(error);
    }
  };

  // Every time the user changes the selected template (templateId dependency)
  // we can load new fields
  useAsync(fetchFieldsData, [templateID]);

  const onSelection = async (selection: any[]) => {
    if (!selection.length) {
      mapperEditRowID === '' && dispatch(actions.configurator.setMapperAreas([]));
      dispatch(actions.configurator.setMapperSelectedRowId(''));
      return;
    }
    dispatch(actions.configurator.setMapperSelectedRowId(`${selection[0].id}`));
    const {
      x1,
      x2,
      y1,
      y2,
      id = '-1',
      page,
    }: {
      x1: number | voidParam;
      x2: number | voidParam;
      y1: number | voidParam;
      y2: number | voidParam;
      id: string;
      page: number;
    } = selection[0];
    const idNum = Number(id);
    if (typeof x1 !== 'number' || typeof x2 !== 'number' || typeof y1 !== 'number' || typeof y2 !== 'number') {
      dispatch(actions.configurator.setMapperAreas([]));
      return;
    }
    dispatch(
      actions.configurator.setMapperAreas([
        {
          x1: x1 / 100,
          x2: x2 / 100,
          y1: y1 / 100,
          y2: y2 / 100,
          id: idNum,
          pageNumber: page,
        },
      ]),
    );
    dispatch(actions.configurator.setCurrentPdfPage(page));
  };

  const onCancel = () => {
    const templateRowId = mapperEditRowID;
    dispatch(actions.configurator.setMapperEditRowId(''));
    dispatch(actions.configurator.setMapperSelectedRowId(''));
    if (templateRowId && templateRowId === '-1') {
      dispatch(actions.configurator.setMapperRows(rows.slice(1)));
      setMapperEditRow(undefined);
    }
  };

  // function triggered when the user change the active rectangle directly on PDF
  const onAreaChange = (area: Area) => {
    const { x1, x2, y1, y2 } = area;
    setMapperEditRow({
      ...area,
      x1: x1 * 100,
      x2: x2 * 100,
      y1: y1 * 100,
      y2: y2 * 100,
    });
  };

  const onSave = async (row: OriginalRow) => {
    try {
      const { id, x1, x2, y1, y2, content, name } = row as {
        id: number;
        x1: number;
        x2: number;
        y1: number;
        y2: number;
        content: string;
        inputType: number;
        type: number;
        name: string;
      };
      // taking all old params of the old row and adding the new ones
      if (templateID !== null) {
        if (id === -1) {
          const fieldType = fieldNameTypes.find((field) => field.label === name);
          if (fieldType) {
            const { idField, idColumn } = fieldType;
            const rowToSave: AddRow = {
              idColumn,
              idField,
              idTemplate: templateID,
              content,
              x1,
              x2,
              y1,
              y2,
              page: selectCurrentPdfPage,
            };
            await addNewField([rowToSave], t, fetchFieldsData, onCancel);
            return;
          }
          throw new Error('Field type not found');
        }
        // EDIT CASE
        const { rowNumber, idTemplate, page } = mapperSelectedRow as SubRowType | SubRowTypeColumn;
        const { idField, idColumn } = idFieldIdColumnFromRows;
        // xor operator
        if (idField !== null ? idColumn === null : idColumn !== null) {
          const rowToSave: EditRow = {
            idColumn,
            idField,
            idTemplate,
            content,
            x1,
            x2,
            y1,
            y2,
            page,
            rowNumber,
          };
          await editField([rowToSave], t, fetchFieldsData, onCancel);
          return;
        }
        throw new Error('idField and idColumn are both null or both not null');
      }
    } catch (error) {
      console.error(error);
      utils.app.notify('fail', t(`${error as string}`));
    } finally {
      setMapperEditRow(undefined);
    }
  };

  const PdfMemo = React.useMemo(() => {
    return (
      <Pdf
        base64={PdfContent}
        mapperProps={{
          onAreaChange: onAreaChange,
          isSelected: mapperSelectedRow !== undefined,
          isEditing: mapperEditRowID !== '',
        }}
        showFitButtons={false}
        showOcrButtons={false}
        showRotateButtons={false}
        showZoomButtons={false}
      />
    );
  }, [ PdfContent, mapperEditRowID, mapperSelectedRow]);

  const pdfContent = PdfContent ? PdfMemo : null;

  const onEditRowChange = (row: any) => {
    const { x1 = 0, y1 = 0, x2 = 0, y2 = 0 } = row;
    dispatch(
      actions.configurator.setMapperAreas([
        {
          ...areas[0],
          pageNumber: areas[0].pageNumber,
          x1: x1 === '' ? 0 : x1 / 100,
          x2: x2 === '' ? 0 : x2 / 100,
          y1: y1 === '' ? 0 : y1 / 100,
          y2: y2 === '' ? 0 : y2 / 100,
        },
      ]),
    );
  };

  const MemoTable = React.useMemo(
    () => (
      <Table
        columns={allColumns()}
        rows={rows}
        isEditable
        hasSelection
        editRowID={mapperEditRowID}
        editRow={mapperEditRow}
        onSelection={onSelection}
        onEditRowChange={onEditRowChange}
        onSave={onSave}
        onCancel={onCancel}
        useExpandibleRows
        hasFilter
        hasPagination
        pageSize={20}
        hasToolbar
        hasSort
        onSaveValidation={rowSchema(mapperEditRowID === '-1')}
        initialSelection={mapperEditRowID ? [Number(mapperEditRowID)] : []}
      />
    ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [allColumns, rows, mapperEditRowID, mapperEditRow],
  );

  return (
    <>
      <Modal onClose={closeModal} open={isModalVisible}>
        <ModalsSwitcher />
      </Modal>
      {templateID !== null && mapperFields && (
        <ErrorBoundary>
          <Box>
            <Item>{MemoTable}</Item>
            <Item>{pdfContent}</Item>
          </Box>
        </ErrorBoundary>
      )}
    </>
  );
};
export default Mapper;
