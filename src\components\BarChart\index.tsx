import React, { useRef } from 'react';
import { Bar } from 'react-chartjs-2';
import { useTheme } from 'providers/ThemeProvider';

type Value = { x: string; y: number | string };

export interface BarChartProps {
  datasets: {
    data: Value[];
    label: string;
    name?: string;
  }[];
  percentage?: boolean;
  colors?: string[];
}

const BarChart = ({ datasets, percentage, colors }: BarChartProps) => {
  const chartRef = useRef<any>(null);
  const { theme } = useTheme();
  const isDarkMode = theme.colorPalette.isDarkMode;

  const data = {
    datasets: datasets.map((dataset, index) => {
      return {
        ...dataset,
        backgroundColor:
          colors?.[index] || (isDarkMode ? theme.colorPalette.turquoise.light : theme.colorPalette.turquoise.normal),
      };
    }),
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    barThickness: 17,
    borderWidth: 2,
    borderColor: 'transparent',
    borderRadius: {
      topLeft: 4,
      topRight: 4,
    },

    scales: {
      x: {
        grid: {
          display: false,
          color: isDarkMode ? theme.colorPalette.grey.grey4 : theme.colorPalette.grey.grey3,
        },
        ticks: {
          font: {
            size: 11,
            family: 'Roboto',
          },
          color: isDarkMode ? theme.colorPalette.grey.grey9 : theme.colorPalette.grey.grey7,
        },
      },
      y: {
        max: percentage ? 100 : null,
        grid: {
          color: isDarkMode ? theme.colorPalette.grey.grey4 : theme.colorPalette.grey.grey3,
        },
        ticks: {
          callback: (value: number | string) => {
            return percentage ? value + ' %' : value;
          },
          font: {
            size: 9,
            family: 'Roboto',
            weight: 'lighter',
          },
          color: isDarkMode ? theme.colorPalette.grey.grey9 : theme.colorPalette.grey.grey7,
        },
      },
    },
    plugins: {
      legend: {
        align: 'end',
        labels: {
          boxWidth: 20,
          boxHeight: 10,
          padding: 15,
          font: {
            size: 13,
            family: 'Roboto',
            weight: 'lighter',
          },
          color: isDarkMode ? theme.colorPalette.grey.grey9 : theme.colorPalette.grey.grey7,
        },
      },
      title: {
        display: false,
      },
      tooltip: {
        callbacks: {
          title: () => '',
          label: (tooltipItem: { raw: { y: number | string } }) => {
            const value = tooltipItem.raw.y;
            return percentage ? value + ' %' : value;
          },
        },
        backgroundColor: isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.white,
        titleColor: isDarkMode ? theme.colorPalette.grey.grey9 : theme.colorPalette.turquoise.dark,
        bodyColor: isDarkMode ? theme.colorPalette.grey.grey9 : theme.colorPalette.turquoise.dark,
        displayColors: false,
        bodyFont: {
          size: 12,
          weight: 400,
        },
        caretPadding: 10,
        borderColor: isDarkMode ? theme.colorPalette.grey.grey4 : theme.colorPalette.grey.grey3,
        borderWidth: 1,
        padding: 7,
      },
    },
  };

  return (
    <Bar ref={chartRef} data={data} options={options as any} />
  );
};

export default BarChart;
