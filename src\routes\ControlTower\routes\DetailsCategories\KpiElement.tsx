import React from 'react';
import { useHistory, useRouteMatch } from 'react-router';
import KpiItem from 'components/KpiItem';
import Tooltip from '../../../../components/Tooltip';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { KpiWrapper, KpiHeading, KpiTitle, Info, KpiTitleLink, ExportButtonWrapper, IconsWrapper } from './style';
import utils from 'utils';
import { URLS } from 'utils/constants';
import { KpiElement as KpiElementProps } from 'routes/ControlTower/interfaces';
import { SEMAPHORE_COLORS } from '.';
import styled from 'styled-components';

const HeadingContainer = styled.div`
  padding-bottom: 10px;
`;

export const KpiElement = ({ kpi, categoryId, exportReport, drillDownProtocols }: KpiElementProps) => {
  const history = useHistory();
  const t = utils.intl.useTranslator();

  // @note we need to understand how to retreive this param
  const idGeoList = null;
  const { path } = useRouteMatch();

  const {
    name,
    semaphore,
    description,
    lowerRange,
    higherRange,
    formatType,
    result,
    timeframePeriod,
    tooltip,
    idCharts,
    idReport,
    idMetric,
  } = kpi;

  const units = {
    PERCENTAGE: '%',
    TIMEFRAME: utils.app.textTranslated(timeframePeriod, t) || '',
  };

  const unit = units[formatType as keyof typeof units];
  const rangeDescription =
    (lowerRange || lowerRange === 0) && higherRange
      ? `${t('range')} ${lowerRange}${unit || ''} - ${higherRange}${unit || ''}`
      : '';

  const openChartPage = (idCharts: number | number[]) => {
    history.push({
      pathname: `${path}/${URLS.controlTower.chart}`,
      state: {
        idCharts,
        categoryId,
      },
    });
  };

  return (
    <KpiWrapper>
      <HeadingContainer>
        <KpiHeading>
          {idCharts && !(Array.isArray(idCharts) && idCharts.length === 0) ? (
            <KpiTitleLink onClick={() => openChartPage(idCharts)}>{name}</KpiTitleLink>
          ) : (
            <KpiTitle>{name}</KpiTitle>
          )}
          <IconsWrapper>
            {tooltip && (
              <Tooltip text={tooltip}>
                <Info />
              </Tooltip>
            )}
            {idReport && (
              <Tooltip text={utils.app.textTranslated('Export report', t)}>
                <ExportButtonWrapper onClick={() => exportReport(idReport)}>
                  <FontAwesomeIcon size="sm" icon="arrow-down" />
                </ExportButtonWrapper>
              </Tooltip>
            )}
            {idReport && (
              <Tooltip text={utils.app.textTranslated('Link to documents', t)}>
                <ExportButtonWrapper onClick={() => drillDownProtocols(idMetric, idGeoList)}>
                  <FontAwesomeIcon size="sm" icon="arrow-up" />
                </ExportButtonWrapper>
              </Tooltip>
            )}
          </IconsWrapper>
        </KpiHeading>
      </HeadingContainer>
      <KpiItem
        variant="dot"
        dotColor={SEMAPHORE_COLORS[semaphore as keyof typeof SEMAPHORE_COLORS]}
        description={rangeDescription || description}
        unit={unit}
        value={result}
      />
    </KpiWrapper>
  );
};
