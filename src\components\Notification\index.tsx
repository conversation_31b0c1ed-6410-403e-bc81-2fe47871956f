import React from 'react';
import styled from 'styled-components/macro';

const StyledNotificationWrapper = styled.div<{ type?: string }>`
  display: flex;
  align-items: center;
  min-width: 100px;
  max-width: 450px;
  padding: 15px 25px;
  border-radius: 25px;
  border: 1px solid ${({ theme }) => theme.colorPalette.grey.medium};
  box-shadow: 0px 0px 6px 0px rgba(50, 50, 50, 0.15);
  cursor: pointer;
  color: ${({ theme }) => theme.colorPalette.white};
  ${({ type, theme }) => {
    switch (type) {
      case 'success':
        return `
            background-color: ${theme.colorPalette.green.medium};
          `;
      case 'fail':
        return `
            background-color: ${theme.colorPalette.red.medium};
          `;
      case 'warning':
        return `
            background-color: ${theme.colorPalette.orange.medium};
          `;
      default:
        break;
    }
  }}

  > p {
    color: ${({ theme }) => theme.colorPalette.white};
  }
`;

const Message = styled.div`
  display: block;
  font-size: 13px;
  line-height: 17px;
  margin-right: 10px;
`;

const Close = styled.p`
  margin-left: 10px;
`;
const CountBadge = styled.span`
  position: absolute;
  top: 10px;
  right: 10px;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background-color: ${({ theme }) => theme.colorPalette.white};
  color: ${({ theme }) => theme.colorPalette.black};
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
`;

export interface Props {
  message: string;
  type: 'success' | 'fail' | 'warning';
  hasClose?: boolean;
  count?: number;
}

const title: { [key: string]: string } = {
  success: 'Success',
  warning: 'Warning',
  fail: 'Fail',
};

const Notification = (props: Props) => {
  const { message, type, hasClose, count } = props;
  return (
    <StyledNotificationWrapper type={type} data-testid="notification">
      {(count ?? 0) > 1 && <CountBadge>{count}</CountBadge>}
      <Message>
        <b>{title[type]}</b>
        <br />
        <span dangerouslySetInnerHTML={{ __html: message && message.length ? message : '' }} />
      </Message>
      {hasClose && <Close>| X</Close>}
    </StyledNotificationWrapper>
  );
};

export default Notification;
