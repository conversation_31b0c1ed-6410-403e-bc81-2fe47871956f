import React from 'react';

import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import { definitionTaskGroupTabs, modalActionType } from 'utils/constants';
import selectors from 'state/selectors';
import { actionType } from 'utils/constants';

const Clone = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);

  const selectedDefinitionTaskGroup = useSelector(selectors.configurator.getSelectedDefinitionTaskGroup);
  const selectedDefinitionFromCompanySelected = useSelector(
    selectors.configurator.getSelectedDefinitionFromCompanySelected,
  );
  const isWfDefinitionInEdit = useSelector(selectors.configurator.getIsWfTableEdit);
  const selectedGroupFromCompanySelected = useSelector(selectors.configurator.getSelectedGroupFromCompanySelected);

  const checkDisabledButton = () => {
    switch (selectedDefinitionTaskGroup) {
      case definitionTaskGroupTabs.WORKFLOW_DEFINITION:
        if (
          selectedDefinitionFromCompanySelected === null ||
          isWfDefinitionInEdit ||
          !utils.user.isActionByCompanyActive(actionType.CLONE_WF_DEFINITION, companiesDefinition?.companyName)
        )
          return true;
        break;
      case definitionTaskGroupTabs.WORKFLOW_GROUP:
        if (
          selectedGroupFromCompanySelected === null ||
          isWfDefinitionInEdit ||
          !utils.user.isActionByCompanyActive(actionType.CLONE_WF_GROUP, companiesDefinition?.companyName)
        )
          return true;
        break;

      default:
        return false;
    }
  };

  return (
    <CommonButton
      action={() => {
        dispatch(
          actions.modal.setModal({
            actionType: modalActionType.configurator.CLONE_WF_TABLE_MODAL,
          }),
        );
      }}
      scope="tertiary"
      value={t('clone')}
      icon="circle"
      disabled={checkDisabledButton()}
    />
  );
};

export default Clone;
