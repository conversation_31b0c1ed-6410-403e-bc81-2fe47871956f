import { FontWeightPalette } from 'models/style';
import styled from 'styled-components/macro';

export const fontWeightPalette: FontWeightPalette = {
  light: 300,
  regular: 400,
  medium: 500,
  bold: 700,
};

type fontWeightPaletteType = keyof typeof fontWeightPalette;

interface TdProps {
  fontWeight: fontWeightPaletteType;
}


export const TableBorder = styled.table`
  border-collapse: separate;
  border-spacing: 0;
`;

export const RowBorder = styled.tr``;

export const StyledTableTHead = styled.thead`
  background-color: ${({ theme }) => theme.colorPalette.grey.grey4};
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-size: ${({ theme }) => theme.fontSizePalette.small};
  font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  width: 473px;
  height: 30px;
  tr:first-child th:first-child {
    border-top-left-radius: 10px;
  }
  tr:first-child th:last-child {
    border-top-right-radius: 10px;
  }
`;

export const StyledTableTitle = styled.th`
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-size: ${({ theme }) => theme.fontSizePalette.small};
  font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  line-height: 16px;
  letter-spacing: -0.4375px;
  line-height: 2;
  width: 473px;
`;

export const Td = styled.td<TdProps>`
  font-size: ${({ theme }) => theme.fontSizePalette.small};
  font-weight: ${({ fontWeight }) => fontWeightPalette[fontWeight as fontWeightPaletteType]};
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  line-height: 16px;
  padding: 10px 10px 10px 20px;
  border: 1px ${({ theme }) => theme.colorPalette.grey.grey5};
  border-style: none solid solid none;
  padding: 10px;
`;



export const Tbody = styled.tbody`
  tr:last-child td:first-child {
    border-bottom-left-radius: 5px;
  }
  tr:last-child td:last-child {
    border-bottom-right-radius: 5px;
  }

  tr:first-child td {
    border-top-style: solid;
  }
  tr td:first-child {
    border-left-style: solid;
    border-right-style: none;
  }
`;

export const RowTr = styled.tr``;
