import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import utils from 'utils';
import actions from 'state/actions';
import { actionType } from 'utils/constants';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';

const AddOverdueNotification = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();

  const selectedRow = useSelector(selectors.configurator.getSelectedOverdueSettings);
  const formMode = useSelector(selectors.configurator.getFormModeOverdue);

  return (
    <CommonButton
      action={() => dispatch(actions.configurator.setFormModeOverdue('add'))}
      scope="tertiary"
      value={t(actionType.ADD_OVERDUE_NOTIFICATION)}
      icon="circle"
      disabled={!utils.user.isActionByCompanyActive(actionType.ADD_OVERDUE_NOTIFICATION) || !!selectedRow || !!formMode}
    />
  );
};

export default AddOverdueNotification;
