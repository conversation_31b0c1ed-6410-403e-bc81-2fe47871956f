import { DateError } from 'utils/helpers/errors.helpers';

import { UserDate } from 'models/user';
import selectors from 'state/selectors';
import store from 'state/store';
import { defaultUserDateFormat } from 'utils/constants';
import moment from 'moment-timezone';
import { useSelector } from 'react-redux';
// check is a valid Date object
const isValidDate = (date: Date) => date instanceof Date && !isNaN(date.getTime());

/**
 * Convert date by user's preference format and timezone
 * @param { Date | string | number } date - date to be formatted
 * @param { string } timezone - timezone for the formatted date
 * @param { string } dateUserFormat - date format
 * @return { string } date formatted by user's preference and timezone
 */
const convertDate = (date: Date | string | number, timezone?: string, dateUserFormat?: UserDate): string => {
  if (!date) return '';
  const { dateFormat = defaultUserDateFormat, timeZone  } = selectors.user.getUserPreference(store.getState());
  const currentTimeZone = timezone ?? timeZone ?? 'UTC';
  const format = (dateUserFormat || dateFormat).toUpperCase();

  if (typeof date === 'number') {
    return date ? moment.utc(date, 'x').tz(currentTimeZone).format(`${format}, HH:mm:ss`) : '';
  }

  if (typeof date === 'string') {
    const dateExtended = moment(date, 'MMM D, YYYY h:mm:ss A', true).isValid();
    if (dateExtended) {
      return moment.utc(date, 'MMM DD, YYYY h:mm:ss A').tz(currentTimeZone).format(`${format} h:mm:ss A`) || '';
    }
    return moment.utc(date, 'DD-MM-YYYY').tz(currentTimeZone).format(format) || '';
  }

  // dd/mm/yyyy
  if (isValidDate(date)) {
    return moment.utc(date).tz(currentTimeZone).format(format);
  }
  return '';
};

/**
 * Convert date by user's preference format and timezone
 * @return { dateConverter } dateConverter - function to convert date
 * @returns { string } dateConverter - date formatted by user's preference and timezone
 * @param { Date | string | number } date - date to be formatted
 * @example
 * const { dateConverter } = useConvertDate();
 * const date = dateConverter(new Date());
 */

const useConvertDate = () => {
  const userPreference = useSelector(selectors.user.getUserPreference);
  const converter = (date: Date | string | number) =>
    convertDate(date, userPreference.timeZone, userPreference.dateFormat);
  return { dateConverter: converter };
};
/**
 * Convert date from DD-MM-YYYY format to Object Date
 * @param {string} date DD-MM-YYYY date to be converted
 * @return { Date | never } Object Datee
 */
const convertToObjectDate = (date: string): Date | never => {
  const momentDate = moment(date, 'DD-MM-YYYY');
  if (typeof date !== 'string' || !momentDate.isValid()) throw new DateError(`Invalid date format received: ${date}`);
  return momentDate.toDate();
};

/*
  questa funzione converte date nel formato string 'dd-mm-yyyy' o
  oggetti Data in stringe di formato 'dd-mm-yyyy'
*/
const formatDateForDb = (date: string | Date): string | never => {
  if (typeof date === 'string') {
    try {
      convertToObjectDate(date);
      return date;
    } catch (e) {
      throw new DateError(`Invalid date format received: ${date}`);
    }
  }
  if (date instanceof Date && isValidDate(date)) {
    let month = '' + (date.getMonth() + 1);
    let day = '' + date.getDate();
    const year = date.getFullYear();
    if (month.length < 2) month = '0' + month;
    if (day.length < 2) day = '0' + day;
    return [day, month, year].join('-');
  } else {
    throw new DateError(`Invalid date format received: ${date}`);
  }
};

const convertDateReverse = (date: string) => {
  if (date) {
    const dateArray = date.split('-');
    const year = dateArray[2];
    const month = dateArray[1];
    const day = dateArray[0];
    return [year, month, day].join('/');
  }
};

const convertDdMmYyyyToMmDdYyyy = (date: string) => {
  if (date) {
    const dateArray = date.split('-');
    const year = dateArray[2];
    const month = dateArray[1];
    const day = dateArray[0];
    return [month, day, year].join('/');
  }
};

/**
 * function used to filter rows using date in epoch format
 * @param {string} timeZone timezone to be used to convert date
 * @param {boolean} isString if the date is passed as a string
 * @param {any[]} rows all table rows to be filtered
 * @param {string[]} columnIds expected in the default lucy DD-MM-YYYY format
 * @param {string} filterValue expected in the default lucy DD-MM-YYYY format
 * @return {any[] } filtered rows
 */
const epochFilterFunction =
  (timeZone: string, isString?: boolean) =>
  (rows: any[], columnIds: string[], filterValue: string): any[] => {
    try {
      if (filterValue === null || filterValue === undefined || filterValue === '') {
        return rows;
      }
      if (columnIds.length !== 1) throw new Error('epochFilterFunction can only be used with one column');
      const colId = columnIds[0]; // id of the column
      // independent of the type of date format i'll convert to same date format to avoid problems
      const filterValueToUserFormat = convertDate(filterValue, timeZone, 'yyyy-MM-dd') as string;
      if (filterValueToUserFormat === 'Invalid date')
        throw new DateError(`Invalid date format received: ${filterValue}`);
      // filter  each row by epoch time transformed in time string
      const ros = rows.filter((row) => {
        if (!row.values) throw new Error('impossibile filtrare');
        const { values } = row;
        // transform epoch time in time string, delete the time part and compare the date part
        const rowValueStringNoTime = new Date(Number(values[colId])).toLocaleString().split(',')[0];
        const valueToUserFormat = convertDate(
          isString ? values[colId] : rowValueStringNoTime,
          timeZone,
          'yyyy-MM-dd',
        ) as string;
        return valueToUserFormat === filterValueToUserFormat;
      });
      return ros;
    } catch (error) {
      console.error(error);
      return rows;
    }
  };

const getFirstDayOfPreviousMonth = (date: Date) => {
  if (isValidDate(date)) {
    // Set the date to the first day of the current month
    date.setDate(1);
    // Subtract a day to go back to the last day of the previous month
    date.setDate(0);
    // Get the first day of the previous month
    const firstDayOfPreviousMonth = new Date(date.getFullYear(), date.getMonth(), 1);

    return firstDayOfPreviousMonth;
  }
};

const matchesUserDateFormat = (contentValue: string) => {
  // Regex for dd/mm/yyyy
  const longDateRegex = /^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/(\d{4})$/;
  // Regex for dd/mm/yy
  const shortDateRegex = /^(0[1-9]|[12][0-9]|3[01])\/(0[1-9]|1[0-2])\/(\d{2})$/;

  // Check if contentValue is in the format dd/mm/yy
  if (shortDateRegex.test(contentValue)) {
    const [day, month, yearShort] = contentValue.split('/');
    const yearLong = `20${yearShort}`; // Convert to 4-digit year
    contentValue = `${day}/${month}/${yearLong}`; // Convert to dd/mm/yyyy
  }

  // Check if contentValue matches the format dd/mm/yyyy
  return longDateRegex.test(contentValue);
};

const convertDateStringToISO = (date: string) => {
  const [day, month, year] = date.split('/');
  const yearLong = year.length === 2 ? `20${year}` : year; // Check if the year is in yy format
  const isoFormattedDate = `${yearLong}-${month}-${day}`;
  return new Date(isoFormattedDate);
};

const convertSelectedDateFromPrediction = (date: string) => {
  const newDate = new Date(date);
  if (newDate) {
    if (isValidDate(newDate)) return formatDateForDb(newDate);
  }
};

export default {
  convertDate,
  formatDateForDb,
  convertToObjectDate,
  convertDateReverse,
  convertDdMmYyyyToMmDdYyyy,
  isValidDate,
  epochFilterFunction,
  useConvertDate,
  getFirstDayOfPreviousMonth,
  matchesUserDateFormat,
  convertDateStringToISO,
  convertSelectedDateFromPrediction,
};
