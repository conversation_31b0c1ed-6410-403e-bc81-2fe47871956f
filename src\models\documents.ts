import {
  AIsuggestion,
  BoundingBox,
  DocumentFields,
  DocumentHeaderResponse,
  FieldGroupColumn,
  TableColumn,
  Template,
  FieldInputType,
  GetEngineInfoResponse,
  FieldTriggerConfig,
} from './response';

import { DataGridRow } from 'components/DataGrid';
import { Row } from 'models/table';
import { TableState } from 'react-table';
import { IFilterValue } from './request';
export type focussedCell = { rowId: number | string; columnId: number; type: FieldType } | null;
export interface EditDocuments {
  balanceField: string | null;
  columnsBody: FieldGroupColumn[] | null;
  nameBody: string | null;
  documentFields: DocumentFields | null;
  documentDecimal: number;
  documentHeader: DocumentHeaderResponse | null;
  showBody: boolean;
  focussedQ3Cell: { rowId: number | string; columnId: number; type: FieldType } | null;
  headerName: string | null;
  fieldGroupId: number | null;
  isDocReadOnly: boolean;
  ocr: {
    q1: {
      fieldInputType: FieldType;
      name: string;
      multi?: boolean;
      index?: number;
    };
  };
  predictionsData: PredictionColumn[];
  formattedPredictionsData: DataGridRow[];
  selectedRowPrediction: DataGridRow | null;
}

type IsJiesRunningTypes = 'green' | 'yellow' | 'red';

export interface DocumentsState {
  edit: EditDocuments;
  activeTemplate: number | null;
  templates: Template[];
  activeDocumentId: number[];
  documentsTable: DocumentsTable;
  documentImage: string;
  documentImageId: number;
  importedDocData: string | ArrayBuffer | null;
  layout: Layout;
  aiSuggestions: AIsuggestion[];
  activeBox: ActiveBox | null;
  isSearchFilterVisible: boolean;
  documentSearchFormValue: IFilterValue[];
  isJiesRunning: IsJiesRunningTypes;
  engineInfo: GetEngineInfoResponse | null;
}

export interface ActiveBox extends BoundingBox {
  pageNumber: number;
}

export interface DocumentsTable {
  isAllAuth: boolean;
  columns: TableColumn['columnsList'];
  rows: Row[];
  filters: TableState['filters'];
  sortBy: TableState['sortBy'];
  pageSize: number;
  fixedColumns: number;
  currentPage: number;
  filteredDocument: Row[];
}

export interface Q1Value {
  content: AIsuggestion[];
  body: DataGridRow[] | null;
  fieldType: number;
  predictionsValues: PredictionColumn[] | null;
  triggerConfig: FieldTriggerConfig;
}

export interface DocTypeValue {
  label: string;
  value: number;
  option: boolean;
}

export interface CompanyValue {
  label: string;
  value: number;
}

export interface MovTypeValue {
  label: string;
  value: string;
  changed?: boolean;
}

export interface SubjectValue {
  name: string;
  id: number;
}

// 0 untouched
// 1 error
// 2 warning
// 3 ok
export type FieldStatus = 0 | 1 | 2 | 3;

// 0 string
// 1 date
// 2 int
// 3 float
// 4 boolean
export type FieldType = FieldInputType;
export type FormValueType = Q1Value | DocTypeValue | CompanyValue | MovTypeValue | SubjectValue | string | null;

export interface FormValues {
  [key: string]: FormValueType;
  documentTypeHeader: DocTypeValue | null;
  companyHeader: CompanyValue | null;
  movTypeHeader: MovTypeValue | null;
  subjectHeader: { name: string; id: number } | null;
  supplierCode: string | null;
  isChildOf: string;
  vatNumber: string | null;
}

export type Layout = 'rows' | 'full' | 'columns' | 'three';

export interface PredictionContent {
  content: string;
  confidence: number;
  idxNumber: number;
}

export interface PredictionColumn {
  [key: string]: {
    idColumn: number;
    rowNumber: number;
    idFieldGroup: number;
    fixedName: string;
    predictionContentList: PredictionContent[];
  };
}

export interface FieldGroupColumnNew {
  code: number;
  title: string;
  type: 'text' | 'select';
  predictionData: PredictionColumn[];
  translate?: string | null;
  width: number;
}
