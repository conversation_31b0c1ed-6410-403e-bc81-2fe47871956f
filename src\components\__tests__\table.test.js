import React from 'react';
import Table from '../Table';
import { renderWithStyle } from 'utils/helpers/test.helpers';
const columns = [{ accessor: 'id', Header: 'ID' }];
const rows = [{ id: 0 }, { id: 1 }];

describe('Table Component', () => {
  test('renders without crash', () => {
    renderWithStyle(<Table columns={columns} rows={rows} />);
  });

  test('has two rows', () => {
    renderWithStyle(<Table columns={columns} rows={rows} />);
    expect(document.querySelectorAll('tbody tr').length).toEqual(2);
  });

  test('has pagination section', () => {
    renderWithStyle(<Table columns={columns} rows={rows} hasPagination />);
    expect(document.querySelectorAll('.table-pagination').length).toBe(1);
  });

  test('has a row selected at mount', () => {
    renderWithStyle(<Table columns={columns} rows={rows} hasSelection initialSelection={[0]} />);
    expect(document.querySelectorAll('tbody tr.row-selected').length).toEqual(1);
  });

  test('s selection functionality works', () => {
    renderWithStyle(<Table columns={columns} rows={rows} hasSelection />);
    expect(document.querySelectorAll('tbody tr.row-selected').length).toEqual(0);
    const firstRow = document.querySelector('tbody tr');
    firstRow.dispatchEvent(new MouseEvent('click', { bubbles: true }));
    expect(document.querySelectorAll('tbody tr.row-selected').length).toEqual(1);
  });

  test('has the rigth amount of rows with a different row\'s id property', () => {
    const rows = [{ codice: 0 }, { codice: 1 }, { codice: 2 }];
    renderWithStyle(<Table columns={columns} rows={rows} />);
    expect(document.querySelectorAll('tbody tr').length).toEqual(3);
  });
});
