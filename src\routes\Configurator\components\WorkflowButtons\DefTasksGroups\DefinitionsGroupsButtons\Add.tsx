import React, { useEffect } from 'react';

import utils from 'utils';
import CommonButton from 'components/Buttons/CommonButton';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import selectors from 'state/selectors';
import { definitionTaskGroupTabs } from 'utils/constants';
import services from 'services';
import { mutate } from 'swr';
import { actionType } from 'utils/constants';

const Add = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const companiesDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);

  // WF DEFINITION
  const listOfDefinitions = useSelector(selectors.configurator.getListOfDefinitionsForCompanySelected);
  const isWfTableInEdit = useSelector(selectors.configurator.getIsWfTableEdit);
  const selectedDefinitionTaskGroup = useSelector(selectors.configurator.getSelectedDefinitionTaskGroup);

  const addRowWfDefinition = () => {
    const row = {
      idDefinition: -1,
      id: -1,
      companyName: '',
      wfName: '',
      suspendType: null,
      noteMandatory: false,
      currencyExchange: false,
      takeNextSingleTask: false,
      taskOnMinCausalWeight: false,
      taskList: [],
    };

    dispatch(actions.configurator.setListOfDefinitionsForCompanySelected([row, ...listOfDefinitions]));
    dispatch(actions.configurator.setEditedRowIdWfDefinition(-1));
    dispatch(actions.configurator.setIsWfTableEdit(true));
  };

  // WF GROUPS
  const selectedCompanyDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  const { groups } = services.useGetCompanyGroups('getCompanyGroupsKey', selectedCompanyDefinition?.idCompany);

  const addRowWfGroup = async () => {
    if (selectedCompanyDefinition?.idCompany) {
      const { data } = await services.getUsersForGroup(selectedCompanyDefinition.idCompany);
      dispatch(actions.configurator.setListOfUserForGroup(data));
      const row = {
        idGroup: -1,
        groupName: '',
        groupDesc: '',
        oUserList: [],
      };
      mutate('getCompanyGroupsKey', [row, ...(groups || [])], {
        revalidate: false,
      });
      dispatch(actions.configurator.setEditedRowIdWfGroup(-1));
      dispatch(actions.configurator.setSelectedGroupFromCompanySelected(null));
      dispatch(actions.configurator.setIsWfTableEdit(true));
    }
  };

  const addRow = () => {
    switch (selectedDefinitionTaskGroup) {
      case definitionTaskGroupTabs.WORKFLOW_DEFINITION:
        addRowWfDefinition();
        break;
      case definitionTaskGroupTabs.WORKFLOW_GROUP:
        addRowWfGroup();
        break;
    }
  };

  const checkDisabledButton = () => {
    switch (selectedDefinitionTaskGroup) {
      case definitionTaskGroupTabs.WORKFLOW_DEFINITION:
        if (
          isWfTableInEdit ||
          !utils.user.isActionByCompanyActive(actionType.ADD_WF_DEFINITION, companiesDefinition?.companyName)
        )
          return true;
        break;
      case definitionTaskGroupTabs.WORKFLOW_GROUP:
        if (
          isWfTableInEdit ||
          !utils.user.isActionByCompanyActive(actionType.ADD_WF_GROUP, companiesDefinition?.companyName)
        )
          return true;
        break;
      default:
        return false;
    }
  };

  useEffect(() => {
    checkDisabledButton();
  });

  return (
    <CommonButton
      action={() => {
        addRow();
      }}
      scope="tertiary"
      value={t('add')}
      icon="circle"
      disabled={checkDisabledButton()}
    />
  );
};

export default Add;
