import React, { useCallback, useMemo } from 'react';
import { useSelector } from 'react-redux';

import selectors from 'state/selectors';

import Table from 'components/Table';

type RowsType = {
  id: string;
  companyName: string;
  subRows: { id: string; companyName: string; roleName: string; subRows: never[] }[];
  roleName: string;
};
const RolesTable = () => {
  const roles = useSelector(selectors.configurator.getUserRoles);
  const rows = useCallback((): RowsType[] => {
    const result: RowsType[] = [];
    let counter = 1;
    roles.forEach((role) => {
      if (role.oRolesList.length === 1) {
        result.push({
          id: `${counter++}`,
          companyName: role.name + ' - ' + role.socialReason,
          roleName: role.oRolesList[0].roleName,
          subRows: [],
        });
        return;
      }
      result.push({
        id: `${counter++}`,
        companyName: role.name + ' - ' + role.socialReason,
        roleName: '',
        subRows: role.oRolesList.map((subrole) => ({
          id: `${counter++}`,
          companyName: role.name + ' - ' + role.socialReason,
          roleName: subrole.roleName,
          subRows: [],
        })),
      });
    });
    return result;
  }, [roles]);

  const columns = useMemo(
    () => [
      {
        id: '0',
        Header: 'Company',
        accessor: 'companyName',
        width: 300,
      },
      { id: '1', Header: 'Role', accessor: 'roleName' },
    ],
    [],
  );
  return <Table columns={columns} rows={rows()} useExpandibleRows hasExport={false} />;
};

export default RolesTable;
