import React, { ReactElement } from 'react';
import styled from 'styled-components/macro';

const ErrorContainer = styled.div`
  font-family: ${({ theme }) => theme.fontFamily};
  font-size: ${({ theme }) => theme.fontSizePalette.small};
  line-height: 150%;
`;

const ErrorTitle = styled.h1`
  color: ${({ theme }) => theme.colorPalette.black};
  font-size: inherit;
  font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  line-height: inherit;
`;
const ErrorDescription = styled.p`
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-size: inherit;
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  line-height: inherit;
`;
export interface Props {
  children: ReactElement;
}

interface State {
  error: Error | null;
}
class ErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { error };
  }

  render() {
    const { error } = this.state;
    return !error ? (
      this.props.children
    ) : (
      <ErrorContainer>
        <ErrorTitle>Ops! Something went wrong...</ErrorTitle>
        <ErrorDescription>{`${error.name}: ${error.message}`}</ErrorDescription>
      </ErrorContainer>
    );
  }
}

export default ErrorBoundary;
