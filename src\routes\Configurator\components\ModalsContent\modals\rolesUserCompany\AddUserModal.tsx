import React, { useState } from 'react';
import { useAsync } from 'react-use';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components/macro';

import utils from 'utils';
import services from 'services';
import actions from 'state/actions';
import selectors from 'state/selectors';

import { GetUserForCompany } from 'models/response';
import { UserForRoleRow } from 'models/configurator';

import { IconsStatusContainer } from '../../../../styles';

import CommonButton from 'components/Buttons/CommonButton';
import Header from 'components/Modal/Header';

import Table, { TableColumn } from 'components/Table';
import Icon from 'components/Input/Icon';
import Legenda, { Item } from 'components/Legenda';

export const ButtonContainer = styled.div`
  text-align: center;
  button {
    margin: 10px;
  }
`;

const AddUserModal = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();

  const selectedCompanyId = useSelector(selectors.configurator.getSelectedCompanyForRoles);
  const transformedUserRows = useSelector(selectors.configurator.getTransformedUserRows);

  const [users, setUsers] = useState<GetUserForCompany[]>([]);
  const [selectedUserForAdd, setSelectedUserForAdd] = useState<GetUserForCompany[]>([]);

  const legendaProps: Item[][] = [
    [
      {
        icon: 'green-circle',
        label: t('not_assigned_user'),
      },
      {
        icon: 'already-assigned',
        label: t('assigned_user'),
      },
    ],
  ];

  useAsync(async () => {
    if (selectedCompanyId !== null) {
      try {
        const { data } = await services.getUsersForCompany(selectedCompanyId);
        const userResponse = data.map((el) => ({ ...el, disabled: el.status === 0 }));
        setUsers(userResponse);
      } catch (error) {
        console.error(error);
      }
    }
  }, []);

  const userColumns: TableColumn[] = [
    {
      id: '9',
      accessor: 'status',
      Header: t('status'),
      filterType: 'select',
      selectFilterOptions: [
        {
          value: 1,
          label: (
            <span>
              <Icon custom icon="green-circle" style={{ width: 11 }} />
            </span>
          ),
        },
        {
          value: 0,
          label: (
            <span>
              <Icon custom icon="already-assigned" style={{ width: 10 }} />
            </span>
          ),
        },
      ],
      width: 100,
      disableResizing: true,
      Cell: ({ value }: { value: number }) => {
        try {
          const icons: JSX.Element[] = [];
          switch (value) {
            case 1:
              icons.push(
                <span>
                  <Icon custom icon="green-circle" style={{ width: 11, margin: 10 }} />
                </span>,
              );
              break;
            case 0:
              icons.push(
                <span>
                  <Icon custom icon="already-assigned" style={{ width: 10, margin: 10 }} />
                </span>,
              );
              break;
          }
          return <IconsStatusContainer>{icons}</IconsStatusContainer>;
        } catch (e) {
          console.error(e);
          return null;
        }
      },
    },
    { id: '10', accessor: 'username', Header: t('username') },
    { id: '11', accessor: 'name', Header: t('name') },
    { id: '12', accessor: 'email', Header: t('email') },
  ];

  const setSelectedUser = (rows: GetUserForCompany[]) => {
    setSelectedUserForAdd(rows);
  };

  const addUserAction = async () => {
    if (selectedCompanyId !== null) {
      const idUserSelected = selectedUserForAdd[0].idUser;
      try {
        const { data } = await services.getUserAvailableRoles(selectedCompanyId, idUserSelected);
        dispatch(actions.configurator.setUserAvailableRoles(data));
        const newRow: UserForRoleRow = {
          id: -1,
          idUser: selectedUserForAdd[0].idUser,
          username: selectedUserForAdd[0].username,
          name: selectedUserForAdd[0].name,
          email: selectedUserForAdd[0].email,
          idRole: null,
          role: '',
          programResponseList: [],
          subRows: [],
        };
        const newRows = [newRow, ...transformedUserRows];
        dispatch(actions.configurator.setIsEditingUserRows(newRows));
        dispatch(actions.configurator.setRowIdUserRoles(newRow.id.toString()));
        dispatch(actions.configurator.setExternalSelectedUserId([newRow.id]));
        dispatch(actions.modal.closeModal());
      } catch (e) {
        console.error(e);
      }
    }
  };
  const specialColumns = {
    status: (value: any) => {
      switch (value.status) {
        case 0:
          return t('available');
        case 1:
          return t('assigned');
      }
      return '';
    },
  };

  return (
    <>
      <Header title={t('add_user')} />
      <Legenda items={legendaProps} />
      <Table
        rowId="idUser"
        onSelection={(rows: GetUserForCompany[]) => setSelectedUser(rows)}
        hasToolbar
        columns={userColumns}
        rows={users}
        hasSelection
        hasPagination
        hasResize
        hasSort
        hasFilter
        specialColumns={specialColumns}
        hasExport={true}
      />
      <ButtonContainer>
        <CommonButton value={t('continua')} action={addUserAction} disabled={selectedUserForAdd.length === 0} />
        <CommonButton
          scope="secondary"
          value={t('annulla')}
          action={() => {
            dispatch(actions.modal.closeModal());
          }}
        />
      </ButtonContainer>
    </>
  );
};

export default AddUserModal;
