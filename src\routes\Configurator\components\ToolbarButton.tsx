import React from 'react';

import { useSelector } from 'react-redux';

import selectors from 'state/selectors';

import { actionType } from 'utils/constants';
import mapperButtons from './MapperButtons';

// Users
import AddUser from './UsersButtons/AddUser';
import EditUser from './UsersButtons/EditUser';
import ImportUsers from './UsersButtons/ImportUsers';
import ExportUsers from './UsersButtons/ExportUsers';
import CloneUser from './UsersButtons/CloneUser';
import DeleteUser from './UsersButtons/BlockUsers';
import SeeRoles from './UsersButtons/SeeRoles';

// Tables
import EditRow from './TablesButtons/EditRow';
import AddRow from './TablesButtons/AddRow';
import DeleteRows from './TablesButtons/DeleteRows';
import ExportTableToStorage from './TablesButtons/ExportTableToStorage';

// Roles
import BackToList from './RolesUserCompanyButtons/BackToList';
import AddRole from './RolesUserCompanyButtons/AddRole';
import AddUserRole from './RolesUserCompanyButtons/AddUserRole';
import ConfigureRole from './RolesUserCompanyButtons/ConfigureRole';
import DeleteRole from './RolesUserCompanyButtons/DeleteRole';
import EditRole from './RolesUserCompanyButtons/EditRole';
import CloneRole from './RolesUserCompanyButtons/CloneRole';

// Companies
import AddCompany from './CompaniesButtons/AddCompany';
import EditCompany from './CompaniesButtons/EditCompany';
import CloneCompany from './CompaniesButtons/CloneCompany';

// Action Tables
import AddTemplate from './ActionButtons/AddTemplate';
import EditTemplate from './ActionButtons/EditTemplate';
import DeleteTemplate from './ActionButtons/DeleteTemplate';
import EditCorporateApproval from './CorporateApproval/Edit';
import { NopoAdd, NopoDelete, NopoEdit, PoAdd, PoDelete, PoEdit } from './ActionTable/buttons';

// Workflow
import ViewCompanyWf from './WorkflowButtons/ViewCompanyWf';
import BackToViewMode from './WorkflowButtons/BackToViewMode';

import Delete from './WorkflowButtons/DefTasksGroups/DefinitionsGroupsButtons/Delete';
import Clone from './WorkflowButtons/DefTasksGroups/DefinitionsGroupsButtons/Clone';
import Edit from './WorkflowButtons/DefTasksGroups/DefinitionsGroupsButtons/Edit';
import Add from './WorkflowButtons/DefTasksGroups/DefinitionsGroupsButtons/Add';

import AddTask from './WorkflowButtons/DefTasksGroups/TasksButtons/Add';
import EditTask from './WorkflowButtons/DefTasksGroups/TasksButtons/Edit';
import DeleteTask from './WorkflowButtons/DefTasksGroups/TasksButtons/Delete';

import AddAssociation from './WorkflowButtons/Associations/AddAssociation';
import AddTaskToDefinition from './WorkflowButtons/Associations/AddTaskToDefinition';
import CloneAssociation from './WorkflowButtons/Associations/CloneAssociation';
import EditAssociation from './WorkflowButtons/Associations/EditAssociation';
import DeleteAssociation from './WorkflowButtons/Associations/DeleteAssociation';
import ResetSearchVendor from './VendorButtons/ResetSearchVendor';
import EditVendor from './VendorButtons/EditVendor';
import ViewDetailVendor from './VendorButtons/ViewDetailVendor';
import BackListVendor from './VendorButtons/BackListVendor';
import BackViewModeVendor from './VendorButtons/BackViewModeVendor';
import EditVendorDetail from './VendorButtons/EditDetailVendor';
import EditVendorDetails from './VendorButtons/EditVendorDetails';
import { useLocation } from 'react-router-dom';
import AddMassiveUser from './WorkflowButtons/DefTasksGroups/DefinitionsGroupsButtons/AddMassiveUser';
import RemoveMassiveUser from './WorkflowButtons/DefTasksGroups/DefinitionsGroupsButtons/RemoveMassiveUser';
import ReplaceMassiveUser from './WorkflowButtons/DefTasksGroups/DefinitionsGroupsButtons/ReplaceMassiveUser';
import AddOpenWfNotification from './Notifications/buttons/OpenWorkflow/AddOpenWfNotification';
import EditOpenWfNotification from './Notifications/buttons/OpenWorkflow/EditOpenWfNotification';
import DeleteOpenWfNotification from './Notifications/buttons/OpenWorkflow/DeleteOpenWfNotification';
import AddWfReminderNotification from './Notifications/buttons/WorkflowReminder/AddWfReminderNotification';
import EditWfReminderNotification from './Notifications/buttons/WorkflowReminder/EditWfReminderNotification';
import DeleteWfReminderNotification from './Notifications/buttons/WorkflowReminder/DeleteWfReminderNotification';
import AddOverdueNotification from './Notifications/buttons/Overdue/AddOverdueNotification';
import EditOverdueNotification from './Notifications/buttons/Overdue/EditOverdueNotification';
import DeleteOverdueotification from './Notifications/buttons/Overdue/DeleteOverdueNotification';

interface ToolbarButton {
  action: string;
}

const ToolbarButton = (props: ToolbarButton) => {
  const { AddRowButton, DeleteRowButton, DeleteTemplateButton, EditRowButton, EditTemplateButton, NewTemplateButton } =
    mapperButtons;
  const selectedCompanyId = useSelector(selectors.configurator.getSelectedCompanyForRoles);
  const activeView = useSelector(selectors.configurator.selectActionsTableView);
  const currentView = (activeView ?? { value: '' }).value;

  const location = useLocation();
  const isVendorDetailView = location.pathname.includes('vendor-detail');
  const isEditedVendorDetailView = useSelector(selectors.configurator.getIsEditedVendorDetail);

  switch (props.action) {
    // Users
    case actionType.ADD_USER:
      return <AddUser />;
    case actionType.EDIT_USER:
      return <EditUser />;
    case actionType.IMPORT_USER:
      return <ImportUsers />;
    case actionType.EXPORT_USER:
      return <ExportUsers />;
    case actionType.BLOCK_USER:
      return <DeleteUser />;
    case actionType.CLONE_USER:
      return <CloneUser />;
    case actionType.USER_ROLES:
      return <SeeRoles />;
    // Tables
    case actionType.ADD_TABLE_ROW:
      return <AddRow />;
    case actionType.DELETE_TABLE_ROW:
      return <DeleteRows />;
    case actionType.EDIT_TABLE_ROW:
      return <EditRow />;
    case actionType.EXPORT_TABLE_TO_STORAGE:
      return <ExportTableToStorage />;
    // Companies
    case actionType.ADD_COMPANY:
      return <AddCompany />;
    case actionType.EDIT_COMPANY:
      return <EditCompany />;
    case actionType.CLONE_COMPANY:
      return <CloneCompany />;
    // Roles
    case actionType.ROLES_BACK_TO_COMPANY_LIST:
      return selectedCompanyId !== null ? <BackToList /> : null;
    case actionType.ADD_USER_ROLE:
      return selectedCompanyId !== null ? <AddUserRole /> : null;
    case actionType.DELETE_ROLE:
      return selectedCompanyId !== null ? <DeleteRole /> : null;
    case actionType.CONFIGURE_ROLE:
      return selectedCompanyId !== null ? <ConfigureRole /> : null;
    case actionType.ADD_ROLE:
      return selectedCompanyId !== null ? <AddRole /> : null;
    case actionType.EDIT_ROLE:
      return selectedCompanyId !== null ? <EditRole /> : null;
    case actionType.CLONE_ROLE:
      return selectedCompanyId !== null ? <CloneRole /> : null;
    // Mapper
    case actionType.MAPPER_EDIT_ROW:
      return <EditRowButton />;
    case actionType.MAPPER_ADD_ROW:
      return <AddRowButton />;
    case actionType.DELETE_ROWS:
      return <DeleteRowButton />;
    case actionType.NEW_TEMPLATE:
      return <NewTemplateButton />;
    case actionType.EDIT_TEMPLATE:
      return <EditTemplateButton />;
    case actionType.DELETE_TEMPLATE:
      return <DeleteTemplateButton />;
    // Action Table
    case actionType.ACTION_TABLE_ADD_ROW:
      if (currentView === 'PO') {
        return <PoAdd />;
      }
      return <NopoAdd />;
    case actionType.ACTION_TABLE_EDIT_ROW:
      if (currentView === 'PO') {
        return <PoEdit />;
      }
      return <NopoEdit />;
    case actionType.ACTION_TABLE_DELETE_ROW:
      // if current view is PO
      if (currentView === 'PO') {
        return <PoDelete />;
      }
      // if current view is NOPO
      return <NopoDelete />;
    // AP2 TEMPLATE
    case actionType.ADD_AP2_TEMPLATE:
      return <AddTemplate />;
    case actionType.EDIT_AP2_TEMPLATE:
      return <EditTemplate />;
    case actionType.DELETE_AP2_TEMPLATE:
      return <DeleteTemplate />;
    case actionType.EDIT_CORPORATE_APPROVAL:
      return <EditCorporateApproval />;
    // WORKFLOW
    case actionType.VIEW_COMPANY_WORKFLOW:
      return <ViewCompanyWf />;
    case actionType.BACK_TO_VIEW_MODE:
      return <BackToViewMode />;
    // WORKFLOW DEFINITIONS TASKS GROUPS
    case actionType.DELETE_WF_TASK:
      return <DeleteTask />;
    case actionType.DELETE_WF_DEFINITION:
    case actionType.DELETE_WF_GROUP:
      return <Delete />;
    case actionType.CLONE_WF_DEFINITION:
    case actionType.CLONE_WF_GROUP:
      return <Clone />;
    case actionType.EDIT_WF_TASK:
      return <EditTask />;
    case actionType.EDIT_WF_DEFINITION:
    case actionType.EDIT_WF_GROUP:
      return <Edit />;
    case actionType.ADD_WF_TASK:
      return <AddTask />;
    case actionType.ADD_WF_DEFINITION:
    case actionType.ADD_WF_GROUP:
      return <Add />;
    // WORKFLOW ASSOCIATION
    case actionType.ADD_WF_ASSOCIATION:
      return <AddAssociation />;
    case actionType.ADD_TASK_TO_DEFINITION:
      return <AddTaskToDefinition />;
    case actionType.CLONE_WF_ASSOCIATION:
      return <CloneAssociation />;
    case actionType.EDIT_WF_ASSOCIATION:
      return <EditAssociation />;
    case actionType.DELETE_WF_ASSOCIATION:
      return <DeleteAssociation />;
    // VMD
    case actionType.RESET_SEARCH_VENDOR:
      return !isVendorDetailView ? <ResetSearchVendor /> : null;
    case actionType.EDIT_VENDOR:
      return !isVendorDetailView ? <EditVendor /> : null;
    case actionType.VIEW_DETAIL_VENDOR:
      return !isVendorDetailView ? <ViewDetailVendor /> : null;
    case actionType.BACK_LIST_VENDOR:
      return isVendorDetailView ? <BackListVendor /> : null;
    case actionType.EDIT_VENDOR_DETAILS:
      return isVendorDetailView && !isEditedVendorDetailView ? <EditVendorDetails /> : null;
    case actionType.BACK_VIEW_MODE_VENDOR:
      return isVendorDetailView && isEditedVendorDetailView ? <BackViewModeVendor /> : null;
    case actionType.EDIT_DETAIL_VENDOR:
      return isVendorDetailView && isEditedVendorDetailView ? <EditVendorDetail /> : null;
    case actionType.ADD_MASSIVE_USER:
      return <AddMassiveUser />;
    case actionType.REMOVE_MASSIVE_USER:
      return <RemoveMassiveUser />;
    case actionType.REPLACE_MASSIVE_USER:
      return <ReplaceMassiveUser />;
    // NOTIFICATIONS-USER
    case actionType.ADD_OPEN_WF_NOTIFICATIONS:
      return <AddOpenWfNotification />;
    case actionType.EDIT_OPEN_WF_NOTIFICATIONS:
      return <EditOpenWfNotification />;
    case actionType.DELETE_OPEN_WF_NOTIFICATIONS:
      return <DeleteOpenWfNotification />;
    case actionType.ADD_WF_REMINDER_NOTIFICATIONS:
      return <AddWfReminderNotification />;
    case actionType.EDIT_WF_REMINDER_NOTIFICATIONS:
      return <EditWfReminderNotification />;
    case actionType.DELETE_WF_REMINDER_NOTIFICATIONS:
      return <DeleteWfReminderNotification />;
    case actionType.ADD_OVERDUE_NOTIFICATION:
      return <AddOverdueNotification />;
    case actionType.EDIT_OVERDUE_NOTIFICATION:
      return <EditOverdueNotification />;
    case actionType.DELETE_OVERDUE_NOTIFICATION:
      return <DeleteOverdueotification />;
    default:
      return <p>{props.action} still not supported</p>;
  }
};

export default ToolbarButton;
