import React, { useEffect, useCallback } from 'react';
import { useAsync } from 'react-use';
import { useDispatch, useSelector } from 'react-redux';

import utils from 'utils';
import { idAccessor } from 'utils/constants';
import services from 'services';
import actions from 'state/actions';
import selectors from 'state/selectors';

import { TableColumn } from 'components/Table/interfaces';
import Table from 'components/Table';

import { notDraggable } from 'utils/constants';
import Modal from 'components/Modal';
import ModalsContent from '../../components/ModalsContent/ModalSwitcher';

import { Wrapper, Title, Container } from '../../styles';
import * as yup from 'yup';

const Tables = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const idProgram = useSelector(selectors.app.getProgramCode) || -1;
  const tablesSelectValues = useSelector(selectors.configurator.selectTablesSelectValues);
  const activeTable = useSelector(selectors.configurator.selectActiveTable);
  const tableColumns = useSelector(selectors.configurator.selectTableColumns);
  const tableRows = useSelector(selectors.configurator.selectTableRows);
  const editRowID = useSelector(selectors.configurator.selectTablesEditRowID);
  const tableMode = useSelector(selectors.configurator.selectTableMode);
  const activeTemplateId = useSelector(selectors.configurator.selectActiveTemplateId);
  const selectedRow = useSelector(selectors.configurator.selectSelectedRow);
  const externalTablesRowSelected = useSelector(selectors.configurator.selectExternalTablesRowSelected);

  const closeModal = () => dispatch(actions.modal.closeModal());
  const isModalVisible = useSelector(selectors.modal.getVisibility);
  const currentModal = useSelector(selectors.modal.getActionType);
  const isModalDraggable = notDraggable.includes(currentModal || '');

  const getTableData = useCallback(
    async (activeTableValue: string) => {
      const { data } = await services.getTablesData(activeTableValue, idProgram);
      dispatch(actions.configurator.setTableColumns(data.columns));
      const tableRowsWithId = data.queryResults.map((ele, i) => {
        return {
          ...ele,
          [idAccessor]: i + 1,
        };
      });
      dispatch(actions.configurator.setTableRows(tableRowsWithId));
      dispatch(actions.configurator.setCompanyPriv(data.checkCompanyPriv));
    },
    [dispatch, idProgram],
  );

  // We use this function to reset some Redux states
  const cleanData = React.useCallback(
    (cleanSelection?: boolean, ids?: number[]) => {
      if (cleanSelection && ids) {
        dispatch(actions.configurator.removeTableRows(ids));
        dispatch(actions.configurator.setTablesSelectedRow([]));
        dispatch(actions.configurator.setExternalTablesRowSelected([]));
      }
      dispatch(actions.configurator.setTablesEditRowID(''));
      dispatch(actions.configurator.setTableMode(null));
    },
    [dispatch],
  );

  useAsync(async () => {
    if (activeTemplateId != null) {
      try {
        const { data: tables } = await services.getTables();
        dispatch(actions.configurator.setTablesSelectValues(tables));
        if (tables.length && !activeTable) {
          dispatch(actions.configurator.setActiveTable({ value: tables[0].table, label: tables[0].tableDesc }));
        }
      } catch (err) {
        console.error(err);
      }
    }
  }, [activeTemplateId]);

  useEffect(() => {
    if (activeTable) {
      cleanData();
      getTableData(activeTable.value);
    }
  }, [activeTable, getTableData, cleanData]);

  const columns: TableColumn[] = tableColumns.map((col) => {
    return {
      accessor: col.columnName,
      Header: t(`${col.columnHeader}`),
      readOnly: col.readOnly,
      inputType: col.type === 'Number' ? 'number' : 'text',
      hasLocalKeys: col.type === 'LocalizableStringFe' ? ['LocalizableStringFe', 'configuratorTables'] : undefined,
      cellEditValueRequired: col.required,
      columnName: col.columnName,
      columnHeader: col.columnHeader,
    };
  });

  const onSelection = (rows: any[]) => {
    dispatch(actions.configurator.setTablesSelectedRow(rows));
  };

  const onCancel = () => {
    cleanData(selectedRow[0].newRow, [selectedRow[0].idRow]);
  };

  const onSave = async (newRow: any) => {
    try {
      if (activeTable) {
        const newRowModified = newRow;
        const newRows: any[] = [];
        tableRows.forEach((row) => {
          if (Number(row[idAccessor]) === Number(newRow[idAccessor])) {
            return newRows.push({ ...newRow, newRow: false });
          }
          newRows.push({ ...row, newRow: false });
        });
        newRows.forEach((e) => {
          delete e.newRow;
          delete e[idAccessor];
        });
        delete newRowModified.newRow;
        delete newRowModified[idAccessor];
        if (tableMode === 'edit') {
          await services.updateTablesData({ table: activeTable.value, values: [newRowModified] });
        } else if (tableMode === 'add') {
          const addedRow = newRow;
          delete addedRow.newRow;
          delete addedRow[idAccessor];
          await services.insertTablesData({ table: activeTable.value, values: [addedRow] });
          getTableData(activeTable.value);
        }
        utils.app.notify('success', t(`table-${tableMode}-success`));
        const updateRows = newRows.map((e, i) => {
          return {
            ...e,
            [idAccessor]: (i + 1).toString(),
          };
        });
        dispatch(actions.configurator.setTableRows(updateRows));
      }
    } catch (err) {
      console.error(err);
    } finally {
      cleanData(true, [Number(selectedRow[0].idRow)]);
    }
  };

  const dinamicValidation = () => {
    const schemaFields: { [key: string]: yup.Schema<any> } = {};
    columns.forEach((column) => {
      if (column.cellEditValueRequired && column.columnName && column.columnHeader && !column.readOnly) {
        switch (column.inputType) {
          case 'text':
            schemaFields[column.columnName] = yup
              .string()
              .nullable()
              .required(t(`missing ${column.columnHeader}`));
            break;
          case 'number':
            schemaFields[column.columnName] = yup
              .number()
              .nullable()
              .required(t(`missing ${column.columnHeader}`));
            break;
          default:
            schemaFields[column.columnName] = yup
              .mixed()
              .nullable()
              .required(t(`missing ${column.columnHeader}`));
        }
      }
    });
    return yup.object().shape(schemaFields);
  };

  return (
    <>
      <Modal onClose={closeModal} open={isModalVisible} isDraggable={!isModalDraggable}>
        <ModalsContent />
      </Modal>
      <Wrapper>
        <Title>
          <h2>{t('tables')}</h2>
        </Title>
        <Container>
          {tablesSelectValues.length && columns && columns.length ? (
            <Table
              isEditable
              hasToolbar
              rowId={idAccessor}
              editRowID={`${editRowID}`}
              onCancel={onCancel}
              onSave={onSave}
              rows={tableRows}
              columns={[
                ...columns,
                {
                  accessor: idAccessor,
                  Header: 'idRow',
                  isVisible: false,
                  id: 'idRow',
                },
              ]}
              onSelection={onSelection}
              initialSelection={externalTablesRowSelected.length ? externalTablesRowSelected : undefined}
              hasSelection
              hasMultiSelection
              hasPagination
              hasFilter
              hasSort
              onSaveValidation={dinamicValidation()}
            />
          ) : null}
        </Container>
      </Wrapper>
    </>
  );
};

export default Tables;
