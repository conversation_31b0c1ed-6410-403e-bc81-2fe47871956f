import React, { useState } from 'react';
import { useAsync } from 'react-use';
import services from 'services';
import utils from 'utils';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import TableWfReminder from './TableWfReminder';
import { Formik, FormikState } from 'formik';
import selectors from 'state/selectors';
import FormWfReminder from './FormWfReminders';
import { generateCronExpression } from '../utils';
import { FormValues, setInitialValues, validationLogic } from './wfReminderFormUtils';
import { AddWfReminderNotificationSettings } from 'models/request';

const WorkflowReminder = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const formMode = useSelector(selectors.configurator.getFormModeWfReminder);
  const selectedRow = useSelector(selectors.configurator.getSelectedWfReminderSettings);
  const wfReminderList = useSelector(selectors.configurator.getListOfWfReminderSettings);
  const templatesList = useSelector(selectors.configurator.getTemplatesMailNotification);

  const [userList, setUserList] = useState<{ idUser: number; name: string }[]>([]);

  useAsync(async () => {
    try {
      const { data } = await services.getWfReminderNotificationSettings();
      dispatch(actions.configurator.setListOfWfReminderSettings(data));
    } catch (e) {
      console.error(e);
    }
  }, []);

  useAsync(async () => {
    if (formMode !== 'add') return;
    const { data } = await services.getUsersForWfReminderNotificationSettings();
    setUserList(data);
  }, [formMode]);

  const onSubmit = async (
    values: FormValues,
    resetForm: (nextState?: Partial<FormikState<FormValues>> | undefined) => void,
  ) => {
    const {
      user,
      reminderSendMail,
      startDeltaReminder,
      endDeltaReminder,
      reminderDays,
      maxReminder,
      reminderMaxDocuments,
      reminderMaxDocumentDetails,
      idTemplateScheduledReminder,
      hour,
      minute,
      allDays,
      daysOfWeek,
    } = values;
    if (!user || !idTemplateScheduledReminder) return;
    const bodyRequest: AddWfReminderNotificationSettings = {
      idUser: user.value,
      reminderSendMail,
      startDeltaReminder,
      endDeltaReminder,
      reminderDays,
      maxReminder,
      reminderMaxDocuments,
      reminderMaxDocumentDetails,
      idTemplateScheduledReminder: idTemplateScheduledReminder?.value,
      reminderFrequency: generateCronExpression(hour, minute, allDays, daysOfWeek),
    };
    try {
      if (formMode === 'add') {
        await services.addWfReminderNotificationSettings(bodyRequest);
        utils.app.notify('success', t('user-notification-created'));
        dispatch(
          actions.configurator.setListOfWfReminderSettings([
            {
              name: user.label,
              ...bodyRequest,
            },
            ...wfReminderList,
          ]),
        );
      }
      if (formMode === 'edit') {
        await services.editWfReminderNotificationSettings(bodyRequest);
        const newList = wfReminderList.map((e) =>
          e.idUser === bodyRequest.idUser ? { name: user.label, ...bodyRequest } : e,
        );
        dispatch(actions.configurator.setListOfWfReminderSettings(newList));
        dispatch(actions.configurator.setSelectedWfReminderSettings(null));
        utils.app.notify('success', t('user-notification-edited'));
      }
    } catch (e) {
      console.error(e);
    } finally {
      dispatch(actions.configurator.setFormModeWfReminder(null));
      resetForm();
    }
  };

  return (
    <div>
      <h2>{t('Workflow-Reminder')}</h2>
      <TableWfReminder />
      <Formik
        initialValues={setInitialValues(selectedRow, formMode, t, templatesList)}
        onSubmit={(values, { resetForm }) => {
          onSubmit(values, resetForm);
        }}
        validationSchema={validationLogic(t)}
        enableReinitialize
      >
        {({ values, isValid, isValidating, dirty, setFieldValue, submitForm }) => (
          <div>
            {formMode && (
              <FormWfReminder
                values={values}
                setFieldValue={setFieldValue}
                dirty={dirty}
                isValid={isValid}
                isValidating={isValidating}
                submitForm={submitForm}
                userList={userList}
                templatesList={utils.input.buildOptions(templatesList, 'templateName', 'idMailTemplate', true)}
              />
            )}
          </div>
        )}
      </Formik>
    </div>
  );
};
export default WorkflowReminder;
