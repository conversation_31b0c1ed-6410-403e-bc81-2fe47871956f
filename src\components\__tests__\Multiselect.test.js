import React from 'react';
import '@testing-library/jest-dom';
import { screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom/extend-expect';
import { renderWithStyle } from 'utils/helpers/test.helpers';

import MultiSelectInput from 'components/Input/MultiSelect';

describe('MultiSelectInput', () => {
  it('gets rendered in DOM without crashing', async () => {
    const options = [];
    renderWithStyle(<MultiSelectInput options={options} />);
  });
  test('renders options and selects an item', () => {
    const options = [
      { label: 'Option 1', value: 'option1' },
      { label: 'Option 2', value: 'option2' },
      { label: 'Option 3', value: 'option3' },
    ];

    const { container } = renderWithStyle(<MultiSelectInput id="1" options={options} />);
    const selectElement = container.querySelector('.dropdown-heading');
    screen.debug(selectElement);
    userEvent.click(selectElement);
    const optionElement = container.querySelector('.select-item');
    expect(optionElement).toBeInTheDocument();

  });
  test('renders options and selects an item', () => {
    const options = [
      { label: 'Option 1', value: 'option1' },
      { label: 'Option 2', value: 'option2' },
      { label: 'Option 3', value: 'option3' },
    ];

    const { container } = renderWithStyle(<MultiSelectInput id="1" options={options} />);
    screen.debug();
    const selectElement = container.querySelector('.dropdown-heading');
    screen.debug(selectElement);
    userEvent.click(selectElement);
    const optionElement = container.querySelector('.select-item');
    expect(optionElement).toBeInTheDocument();
  });
});
