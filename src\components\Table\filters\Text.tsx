import React from 'react';
import { Props } from '.';
import Search from '../../Input/SearchInput';

const Text = (column: Props) => {
  const { filterValue, setFilter, id, Header } = column;

  let timeoutId = 0;

  const setDebounceFilter = (e: React.ChangeEvent<HTMLInputElement>) => {
    clearTimeout(timeoutId);
    const { value } = e.target;
    timeoutId = setTimeout(() => {
      setFilter(value);
      document.getElementById(`${id}-${Header}`)?.focus();
    }, 750);
  };
  return (
    <Search id={`${id}-${Header}`} placeholder="" fullWidth small onChange={setDebounceFilter} value={filterValue} />
  );
};

export default Text;
