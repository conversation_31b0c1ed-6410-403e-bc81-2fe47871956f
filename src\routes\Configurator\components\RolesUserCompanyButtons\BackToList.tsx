import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import utils from 'utils';
import actions from 'state/actions';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';

const BackToList = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const isEditingUserRows = useSelector(selectors.configurator.getIsEditingUserRows);

  const removeSelectedCompany = () => {
    dispatch(actions.configurator.setSelectedCompanyForRoles(null));
    dispatch(actions.configurator.setSelectedUserForRoles([]));
  };

  return (
    <CommonButton
      action={removeSelectedCompany}
      scope="tertiary"
      value={t('BackToList')}
      icon="angle-left"
      disabled={isEditingUserRows.length > 0}
    />
  );
};

export default BackToList;
