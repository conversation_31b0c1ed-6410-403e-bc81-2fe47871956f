import TextInput from '../Input/TextInput';
import React from 'react';
import '@testing-library/jest-dom';
import { fireEvent, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom/extend-expect';
import { colorPalette } from 'utils/styleConstants';
import { renderWithStyle } from 'utils/helpers/test.helpers';

describe('TextInput', () => {
  test('textInput gets rendered in DOM', async () => {
    renderWithStyle(<TextInput placeholder="PlaceolderTest" />);
    const inputElement = screen.getByPlaceholderText('PlaceolderTest');
    expect(inputElement).toBeInTheDocument();
  });

  it('Text Input has the correct default value', () => {
    renderWithStyle(<TextInput placeholder="PlaceolderTest" value="test" />);
    const inputElement = screen.getByPlaceholderText('PlaceolderTest');
    expect(inputElement).toHaveValue('test');
  });

  it('TextInput does change the value', () => {
    renderWithStyle(<TextInput placeholder="PlaceolderTest" value="test" />);
    const inputElement = screen.getByPlaceholderText('PlaceolderTest');
    expect(inputElement).toHaveValue('test');
    fireEvent.change(inputElement, { target: { value: 'changed' } });
    expect(inputElement).toHaveValue('changed');
  });

  it('TextInput is disabled', () => {
    renderWithStyle(<TextInput placeholder="PlaceolderTest" value="test" disabled={true} />);
    const inputElement = screen.getByPlaceholderText('PlaceolderTest');
    expect(inputElement).toBeDisabled();
  });
  it('TextInput has a label', () => {
    renderWithStyle(<TextInput placeholder="PlaceolderTest" value="test" label="LabelTest" />);
    const labelElement = screen.getByText('LabelTest');
    expect(labelElement).toBeInTheDocument();
  });
  it('TextInput has a feedback', () => {
    renderWithStyle(<TextInput placeholder="PlaceolderTest" value="test" hasFeedback={true} />);
    const FeedbackElement = screen.getByPlaceholderText('PlaceolderTest');
    expect(FeedbackElement).toHaveStyle(`border-color: ${colorPalette.red.error.toLowerCase()}`);
  });
  it('TextInput has a feedback with error message', () => {
    renderWithStyle(
      <TextInput placeholder="PlaceolderTest" value="test" hasFeedback={true} label="ErrorMessageTest" />,
    );
    const labelElement = screen.getByText('ErrorMessageTest');
    expect(labelElement).toBeInTheDocument();
  });
  it('Disabled TextInput does not change the value', () => {
    renderWithStyle(<TextInput placeholder="PlaceolderTest" value="" disabled={true} />);
    const inputElement = screen.getByPlaceholderText('PlaceolderTest');
    expect(inputElement).toHaveValue('');
    userEvent.type(inputElement, 'hello');
    expect(inputElement).toHaveValue('');
  });

  it('TextInput has a feedback with error message', () => {
    renderWithStyle(
      <TextInput placeholder="PlaceolderTest" value="test" hasFeedback={true} label="ErrorMessageTest" />,
    );
    const labelElement = screen.getByText('ErrorMessageTest');
    expect(labelElement).toBeInTheDocument();
  });
});
