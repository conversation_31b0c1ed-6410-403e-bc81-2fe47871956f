/* eslint-disable max-lines */
import Table, { OriginalRow } from 'components/Table';
import { PoMicroCategoryList, PoVendorList } from 'models/response';
import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { CustomTitle, TablesContainer } from 'routes/Configurator/styles';
import actions from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import utils from 'utils';
import { modalActionType } from 'utils/constants';
import {
  Microcategory,
  microCategoryColumns,
  Vendor,
  vendorColumns,
  poSchema,
  searchFunc,
  checkMicrocategoryFunc,
  checkVendorListFunc,
  subVendorColumns,
  subMicroCategoryColumns,
  poSchemaNewRow,
} from './utils';
import { RowAccessor } from 'components/Table/interfaces';
import services from 'services';
import { CreateExpActionTable } from 'models/request';
import axios from 'axios';
import _ from 'lodash';
import Modal from 'components/Modal';
import ModalsContent from '../../../../components/ModalsContent/ModalSwitcher';
import SearchBar from './SearchBar';

const Content = styled.div`
  padding-right: 15px;
`;

const MainTable = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const rows = useSelector(selectors.configurator.getLeftTableRows);
  const selectedTable = useSelector(selectors.configurator.selectTableToSearch);
  const rowSelectedMainTable = useSelector(selectors.configurator.getSelectedRow);
  const currentEditedRowId = useSelector(selectors.configurator.getEditedRowId);

  const optionsValue = useSelector(selectors.configurator.getOptionValueDropdown);
  const selectedCompanies = useSelector(selectors.configurator.getSelectedCompanies);
  const selectedOptionValue = useSelector(selectors.configurator.getSelectedOptionValue);

  // select for table columns
  const wfCasuals = useSelector(selectors.configurator.getWfCasuals);
  const wfTaskDefinitions = useSelector(selectors.configurator.getWfDefinitions);
  const allMicroCategories = useSelector(selectors.configurator.getAllMicrocategories);
  const companies = useSelector(selectors.app.getCompanies);
  const allSubjects = useSelector(selectors.configurator.getAllSubjects);
  const notModifiedRow = useSelector(selectors.configurator.getNotModifiedRow);
  const saveValidationSchema =
    currentEditedRowId === '-1' ? poSchemaNewRow(t, selectedTable) : poSchema(t, notModifiedRow);
  const isModalVisible = useSelector(selectors.modal.getVisibility);
  const modalProps = useSelector(selectors.modal.getModalProps);
  const ATCompanies = useSelector(selectors.configurator.getATcompanies);
  const allAuthorizedCompanies = companies
    .filter((company) => {
      if (company.idCompany === 0) return false;
      const action = company.actions.find((action) => action.type === 'AddExpAction');
      return action && action.active;
    })

    .map(({ name: label, idCompany: value }) => {
      return { label, value };
    });

  const fluxes = services.useFluxes('PO');

  const columns = useCallback(() => {
    return selectedTable === 'microcategory'
      ? microCategoryColumns(
          wfCasuals,
          wfTaskDefinitions,
          allMicroCategories,
          allAuthorizedCompanies,
          fluxes,
          currentEditedRowId === '-1',
          t,
        )
      : vendorColumns(
          wfCasuals,
          wfTaskDefinitions,
          allAuthorizedCompanies,
          fluxes,
          currentEditedRowId === '-1',
          allSubjects,
          t,
        );
  }, [
    selectedTable,
    wfCasuals,
    wfTaskDefinitions,
    allMicroCategories,
    currentEditedRowId,
    allSubjects,
    allAuthorizedCompanies,
    fluxes,
    t,
  ]);

  const onSelection = (rows: PoVendorList[] | PoMicroCategoryList[]) => {
    dispatch(actions.configurator.setSelectedRow(rows[0]));
  };

  const clearState = () => {
    dispatch(actions.configurator.setEditedRowId(undefined));
    dispatch(actions.configurator.setSelectedRow(null));
    dispatch(actions.configurator.setWfDefinitions([]));
    dispatch(actions.configurator.setAllSubject([]));
    dispatch(actions.configurator.setWfCasuals([]));
    dispatch(actions.configurator.setAllMicroCategories([]));
  };

  const onSaveEditedRow = async (row: OriginalRow) => {
    try {
      if (currentEditedRowId === '-1') {
        const selectedOption = [...selectedOptionValue];

        if (selectedTable === 'microcategory') {
          const typedRow = row as Microcategory;
          const chosenMicrocategory = allMicroCategories.find(
            (microcategory) => microcategory.value === typedRow.microcategoryDescription,
          );

          const chosenCasual = wfCasuals.find((casual) => casual.value === Number(typedRow.wfCausal));
          const chosenDefinition = wfTaskDefinitions.find(
            (definition) => definition.value === Number(typedRow.workflowDefinition),
          );
          const chosenCompany = companies.find((company) => company.idCompany === (typedRow.companyName as number));
          if (!chosenMicrocategory || !chosenCompany) {
            throw new Error('Missing data');
          }
          // new row to add
          const newRow: CreateExpActionTable = {
            companyName: chosenCompany.name,
            costCenterCode: '',
            vendorCode: '',
            vendorCategoryCode: chosenMicrocategory.value,
            fluxName: typedRow.fluxName,
            wfName: chosenDefinition?.wfName ?? null,
            wfIdGroups: null,
            wfIdCausal: chosenCasual?.idWfCausal ?? null,
            wfIdTaskDefinition: null,
            wfIdWorkflowDefinition: chosenDefinition?.idDefinition ?? null,
            fullOcr: typedRow.fullOcr,
            corporateApproval: false,
            wfNote: null,
          };
          await services.createExpActionTable(newRow).then(({ data }) => {
            utils.app.notify('success', `${t('newActionTableRowCreated')} ${data}`);

            const companySelected = selectedCompanies.find((el) => el.idCompany === chosenCompany.idCompany);

            if (companySelected && chosenMicrocategory) {
              const newOptionValue = {
                value: chosenMicrocategory.value,
                label: `${chosenMicrocategory.value} - ${chosenMicrocategory.description}`,
              };

              dispatch(actions.configurator.setOptionValueDropdown([...optionsValue, newOptionValue]));

              selectedOption.push(newOptionValue);
            }
          });
        }
        if (selectedTable === 'vendor') {
          const typedRow = row as Vendor;
          const chosenCasual = wfCasuals.find((casual) => casual.value === Number(typedRow.wfCausal));
          const chosenDefinition = wfTaskDefinitions.find(
            (definition) => definition.value === Number(typedRow.workflowDefinition),
          );
          const chosenSubject = allSubjects.find((subject) => subject.supplierCode === typedRow.subjectName);
          const chosenCompany = companies.find((company) => company.idCompany === (typedRow.companyName as number));

          if (chosenCompany === undefined) {
            throw new Error(t('companyNotSelected'));
          }

          const newRow: CreateExpActionTable = {
            companyName: chosenCompany?.name,
            costCenterCode: '',
            vendorCode: typedRow.subjectName,
            vendorCategoryCode: '',
            fluxName: typedRow.fluxName,
            wfName: chosenDefinition?.wfName ?? null,
            wfIdGroups: null,
            wfIdCausal: chosenCasual?.idWfCausal ?? null,
            wfIdTaskDefinition: null,
            wfIdWorkflowDefinition: chosenDefinition?.idDefinition ?? null,
            fullOcr: typedRow.fullOcr,
            corporateApproval: false,
            wfNote: null,
          };
          const { data } = await services.createExpActionTable(newRow);
          utils.app.notify('success', `${t('newActionTableRowCreated')} ${data}`);

          const companySelected = selectedCompanies.find((el) => el.idCompany === chosenCompany.idCompany);

          if (companySelected && chosenSubject) {
            const newOptionValue = {
              value: typedRow.subjectName,
              label: `${typedRow.subjectName} - ${chosenSubject.label}`,
            };

            dispatch(actions.configurator.setOptionValueDropdown([...optionsValue, newOptionValue]));

            selectedOption.push(newOptionValue);
          }
        }

        dispatch(actions.configurator.setSelectedOptionValue(selectedOption));

        const checkMicroCategory = checkMicrocategoryFunc(selectedOption);
        const checkVendorList = checkVendorListFunc(selectedOption);
        const search = searchFunc(selectedTable, selectedCompanies, checkMicroCategory, dispatch, checkVendorList);
        search();
        clearState();
        dispatch(actions.configurator.setLeftTableRows((rows as any[]).filter((row) => row.id !== '-1')));
        return;
      }
      // edited row
      dispatch(actions.configurator.setEditedRowId(undefined));
      dispatch(actions.configurator.setSelectedRow(null));
      dispatch(
        actions.modal.setModal({
          actionType: modalActionType.configurator.SELECT_COMPANIES,
          props: { row, oldRow: notModifiedRow },
        }),
      );
    } catch (error) {
      clearState();
      dispatch(actions.configurator.setLeftTableRows((rows as any[]).filter((row) => row.id !== '-1')));
      if (axios.isAxiosError(error)) {
        utils.app.notify('fail', error.response?.data);
        console.error(error.response?.data);
        return;
      }
      console.error(error);
    }
  };

  const onEditedRowChange = async (row: OriginalRow, accessor?: RowAccessor) => {
    const currentRows: PoMicroCategoryList[] | PoVendorList[] = _.cloneDeep(rows);
    const index = currentRows.findIndex((r: any) => r?.id === currentEditedRowId);
    if (index !== -1 && currentEditedRowId !== undefined) {
      if (accessor === 'companyName') {
        const newRow = {
          ...currentRows[index],
          [accessor as string]: row[accessor as string],
          workflowDefinition: '',
          wfCausal: '',
          microcategoryDescription: '',
        };
        if (selectedTable === 'microcategory') {
          currentRows[index] = {
            ...newRow,
            microcategoryDescription: '',
          };
        } else {
          currentRows[index] = {
            ...newRow,
            subjectName: '',
          };
        }
      } else {
        currentRows[index] = { ...currentRows[index], [accessor as string]: row[accessor as string] };
      }
      dispatch(actions.configurator.setLeftTableRows(currentRows));
    }
    if (accessor === 'companyName') {
      const chosenCompany = companies.find((company) => company.idCompany === row.companyName);
      if (!chosenCompany) return;
      const { data: wfCasuals } = await services.getWfCasuals(chosenCompany.idCompany);
      const { data: wfTaskDefinitions } = await services.getWfDefinitions(chosenCompany.idCompany);
      dispatch(actions.configurator.setWfCasuals(wfCasuals));
      dispatch(actions.configurator.setWfDefinitions(wfTaskDefinitions));

      if (selectedTable === 'vendor') {
        const { data: subjects } = await services.getAllSubject(chosenCompany.idCompany);
        dispatch(actions.configurator.setAllSubject(subjects));
        return;
      }
      // microcategory
      const { data: microCategories } = await services.getAllMicrocategories(chosenCompany.idCompany);
      dispatch(actions.configurator.setAllMicroCategories(microCategories));
    }
  };

  const onCanceledEdit = async () => {
    clearState();
    // @note async is a workaround to avoid that the table is not updated when the user cancels
    const currentRows: PoMicroCategoryList[] | PoVendorList[] = await _.cloneDeep(rows);
    if (notModifiedRow) {
      const idx = currentRows.findIndex((r: any) => r?.id === currentEditedRowId);
      if (idx !== -1) {
        currentRows[idx] = notModifiedRow;
        dispatch(actions.configurator.setLeftTableRows(currentRows));
        dispatch(actions.configurator.setNotModifiedRow(undefined));
        return;
      }
    }
    dispatch(actions.configurator.setLeftTableRows((currentRows as any[]).filter((row) => row.id !== '-1')));
  };

  const memoTable = React.useMemo(
    () => (
      <Table
        hasToolbar
        onSelection={onSelection}
        columns={columns()}
        rows={rows}
        hasSelection
        hasPagination
        hasResize
        hasSort
        hasFilter
        isEditable
        editRowID={currentEditedRowId}
        onSave={onSaveEditedRow}
        onCancel={() => {
          onCanceledEdit();
        }}
        onEditRowChange={onEditedRowChange}
        initialSelection={currentEditedRowId !== undefined ? [currentEditedRowId] : undefined}
        onSaveValidation={saveValidationSchema}
      />
    ),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [rows, columns, currentEditedRowId],
  );

  const closeModal = async () => {
    const { oldRow }: { oldRow: PoMicroCategoryList | PoVendorList } = modalProps;
    if (notModifiedRow) {
      const currentRows: PoMicroCategoryList[] | PoVendorList[] = await _.cloneDeep(rows);
      const idx = currentRows.findIndex((r: any) => r?.id === oldRow?.id);
      if (idx !== -1) {
        currentRows[idx] = { ...notModifiedRow };
        dispatch(actions.configurator.setLeftTableRows(currentRows));
      }
    }
    clearState();
    dispatch(actions.modal.closeModal());
  };

  return (
    <>
      <Modal onClose={closeModal} open={isModalVisible}>
        <ModalsContent />
      </Modal>
      {selectedCompanies.length > 0 && ATCompanies.length > 0 && <SearchBar />}
      {rows.length > 0 ? (
        <TablesContainer>
          <Content>
            <CustomTitle>{selectedTable === 'microcategory' ? t('microcategory') : t('vendor_code')}</CustomTitle>
            {memoTable}
          </Content>
          {rowSelectedMainTable && rowSelectedMainTable.id !== '-1' ? (
            <Content>
              <CustomTitle>{selectedTable === 'microcategory' ? t('vendor_code') : t('microcategory')}</CustomTitle>
              <Table
                hasToolbar
                columns={selectedTable === 'microcategory' ? subMicroCategoryColumns(t) : subVendorColumns(t)}
                rows={
                  rowSelectedMainTable
                    ? selectedTable === 'microcategory'
                      ? (rowSelectedMainTable as PoMicroCategoryList).microcategoryDetails
                      : (rowSelectedMainTable as PoVendorList).vendorDetails
                    : []
                }
                hasPagination
                hasResize
                hasSort
                hasFilter
              />
            </Content>
          ) : null}
        </TablesContainer>
      ) : null}
    </>
  );
};
export default MainTable;
