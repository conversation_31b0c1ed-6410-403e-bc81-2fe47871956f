/* eslint max-lines: 0 */
import React, { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components/macro';
import intlHelper from 'utils/helpers/intl.helper';
import { actionType, modalActionType, moduleProgramCode } from 'utils/constants';
import { useField, useFormikContext } from 'formik';
import { FormValues } from 'models/documents';
import { RootState } from 'models';

import action from 'state/actions';
import selectors from 'state/selectors';
import services from 'services';
import utils from 'utils';

import CommonButton from 'components/Buttons/CommonButton';
import Dropdown from 'components/Input/Dropdown';
import TextInput, { InputLabel } from 'components/Input/TextInput';
import { Icon } from 'components/Input';
import { fontSizePalette } from 'utils/styleConstants';

const DocHeaderContainer = styled.div`
  padding: 20px;
  display: flex;
  min-height: 140px;
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  justify-content: space-between;
  overflow-x: auto;
`;

const DocHeaderSection = styled.div`
  display: flex;
  align-items: center;
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  justify-content: flex-start;
  flex-wrap: wrap;
  > * {
    margin-bottom: 20px;
    margin-right: 20px;
  }
`;

const DocHeaderSectionLeft = styled.div`
  display: flex;
  flex-direction: column;
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  justify-content: space-around;
`;

const DocHeaderSectionGroup = styled.div`
  display: flex;

  > div {
    margin-right: 60px;
    margin-top: 20px;
  }
`;

const DropDownContainer = styled.div`
  display: flex;
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  justify-content: flex-end;
  align-items: center;
`;

const InputsContainer = styled.div<{ notAllowed?: boolean; disabled?: boolean }>`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  input {
    cursor: ${({ notAllowed }) => (notAllowed ? 'not-allowed' : 'pointer')};
  }
  pointer-events: ${({ disabled }) => (disabled ? 'none' : 'auto')};
`;

const DocHeaderTitle = styled.h2`
  color: ${({ theme }) => theme.colorPalette.grey.grey7};
`;

const DocHeaderDetails = styled.div`
  font-size: ${({ theme }) => theme.fontSizePalette.xSmall};
  margin-top: 5px;
`;

const ButtonContainer = styled.div``;

const InputStoreWrapper = styled.div`
  display: flex;
  align-items: center;
  position: relative;
`;

interface Props {
  isReadOnly?: boolean;
}

const IconWrapper = styled.span`
  margin: 5px;
  position: absolute;
  right: -40px;
  cursor: pointer;
  display: flex;
  width: 30px;
  justify-content: space-between;
`;

const DocHeader = (props: Props) => {
  const { isReadOnly } = props;
  const record = useSelector(selectors.documents.selectActiveDocument);
  const t = intlHelper.useTranslator();
  const { submitForm, errors, dirty } = useFormikContext();
  const dispatch = useDispatch();
  const protocol = useSelector(selectors.documents.selectActiveDocumentID);
  const idProgram = useSelector(selectors.app.getProgramCode);
  const documentHeader = useSelector(selectors.documents.selectDocumentHeader);
  const [fieldDocType] = useField<FormValues['documentTypeHeader']>('documentTypeHeader');
  const [fieldCompany] = useField<FormValues['companyHeader']>('companyHeader');
  const [fieldVatNumber] = useField<FormValues['vatNumber']>('vatNumber');
  const [fieldMovType, , helpersMovType] = useField<FormValues['movTypeHeader']>('movTypeHeader');
  const [fieldChildOf] = useField<FormValues['isChildOf']>('isChildOf');
  const [fieldSubject] = useField<FormValues['subjectHeader']>('subjectHeader');
  const [supplierCode] = useField<FormValues['supplierCode']>('supplierCode');
  const idSubject = useSelector(selectors.documents.selectIdSubject);
  const saveDocument = utils.hooks.useSaveDocument(true);
  const nameBody = useSelector(selectors.documents.selectBodyName) || '';
  const documentFields = useSelector(selectors.documents.selectDocumentFields);
  const configAutofocus = useSelector((store: RootState) =>
    selectors.app.getConfig(store, actionType.AUTOFOCUS_Q3_FIELD),
  );
  const SHOW_SUBJECT_DETAILS =
    useSelector((store: RootState) => selectors.app.getConfig(store, actionType.SHOW_SUBJECT_DETAILS))?.val === 1;

  const autosave =
    useSelector((store: RootState) => selectors.app.getConfig(store, actionType.AUTOSAVE_DOCUMENT))?.val === 1;

  const { val: HIDE_MOVE_TYPE } = useSelector((store: RootState) =>
    selectors.app.getConfig(store, actionType.HIDE_MOVE_TYPE, 0),
  );

  const hideAddStoreButton = useSelector(
    (store: RootState) => selectors.app.getConfig(store, actionType.HIDE_ADD_STORE_BUTTON)?.val === 1,
  );

  const idTemplate = useSelector(selectors.documents.selectActiveTemplateId);

  // Program check
  const isContractValidation = idProgram === moduleProgramCode.contractValidation;

  document.getElementById('store')?.addEventListener('keydown', (event) => event.preventDefault());
  document.getElementById('store-details')?.addEventListener('keydown', (event) => event.preventDefault());

  const reloadPage = () => {
    if (protocol && idTemplate) {
      dispatch(action.documents.setDocumentHeader(protocol, idProgram, idTemplate));
      dispatch(action.documents.setDocumentFields(protocol, idProgram));
    }
  };

  const setDocumentType = async (opt: { value: number; label: string }) => {
    try {
      if (protocol && fieldCompany.value) {
        const { data } = await services.changeDocumentType({
          idDocType: opt.value,
          idCompany: fieldCompany.value.value,
          protocols: protocol ? [protocol] : [],
          idProgram,
        });
        if (data.success) {
          utils.app.notify('success', t('doc_type_changed_correctly'));
          if (protocol) {
            dispatch(
              action.documents.updateTableRows({ protocols: [protocol], property: 'documentName', value: opt.label }),
            );
          }
        } else {
          utils.app.notify('warning', t('doc_type_not_changed_correctly'));
        }
        reloadPage();
      }
    } catch (e) {
      console.error(e);
    }
  };

  const setCompany = async (opt: { value: number; label: string }) => {
    try {
      if (protocol && fieldDocType.value) {
        const { data } = await services.changeCompanyDocument({
          idDocType: fieldDocType.value.value,
          idCompany: opt.value,
          protocols: protocol ? [protocol] : [],
          idProgram,
        });
        if (data.success) {
          utils.app.notify('success', t('company_changed_correctly'));
          if (protocol) {
            dispatch(
              action.documents.updateTableRows({
                protocols: [protocol],
                property: 'companyName',
                value: documentHeader?.availableCompanies?.find((e) => e.idCompany === opt.value)?.name,
              }),
            );
            dispatch(
              action.documents.updateTableRows({ protocols: [protocol], property: 'idCompany', value: opt.value }),
            );
          }
        } else {
          utils.app.notify('warning', t('company_not_changed_correctly'));
        }
        reloadPage();
      }
    } catch (e) {
      console.error(e);
    }
  };

  const openSubjectModal = () => {
    dispatch(action.modal.openModal(modalActionType.documents.SEARCH_SUBJECT));
  };

  const openChildOfModal = () => {
    dispatch(action.modal.openModal(modalActionType.documents.CHILD_OF));
  };

  const openChangeCompanyModal = async (opt: { value: number; label: string }) => {
    dispatch(
      action.modal.openModal(modalActionType.documents.GO_AHEAD, {
        subtitle: 'change_company_modal',
        func: async () => {
          if ((await saveDocument()) == null) {
            return;
          }
          setCompany(opt);
        },
      }),
    );
  };

  const onCompanyChange = async (opt: { value: number; label: string }) => {
    if (dirty && !autosave) {
      dispatch(
        action.modal.openModal(modalActionType.documents.GO_AHEAD, {
          func: openChangeCompanyModal,
          value: opt,
          dontCloseModal: true,
        }),
      );
    } else {
      openChangeCompanyModal(opt);
    }
  };

  const onDocumentTypeChange = async (opt: { value: number; label: string }) => {
    if (dirty && !autosave) {
      dispatch(action.modal.openModal(modalActionType.documents.GO_AHEAD, { func: setDocumentType, value: opt }));
    } else {
      if ((await saveDocument()) == null) {
        return;
      }
      setDocumentType(opt);
    }
  };

  const handleQ3Movement = (Q3MovType: boolean) => {
    // If the body is empty and Q3 movement type is enabled
    if (nameBody.length === 0 && Q3MovType) {
      if (documentFields?.length) {
        // Find the default Q3 field that should be open
        const defaultQ3Open = documentFields
          .map((doc) => doc.sectionFields)
          .reduce((result, current) => result.concat(current), [])
          .find((field) => field.fixedName === configAutofocus?.val);

        if (defaultQ3Open?.fieldGroup && Q3MovType) {
          dispatch(action.documents.setBodyColumns(defaultQ3Open.fieldGroup.fieldColumn));
          dispatch(action.documents.setBalanceField(defaultQ3Open.fieldGroup.balanceField));
          dispatch(action.documents.setHeaderNameQ3(defaultQ3Open.fieldGroup.name));
          dispatch(action.documents.setBodyName(defaultQ3Open.fieldName));
          dispatch(action.documents.setFieldGroupId(defaultQ3Open.fieldGroup.idFieldGroup));
          dispatch(action.documents.setPredictionsData(defaultQ3Open.fieldGroup.predictionsValues));
        }
      }
    }

    // to show the body based on Q3 movement type
    dispatch(action.documents.setShowBody(Q3MovType));
  };

  const onMovTypeChange = (opt: { label: string; value: string }) => {
    helpersMovType.setValue({
      ...opt,
      changed: true,
    });

    // Find the Q3 movement type
    const Q3MovType = documentHeader?.availableHMoveTypes?.find((e) => e.code === opt.value)?.q3;

    // If a Q3 movement type is found, handle it
    if (Q3MovType !== undefined) {
      handleQ3Movement(Q3MovType);
    }
  };

  const onChildOfInputClick = () => {
    const documentInput: HTMLElement | null = document.getElementById('childof');
    if (documentInput) documentInput.blur();
    if (fieldDocType.value?.option) {
      if (fieldCompany.value && fieldSubject.value) {
        if (dirty) {
          dispatch(
            action.modal.openModal(modalActionType.documents.GO_AHEAD, {
              func: openChildOfModal,
              dontCloseModal: true,
            }),
          );
        } else {
          openChildOfModal();
        }
      } else {
        utils.app.notify('warning', t('subjectOrCompanyMissing'));
      }
    }
  };

  const onStoreInputClick = () => {
    const documentInput: HTMLElement | null = document.getElementById('store');
    if (documentInput) documentInput.blur();
    openSubjectModal();
  };

  const onStoreDetailInputClick = () => {
    const documentInput: HTMLElement | null = document.getElementById('store-details');
    if (documentInput) documentInput.blur();
    dispatch(action.modal.openModal(modalActionType.documents.SUBJECT_DETAILS, { isReadOnly }));
  };

  const checkReadOnly = (value: string) => {
    const val = documentHeader?.readOnlyHeader?.find((e) => e === value);
    return val ? true : false;
  };

  const saveButtonHandler = useCallback(() => {
    if (Object.entries(errors).length !== 0) {
      utils.app.notify('warning', t('Errors on fields detected'));
    }
    submitForm();
  }, [errors, submitForm, t]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'F4' && !autosave && !isReadOnly) {
        event.preventDefault();
        saveButtonHandler();
      }
    };

    window.addEventListener('keydown', handleKeyDown);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
    };
  }, [errors, submitForm, saveButtonHandler, autosave, isReadOnly]);

  return documentHeader ? (
    <DocHeaderContainer>
      <DocHeaderSectionGroup>
        <DocHeaderSectionLeft>
          <DocHeaderTitle>{t('doc-details-title')}</DocHeaderTitle>
          <DocHeaderDetails>
            {t('contrattoN')} {record?.protocolIn}
          </DocHeaderDetails>
          {record?.registrationProtocol && (
            <DocHeaderDetails>
              {t('reg_protocol')} {record?.registrationProtocol}
            </DocHeaderDetails>
          )}
        </DocHeaderSectionLeft>

        <DocHeaderSection>
          <DropDownContainer>
            <InputLabel>{t('company')}</InputLabel>
            <Dropdown
              disabled={!!isReadOnly || checkReadOnly('availableCompanies')}
              value={fieldCompany.value}
              onChange={onCompanyChange}
              name="company"
              options={utils.input.buildOptions(documentHeader.availableCompanies ?? [], 'label', 'idCompany')}
            />
          </DropDownContainer>
          {isContractValidation ? (
            <InputsContainer
              disabled={!!isReadOnly}
              notAllowed={!fieldDocType.value?.option}
              onClick={onChildOfInputClick}
            >
              <TextInput
                id="childof"
                value={fieldChildOf.value || ''}
                disabled={(isReadOnly || !fieldDocType.value?.option) ?? false}
                label={t('childof')}
              />
            </InputsContainer>
          ) : null}
          {!HIDE_MOVE_TYPE ? (
            <DropDownContainer>
              <InputLabel>{t('mov-type')}</InputLabel>
              <Dropdown
                disabled={!!isReadOnly || checkReadOnly('availableHMoveTypes')}
                value={fieldMovType.value}
                onChange={onMovTypeChange}
                name="movType"
                options={utils.input.buildOptions(documentHeader?.availableHMoveTypes ?? [], 'description', 'code')}
              />
            </DropDownContainer>
          ) : null}
          <DropDownContainer>
            <InputLabel>{t('document-type')}</InputLabel>
            <Dropdown
              disabled={isReadOnly || checkReadOnly('availableDocumentType')}
              value={fieldDocType.value}
              onChange={onDocumentTypeChange}
              name="document-type"
              options={utils.input.buildOptions(documentHeader.availableDocumentType || [], 'docName', 'idDocType')}
            />
          </DropDownContainer>

          <InputStoreWrapper>
            <InputsContainer
              disabled={isReadOnly || checkReadOnly('availableSubjects') || false}
              onClick={onStoreInputClick}
            >
              <TextInput
                title={fieldSubject.value?.name || ''}
                borderRadius="5px"
                fontSize={fontSizePalette.body.XS}
                fontWeight="300"
                id="store"
                value={fieldSubject.value?.name || ''}
                label={t('store')}
                disabled={isReadOnly || checkReadOnly('availableSubjects') || false}
              />
            </InputsContainer>
            {!hideAddStoreButton && (
              <ButtonContainer>
                <CommonButton
                  scope="secondary"
                  action={() => dispatch(action.modal.openModal(modalActionType.documents.NEW_STORE))}
                  disabled={isReadOnly || checkReadOnly('availableSubjects')}
                  icon="plus"
                />
              </ButtonContainer>
            )}
          </InputStoreWrapper>
          {SHOW_SUBJECT_DETAILS && idSubject ? (
            <InputStoreWrapper>
              <InputsContainer onClick={onStoreDetailInputClick}>
                <TextInput
                  title={supplierCode.value || ''}
                  borderRadius="5px"
                  fontSize={fontSizePalette.body.XS}
                  fontWeight="300"
                  id="store-details"
                  value={supplierCode.value || ''}
                  label={t('store_details')}
                  autoComplete="off"
                />
              </InputsContainer>
              <IconWrapper>
                <Icon
                  icon="info"
                  label={t('info-vendor-code')}
                  onClick={() => {
                    dispatch(
                      action.modal.openModal(modalActionType.documents.SUPPLIER_IBAN_LIST, {
                        idCompany: fieldCompany.value?.value ?? null,
                        supplierCode: supplierCode?.value ?? null,
                      }),
                    );
                  }}
                />
                {documentHeader?.numberOfDetails && documentHeader?.numberOfDetails > 1 && (
                  <Icon icon="layer-group" label={t('multiple-vendor-code')} />
                )}
              </IconWrapper>
            </InputStoreWrapper>
          ) : null}
          <InputsContainer disabled={true}>
            <TextInput
              title={fieldVatNumber.value ?? ''}
              borderRadius="5px"
              fontSize={fontSizePalette.body.XS}
              fontWeight="300"
              id="vat-number"
              value={fieldVatNumber.value ?? ''}
              label={t('vatNumber')}
              disabled={true}
            />
          </InputsContainer>
        </DocHeaderSection>
      </DocHeaderSectionGroup>

      <DocHeaderSection>
        {!autosave ? (
          <CommonButton
            disabled={isReadOnly}
            scope="primary"
            value={t('save-document')}
            action={() => {
              Object.entries(errors).length !== 0 && utils.app.notify('warning', t('Errors on fields detected'));
              submitForm();
            }}
          />
        ) : null}
      </DocHeaderSection>
    </DocHeaderContainer>
  ) : null;
};

export default DocHeader;
