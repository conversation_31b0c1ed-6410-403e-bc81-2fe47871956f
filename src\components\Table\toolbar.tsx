import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import Button from 'components/Buttons/CommonButton';
import { useTheme } from 'providers/ThemeProvider';
import React from 'react';
import styled from 'styled-components/macro';
import intlHelper from 'utils/helpers/intl.helper';

const ToolbarWrapper = styled.div<{ width?: string }>`
  display: flex;
  justify-content: space-between;
  align-items: center;
  ${({ width }) => (width ? `width: ${width}` : null)}
`;

const Results = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
`;

interface Props {
  results: number;
  onRemoveFilters: any;
  hasFilters: number;
  hasExport?: boolean;
  onExportClick?: React.MouseEventHandler<SVGSVGElement>;
  width?: string;
}

const Toolbar = (props: Props) => {
  const { results, onRemoveFilters, hasFilters, hasExport, onExportClick } = props;
  const t = intlHelper.useTranslator();
  const { theme, isDarkMode } = useTheme();
  return (
    <ToolbarWrapper width={props.width}>
      <Results>{`${results} ${t('results')}`}</Results>
      <div>
        {hasExport ? (
          <FontAwesomeIcon
            icon="file-csv"
            onClick={onExportClick}
            style={{ color: isDarkMode ? theme.colorPalette.turquoise.light : theme.colorPalette.grey.grey6 }}
          />
        ) : null}
        <Button
          icon="trash"
          value={t('removeFilters')}
          scope="tertiary"
          disabled={hasFilters === 0}
          action={onRemoveFilters}
        />
      </div>
    </ToolbarWrapper>
  );
};

export default Toolbar;
