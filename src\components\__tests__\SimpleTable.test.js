import React from 'react';
import { screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import '@testing-library/jest-dom/extend-expect';
import SimpleTable from 'components/SimpleTable/SimpleTable';
import { renderWithStyle } from 'utils/helpers/test.helpers';

describe('SimpleTable Component', () => {
  test('renders table with a single data item', () => {
    const data = [{ title: 'Title', info: 'Info' }];
    const title = 'Table Title';
    const table = <SimpleTable data={data} title={title} />;
    renderWithStyle(table);
    screen.debug();
    const titleElement = screen.getByText(data[0].title);
    const infoElement = screen.getByText(data[0].info);
    expect(titleElement).toBeInTheDocument();
    expect(infoElement).toBeInTheDocument();
  });
  test('renders table with multiple data items', () => {
    const data = [
      { title: 'Title0', info: 'Info0' },
      { title: 'Title1', info: 'Info1' },
    ];
    const title = 'Table Title';
    const table = <SimpleTable data={data} title={title} />;
    renderWithStyle(table);
    // verify if there is multiple data items
    for (let i = 0; i < data.length; i++) {
      const titleElement = screen.getByText(data[i].title);
      const infoElement = screen.getByText(data[i].info);
      expect(titleElement).toBeInTheDocument();
      expect(infoElement).toBeInTheDocument();
    }
  });
  test('renders table with the title', () => {
    const data = [{ title: 'Title', info: 'Info' }];
    const title = 'Table Title';
    const table = <SimpleTable data={data} title={title} />;
    renderWithStyle(table);
    const titleElement = screen.getByText(title);
    expect(titleElement).toBeInTheDocument();
  });
  test('renders table with no data', () => {
    const data = [];
    const title = 'Table Title';
    const table = <SimpleTable data={data} title={title} />;
    renderWithStyle(table);
    const tbodyElement = screen.getByTestId('body');
    expect(tbodyElement).toBeEmptyDOMElement();
  });
  test('renders table with right style', () => {
    const data = [{ title: 'Title', info: 'Info' }];
    const title = 'Table Title';
    const table = <SimpleTable  data={data} title={title} />;
    renderWithStyle(table);
    const tableElement = screen.getByTestId('table');
    expect(tableElement).toBeInTheDocument();
    expect(tableElement).toHaveStyle('border: 0px');
    expect(tableElement).toHaveStyle('border-collapse: collapse');
    expect(tableElement).toHaveStyle('margin: 0px');
  });
});
