/* eslint max-lines: 0 */
import React, { useEffect, useCallback, useState, useRef } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import utils from 'utils';
import action from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components/macro';
import Dropdown from 'components/Input/Dropdown';
import Checkbox from 'components/Buttons/CheckboxButton';
import CommonButton from 'components/Buttons/CommonButton';
import TextInput from 'components/Input/TextInput';
import { Form, Formik } from 'formik';
import { CompanyWithDocAndMeta, DocumentTypeWithMeta } from 'models/response';

import Modal from 'components/Modal';
import ModalContent from '../components/ModalsContent';
import { modalActionType } from 'utils/constants';

const Container = styled.div`
  display: flex;
  text-align: center;
  justify-content: center;
`;

const Helper = styled.div`
  border: 1px solid grey;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  margin-left: 5px;
  position: relative;
  cursor: pointer;
`;

const I = styled.span`
  position: absolute;
  top: 1px;
  left: 0;
  right: 0;
  bottom: 0;
`;

const CheckContainer = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  margin-left: 200px;
`;

const DropContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: end;
  margin-bottom: 20px;

  p {
    margin: 0 20px;
  }
`;

const InputClickContainer = styled.div`
  margin-bottom: 15px;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  input {
    cursor: pointer;
  }
  p {
    margin: 0 20px;
  }
`;

const P = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.XS};
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
`;

const CheckText = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.XS};
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  margin-left: 10px;
`;

const Left = styled.div`
  margin-right: 40px;
`;

const Right = styled.div``;

export interface FormData {
  company: { label: string; value: number } | null;
  docType: { label: string; value: number } | null;
  subject: { label: string; value: number } | null;
  remember: boolean | undefined;
  pageManagement: string | null;
  idParent: number | null;
}

export interface Props {
  mode?: string;
  splitString?: string;
  splitStringError?: boolean;
  handleSplitStringChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  companiesWithDoc?: CompanyWithDocAndMeta[];

  activeFile?: string;
  pageRotation?: number;
  pageNumber?: number;

  setRemember?: (val: boolean) => void;
  remember?: boolean;
  setRememberdata?: (data: FormData | null) => void;
  rememberData?: FormData | null;

  splitDocument?: (e?: React.SyntheticEvent, force?: boolean) => void;
  percentage: number;
  filename: string;
}

const Bottom = (props: Props) => {
  const {
    mode,
    splitString,
    splitStringError,
    handleSplitStringChange,
    companiesWithDoc,

    activeFile,
    pageRotation,
    pageNumber,
    percentage,
    filename,

    setRemember,
    remember,
    setRememberdata,
    rememberData,
    splitDocument,
  } = props;
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const formikRef = useRef<any>();
  const [selectedCompany, setSelectedCompany] = useState<CompanyWithDocAndMeta | null>(null);
  const [selectedDocType, setSelectedDocType] = useState<DocumentTypeWithMeta | null>(null);
  // Modal
  const isModalVisible = useSelector(selectors.modal.getVisibility);
  const closeModal = () => dispatch(action.modal.closeModal());

  const onSubmit = () => {
    splitDocument && splitDocument();
  };

  useEffect(() => {
    formikRef.current.setFieldValue('pageManagement', splitString);
  }, [splitString]);

  const syncStates = () => {
    if (remember && rememberData && companiesWithDoc) {
      const company = companiesWithDoc?.find((ele) => ele.idCompany === rememberData?.company?.value);
      setSelectedCompany(company || null);
      const docType = company?.documentTypes?.find((ele) => ele.idDocType === rememberData?.docType?.value);
      setSelectedDocType(docType || null);
    }
  };

  const stableSyncStates = useCallback(syncStates, []);

  useEffect(() => {
    stableSyncStates();
  }, [stableSyncStates]);

  useEffect(() => {
    if (
      companiesWithDoc?.length === 1 &&
      (!selectedCompany || selectedCompany.idCompany !== companiesWithDoc[0].idCompany)
    ) {
      const singleCompany = companiesWithDoc[0];
      const companyObj = { value: singleCompany.idCompany, label: singleCompany.name };

      setSelectedCompany(singleCompany);

      if (setRememberdata) {
        setRememberdata({ ...formikRef.current?.values, company: companyObj });
      }

      formikRef.current?.setFieldValue('company', companyObj);
    }
  }, [companiesWithDoc, selectedCompany, setRememberdata]);

  const openSubjectModal = (values: FormData) => {
    dispatch(
      action.modal.openModal(modalActionType.pdfUploader.SEARCH_SUBJECT, {
        func: setRememberdata && setRememberdata,
        values,
      }),
    );
  };

  const onStoreInputClick = (values: FormData) => {
    const documentInput: HTMLElement | null = document.getElementById('subject');
    if (documentInput) documentInput.blur();
    openSubjectModal(values);
  };

  const renderInitialValues = () => {
    if (remember && rememberData) {
      return rememberData;
    } else {
      return {
        company: null,
        docType: null,
        subject: null,
        remember,
        pageManagement: null,
        idParent: null,
      };
    }
  };
  const actionType = useSelector(selectors.modal.getActionType);
  const isClosableOrDraggable = actionType !== modalActionType.pdfUploader.UPLOAD_PROGRESS;
  return companiesWithDoc ? (
    <Formik
      // @ts-ignore
      innerRef={formikRef}
      initialValues={renderInitialValues()}
      onSubmit={onSubmit}
    >
      {({ values, setFieldValue, handleSubmit }) => {
        return (
          <Form onSubmit={handleSubmit}>
            <Modal
              onClose={closeModal}
              open={isModalVisible}
              isDraggable={isClosableOrDraggable}
              isClosable={isClosableOrDraggable}
            >
              <ModalContent
                filename={filename}
                percentage={percentage}
                activeFile={activeFile}
                pageRotation={pageRotation}
                pageNumber={pageNumber}
              />
            </Modal>
            <Container>
              <Left>
                <DropContainer>
                  <P>{t('Company')}</P>
                  <Dropdown
                    value={values.company}
                    onChange={(e) => {
                      const company = companiesWithDoc.find((ele) => ele.idCompany === e.value);
                      company && setSelectedCompany(company);
                      setRememberdata && setRememberdata({ ...values, company: e });
                      setFieldValue('company', e);
                      setFieldValue('docType', '');
                      setSelectedDocType(null);
                      setFieldValue('subject', '');
                      setFieldValue('idParent', '');
                    }}
                    name="company"
                    options={utils.input.buildOptions(companiesWithDoc, 'name', 'idCompany', true)}
                    disabled={companiesWithDoc.length === 1}
                  />
                </DropContainer>
                <DropContainer>
                  <P>{t('Document Type')}</P>
                  <Dropdown
                    disabled={!selectedCompany}
                    value={values.docType}
                    onChange={(e) => {
                      const docType = selectedCompany?.documentTypes.find((ele) => ele.idDocType === e.value);
                      docType && setSelectedDocType(docType);
                      setRememberdata && setRememberdata({ ...values, docType: e });
                      setFieldValue('docType', e);
                      setFieldValue('subject', '');
                      setFieldValue('idParent', '');
                    }}
                    name="docType"
                    options={utils.input.buildOptions(
                      selectedCompany?.documentTypes || [],
                      'docName',
                      'idDocType',
                      true,
                    )}
                  />
                </DropContainer>
                {selectedDocType?.metadataList?.find((ele) => ele.idMetadata === 1) ? (
                  <InputClickContainer onClick={() => onStoreInputClick(values)}>
                    <P>{t('Subject')}</P>
                    <TextInput id="subject" value={values.subject ? values.subject.label : ''} />
                  </InputClickContainer>
                ) : null}
                <CheckContainer>
                  <Checkbox
                    action={() => {
                      setRemember && setRemember(!remember);
                      setRememberdata && setRememberdata(values);
                    }}
                    isActive={remember || false}
                    name="name"
                  />
                  <CheckText>{t('remember')}</CheckText>
                </CheckContainer>
              </Left>
              <Right>
                <DropContainer>
                  <P>{t('Pages Management')}</P>
                  <TextInput
                    disabled={mode === 'all'}
                    id="pageManagement"
                    hasFeedback={splitStringError}
                    value={values.pageManagement || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                      handleSplitStringChange && handleSplitStringChange(e);
                      setFieldValue('pageManagement', e.target.value);
                    }}
                    placeholder={t('page-management-placeholder')}
                  />
                  <Helper>
                    <I>i</I>
                  </Helper>
                </DropContainer>
                {selectedDocType?.metadataList?.find((ele) => ele.idMetadata === 2) ? (
                  <DropContainer>
                    <P>{t('ID Parent Document')}</P>
                    <div style={{ width: 110, paddingRight: 7 }}>
                      <TextInput
                        id="idParent"
                        value={values.idParent || ''}
                        disabled={true}
                        fullWidth
                        placeholder={t('id-parent-placeholder')}
                      />
                    </div>
                    <CommonButton
                      scope="secondary"
                      type="button"
                      value={'search'}
                      action={() =>
                        dispatch(
                          action.modal.openModal(modalActionType.pdfUploader.CHILD_OF, {
                            func: setRememberdata && setRememberdata,
                            values,
                          }),
                        )
                      }
                      disabled={!values.subject || !selectedDocType.genericOption}
                    />
                  </DropContainer>
                ) : null}
              </Right>
            </Container>
            <div style={{ textAlign: 'center' }}>
              <CommonButton
                disabled={!values.company || !values.docType}
                type="submit"
                scope="primary"
                value={t('confirm-process')}
              />
            </div>
          </Form>
        );
      }}
    </Formik>
  ) : null;
};

export default Bottom;
