import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import utils from 'utils';
import selectors from 'state/selectors';
import CommonButton from 'components/Buttons/CommonButton';
import { modalActionType } from 'utils/constants';
import { AddDeleteUserCompanyRole } from 'models/request';
import services from 'services';
import { actionType } from 'utils/constants';

const DeleteRole = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();

  const selectedCompanyId = useSelector(selectors.configurator.getSelectedCompanyForRoles);
  const companies = useSelector(selectors.configurator.getCompaniesForRoles);
  const selectedUser = useSelector(selectors.configurator.getSelectedUserForRoles);
  const isEditingUserRows = useSelector(selectors.configurator.getIsEditingUserRows);

  const getRolesToDelete = () => {
    const roles: AddDeleteUserCompanyRole[] = [];
    if (selectedCompanyId !== null) {
      if (selectedUser.length === 1 && selectedUser[0]?.idRole !== null) {
        const role = {
          idUser: selectedUser[0].idUser,
          idCompany: selectedCompanyId,
          idRole: selectedUser[0].idRole,
        };
        roles.push(role);
      } else if (selectedUser.length > 1) {
        for (let i = 1; i < selectedUser.length; i++) {
          const role: AddDeleteUserCompanyRole = {
            idUser: selectedUser[i].idUser,
            idCompany: selectedCompanyId,
            idRole: selectedUser[i].idRole || 0,
          };
          roles.push(role);
        }
      }
    }
    return roles;
  };

  const onConfirDeleteRole = async () => {
    if (selectedCompanyId !== null) {
      try {
        await services.deleteUserCompanyRoles(getRolesToDelete());
        const { data } = await services.getUserCompanyRoles(selectedCompanyId);
        dispatch(actions.configurator.setUsersForCompanyRoles(data));
        dispatch(actions.modal.closeModal());
        utils.app.notify('success', t('user_role_deleted'));
      } catch (e) {
        console.error(e);
      }
    }
  };

  return (
    <CommonButton
      action={() =>
        dispatch(
          actions.modal.setModal({
            actionType: modalActionType.configurator.DELETE_CONFIRMATION,
            props: {
              subtitle: t('delete-rows-role'),
              func: () => onConfirDeleteRole(),
            },
          }),
        )
      }
      disabled={
        !selectedUser.length ||
        (selectedUser.length === 1 && selectedUser[0].idRole === null) ||
        isEditingUserRows.length > 0 ||
        !utils.user.isActionByCompanyActive(
          actionType.DELETE_ROLE,
          companies.find((e) => e.idCompany === selectedCompanyId).name,
        )
      }
      scope="tertiary"
      value={t('DeleteRole')}
      icon="circle"
    />
  );
};

export default DeleteRole;
