/* eslint max-lines: 0 */
/**
 * @deprecated This file is deprecated and will be removed in a future version.
 * Please use the new implementation in the updated module.
 */
import React, { useCallback, useEffect, useRef, useState, MouseEvent } from 'react';
// @ts-ignore
import { Document, Page, pdfjs } from 'react-pdf';
import styled from 'styled-components/macro';
import { colorPalette } from 'utils/styleConstants';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { BoundingBox } from 'models/response';
import { FormValues } from 'models/documents';
import utils from 'utils';
import { useMeasure } from 'react-use';
import Preloader from 'components/Preloader';
import iconOcr from 'images/lucy4/icon_ocr.svg';
import iconOcrActive from 'images/lucy4/icon_ocr_active.svg';
import iconOcrClick from 'images/lucy4/icon_ocr_click.svg';
import iconOcrClickActive from 'images/lucy4/icon_ocr_click_active.svg';
import iconOcrArea from 'images/lucy4/icon_ocr_area.svg';
import iconOcrAreaActive from 'images/lucy4/icon_ocr_area_active.svg';
import { useDispatch, useSelector } from 'react-redux';
import selectors from 'state/selectors';
import actions from 'state/actions';
import services from 'services';
import { useFormikContext } from 'formik';
import { Q1Value } from 'models/documents';
import { PageInput, PdfButton, PdfToolbar } from './advancedPdf.style';
import { OCRError } from 'utils/helpers/errors.helpers';
import TopPage from 'images/topPage';
import { useTheme } from 'providers/ThemeProvider';

pdfjs.GlobalWorkerOptions.workerSrc = `${window.location.origin}/${pdfjs.version}.pdf.worker.min.js`;

const OcrIcon = styled.img`
  height: 20px;
  margin-top: 5px;
`;

const PdfWrapper = styled.div<{ height: string }>`
  width: 100%;
  height: ${({ height }) => (height ? `${height}` : 'auto')};
  border: 1px solid ${({ theme }) => theme.colorPalette.grey.grey5};
  border-radius: 5px;
  position: relative;
`;

const PageNavigation = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-width: 90px;
`;

const VerticalBar = styled.div`
  background-color: ${({ theme }) => theme.colorPalette.grey.grey5};
  height: 26px;
  width: 1px;
  border-radius: 0.5px;
  margin-left: 10px;
`;

const NumPages = styled.p`
  font-family: Roboto;
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  font-weight: ${({ theme }) => theme.fontWeightPalette.medium};
  font-size: ${({ theme }) => theme.fontSizePalette.body.XS};
`;

const Left = styled.div`
  display: flex;
  align-items: center;

  > * {
    margin-right: 10px;
  }
`;

const Center = styled.div`
  display: flex;
  align-items: center;
  > * {
    margin-right: 10px;
  }
`;

const Right = styled.div`
  display: flex;
  align-items: center;

  > * {
    margin-left: 15px;
  }
`;

const Zoom = styled.div`
  display: flex;
  align-items: center;

  > * {
    margin-right: 10px;
  }
`;

const InputContainer = styled.div`
  margin: 0 10px;
  display: flex;
  align-items: center;
`;


const PdfDocument = styled.div`
  border-radius: 5px;
  z-index: ${({ theme }) => theme.zIndexPalette.lowest};
  width: 100%;
  height: calc(100% - 50px);
  background-color: ${({ theme }) => theme.colorPalette.grey.grey1};
  overflow: auto;
  canvas {
    margin: auto;
  }
`;

const Rect = styled.div<{ left: number; top: number; height: number; width: number }>`
  background-color: yellow;
  position: absolute;
  width: ${({ width }) => `${width}px`};
  height: ${({ height }) => `${height}px`};
  top: ${({ top }) => `${top}px`};
  left: ${({ left }) => `${left}px`};
  z-index: ${({ theme }) => theme.zIndexPalette.low};
  opacity: 0.3;
`;

const CreateRect = styled.div`
  position: absolute;
  border: 1px dotted black;
  background-color: yellow;
  opacity: 0.3;
  z-index: ${({ theme }) => theme.zIndexPalette.high};
`;

export interface AdvancedPdfProps {
  docImage: string;
  fixedHeight?: string;
}

const selectionRect = { x1: 0, y1: 0, x2: 0, y2: 0 };

const AdvancedPdf = (props: AdvancedPdfProps) => {
  const { docImage, fixedHeight } = props;

  const pdfContainer = useRef<HTMLDivElement | null>(null);
  const protocol = useSelector(selectors.documents.selectActiveDocumentID);
  const [numPages, setNumPages] = useState(0);
  const [pageNumber, setPageNumber] = useState(1);
  const [zoom, setZoom] = useState(1);
  const [pageWidth, setWidth] = useState<number | null>(null);
  const [pageHeight, setHeight] = useState<number | null>(null);
  const [rotation, setRotation] = useState(0);
  const page = useRef<HTMLCanvasElement | null>(null);
  const [fitMode, setFitMode] = useState('width');
  const [sizeRef, { width, height }] = useMeasure();
  const suggestedBoxRef = useRef<HTMLDivElement>(null);
  const [ocrMode, setOcrMode] = useState('area');
  const [isResizing, setIsResizing] = useState(false);
  const dispatch = useDispatch();
  const q3FocusData = useSelector(selectors.documents.selectFocussedQ3Cell);
  const q1FocusData = useSelector(selectors.documents.selectFocussedQ1Cell);
  const nameBodyQ3 = useSelector(selectors.documents.selectBodyName) || '';
  const t = utils.intl.useTranslator();
  const { setFieldValue, values } = useFormikContext<{ [key: string]: Q1Value }>();
  const activeRectangle = useSelector(selectors.documents.selectActiveBox);
  const [translateActive, setTranlsateActive] = useState<boolean>(false);
  const isArchived = useSelector(selectors.documents.selectIsTemplateArchived);

  // OCR area selection
  const div = document.getElementById('resize-div');
  const reCalc = (canvasRef: any) => {
    if (isResizing) {
      const x3 = Math.min(selectionRect.x1, selectionRect.x2);
      const x4 = Math.max(selectionRect.x1, selectionRect.x2);
      const y3 = Math.min(selectionRect.y1, selectionRect.y2);
      const y4 = Math.max(selectionRect.y1, selectionRect.y2);

      if (div) {
        // adding left margin of the pdf canvas
        div.style.left = x3 + canvasRef.current.offsetLeft + 'px';
        div.style.top = y3 + 'px';
        div.style.width = x4 - x3 + 'px';
        div.style.height = y4 - y3 + 'px';
      }
    }
  };

  const onMouseDown = (e: MouseEvent, container: any) => {
    if (div) {
      setIsResizing(true);
      div.hidden = false;
      const rect = container.current.getBoundingClientRect();
      selectionRect.x1 = e.clientX - rect.left;
      selectionRect.y1 = e.clientY - rect.top;
      reCalc(container);
    }
  };

  const onMouseMove = (e: MouseEvent, container: any) => {
    if (isResizing) {
      e.persist();
      const rect = container.current.getBoundingClientRect();
      selectionRect.x2 = e.clientX - rect.left;
      selectionRect.y2 = e.clientY - rect.top;
      reCalc(container);
    }
  };

  const valueQ1InputFromOCR = ({
    q1FieldName,
    content,
    boundingBox,
    pageNumber,
  }: {
    q1FieldName: string;
    content: string;
    boundingBox: BoundingBox;
    pageNumber: number;
  }) => {
    const { multi, index } = q1FocusData;
    const arrayValues = [...values[q1FieldName].content];
    arrayValues[multi && (index || index === 0) ? index : 0] = {
      ...values[q1FieldName].content[multi && (index || index === 0) ? index : 0],
      content,
      confidence: -1,
      boundingBox: {
        x1: boundingBox.x1,
        x2: boundingBox.x2,
        y1: boundingBox.y1,
        y2: boundingBox.y2,
      },
      pageNumber,
    };
    setFieldValue(q1FieldName, { ...values[q1FieldName], content: arrayValues, status: 3 } as FormValues['Q1Value']);
  };

  // when OCR rectangle is drawn
  const onMouseUp = async (e: MouseEvent, container: any, pageNumber: number) => {
    if (isResizing && div && protocol) {
      setIsResizing(false);
      div.hidden = true;
      const rect = container.current.getBoundingClientRect();

      try {
        const x1 = utils.converter.scaleValue(selectionRect.x1, rect.width, 100).toFixed(4);
        const y1 = utils.converter.scaleValue(selectionRect.y1, rect.height, 100).toFixed(4);
        const x2 = utils.converter.scaleValue(selectionRect.x2, rect.width, 100).toFixed(4);
        const y2 = utils.converter.scaleValue(selectionRect.y2, rect.height, 100).toFixed(4);

        // sort coordinates
        const dataToSort = [
          [x1, y1],
          [x2, y2],
        ];
        const sorted = dataToSort.sort((a: any, b: any) => {
          return a[0] === b[0] ? a[1] - b[1] : a[0] - b[0];
        });

        const q1FieldName = q1FocusData.name;
        const fieldType = q3FocusData ? q3FocusData.type : q1FocusData.fieldInputType;
        const { data } = await services.getZoneText({
          protocol,
          pageNumber,
          fieldType,
          x1: sorted[0][0],
          y1: sorted[0][1],
          x2: sorted[1][0],
          y2: sorted[1][1],
        });

        if (data.content?.length === 0 || !data.content) {
          throw new OCRError(t('Empty or null prediction'));
        }

        // if we get an invalid lucy date format dd-mm-yyy
        // if a can covert i have a valid date
        if (data.fieldType === '1') utils.date.convertToObjectDate(data.content);

        // q3 case
        if (q3FocusData?.columnId) {
          const oldBody = values[nameBodyQ3].body;
          const newBody = oldBody?.map((el: any) =>
            Number(el.id) === q3FocusData.rowId
              ? {
                  ...el,
                  [q3FocusData.columnId]: {
                    content: data.content,
                    pageNumber: pageNumber - 1,
                    boundingBox: { x1: data.x1, y1: data.y1, x2: data.x2, y2: data.y2 },
                  },
                }
              : el,
          );

          setFieldValue(nameBodyQ3, { ...values[nameBodyQ3], body: newBody });
        }

        // q1 case
        if (q1FieldName.length > 0) {
          valueQ1InputFromOCR({
            q1FieldName,
            content: data.content,
            pageNumber: pageNumber - 1,
            boundingBox: {
              x1: data.x1,
              x2: data.x2,
              y1: data.y1,
              y2: data.y2,
            },
          });
        }
        dispatch(actions.documents.setFocussedQ1({ fieldInputType: 0, name: '' }));
        dispatch(actions.documents.setQ3Cell(null));
      } catch (e) {
        console.error(e);
        utils.app.notify('warning', e as Error, 3000, 'ocr-error');
      }

      div.style.width = '0px';
      div.style.height = '0px';
    }
  };

  const getCoordinates = async (e: MouseEvent, containerRef: any, pageNumber: number) => {
    if (protocol && containerRef) {
      const getCordinates = (container: any) => {
        const rect = container.current.getBoundingClientRect();

        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        try {
          return {
            x: utils.converter.scaleValue(x, rect.width, 100).toFixed(4),
            y: utils.converter.scaleValue(y, rect.height, 100).toFixed(4),
          };
        } catch (e) {
          console.error(e);
        }
      };

      const { x, y } = getCordinates(containerRef) || {};

      if (x && y) {
        try {
          const q1FieldName = q1FocusData.name;
          const fieldType = q3FocusData ? q3FocusData.type : q1FocusData.fieldInputType;
          const { data } = await services.getZoneText({
            protocol,
            pageNumber,
            fieldType,
            x1: x,
            y1: y,
          });

          if (data.content?.length === 0 || !data.content) {
            throw new OCRError(t('Empty or null prediction'));
          }

          // q3 case
          if (q3FocusData?.columnId) {
            const oldBody = values[nameBodyQ3].body;
            const newBody = oldBody?.map((el: any) =>
              Number(el.id) === q3FocusData.rowId
                ? {
                    ...el,
                    [q3FocusData.columnId]: {
                      content: data.content,
                      pageNumber: pageNumber - 1,
                      boundingBox: { x1: data.x1, y1: data.y1, x2: data.x2, y2: data.y2 },
                    },
                  }
                : el,
            );

            // if we get an invalid lucy date format dd-mm-yyy
            // if a can covert i have a valid date
            if (data.fieldType === '1') utils.date.convertToObjectDate(data.content);

            setFieldValue(nameBodyQ3, { ...values[nameBodyQ3], body: newBody });
          }
          // q1 case
          if (q1FieldName.length > 0) {
            // set new q1 field value
            valueQ1InputFromOCR({
              q1FieldName,
              content: data.content,
              pageNumber: pageNumber - 1,
              boundingBox: {
                x1: data.x1,
                x2: data.x2,
                y1: data.y1,
                y2: data.y2,
              },
            });
          }

          // reset states
          dispatch(actions.documents.setFocussedQ1({ fieldInputType: 0, name: '' }));
          dispatch(actions.documents.setQ3Cell(null));
        } catch (e) {
          console.error(e);
          utils.app.notify('warning', e as Error, 3000, 'ocr-error');
        }
      }
    }
  };

  // rectangle
  const [suggestedBox, setSuggestedBox] = useState<any>([]);

  const alignSuggestionBox = () => {
    if (suggestedBoxRef?.current && pdfContainer?.current) {
      const rect = suggestedBoxRef.current;
      const heightCenter = pdfContainer.current.clientHeight / 2;
      const widthCenter = pdfContainer.current.clientWidth / 2;
      const left = rect.offsetLeft + rect.clientWidth / 2;
      const top = rect.offsetTop + rect.clientHeight / 2;
      pdfContainer.current.scrollLeft = left - widthCenter;
      pdfContainer.current.scrollTop = top - heightCenter;
    }
  };

  const alignmentDependency = suggestedBox[0]?.key;

  useEffect(() => {
    setTimeout(() => alignSuggestionBox(), 200);
    // to be called only when suggest box actually changes
  }, [alignmentDependency]);

  const createSuggestedBox = useCallback(() => {
    try {
      if (!activeRectangle) return;
      if (rotation !== 0) {
        setSuggestedBox([]);
        utils.app.notify('warning', t('no-suggestion-when-pdf-is-rotated'), 2000, 'rotated-pdf');
      }
      const { x1, x2, y1, y2 } = activeRectangle;
      // checking if the box is null(when all values are 0)
      if (x1 === 0 && x2 === 0 && y1 === 0 && y2 === 0) {
        utils.app.notify('warning', t('null-box'), 5000, 'null-box');
        return;
      }
      if (!(x2 > x1 && y2 > y1)) {
        throw new Error(t('Invalid suggestion box size!'));
      }
      const divsArray: any[] = [];
      const pdfHeight = page.current?.clientHeight;
      const pdfWidth = page.current?.clientWidth;
      const top = page.current?.offsetTop || 0;
      const left = page.current?.offsetLeft || 0;
      if (page.current && pdfHeight && pdfWidth && activeRectangle) {
        const scaledX1 = utils.converter.scaleValue(activeRectangle.x1, 1, pdfWidth);
        const scaledY1 = utils.converter.scaleValue(activeRectangle.y1, 1, pdfHeight);
        const scaledX2 = utils.converter.scaleValue(activeRectangle.x2, 1, pdfWidth);
        const scaledY2 = utils.converter.scaleValue(activeRectangle.y2, 1, pdfHeight);
        // calculate rectangle width and height
        const width = scaledX2 - scaledX1;
        const height = scaledY2 - scaledY1;
        if (width < 4 && height < 4)
          utils.app.notify('warning', t('Suggestion box size is too small'), 5000, 'small-box');
        divsArray.push(
          <Rect
            key={Math.random()}
            ref={suggestedBoxRef}
            height={height}
            width={width}
            top={scaledY1 + top}
            left={scaledX1 + left}
          />,
        );
      }
      setSuggestedBox(divsArray);
      setPageNumber(activeRectangle.pageNumber + 1);
    } catch (e) {
      utils.app.notify('fail', `error: ${e}`);
    }
  }, [activeRectangle, t, rotation]);

  useEffect(() => {
    window.addEventListener('resize', createSuggestedBox);

    return () => {
      window.removeEventListener('resize', createSuggestedBox);
    };
  }, [createSuggestedBox]);

  useEffect(() => {
    createSuggestedBox();
  }, [createSuggestedBox, zoom, activeRectangle, width, height]);

  const zoomOut = () => zoom > 0.25 && setZoom(zoom - 0.25);
  const zoomIn = () => zoom < 2.5 && setZoom(zoom + 0.25);
  const pageNext = () => pageNumber < numPages && setPageNumber(pageNumber + 1);
  const pagePrevious = () => pageNumber > 1 && setPageNumber(pageNumber - 1);
  const lastPage = () => setPageNumber(numPages);
  const firstPage = () => setPageNumber(1);
  const rotateClockwise = () => setRotation((prevRotation) => (prevRotation + 90) % 360);
  const rotateCounterClockwise = () => setRotation((prevRotation) => (prevRotation - 90 + 360) % 360);

  const fitToWidth = () => {
    setFitMode('height');
    const ref = pdfContainer.current;
    setZoom(1);
    setWidth(ref?.clientWidth ? ref?.clientWidth - 20 : null);
    activeRectangle && createSuggestedBox();
  };

  const fitToHeight = () => {
    setFitMode('width');
    const ref = pdfContainer.current;
    setZoom(1);
    setWidth(null);
    setHeight(ref?.clientHeight || null);
    activeRectangle && createSuggestedBox();
  };

  const onDocumentLoadSuccess = (pdf: { numPages: number }) => {
    setNumPages(pdf.numPages);
    setPageNumber(1);
    fitToWidth();
  };
  const isOcrActive = rotation === 0 && (q1FocusData.name.length > 0 || q3FocusData != null);

  const isButtonTranslateDisabled = numPages > 20;

  const translateDoc = async (toActive: boolean) => {
    try {
      if (isButtonTranslateDisabled) return;
      if (protocol) {
        if (toActive) {
          const { data } = await services.translateDocument(protocol?.toString(), 'en');
          dispatch(actions.documents.setDocumentImage(data));
          setTranlsateActive(true);
          utils.app.notify('success', t('document-translated'));
        } else {
          const { data } = await services.getDocumentImage(protocol);
          dispatch(actions.documents.setDocumentImage(data));
          setTranlsateActive(false);
        }
      }
    } catch (e) {
      utils.app.notify('warning', e as Error);
    }
  };

  const checkTooltip = isButtonTranslateDisabled
    ? t('doc-much-pages')
    : translateActive
    ? t('back-to-pdf')
    : t('translate');
  const { isDarkMode } = useTheme();
  return (
    <PdfWrapper ref={sizeRef} height={fixedHeight ? fixedHeight : 'auto'}>
      <Preloader area="pdf" />
      <PdfToolbar>
        <Left>
          {fitMode === 'height' ? (
            <PdfButton
              style={!isDarkMode ? { color: colorPalette.grey.grey9 } : {}}
              disabled={false}
              onClick={() => {
                fitToHeight();
              }}
            >
              <FontAwesomeIcon size="lg" icon="arrows-alt-v" />
            </PdfButton>
          ) : (
            <PdfButton
              style={!isDarkMode ? { color: colorPalette.grey.grey9 } : {}}
              disabled={false}
              onClick={() => {
                fitToWidth();
              }}
            >
              <FontAwesomeIcon size="lg" icon="arrows-alt-h" />
            </PdfButton>
          )}
        </Left>
        <Center>
          <PdfButton disabled={false} onClick={rotateCounterClockwise}>
            <FontAwesomeIcon size="lg" icon="undo-alt" />
          </PdfButton>
          <PdfButton disabled={false} onClick={rotateClockwise}>
            <FontAwesomeIcon size="lg" icon="redo-alt" />
          </PdfButton>
          <VerticalBar />
          <PageNavigation>
            <PdfButton disabled={pageNumber === 1} onClick={firstPage}>
              <TopPage></TopPage>
            </PdfButton>
            <PdfButton disabled={pageNumber === 1} onClick={pagePrevious}>
              <FontAwesomeIcon size="lg" icon="arrow-up" />
            </PdfButton>
            <InputContainer>
              <PageInput onChange={(e: any) => setPageNumber(Number(e.target.value))} value={pageNumber} />
              <NumPages>/{numPages}</NumPages>
            </InputContainer>
            <PdfButton disabled={pageNumber === numPages} onClick={pageNext}>
              <FontAwesomeIcon size="lg" icon="arrow-down" />
            </PdfButton>
            <PdfButton disabled={pageNumber === numPages} onClick={lastPage}>
              <TopPage rotation={180}></TopPage>
            </PdfButton>
          </PageNavigation>
          <VerticalBar />
          <Zoom>
            <PdfButton disabled={false} onClick={zoomOut}>
              <FontAwesomeIcon size="lg" icon="search-minus" />
            </PdfButton>
            <PdfButton disabled={false} onClick={zoomIn}>
              <FontAwesomeIcon size="lg" icon="search-plus" />
            </PdfButton>
          </Zoom>
        </Center>
        <Right>
          {isOcrActive ? <OcrIcon alt="ocr-icon" src={iconOcrActive} /> : <OcrIcon alt="ocr-icon" src={iconOcr} />}
          <PdfButton
            style={{ color: ocrMode === 'area' ? colorPalette.turquoise.normal : colorPalette.grey.grey9 }}
            disabled={false}
            onClick={() => setOcrMode('area')}
          >
            {ocrMode === 'area' ? (
              <OcrIcon alt="ocr-icon" src={iconOcrAreaActive} />
            ) : (
              <OcrIcon alt="ocr-icon" src={iconOcrArea} />
            )}
          </PdfButton>
          <PdfButton disabled={false} onClick={() => setOcrMode('click')}>
            {ocrMode === 'click' ? (
              <OcrIcon alt="ocr-icon" src={iconOcrClickActive} />
            ) : (
              <OcrIcon alt="ocr-icon" src={iconOcrClick} />
            )}
          </PdfButton>
          <VerticalBar />
          <PdfButton
            style={!isDarkMode ? { color: colorPalette.grey.grey9 } : {}}
            disabled={false}
            onClick={() => utils.file.openPdfInNewTab(docImage)}
          >
            <FontAwesomeIcon size="lg" icon="file-pdf" />
          </PdfButton>
          {!isArchived && (
            <PdfButton
              style={{ color: colorPalette.grey.grey9 }}
              disabled={false}
              onClick={() => translateDoc(!translateActive)}
              title={checkTooltip}
            >
              <FontAwesomeIcon
                size="lg"
                icon="globe"
                color={
                  isButtonTranslateDisabled
                    ? colorPalette.grey.grey2
                    : translateActive
                    ? colorPalette.turquoise.dark
                    : undefined
                }
              />
            </PdfButton>
          )}
        </Right>
      </PdfToolbar>
      <PdfDocument ref={pdfContainer} className="pdf-container">
        {docImage && (
          <Document
            file={`data:application/pdf;base64, ${docImage}`}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={console.error}
            options={{
              cMapUrl: `//cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjs.version}/cmaps/`,
              cMapPacked: true,
            }}
          >
            <div style={{ position: 'relative' }} className="pdf-page">
              {rotation === 0 && (
                <>
                  <div
                    onDoubleClick={(e: MouseEvent) => {
                      if (isOcrActive) getCoordinates(e, page, pageNumber);
                    }}
                    onMouseDown={(e: MouseEvent) => {
                      if (isOcrActive && ocrMode === 'area') onMouseDown(e, page);
                    }}
                    onMouseMove={(e: MouseEvent) => {
                      if (isOcrActive && ocrMode === 'area') onMouseMove(e, page);
                    }}
                    onMouseUp={(e: MouseEvent) => {
                      if (isOcrActive && ocrMode === 'area') onMouseUp(e, page, pageNumber);
                    }}
                  >
                    {activeRectangle?.pageNumber === pageNumber - 1 && suggestedBox}
                  </div>

                  <CreateRect
                    hidden={!isOcrActive}
                    onMouseUp={(e) => {
                      if (isOcrActive && ocrMode === 'area') onMouseUp(e, page, pageNumber);
                    }}
                    onMouseMove={(e) => {
                      if (isOcrActive && ocrMode === 'area') onMouseMove(e, page);
                    }}
                    id="resize-div"
                  />
                </>
              )}
              <Page
                onMouseDown={(e: MouseEvent) => {
                  if (isOcrActive && ocrMode === 'area') onMouseDown(e, page);
                }}
                onMouseMove={(e: MouseEvent) => {
                  if (isOcrActive && ocrMode === 'area') onMouseMove(e, page);
                }}
                onMouseUp={(e: MouseEvent) => {
                  if (isOcrActive && ocrMode === 'area') onMouseUp(e, page, pageNumber);
                }}
                onDoubleClick={(e: MouseEvent) => {
                  if (isOcrActive) getCoordinates(e, page, pageNumber);
                }}
                canvasRef={page}
                rotate={rotation}
                height={pageHeight}
                width={pageWidth}
                scale={zoom}
                renderTextLayer={false}
                pageNumber={pageNumber}
              />
            </div>
          </Document>
        )}
      </PdfDocument>
    </PdfWrapper>
  );
};

export default AdvancedPdf;
