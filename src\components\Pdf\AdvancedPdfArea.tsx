/**
 * @deprecated This file is deprecated and will be removed in a future version.
 * Please use the new implementation in the updated module.
 */
/* eslint max-lines: 0 */
import React, { useCallback, useEffect, useRef, useState } from 'react';
// @ts-expect-error
import { Document, Page, pdfjs } from 'react-pdf';
import { colorPalette } from 'utils/styleConstants';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import utils from 'utils';
import Preloader from 'components/Preloader';
import MovableAreasWrapper, { RectangleProps, TableRowsStates } from 'components/MovableAreasWrapper';

import {
  Center,
  InputContainer,
  Left,
  NumPages,
  PageInput,
  PageNavigation,
  PdfButton,
  PdfDocument,
  PdfToolbar,
  PdfWrapper,
  Right,
  VerticalBar,
  Zoom,
} from './advancedPdf.style';
import { BoxSizes } from 'components/MovableArea/MovableArea';
import { Area } from 'models/configurator';
import Icon from 'components/Input/Icon';

pdfjs.GlobalWorkerOptions.workerSrc = `${window.location.origin}/${pdfjs.version}.pdf.worker.min.js`;

export interface AdvancedPdfProps {
  docImage: string;
  fixedHeight?: string;
  areas?: Area[];
  onAreaChange?: (area: Area) => void;
  pageNumber: number;
  setPageNumber: (page: number) => void;
  blockPageChange?: boolean;
}
type Props = AdvancedPdfProps & TableRowsStates;

const AdvancedPdf = (props: Props) => {
  const {
    docImage,
    fixedHeight,
    areas,
    onAreaChange,
    setEditedRowIndex,
    setIsEdit,
    editedRowIndex,
    isEdit,
    pageNumber,
    setPageNumber,
    blockPageChange,
  } = props;

  const pdfDocument = useRef<HTMLDivElement | null>(null);
  const [numPages, setNumPages] = useState(0);
  const [zoom, setZoom] = useState(1);
  const [page, setPage] = useState<HTMLCanvasElement | null>(null);
  const [fitMode, setFitMode] = useState('width');
  const [suggestedBox, setSuggestedBox] = useState<
    {
      id: number;
      area: BoxSizes;
      pageNumber: number;
      isActive: boolean;
    }[]
  >([]);
  const lastPage = () => setNumPages(numPages);
  const firstPage = () => setNumPages(1);

  const pageRef = useCallback((node: HTMLCanvasElement | null) => {
    if (node !== null) setPage(node);
  }, []);

  const createSuggestedBox = useCallback(() => {
    try {
      // pdf dimensions
      const pdfHeight = page?.clientHeight;
      const pdfWidth = page?.clientWidth;
      const divArray: RectangleProps[] = [];
      if (!areas || pdfHeight === undefined || pdfWidth === undefined) return divArray;
      areas.forEach((area) => {
        const x1 = utils.converter.scaleValue(area.x1, 1, pdfWidth);
        const y1 = utils.converter.scaleValue(area.y1, 1, pdfHeight);
        const x2 = utils.converter.scaleValue(area.x2, 1, pdfWidth);
        const y2 = utils.converter.scaleValue(area.y2, 1, pdfHeight);
        // if ( width < 10 && height < 10) utils.app.notify('warning', 'Suggestion box size is too small');
        if (area.x2 > area.x1 && area.y2 > area.y1) {
          divArray.push({
            id: area.id,
            pageNumber: area.pageNumber,
            area: {
              height: y2 - y1,
              width: x2 - x1,
              top: y1,
              left: x1,
            },
            name: area?.name,
            isActive: false,
          });
        }
      });
      setSuggestedBox(divArray);
    } catch (e) {
      utils.app.notify('fail', `error: ${e}`);
      return [];
    }
  }, [areas, page]);

  useEffect(() => {
    createSuggestedBox();
  }, [zoom, areas, page, createSuggestedBox]);
  useEffect(() => {
    window.addEventListener('resize', createSuggestedBox);

    return () => {
      window.removeEventListener('resize', createSuggestedBox);
    };
  }, [createSuggestedBox]);
  const zoomOut = () => zoom > 0.25 && setZoom(zoom - 0.25);
  const zoomIn = () => zoom < 2.5 && setZoom(zoom + 0.25);
  const pageNext = () => pageNumber < numPages && setPageNumber(pageNumber + 1);
  const pagePrevious = () => pageNumber > 1 && setPageNumber(pageNumber - 1);

  const fitToWidth = () => {
    setFitMode('height');
    setZoom(1);
  };

  const fitToHeight = () => {
    setFitMode('width');
    setZoom(1);
  };

  const onDocumentLoadSuccess = (pdf: { numPages: number }) => {
    setNumPages(pdf.numPages);
    setPageNumber(1);
    fitToWidth();
  };

  return (
    <PdfWrapper height={fixedHeight ? fixedHeight : 'auto'}>
      <Preloader area="pdf" />
      <PdfToolbar>
        <Left>
          {fitMode === 'height' ? (
            <PdfButton
              disabled={false}
              onClick={() => {
                fitToHeight();
              }}
            >
              <FontAwesomeIcon size="lg" icon="arrows-alt-v" />
            </PdfButton>
          ) : (
            <PdfButton
              disabled={false}
              onClick={() => {
                fitToWidth();
              }}
            >
              <FontAwesomeIcon size="lg" icon="arrows-alt-h" />
            </PdfButton>
          )}
        </Left>
        <Center>
          <PageNavigation>
            <Icon icon="top-page" custom disabled={pageNumber === 1} onClick={firstPage} />
            <PdfButton disabled={blockPageChange || pageNumber === 1} onClick={pagePrevious}>
              <FontAwesomeIcon size="lg" icon="arrow-up" />
            </PdfButton>
            <InputContainer>
              <PageInput onChange={(e: any) => setPageNumber(Number(e.target.value))} value={pageNumber} />
              <NumPages>/{numPages}</NumPages>
            </InputContainer>
            <PdfButton disabled={blockPageChange || pageNumber === numPages} onClick={pageNext}>
              <FontAwesomeIcon size="lg" icon="arrow-down" />
            </PdfButton>
            <Icon
              icon="top-page"
              style={{
                rotate: '180deg',
              }}
              custom
              disabled={pageNumber === numPages}
              onClick={lastPage}
            />
          </PageNavigation>
          <VerticalBar />
          <Zoom>
            <PdfButton disabled={false} onClick={zoomOut}>
              <FontAwesomeIcon size="lg" icon="search-minus" />
            </PdfButton>
            <PdfButton disabled={false} onClick={zoomIn}>
              <FontAwesomeIcon size="lg" icon="search-plus" />
            </PdfButton>
          </Zoom>
        </Center>
        <Right>
          <VerticalBar />
          <PdfButton
            style={{ color: colorPalette.grey.grey9 }}
            disabled={false}
            onClick={() => utils.file.openPdfInNewTab(docImage)}
          >
            <FontAwesomeIcon size="lg" icon="file-pdf" />
          </PdfButton>
        </Right>
      </PdfToolbar>
      <PdfDocument isFullWidth={zoom === 1 && fitMode === 'height'} ref={pdfDocument} className="pdf-container">
        {docImage && (
          <Document
            file={`data:application/pdf;base64, ${docImage}`}
            onLoadSuccess={onDocumentLoadSuccess}
            onLoadError={console.error}
            options={{
              cMapUrl: `//cdn.jsdelivr.net/npm/pdfjs-dist@${pdfjs.version}/cmaps/`,
              cMapPacked: true,
            }}
          >
            <div style={{ position: 'relative' }} className="pdf-page">
              <MovableAreasWrapper
                areas={suggestedBox}
                wrapperSize={{
                  width: page?.clientWidth || 0,
                  height: page?.clientHeight || 0,
                  top: 0,
                  left: page?.offsetLeft || 0,
                }}
                activePage={pageNumber}
                position="absolute"
                onAreaChange={onAreaChange}
                activeArea={editedRowIndex}
                setEditedRowIndex={setEditedRowIndex}
                setIsEdit={setIsEdit}
                isEdit={isEdit}
              />
              <Page
                canvasRef={pageRef}
                // height={page?.clientHeight || null}
                // width={page?.clientWidth || null}
                scale={zoom}
                renderTextLayer={false}
                pageNumber={pageNumber}
              />
            </div>
          </Document>
        )}
      </PdfDocument>
    </PdfWrapper>
  );
};

export default AdvancedPdf;
