import { OriginalRow, TableColumn } from 'components/Table';
import { SubjectDetail, SubjectDetailRows } from 'models/response';
import utils from 'utils';

export const filterOperator = [
  {
    label: 'EQUAL',
    value: 0,
  },
  {
    label: 'LIKE',
    value: 1,
  },
  {
    label: 'DISTANCE',
    value: 2,
  },
];

export const getFilterColumn = (t: (label: string) => string) => [
  { label: t('soggetto'), value: 'SOGGETTO' },
  { label: t('codforn'), value: 'CODFORN' },
  { label: t('piva'), value: 'PIVA' },
  { label: t('codfisc'), value: 'CODFISC' },
  { label: t('tel'), value: 'TEL' },
  { label: t('email'), value: 'EMAIL' },
  { label: t('address'), value: 'ADDRESS' },
];

export const getInputType  = (type: string): TableColumn['inputType'] => {
  switch (type.toLowerCase()) {
    case 'string':
      return 'text';
    case 'number':
      return 'number';
    case 'date':
      return 'date';
    case 'boolean':
      return 'checkbox';
    default:
      return 'text';
  }
}

export const dateTypeOptions = [
  { label: 'dd-MM-yyyy', value: 0 },
  { label: 'MM-dd-yyyy', value: 1 },
  { label: 'yyyy-MM-dd', value: 2 }
]

export const getDetailInputType  = (link: string | null, code: number): TableColumn['inputType'] => {
  if (link) {
    return 'select';
  }
  switch (code) {
    case 0: // string
    case 1: // date
    case 3: // currency
      return 'text';
    case 2:  // number
      return 'number';
    case 4: // boolean
      return 'checkbox';
    default:
      return 'text';
  }
}

export const transformRowsToDetails = (rows: OriginalRow[], columns: TableColumn[], idSubject: number) => {
  const result: SubjectDetail[] = [];

  rows.forEach(row => {
    const { supplierCode, ...details } = row;

    Object.entries(details).forEach(([position, content]) => {
      if (content) {
        const idDetail = columns.find(column => column.accessor === position)?.id
        result.push({
          idDetail: parseInt(idDetail || ''),
          idSubject,
          supplierCode: `${supplierCode}`,
          content
        });
      }
    });
  });

  return result;
}

export const transformDetailsToRows = (data?: SubjectDetailRows): OriginalRow[] => {
  if (!data) return []
  const dataRows: SubjectDetailRows[] = Object.values(data);

  const arrayRows = dataRows.map((ele) => Object.values(ele));

  const rows = arrayRows.map((ele) =>
    ele.reduce(
      (acc, { position, content, supplierCode }) => ({ ...acc, [position]: content, supplierCode }),
      {},
    ),
  );

  return rows as OriginalRow[];
}

export const convertDateValues = (data: OriginalRow, columns: TableColumn[], timeZone?: string) => {
  const dataMod = { ...data };
  Object.keys(dataMod).forEach((key) => {
    const column = columns.find(({ accessor }) => accessor === key);
    if (column?.inputType === 'date' && dataMod[key]) {
      const dateString = utils.date.convertDate(data[key], timeZone ?? 'UTC', 'dd-MM-yyyy');
      dataMod[key] = dateString
    }
  })

  return dataMod
}