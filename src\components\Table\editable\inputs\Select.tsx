import React from 'react';
import { Dropdown } from 'components/Input';

interface Props {
  value: string;
  onChange: (value: string) => void;
  options: any;
}

export default (props: Props) => {
  const { value, onChange, options } = props;
  return (
    <Dropdown
      placeholder={value}
      defaultDropdownValue={value}
      options={options || []}
      value={options && options.find((option: any) => option.value === value)}
      onChange={(newOption) => onChange(newOption.value)}
      noBorder
      customHeight="24px"
      fontSize="12px"
      minWidthLabel="100%"
      valueContainerPadding="0"
      singleValueMargin="0"
      margin="2px 0 0 0"
      menuWidth="fit-content"
    />
  );
};
