import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import utils from 'utils';
import { actionType, modalActionType } from 'utils/constants';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';
import actions from 'state/actions';
import services from 'services';
import axios from 'axios';

const DeleteWfReminderNotification = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const selectedRow = useSelector(selectors.configurator.getSelectedWfReminderSettings);
  const formMode = useSelector(selectors.configurator.getFormModeWfReminder);
  const rows = useSelector(selectors.configurator.getListOfWfReminderSettings);

  const deleteRow = async () => {
    if (!selectedRow) return;
    try {
      await services.deleteWfReminderNotificationSettings(selectedRow?.idUser);
      dispatch(
        actions.configurator.setListOfWfReminderSettings(
          (rows as any[]).filter((row) => row.idUser !== selectedRow?.idUser),
        ),
      );
      utils.app.notify('success', t('delete_wfReminder_success'));
    } catch (error) {
      if (axios.isAxiosError(error)) {
        utils.app.notify('fail', `${t('error')} ${error.response}`);
        return;
      }
      console.error(error);
    } finally {
      dispatch(actions.modal.closeModal());
    }
  };

  return (
    <CommonButton
      action={() =>
        dispatch(
          actions.modal.setModal({
            actionType: modalActionType.configurator.DELETE_CONFIRMATION,
            props: {
              subtitle: t('delete-wfReminder-setting'),
              func: async () => await deleteRow(),
            },
          }),
        )
      }
      scope="tertiary"
      value={t(actionType.DELETE_WF_REMINDER_NOTIFICATIONS)}
      icon="circle"
      disabled={
        !utils.user.isActionByCompanyActive(actionType.DELETE_WF_REMINDER_NOTIFICATIONS) || !selectedRow || !!formMode
      }
    />
  );
};

export default DeleteWfReminderNotification;
