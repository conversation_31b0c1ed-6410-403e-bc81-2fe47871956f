import Table, { OriginalRow, TableColumn } from 'components/Table';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import selectors from 'state/selectors';
import utils from 'utils';
import Checkbox from 'components/Buttons/CheckboxButton';
import { CorporateApproval } from 'models/response';
import actions from 'state/actions';
import { Content, CustomTitle } from 'routes/Configurator/styles';
import services from 'services';
import axios from 'axios';
import styled from 'styled-components/macro';

export const Container = styled.div`
  display: flex;
`;

const ContentExtended = styled(Content)`
  margin-right: 20px;
`;

const MainTable = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const rows = useSelector(selectors.configurator.getCorporateApprovalList);
  const selectedRow = useSelector(selectors.configurator.getSelectedCorporateApproval);
  const editRowId = useSelector(selectors.configurator.getEditedRowIdCorporateApproval);
  const corporateListColumns: TableColumn[] = [
    { id: '1', accessor: 'microcategoryCode', Header: t('microcategoryCode'), filterType: 'free', readOnly: true },
    {
      id: '2',
      accessor: 'microcategoryDescription',
      Header: t('microcategoryDescription'),
      filterType: 'free',
      readOnly: true,
    },
    {
      id: '3',
      accessor: 'corporateApproval',
      Header: t('corporateApproval'),
      filterType: 'select',
      sortType: 'boolean',
      Cell: ({ value }: any) => {
        return (
          <div style={{ display: 'flex', height: '100%' }}>
            <Checkbox isActive={value} isEditable={false}></Checkbox>
          </div>
        );
      },
      inputType: 'checkbox',
    },
  ];

  const vendorListColumns: TableColumn[] = [
    { accessor: 'supplierCode', Header: t('supplierCode'), filterType: 'free' },
    { accessor: 'subjectName', Header: t('subjectName'), filterType: 'free' },
  ];

  const onCanceledEdit = () => {
    dispatch(actions.configurator.setEditRowIdCorporateApproval(undefined));
    dispatch(actions.configurator.setSelectedCorporateApproval(null));
  };

  const onSave = async (row: OriginalRow) => {
    try {
      const typedRow = row as CorporateApproval;
      await services.editCorporateApproval({
        microcategoryCode: typedRow.microcategoryCode,
        corporateApproval: typedRow.corporateApproval,
      });
      const selected = rows.find((el) => el.id === row.id?.toString());
      if (selected) {
        const index = selected.id;
        if (index) {
          const copyOfRows = [...rows];
          copyOfRows[parseInt(index)] = {
            ...selected,
            corporateApproval: row.corporateApproval,
          };
          dispatch(actions.configurator.setCorporateApprovalList(copyOfRows));
        }
      }
      utils.app.notify('success', t('CorporateApprovalEditSuccess'));
    } catch (error) {
      if (axios.isAxiosError(error)) {
        utils.app.notify('fail', error.response?.data.message);
        console.error(error.response?.data.message);
        return;
      }
      console.error(error);
    } finally {
      dispatch(actions.configurator.setEditRowIdCorporateApproval(undefined));
      dispatch(actions.configurator.setSelectedCorporateApproval(null));
    }
  };
  return (
    <Container>
      {rows.length ? (
        <ContentExtended>
          <CustomTitle>{t('microcategory')}</CustomTitle>
          <Table
            hasToolbar
            columns={corporateListColumns}
            rows={rows}
            hasSelection
            onSelection={(rows: CorporateApproval[]) =>
              dispatch(actions.configurator.setSelectedCorporateApproval(rows[0]))
            }
            onCancel={onCanceledEdit}
            onSave={onSave}
            hasPagination
            hasResize
            hasSort
            hasFilter
            editRowID={editRowId}
            isEditable
            initialSelection={editRowId !== undefined ? [editRowId] : undefined}
          />
        </ContentExtended>
      ) : null}
      {selectedRow ? (
        <Content>
          <CustomTitle>{t('vendor_code')}</CustomTitle>
          <Table
            hasToolbar
            columns={vendorListColumns}
            rows={selectedRow?.vendorDetails}
            hasPagination
            hasResize
            hasSort
            hasFilter
          />
        </Content>
      ) : null}
    </Container>
  );
};
export default MainTable;
