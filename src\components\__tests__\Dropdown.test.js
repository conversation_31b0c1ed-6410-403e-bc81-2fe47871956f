import React from 'react';
import DropDown from '../Input/Dropdown';
import { render } from '@testing-library/react';

const options = [
  { label: 'label1', value: '1' },
  { label: 'label2', value: '2' },
];

describe('DropDown', () => {
  test('Dropdown button render the inner text', () => {
    render(<DropDown id="test" value={options[0]} options={options} />);

    const select = document.querySelector('#test');

    expect(select.textContent).toBe('label1');
  });

  test('Dropdown is small', () => {
    render(<DropDown id="test" options={options} small />);

    const select = document.querySelector('#test > div');
    const styles = getComputedStyle(select);

    expect(styles.minWidth).toBe('');
  });

  test('Dropdown is not small', () => {
    render(<DropDown id="test" options={options} />);

    const select = document.querySelector('#test > div');
    const styles = getComputedStyle(select);

    expect(styles.minWidth).toBe('165px');
  });
});
