import Modal from 'components/Modal';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import actions from 'state/actions';
import selectors from 'state/selectors';
import { notDraggable } from 'utils/constants';
import ModalsContent from '../../components/ModalsContent/ModalSwitcher';
import Routes from './routes';

const Workflow = () => {
  const dispatch = useDispatch();

  // Modal
  const closeModal = () => dispatch(actions.modal.closeModal());
  const isModalVisible = useSelector(selectors.modal.getVisibility);
  const currentModal = useSelector(selectors.modal.getActionType);
  const isModalDraggable = notDraggable.includes(currentModal || '');

  useEffect(() => {
    dispatch(actions.configurator.resetConfWfState());
  }, [dispatch]);
  return (
    <>
      <Modal onClose={closeModal} open={isModalVisible} isDraggable={!isModalDraggable}>
        <ModalsContent />
      </Modal>
      <Routes />
    </>
  );
};

export default Workflow;
