import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actionType } from 'utils/constants';

import utils from 'utils';
import actions from 'state/actions';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';

const ResetSearchVendor = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();

  const selectedCompany = useSelector(selectors.configurator.getSelectedCompanyVendorConfig);
  const editedVendorId = useSelector(selectors.configurator.getIdEditedVendorFromList);

  const companyName = selectedCompany?.label;
  const hasPermission = utils.user.isActionByCompanyActive(actionType.RESET_SEARCH_VENDOR, companyName);

  return (
    <CommonButton
      action={() => {
        dispatch(
          actions.configurator.setAppliedFilters({
            column: null,
            operator: null,
            value: '',
          }),
        );
        dispatch(actions.configurator.setSubjectList([]));
        dispatch(actions.configurator.setSelectedVendorIdFromList(null))
      }}
      scope="tertiary"
      value={t(actionType.RESET_SEARCH_VENDOR)}
      icon="circle"
      disabled={!hasPermission || !selectedCompany || !!editedVendorId}
    />
  );
};

export default ResetSearchVendor;
