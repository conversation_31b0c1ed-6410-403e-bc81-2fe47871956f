import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { actionType } from 'utils/constants';

import utils from 'utils';
import actions from 'state/actions';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';
import { useHistory, useRouteMatch } from 'react-router';
import services from 'services';

const ViewCompanyWorkflow = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const viewMode = useSelector(selectors.configurator.getViewMode);
  const selectedCompanyDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  const history = useHistory();
  const { path } = useRouteMatch();

  const { revalidateAssociations } = services.useGetDefinitionTaskGroupsAssociations(
    'getDefinitionTaskGroupsAssociationsKey',
    selectedCompanyDefinition?.idCompany,
  );

  return (
    <CommonButton
      action={() => {
        dispatch(actions.configurator.setViewMode(!viewMode));
        revalidateAssociations();
        history.push(`${path}/WORKFLOW/workflow-company`);
      }}
      scope="tertiary"
      value={t(actionType.VIEW_COMPANY_WORKFLOW)}
      icon="circle"
      disabled={
        selectedCompanyDefinition?.idCompany === undefined ||
        !utils.user.isActionByCompanyActive(actionType.VIEW_COMPANY_WORKFLOW, selectedCompanyDefinition?.companyName)
      }
      id="view-mode-workflow"
    />
  );
};

export default ViewCompanyWorkflow;
