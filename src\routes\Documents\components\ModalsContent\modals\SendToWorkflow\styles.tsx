import styled from 'styled-components/macro';

export const Container = styled.div`
  width: 610px;
  .wizard-selector {
    display: flex;
    flex-direction: column-reverse;
  }
`;

export const StyledRowWrapper = styled.div`
  display: flex;
  flex-direction: row;
  // input selector
  > div {
    height: 27px;
    margin-left: 10px;
  }
`;

export const StyledColWrapper = styled.div`
  align-items: start;
  margin-top: 30px;
  display: flex;
  flex-direction: column;
  // input selector
  > div {
    margin-bottom: 15px;
    height: 27px;
    margin-left: 10px;
  }
`;

export const Top = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  > button {
    padding-left: 0;
  }
`;

export const Bottom = styled.div<{ customPadding?: string }>`
  padding-top: ${(props) => (props.customPadding ? props.customPadding : '20px')};
`;

export const Wrapper = styled.div`
  display: flex;
  flex-direction: row;
`;

export const Left = styled.div`
  width: 220px;
`;

export const Right = styled.div`
  width: 100%;
  max-height: 350px;
  overflow: auto;
  margin-left: 10px;
  padding-right: 10px;
`;

export const StyledDefinitionName = styled.h4`
  font-size: ${({ theme }) => theme.fontSizePalette.heading.H4};
  color: ${({ theme }) => theme.colorPalette.grey.grey7};
  font-weight: 300;
  letter-spacing: -0.66px;
  line-height: 24px;
  margin-bottom: 25px;
`;

export const StepContainer = styled.div`
  min-width: 610px;
`;

export const StyledLine = styled.hr``;

export const StyledText = styled.p`
  margin-bottom: 20px;
`;

export const CardContainer = styled.div`
  max-height: 350px;
  overflow: auto;
  padding-right: 15px;
`;
