import Checkbox from 'components/Buttons/CheckboxButton';
import CommonButton from 'components/Buttons/CommonButton';
import Dropdown from 'components/Input/Dropdown';
import TextInput from 'components/Input/TextInput';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  ButtonsContainer,
  CheckboxContainer,
  CheckboxLabel,
  InputContainer,
  InputLabel,
  Right,
  RightTitle,
  RightWrapper,
} from 'routes/Configurator/styles';
import actions from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components';
import utils from 'utils';
import CronFrequencySelector from '../components/CronFrequencySelector';
import { FormValues } from './openWorkflowFormUtils';

export const ExtendedInputLabel = styled(InputLabel)`
  width: 200px;
  white-space: normal;
  overflow: hidden;
`;
export const ExtendedCheckboxLabel = styled(CheckboxLabel)`
  width: 200px;
  white-space: normal;
  overflow: hidden;
`;

interface IProps {
  values: FormValues;
  setFieldValue: Function;
  dirty: boolean;
  isValid: boolean;
  isValidating: boolean;
  submitForm: (e: any) => void;
  userList: { idUser: number; name: string }[];
  templatesList: { value: number; label: string }[];
}

const FormOpenWorkflow = ({
  values,
  setFieldValue,
  dirty,
  isValid,
  isValidating,
  submitForm,
  userList,
  templatesList,
}: IProps) => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const formMode = useSelector(selectors.configurator.getFormModeOpenWf);
  const selectedRow = useSelector(selectors.configurator.getSelectedOpenWfSettings);

  return (
    <RightWrapper>
      <RightTitle> {t(`${formMode}_setting`)}</RightTitle>
      <Right>
        <InputContainer>
          <ExtendedInputLabel>{t('user')}</ExtendedInputLabel>
          {formMode === 'edit' ? (
            <TextInput disabled value={selectedRow?.name} />
          ) : (
            <Dropdown
              name="user"
              value={values['user']}
              onChange={(value) => {
                setFieldValue('user', value);
              }}
              options={utils.input.buildOptions(userList ?? [], 'name', 'idUser')}
              placeholder={t('select_user')}
              width="170px"
            />
          )}
        </InputContainer>
        <CheckboxContainer>
          <ExtendedCheckboxLabel>{t('openWfSendMail')}</ExtendedCheckboxLabel>
          <Checkbox
            name="openWfSendMail"
            isActive={values['openWfSendMail'] ? true : false}
            action={(e) => setFieldValue('openWfSendMail', e)}
          />
        </CheckboxContainer>
        <CheckboxContainer>
          <ExtendedCheckboxLabel>{t('openWfSendNotificationImmediately')}</ExtendedCheckboxLabel>
          <Checkbox
            name="openWfSendNotificationImmediately"
            isActive={values['openWfSendNotificationImmediately'] ? true : false}
            action={(e) => setFieldValue('openWfSendNotificationImmediately', e)}
          />
        </CheckboxContainer>
        <InputContainer>
          <ExtendedInputLabel>{t('openWfMaxDocuments')}</ExtendedInputLabel>
          <TextInput
            name="openWfMaxDocuments"
            value={values['openWfMaxDocuments']}
            onChange={(e: any) => {
              const value = Number(e.target.value);
              setFieldValue('openWfMaxDocuments', value);
            }}
            type="number"
            min={1}
          />
        </InputContainer>
        <InputContainer>
          <ExtendedInputLabel>{t('openWfMaxDocumentDetails')}</ExtendedInputLabel>
          <TextInput
            name="openWfMaxDocumentDetails"
            value={values['openWfMaxDocumentDetails']}
            onChange={(e: any) => {
              const value = Number(e.target.value);
              setFieldValue('openWfMaxDocumentDetails', value);
            }}
            type="number"
            min={1}
          />
        </InputContainer>
        <InputContainer>
          <ExtendedInputLabel>{t('idTemplateImmediateOpenWorkflow')}</ExtendedInputLabel>
          <Dropdown
            name="idTemplateImmediateOpenWorkflow"
            value={values['idTemplateImmediateOpenWorkflow']}
            onChange={(value) => {
              setFieldValue('idTemplateImmediateOpenWorkflow', value);
            }}
            options={templatesList}
            placeholder={t('select_template_immediately_open')}
            width="170px"
          />
        </InputContainer>
        <InputContainer>
          <ExtendedInputLabel>{t('idTemplateScheduledOpenWorkflow')}</ExtendedInputLabel>
          <Dropdown
            name="idTemplateScheduledOpenWorkflow"
            value={values['idTemplateScheduledOpenWorkflow']}
            onChange={(value) => {
              setFieldValue('idTemplateScheduledOpenWorkflow', value);
            }}
            options={templatesList}
            placeholder={t('select_template_scheduled_open')}
            width="170px"
          />
        </InputContainer>
        <CronFrequencySelector
          hour={values['hour']}
          setHour={(value: number) => setFieldValue('hour', value)}
          minute={values['minute']}
          setMinute={(value: number) => setFieldValue('minute', value)}
          allDays={values['allDays']}
          setAllDays={(value: boolean) => setFieldValue('allDays', value)}
          daysOfWeek={values['daysOfWeek']}
          setDaysOfWeek={(value: { label: string; value: number }[]) => setFieldValue('daysOfWeek', value)}
        />
        <ButtonsContainer>
          <CommonButton
            id="saveButton"
            scope="primary"
            disabled={!dirty || !isValid || isValidating}
            type="submit"
            value={t(`${formMode}_settings`)}
            action={submitForm}
          />
          <CommonButton
            id="cancelButton"
            scope="secondary"
            type="button"
            value={t('cancel')}
            action={() => {
              dispatch(actions.configurator.setFormModeOpenWf(null));
            }}
          />
        </ButtonsContainer>
      </Right>
    </RightWrapper>
  );
};
export default FormOpenWorkflow;
