import React, { useState } from 'react';
import { useAsync } from 'react-use';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components/macro';

import utils from 'utils';
import services from 'services';
import actions from 'state/actions';
import selectors from 'state/selectors';

import { GetUserCloneRole } from 'models/response';

import CommonButton from 'components/Buttons/CommonButton';
import Header from 'components/Modal/Header';

import { TableColumn } from 'components/Table';
import Table from 'components/Table';
import { CloneUserRoleConfiguration } from 'models/request';
import { Icon } from 'components/Input';
import Legenda from 'components/Legenda';

export const ButtonContainer = styled.div`
  text-align: center;
  button {
    margin: 10px;
  }
`;

const CloneRoleModal = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();

  const selectedCompanyId = useSelector(selectors.configurator.getSelectedCompanyForRoles);
  const selectedUserForRoles = useSelector(selectors.configurator.getSelectedUserForRoles);

  const [users, setUsers] = useState<GetUserCloneRole[]>([]);
  const [selectedUserForAdd, setSelectedUserForAdd] = useState<GetUserCloneRole[]>([]);

  useAsync(async () => {
    if (selectedCompanyId !== null) {
      try {
        const { data } = await services.getUsersForCompany(selectedCompanyId);
        const datacustom = data.map((el) => {
          return {
            usernameRole: el.username,
            nameRole: el.name,
            emailRole: el.email,
            idUser: el.idUser,
            status: el.status,
          };
        });
        setUsers(datacustom);
      } catch (error) {
        console.error(error);
      }
    }
  }, []);

  const GreenIcon = (
    <span title={t('user_not_belongs_to_company')}>
      <Icon custom icon="green-circle" style={{ width: 11, margin: 10 }} />
    </span>
  );

  const yellowIcon = (
    <span title={t('user_belongs_to_company')}>
      <Icon custom icon="yellow-circle" style={{ width: 11, margin: 10 }} />
    </span>
  );

  const userColumns: TableColumn[] = [
    {
      accessor: 'status',
      Header: t('status'),
      Cell: ({ value }: { value: number }) => {
        switch (value) {
          case 0:
            return yellowIcon;
          case 1:
            return GreenIcon;
        }
      },
      width: 60,
      filterType: 'select',
      selectFilterOptions: [
        {
          value: 0,
          label: yellowIcon,
        },
        {
          value: 1,
          label: GreenIcon,
        },
      ],
    },
    { accessor: 'usernameRole', Header: t('username') },
    { accessor: 'nameRole', Header: t('name') },
    { accessor: 'emailRole', Header: t('email') },
  ];

  const setSelectedUser = (rows: GetUserCloneRole[]) => {
    setSelectedUserForAdd(rows);
  };

  const cloneUserRole = async () => {
    if (selectedCompanyId !== null && selectedUserForAdd && selectedUserForRoles) {
      try {
        const userRoleToClone: CloneUserRoleConfiguration = {
          idCompany: selectedCompanyId,
          idCloningUser: selectedUserForRoles[0].idUser,
          idClonedUser: selectedUserForAdd[0].idUser,
        };
        await services.cloneUserRoleConfiguration(userRoleToClone);
        const { data } = await services.getUserCompanyRoles(selectedCompanyId);
        utils.app.notify('success', t('user-role-cloned'));
        dispatch(actions.modal.closeModal());
        dispatch(actions.configurator.setSelectedUserForRoles([]));
        dispatch(actions.configurator.setUsersForCompanyRoles(data));
      } catch (e) {
        console.error(e);
      }
    }
  };

  return (
    <>
      <Header title={t('clone_role')} />
      <Legenda
        items={[
          [
            {
              icon: 'green-circle',
              label: t('user does not belong to the company'),
            },
            {
              icon: 'yellow-circle',
              label: t('user belong to the company, An error might occur during clone config'),
            },
          ],
        ]}
      />

      <Table
        rowId="idUser"
        onSelection={(rows: GetUserCloneRole[]) => setSelectedUser(rows)}
        hasToolbar
        columns={userColumns}
        rows={users}
        hasSelection
        hasPagination
        hasResize
        hasSort
        hasFilter
      />
      <ButtonContainer>
        <CommonButton value={t('continua')} action={() => cloneUserRole()} disabled={selectedUserForAdd.length === 0} />
        <CommonButton
          scope="secondary"
          value={t('annulla')}
          action={() => {
            dispatch(actions.modal.closeModal());
          }}
        />
      </ButtonContainer>
    </>
  );
};

export default CloneRoleModal;
