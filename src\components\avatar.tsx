import CloseIcon from 'images/lucy4/icon_x_blue.svg';
import React from 'react';
import styled from 'styled-components/macro';
import utils from 'utils';

const Image = styled.img`
  max-width: 100%;
  vertical-align: middle;
  width: inherit;
  height: inherit;
  border-radius: 50%;

  transition: all 0.5s;
  &:hover {
    box-shadow: 0 2px 2px 1px rgba(0, 0, 0, 0.2);
  }
`;

const AvatarContainer = styled.div<{ size: number }>`
  position: relative;
  width: ${({ size }) => size}px;
  height: ${({ size }) => size}px;
  margin-right: 10px;
  &:hover {
    cursor: pointer;
  }
`;

export interface AvatarProps {
  id?: string;
  image?: string;
  userName: string;
  onClick: Function;
  size?: number;
  onRemoveImage?: Function;
}

const AvatarPlaceHolder = styled.div<{ userInitial: string; size: number }>`
  position: relative;
  width: inherit;
  height: inherit;
  border-radius: 50%;
  font-size: ${({ theme }) => theme.fontSizePalette.large};
  font-weight: ${({ theme }) => theme.fontWeightPalette.bold};
  text-transform: uppercase;
  margin-bottom: 20px;
  line-height: ${({ size }) => size}px;
  text-align: center;
  background-color: ${({ theme }) => theme.colorPalette.grey.medium};
  transition: all 0.5s;
  ::before {
    content: '${({ userInitial }) => userInitial}';
  }

  &:hover {
    box-shadow: 0 2px 2px 1px rgba(0, 0, 0, 0.2);
  }
`;

const RemoveImage = styled.div`
  height: 23px;
  width: 23px;
  border-radius: 13px;
  background-color: white;
  border: 1px solid ${({ theme }) => theme.colorPalette.turquoise.normal};
  position: absolute;
  bottom: 5px;
  right: 5px;

  transition: all 0.5s;
  &:hover {
    box-shadow: 2px 2px 2px 1px rgba(0, 0, 0, 0.2);
  }
`;

const Close = styled.img`
  width: 10px;
  height: 10px;

  position: absolute;
  bottom: 5px;
  right: 5px;
`;

const Avatar = ({ image, userName = '', onClick, size = 50, onRemoveImage, id }: AvatarProps) => {
  return (
    <AvatarContainer onClick={() => onClick()} size={size} id={id}>
      {image ? (
        <>
          <Image src={image} />
          {size === 100 && (
            <RemoveImage
              onClick={(e: React.MouseEvent<HTMLElement>) => {
                e.stopPropagation();
                onRemoveImage && onRemoveImage();
              }}
            >
              <Close src={CloseIcon} />
            </RemoveImage>
          )}
        </>
      ) : (
        <AvatarPlaceHolder size={size} userInitial={utils.user.getUserPlaceHolder(userName)} />
      )}
    </AvatarContainer>
  );
};

export default Avatar;
