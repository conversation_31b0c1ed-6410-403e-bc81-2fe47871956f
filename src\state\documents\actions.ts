/* eslint-disable max-lines */
import { RootState } from 'models';
import services from 'services';
import selectors from 'state/selectors';
import { Dispatch } from 'redux';
import { slice } from './slice';
import { FilePosition, FilePositionTypes } from 'routes/Documents/components/DocumentSearch/index.types';
import actions from 'state/actions';
import { DocumentFields } from 'models/response';
import { GetDocument } from 'models/request';
import { moduleProgramCode } from 'utils/constants';
import { Row } from 'models/table';

const updateDocuments = () => async (dispatch: Dispatch, getState: any) => {
  const state: RootState = getState();
  const idUser = selectors.user.getIdUser(state);
  const idTemplate = selectors.documents.selectActiveTemplateId(state);
  const allAuthorized = selectors.documents.selectIsAllAuth(state);
  const idProgram = selectors.app.getProgramCode(state);
  const documentFormValues = selectors.documents.selectDocumentSearchFormValues(state);
  const isArchived = selectors.documents.selectIsTemplateArchived(state);
  const templates = selectors.documents.selectTemplates(state);
  const isSearchFilterVisible = selectors.documents.selectIsSearchFilterVisible(state);

  if (idTemplate && idProgram && idUser) {
    const CommonData = {
      idProgram,
      idUser,
    };

    try {
      if (idProgram === moduleProgramCode.monitor) {
        const objMonitor = {
          ...CommonData,
        };

        const objMonitorDynamic = {
          idTemplate,
          filters: documentFormValues,
          ...CommonData,
        };

        const { data: rows } = isSearchFilterVisible
          ? await services.getDocumentsFromDataRepository(objMonitor)
          : await services.getDocumentsFromDataRepositoryDynamic(objMonitorDynamic);
        dispatch(actions.documents.setDocumentGridRows(rows));
      } else {
        const getDocumentData: GetDocument = {
          idTemplate,
          allAuthorized: isArchived ? true : allAuthorized,
          ...CommonData,
        };

        const getDocumentDataDynamic = {
          filters: documentFormValues,
          allAuthorized: isArchived ? true : allAuthorized,
          idTemplate,
          ...CommonData,
        };
        const customFilter = templates.find((template) => template.idTemplate === idTemplate)?.customFilter;
        const { data: rows } = isSearchFilterVisible
          ? await services.getDocuments(getDocumentData, customFilter)
          : await services.getDocumentsDynamic(getDocumentDataDynamic, customFilter);
        dispatch(actions.documents.setDocumentGridRows(rows));
      }
      dispatch(slice.actions.setActiveDocumentId([]));
    } catch (err) {
      console.error(err);
    }
  }
};

const setDocumentFields = (protocol: number, idProgram: number) => async (dispatch: Dispatch, getState: any) => {
  const state: RootState = getState();
  const rows = state.documents.documentsTable.rows;
  const filePosition = rows.find((el) => el.protocol === protocol)?.position as FilePosition;
  const isArchived = selectors.documents.selectIsTemplateArchived(state);
  const idUser = selectors.user.getIdUser(state);

  try {
    if ((isArchived || filePosition === FilePositionTypes.archived) && idUser) {
      const { data } = await services.getDocumentFieldsArchive({ protocol, idProgram, idUser });
      dispatch(slice.actions.setDocumentFields(data));
    } else {
      const { data } = await services.getDocumentFields({ protocol, idProgram });
      dispatch(slice.actions.setDocumentFields(data));
    }
  } catch (e) {
    console.error(e);
  }
};

const setDocumentFieldsPassed =
  (protocol: number, idProgram: number, docFields?: DocumentFields) => async (dispatch: Dispatch, getState: any) => {
    const state: RootState = getState();
    const isArchived = selectors.documents.selectIsTemplateArchived(state);
    const idUser = selectors.user.getIdUser(state);
    const rows = state.documents.documentsTable.rows;
    const filePosition = rows.find((el) => el.protocol === protocol)?.position as FilePosition;
    try {
      if ((isArchived || filePosition === FilePositionTypes.archived) && idUser) {
        const { data } = await services.getDocumentFieldsArchive({ protocol, idProgram, idUser });
        dispatch(slice.actions.setDocumentFields(data));
      } else {
        if (docFields) {
          dispatch(slice.actions.setDocumentFields(docFields));
          return;
        }
        const { data } = await services.getDocumentFields({ protocol, idProgram });
        dispatch(slice.actions.setDocumentFields(data));
      }
    } catch (e) {
      console.error(e);
    }
  };

const setDocumentHeader =
  (protocol: number, idProgram: number, idTemplate: number) => async (dispatch: Dispatch, getState: any) => {
    const state: RootState = getState();
    const isArchived = selectors.documents.selectIsTemplateArchived(state);
    const rows = state.documents.documentsTable.rows;
    const filePosition = rows.find((el) => el.protocol === protocol)?.position as FilePosition;
    try {
      const { data } =
        isArchived || filePosition === FilePositionTypes.archived
          ? await services.getArchivedDocumentHeader({ protocol, idProgram, idTemplate })
          : await services.getDocumentHeader({ protocol, idProgram, idTemplate });
      dispatch(slice.actions.setDocumentHeader(data));
    } catch (e) {
      console.error(e);
    }
  };

const findNextProtocol = (protocols: Row[], selectedProtocol: number | null) => {
  // Find the selected protocol
  const selected = protocols.find((protocol) => protocol.protocol === selectedProtocol);
  // If the selected protocol is not found, return null
  if (!selected) {
    return null;
  }
  // Find the index of the selected protocol
  const selectedIndex = protocols.indexOf(selected);
  // If the selected protocol is the last protocol, return null
  if (selectedIndex === protocols.length - 1) {
    return null;
  }
  // Return the next protocot
  return protocols[selectedIndex + 1];
};

const openNextDocument = (history: any) => async (dispatch: Dispatch, getState: any) => {
  const state: RootState = getState();

  const protocol = selectors.documents.selectActiveDocumentID(state);
  const filteredDocuments = selectors.documents.selectFilteredDocFromList(state);
  const userPreference = selectors.user.getUserPreference(state);

  if (userPreference.openNextDocument) {
    const nextProtocol = findNextProtocol(filteredDocuments, protocol);
    if (nextProtocol && nextProtocol.protocol !== undefined) {
      dispatch(actions.documents.setActiveDocumentId([nextProtocol.protocol]));
    } else {
      dispatch(actions.documents.setActiveDocumentId([]));
      history.goBack();
    }
  } else {
    dispatch(actions.documents.setActiveDocumentId([]));
    history.goBack();
  }
};

export default {
  ...slice.actions,
  updateDocuments,
  setDocumentFields,
  setDocumentHeader,
  setDocumentFieldsPassed,
  openNextDocument,
};
