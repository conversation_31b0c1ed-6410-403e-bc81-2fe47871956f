/* eslint-disable max-lines */
// https://reactdatepicker.com/
import 'react-datepicker/dist/react-datepicker.css';
import iconClear from 'images/lucy4/icon_x.svg';
import DatePicker, { ReactDatePickerProps } from 'react-datepicker';
import React, { ReactNode, forwardRef, useEffect, useRef, useState } from 'react';
import { colorPalette, fontWeightPalette, fontSizePalette, statusColors } from 'utils/styleConstants';
import { createPortal } from 'react-dom';
import nextArrow from 'images/lucy4/arrow-next.svg';
import previousArrow from 'images/lucy4/arrow-previous.svg';
import styled from 'styled-components/macro';
import { useUpdateEffect } from 'react-use';
import { useTheme } from 'providers/ThemeProvider';

const StyledInputWrapper = styled.div<{ feedbackColor: string | undefined }>`
  input {
    width: 100%;
    border: 1px solid
      ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.grey.grey5)}!important;
    &:focus {
      border-color: ${(props) =>
        props.feedbackColor ? props.feedbackColor : props.theme.colorPalette.turquoise.normal}!important;
    }
  }
  .react-datepicker-wrapper {
    width: 100% !important;
  }
  div {
    .react-datepicker__close-icon {
      &:after {
        background-color: transparent;
        color: ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey8 : 'grey')};
        content: url(${iconClear});
        width: 9px;
        height: 9px;
        right: 10px;
        filter: ${({ theme }) => (theme.colorPalette.isDarkMode ? 'brightness(2.5)' : 'none')};
      }
    }
  }
  .datePicker-input {

  }
  .datePicker-input-disabled {
    border: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? 'none' : `1px solid ${theme.colorPalette.grey.grey5}`}!important;
    color: ${({ theme }) => theme.colorPalette.grey.grey12}!important;
    background-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.grey.grey3}!important;
    opacity: 0.7;
  }
`;

const Relative = styled.div`
  position: relative;
`;
const StyleWrapper = styled.div`
  div {
    font-family: Roboto;
    font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  }

  .react-datepicker {
    border: none;
    background-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.white};
  }

  .react-datepicker-popper {
    z-index: 100 !important;
  }

  .react-datepicker__navigation {
    margin-top: 8px;
  }

  .react-datepicker__header {
    background-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.white};
    border-bottom: 1px solid
      ${({ theme }) =>
        theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey4 : theme.colorPalette.turquoise.light};
  }

  .react-datepicker__current-month {
    color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.grey.grey7};
    font-size: ${({ theme }) => theme.fontSizePalette.heading.H4};
    margin-bottom: 10px;
  }

  .react-datepicker__day-names {
    font-size: ${({ theme }) => theme.fontSizePalette.body.M};
    color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey8 : theme.colorPalette.grey.grey9};
    margin-bottom: 5px;
  }

  .react-datepicker__day-name {
    color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey8 : theme.colorPalette.grey.grey9};
  }

  .react-datepicker__day {
    color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.grey.grey9};

    &:hover {
      border-radius: 1.3em;
      background-color: ${({ theme }) =>
        theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.turquoise.background};
    }
  }

  .react-datepicker__day--outside-month {
    color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey6 : theme.colorPalette.grey.grey5};
  }

  .react-datepicker__day--selected,
  .react-datepicker__day--keyboard-selected {
    background-color: ${({ theme }) => theme.colorPalette.turquoise.normal};
    color: ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.black : theme.colorPalette.white)};
    border-radius: 1.3em;
    &:hover {
      border-radius: 1.3em;
      background-color: ${({ theme }) => theme.colorPalette.turquoise.normal};
    }
  }

  .react-datepicker__month-container {
    box-shadow: ${({ theme }) =>
      theme.colorPalette.isDarkMode
        ? '0 2px 4px 0 rgba(0, 0, 0, 0.23), 0 2px 10px 0 rgba(0, 0, 0, 0.25)'
        : '0 2px 4px 0 rgba(155, 155, 155, 0.23), 0 2px 10px 0 rgba(100, 100, 100, 0.25)'};
    padding: 10px;
    border-radius: 4px;
    background-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.white};
  }
  .react-datepicker__input-container > input {
    background-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.white};
  }
  .react-datepicker__navigation--next {
    background: url(${nextArrow}) no-repeat;
    width: 15px;
    height: 15px;
    border: none;
    filter: ${({ theme }) => (theme.colorPalette.isDarkMode ? 'brightness(2.5)' : 'none')};
    span {
      display: none !important;
    }
  }

  .react-datepicker__navigation--previous {
    background: url(${previousArrow}) no-repeat;
    width: 15px;
    height: 15px;
    border: none;
    filter: ${({ theme }) => (theme.colorPalette.isDarkMode ? 'brightness(2.5)' : 'none')};
    span {
      display: none !important;
    }
  }
`;

export interface Props extends ReactDatePickerProps {
  onChange: (date: Date | null) => void;
  focus?: boolean;
  noBorder?: boolean;
  fontSize?: number;
  small?: boolean;
  portal?: boolean;
  inputWidth?: string;
  isRange?: boolean;
  selectedTo?: Date | null;
  hasFeedback?: boolean;
  feedbackMessage?: string;
  feedbackColor?: string;
  borderRadius?: string;
  backgroundColor?: string;
  height?: string;
  paddingLeft?: string;
  fontColor?: string;
}
const calendarContainer =
  (portal: boolean) =>
  ({ children }: { children: ReactNode }) => {
    const el = document.getElementById('datepicker-portal');
    return portal ? (
      createPortal(<StyleWrapper>{children}</StyleWrapper>, el || document.body)
    ) : (
      <StyleWrapper>{children}</StyleWrapper>
    );
  };

const Datepicker = forwardRef((props: Props, ref?) => {
  const {
    selected,
    selectedTo,
    portal = false,
    inputWidth = '165px',
    isRange = false,
    hasFeedback,
    feedbackMessage,
    feedbackColor,
    borderRadius,
    small,
    backgroundColor,
    height,
    paddingLeft,
    fontColor,
    disabled,
  } = props;
  const [startDate, setStartDate] = useState(selected ?? null);
  const [endDate, setEndDate] = useState(selectedTo ?? null);
  const innerRef = useRef<any>(ref);
  const { theme } = useTheme();
  useUpdateEffect(() => {
    if (innerRef.current) {
      if (props.focus) {
        innerRef.current.input.focus();
      } else {
        innerRef.current.setOpen(false);
      }
    }
  }, [props.focus]);

  useEffect(() => {
    setStartDate(props.selected ?? null);
  }, [props.selected, props.selectedTo]);
  // it sucks but styled component doesn't work in this case due to the portal
  const style: React.CSSProperties = {
    fontFamily: 'Roboto',
    width: inputWidth,
    color: fontColor || (theme?.colorPalette.isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.black),
    height: height ?? (small ? 18 : 25),
    backgroundColor:
      backgroundColor ?? (theme?.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.white),
    borderRadius: borderRadius ? `${borderRadius}` : small ? 9 : 3,
    fontWeight: small ? fontWeightPalette.light : fontWeightPalette.regular,
    paddingLeft: paddingLeft ?? 5,
    fontSize: props.fontSize ? props.fontSize : '12px',
    border:
      hasFeedback && !props.noBorder
        ? `1px solid ${feedbackColor ?? statusColors.error}`
        : props.noBorder
        ? 'none'
        : `1px solid ${theme?.colorPalette.isDarkMode ? theme.colorPalette.grey.grey4 : theme.colorPalette.grey.grey5}`,
  };

  const feedbackLabelStyle: React.CSSProperties = {
    fontFamily: 'Roboto',
    fontSize: fontSizePalette.xxSmall,
    color: feedbackColor ?? colorPalette.red.error,
    margin: '3px 0 0 3px',
    textAlign: 'right',
    position: 'absolute',
    right: 0,
  };

  const customInput = <input id="customDatePickerInput" type="text" style={style} />;

  return (
    <Relative>
      <StyledInputWrapper feedbackColor={feedbackColor}>
        <DatePicker
          // @ts-ignore
          ref={ref ?? innerRef}
          {...props}
          startDate={startDate}
          endDate={endDate}
          popperContainer={calendarContainer(portal)}
          customInput={customInput}
          showPopperArrow={false}
          selected={startDate}
          strictParsing
          className={
            disabled
              ? 'datePicker-input-disabled'
              : props.noBorder
              ? 'datePicker-input-hover-noBorder'
              : 'datePicker-input-hover'
          }
          onChange={(value: any | null) => {
            if (isRange) {
              const [start, end] = value;
              setStartDate(start);
              setEndDate(end);
            } else {
              setStartDate(value);
              setEndDate(null);
            }
            props.onChange(value);
          }}
          selectsRange={isRange}
          closeOnScroll={() => true}
        />
        {hasFeedback && feedbackMessage && <p style={feedbackLabelStyle}>{feedbackMessage}</p>}
      </StyledInputWrapper>
    </Relative>
  );
});

export default Datepicker;
