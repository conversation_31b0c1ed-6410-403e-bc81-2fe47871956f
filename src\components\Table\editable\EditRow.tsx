import React from 'react';
import styled from 'styled-components/macro';
import CommonButton from '../../Buttons/CommonButton';
import utils from '../../../utils';

const ModificationDiv = styled.div`
  display: flex;
  align-items: flex-end;
  justify-content: flex-end;
  padding-bottom: 20px;
  display: 'inline-block';
  padding-top: 16px;
`;

interface Row {
  [key: string]: string | number;
  id: string;
}

type EditRowProps = {
  onCancel: () => void;
  onSave: () => void;
};
const EditRow = (props: EditRowProps) => {
  const { onCancel, onSave } = props;
  const t = utils.intl.useTranslator();
  return (
    <ModificationDiv>
      <CommonButton value={t('cancel')} scope={'tertiary'} icon="times" action={onCancel} noPadding />
      <CommonButton value={t('save-row')} icon="circle" scope={'tertiary'} action={onSave} noPadding />
    </ModificationDiv>
  );
};
export default EditRow;
