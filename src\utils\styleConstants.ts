import {
  BreakpointsPalette,
  ColorPalette,
  FontSizePalette,
  FontWeightPalette,
  StatusColors,
  ZIndexPalette,
} from '../models/style';

export const colorPalette: ColorPalette = {
  isDarkMode: false,
  black: '#000000',
  white: '#FFFFFF',
  grey: {
    light: '#F5F5F5',
    mediumLight: '#DBDBDB',
    medium: '#E5E5E5',
    mediumDark: '#C3C3C3',
    dark: '#666666',
    black: '#373737',
    grey1: '#F3F3F3',
    grey2: '#EDEDED',
    grey3: '#E5E5E5',
    grey4: '#D6D6D6',
    grey5: '#C5C5C5',
    grey6: '#A6A6A6',
    grey7: '#919191',
    grey8: '#6A6A6A',
    grey9: '#4D4D4D',
    grey10: '#2C2C2C',
    grey11: '#FAFAFA',
    grey12: '#999999', // for disable states(with opacity 0.7)
    grey13: '#4c5354',
    grey14: '#F0F0F6',
    grey15: '#808080',
  },
  red: {
    error: '#E61B34',
    light: '#F14359',
    medium: '#D0021B',
    dark: '#BE2828',
  },
  orange: {
    medium: '#FF9400',
  },
  yellow: {
    medium: '#FFD000',
  },
  green: {
    medium: '#7ED321',
  },
  cyan: {
    light: '#D9FFFA',
    medium: '#66D4E0',
    dark: '#498591',
  },
  lightBlue: {
    light: '#D5E8FF',
    medium: '#72ADF6',
    dark: '#0378CC',
  },
  blue: {
    button: '#69B3E7',
    buttonHover: '#6392C5',
    light: '#0090FF',
    medium: '#005BCE',
  },
  turquoise: {
    dark: '#319AA6',
    light: '#CCF7F7',
    normal: '#63CEDA',
    background: '#F1F8F8',
  },
};
export const colorPaletteDark: ColorPalette = {
  ...colorPalette,
  black: '#FFFFFF',
  white: '#252628',
  grey: {
    ...colorPalette.grey,
    light: '#2C2C2C',
    mediumLight: '#3D3D3D',
    medium: '#4D4D4D',
    mediumDark: '#666666',
    dark: '#999999',
    black: '#CCCCCC',
    grey1: '#2A2A2A', // Background variations
    grey2: '#2F3032', // Secondary background // for disable states(with opacity 0.7)
    grey3: '#3C3D3F', // component backgrounds
    grey4: '#4D4D4D', // Border colors
    grey5: '#666666', // Disabled states
    grey6: '#808080', // Medium emphasis text
    grey7: '#999999', // Low emphasis text
    grey8: '#B3B3B3', // Hover states
    grey9: '#E3E3E3', // High contrast text
    grey10: '#E6E6E6', // Primary text
    grey11: '#1F1F1F', // Deepest background
    grey12: '#808080', // Disabled text
    grey13: '#B3B3B3', // Secondary text
    grey14: '#262626', // Alternative background
    grey15: '#999999', // Tertiary text
  },

  turquoise: {
    ...colorPalette.turquoise,
    dark: '#2c4649', // darker green
    light: '#2695A1', // light green
    normal: '#2B585E', // medium green
    background: '#262729', // near-black background
  },
  blue: {
    ...colorPalette.blue,
    button: '#2A4D6B',
    buttonHover: '#3A6B95',
    light: '#0066B3',
    medium: '#004C99',
  },
  isDarkMode: true,
};

export const statusColors: StatusColors = {
  error: '#E61B34',
  warning: '#FF9400',
  success: '#7ED321',
};

export const fontSizePalette: FontSizePalette = {
  xxSmall: '0.6rem', // 10px
  xSmall: '0.75rem', // 12px
  small: '0.9rem', // 14px
  medium: '1rem', // 16px
  large: '1.25rem', // 20px
  xLarge: '1.5rem', // 24px
  xxLarge: '2rem', // 32px
  xxxLarge: '2.25rem', // 36px
  heading: {
    H1: '32px',
    H2: '28px',
    H3: '24px',
    H4: '21px',
  },
  body: {
    XL: '22px',
    L: '18px',
    M: '16px',
    S: '14px',
    XS: '12px',
    XXS: '10px',
    mini: '8px',
  },
};

export const fontWeightPalette: FontWeightPalette = {
  light: 300,
  regular: 400,
  medium: 500,
  bold: 700,
};

export const fontFamily = 'Roboto, sans-serif';

export const opacity = {
  medium: 0.25,
};

export const boxShadowPalette = {
  medium: '0px 2px 10px 0px #64646440',
};

export const zIndexPalette: ZIndexPalette = {
  lowest: -1,
  low: 1,
  medium: 10,
  high: 50,
  highest: 100,
};
export const headerHeight = 70;
export const toogleHeight = 30;
// adding reponsive breakpoints
export const breakpointsPalette: BreakpointsPalette = {
  s: '1024px',
  m: '1224px',
  l: '1380px',
};

export const turquoiseGradient = `linear-gradient(135deg, ${colorPalette.turquoise.normal} 0%, ${colorPalette.turquoise.background} 100%)`;

export const barBackgroundColors = {
  blue: '#87ceea',
  red: '#f0807f',
};
