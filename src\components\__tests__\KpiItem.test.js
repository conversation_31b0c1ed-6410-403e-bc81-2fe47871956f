import React from 'react';
import { screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import '@testing-library/jest-dom/extend-expect';
import { renderWithStyle } from 'utils/helpers/test.helpers';
import KpiItem from 'components/KpiItem';

const kpiData = {
  description: 'Range 1 - 100',
  value: 4.5,
  dotColor: 'red',
  unit: '%',
  variant: 'dot',
};

describe('KpiItem Component', () => {
  describe('when with dot', () => {
    let kpi = null;
    beforeEach(() => {
      kpi = <KpiItem {...kpiData} />;
      renderWithStyle(kpi);
    });

    afterEach(() => {
      kpi = null;
    });

    it('should render component with description', () => {
      const description = screen.getByText(kpiData.description);
      expect(description).toBeInTheDocument();
    });

    it('should render kpi value', () => {
      const value = screen.getByText('4.5');
      expect(value).toBeInTheDocument();
    });

    it('should show colored dot when color passed', () => {
      const dot = document.querySelectorAll('.dot')[0];
      expect(dot).toHaveStyle('background-color: red');
    });

    it('should show percentage unit', () => {
      const unit = screen.getByText('%');
      expect(unit).toBeInTheDocument();
    });
  });

  describe('when is not with dot', () => {
    let kpi = null;
    beforeEach(() => {
      kpi = <KpiItem description="Range 1 - 100" value={4.5} unit={undefined} />;
      renderWithStyle(kpi);
    });

    afterEach(() => {
      kpi = null;
    });

    it('should not show dot', () => {
      const dot = document.querySelectorAll('.dot');
      expect(dot).toHaveLength(0);
    });
  });
  describe('when value is null', () => {
    let kpi = null;
    beforeEach(() => {
      kpi = <KpiItem description="Range 1 - 100" value={null} unit="%" />;
      renderWithStyle(kpi);
    });

    afterEach(() => {
      kpi = null;
    });

    it('should show n/a', () => {
      const value = screen.getByText('n/a');
      expect(value).toBeInTheDocument();
    });
  });
});
