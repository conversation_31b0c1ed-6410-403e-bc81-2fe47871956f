import React from 'react';
import { useSelector, useDispatch } from 'react-redux';

import utils from 'utils';
import actions from 'state/actions';
import selectors from 'state/selectors';
import services from 'services';

import CommonButton from 'components/Buttons/CommonButton';

import { modalActionType } from 'utils/constants';
import { actionType } from 'utils/constants';

const DeleteRows = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const selectedRows = useSelector(selectors.configurator.selectSelectedRow);
  const editRowId = useSelector(selectors.configurator.selectTablesEditRowID);
  const activeTable = useSelector(selectors.configurator.selectActiveTable);

  const deleteRows = async () => {
    try {
      if (activeTable) {
        await services.deleteTablesData({ table: activeTable.value, values: selectedRows });
        const ids = selectedRows.map((e) => e.idRow);
        dispatch(actions.configurator.removeTableRows(ids));
        dispatch(actions.modal.closeModal());
        utils.app.notify('success', t('table-rows-deleted'));
      }
    } catch (err) {
      console.error(err);
    }
  };

  return (
    <CommonButton
      action={() =>
        dispatch(
          actions.modal.setModal({
            actionType: modalActionType.configurator.DELETE_CONFIRMATION,
            props: {
              subtitle: t('delete-rows-table'),
              func: () => deleteRows(),
            },
          }),
        )
      }
      disabled={
        !selectedRows.length ||
        (editRowId ? true : false) ||
        !utils.user.isActionByCompanyActive(actionType.DELETE_TABLE_ROW)
      }
      scope="tertiary"
      value={t('delete-rows')}
      icon="circle"
    />
  );
};

export default DeleteRows;
