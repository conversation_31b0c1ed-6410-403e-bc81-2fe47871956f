import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { actionType } from 'utils/constants';
import { modalActionType } from 'utils/constants';

import actions from 'state/actions';
import utils from 'utils';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';

const ConfigureRole = () => {
  const dispatch = useDispatch();
  const t = utils.intl.useTranslator();
  const isEditingUserRows = useSelector(selectors.configurator.getIsEditingUserRows);
  const selectedCompanyId = useSelector(selectors.configurator.getSelectedCompanyForRoles);
  const companies = useSelector(selectors.configurator.getCompaniesForRoles);

  return (
    <CommonButton
      disabled={
        isEditingUserRows.length > 0 ||
        !utils.user.isActionByCompanyActive(
          actionType.CONFIGURE_ROLE,
          companies.find((e) => e.idCompany === selectedCompanyId).name,
        )
      }
      scope="tertiary"
      value={t('ConfigureRole')}
      icon="circle"
      action={() => dispatch(actions.modal.openModal(modalActionType.configurator.CONFIGURE_ROLES_MODAL))}
    />
  );
};

export default ConfigureRole;
