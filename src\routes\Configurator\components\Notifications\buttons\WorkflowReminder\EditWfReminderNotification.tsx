import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import utils from 'utils';
import { actionType } from 'utils/constants';

import CommonButton from 'components/Buttons/CommonButton';
import selectors from 'state/selectors';
import actions from 'state/actions';

const EditWfReminderNotification = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const selectedRow = useSelector(selectors.configurator.getSelectedWfReminderSettings);
  const formMode = useSelector(selectors.configurator.getFormModeWfReminder);

  return (
    <CommonButton
      action={() => dispatch(actions.configurator.setFormModeWfReminder('edit'))}
      scope="tertiary"
      value={t(actionType.EDIT_WF_REMINDER_NOTIFICATIONS)}
      icon="circle"
      disabled={
        !utils.user.isActionByCompanyActive(actionType.EDIT_WF_REMINDER_NOTIFICATIONS) || !selectedRow || !!formMode
      }
    />
  );
};

export default EditWfReminderNotification;
