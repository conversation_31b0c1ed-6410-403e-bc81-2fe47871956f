import React, { useRef, useState } from 'react';
import NumberFormat from 'react-number-format';
import { UserDecimalSep } from 'models/user';
import { useUpdateEffect } from 'react-use';
import utils from 'utils';
import styled from 'styled-components/macro';
import { FeedbackText } from './TextInput';
import { useTheme } from 'providers/ThemeProvider';

interface StyleProps {
  hasFeedback?: boolean;
  feedbackColor?: string;
  width?: string;
  height?: string;
  border?: string;
  backgroundColor?: string;
  feedbackMessage?: string;
  fontSize?: string;
  padding?: string;
  fontColor?: string;
  fontWeight?: string;
}
interface TransientProps {
  $feedbackColor?: string;
  $hasFeedback?: boolean;
  $backgroundColor?: string;
  $fontColor?: string;
}
export interface NumberInputProps extends StyleProps, TransientProps {
  id?: string;
  decimalSeparator?: UserDecimalSep | boolean;
  onChange?: (value: number | undefined) => void;
  onFocus?: (value: number | undefined) => void;
  onBlur?: (value: number | undefined) => void;
  onKeyDown?: (event: React.KeyboardEvent<HTMLDivElement>) => void;
  value?: number | string;
  focus?: boolean;
  // always show decimals even if number is rounded
  hasFixedDecimals?: boolean;
  decimalScale?: number;
  isSearch?: boolean;
  readOnly?: boolean;
  customInput?: React.ComponentType<any>;

  saveAction?: (value: string | undefined) => void;
}

const Container = styled.div`
  position: relative;
`;

const SaveButton = styled.span`
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.turquoise.light : theme.colorPalette.turquoise.dark};
  text-decoration: underline;
`;

const StyledInput = styled(NumberFormat)<StyleProps>`
  border: 1px solid
    ${({ theme }) => (theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.grey.grey5)};
  ${({ $hasFeedback, $feedbackColor }) =>
    $hasFeedback &&
    `
    border: 1px solid ${$feedbackColor};
    border-radius: 2px;
  `}
  font-family: 'Roboto', sans-serif;
  ${({ fontWeight }) => fontWeight && `font-weight: ${fontWeight};`}
  ${({ $fontColor, theme }) =>
    $fontColor
      ? `color: ${$fontColor};`
      : `color: ${theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey10 : theme.colorPalette.grey.grey9};`}
  ${({ border }) => border && `border: ${border};`}
  ${({ width }) => width && `width: ${width};`}
  ${({ height }) => height && `height: ${height};`}
  ${({ $backgroundColor, theme }) =>
    $backgroundColor
      ? `background-color: ${$backgroundColor};`
      : `background-color: ${
          theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.white
        };`}
  ${({ fontSize }) => fontSize && `font-size: ${fontSize};`}
  ${({ padding }) => padding && `padding: ${padding};`}
  ${({ readOnly, theme }) =>
    readOnly &&
    `color: ${theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey7 : theme.colorPalette.grey.grey12};`}

  &:focus-visible {
    ${({ border }) => border === 'none' && 'outline: none;'}
  }

  &:focus {
    outline: none;
    border-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.turquoise.light : theme.colorPalette.turquoise.normal};
  }
  /* disabled */
  &:disabled {
    border: ${({ theme }) => (theme.colorPalette.isDarkMode ? 'none' : `1px solid ${theme.colorPalette.grey.grey5}`)};
    background-color: ${({ theme }) => theme.colorPalette.grey.grey12};
    color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey12 : theme.colorPalette.grey.grey12};
    background-color: ${({ theme }) =>
      theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey2 : theme.colorPalette.grey.grey3}!important;
    opacity: 0.7;
  }
`;

const NumberInput = (props: NumberInputProps) => {
  const { theme } = useTheme();
  const [isSaveActive, setIsSaveActive] = useState<boolean>(false);
  const inputRef = useRef<HTMLInputElement | null>(null);
  const {
    id = '',
    decimalSeparator = '.',
    decimalScale = 2,
    value,
    focus,
    hasFixedDecimals = false,
    readOnly = false,
    customInput,
    feedbackColor = theme.colorPalette.red.error,
    feedbackMessage,
    hasFeedback = false,
    onChange,
    onFocus,
    onBlur,
    onKeyDown,
    height,
    border,
    backgroundColor,
    fontSize,
    width,
    padding,
    fontColor,
    fontWeight,
    saveAction,
  } = props;

  useUpdateEffect(() => {
    if (inputRef.current && focus) {
      inputRef.current.focus();
    }
  }, [focus]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      if (e.target.value === '') {
        onChange && onChange(undefined);
      } else {
        onChange && onChange(utils.intl.convertToNumber(e.target.value));
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleFocus = (e: React.ChangeEvent<HTMLInputElement>) => {
    saveAction && setIsSaveActive(true);
    try {
      if (!onFocus) return;
      if (e.target.value === '') {
        onFocus(undefined);
      } else {
        onFocus(utils.intl.convertToNumber(e.target.value));
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleBlur = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTimeout(() => {
      saveAction && setIsSaveActive(false);
    }, 500);
    try {
      if (!onBlur) return;
      if (e.target.value === '') {
        onBlur(undefined);
      } else {
        onBlur(utils.intl.convertToNumber(e.target.value));
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleSave = () => {
    saveAction && saveAction(inputRef?.current?.value);
  };

  return (
    <Container>
      <StyledInput
        id={id}
        customInput={customInput}
        value={value}
        decimalScale={decimalScale}
        fixedDecimalScale={hasFixedDecimals}
        getInputRef={inputRef}
        decimalSeparator={decimalSeparator}
        allowedDecimalSeparators={[',', '.']}
        readOnly={readOnly}
        disabled={readOnly}
        onChange={handleChange}
        onFocus={handleFocus}
        onBlur={handleBlur}
        onKeyDown={onKeyDown}
        $feedbackColor={feedbackColor}
        $hasFeedback={hasFeedback}
        height={height}
        border={border}
        width={width}
        $backgroundColor={backgroundColor}
        fontSize={fontSize}
        padding={padding}
        $fontColor={fontColor}
        fontWeight={fontWeight}
      />
      {isSaveActive && saveAction ? <SaveButton onClick={handleSave}>Save</SaveButton> : null}
      {hasFeedback && feedbackMessage && <FeedbackText feedbackColor={feedbackColor}>{feedbackMessage}</FeedbackText>}
    </Container>
  );
};

export default NumberInput;
