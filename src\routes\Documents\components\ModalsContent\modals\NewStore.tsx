/* eslint max-lines: 0 */
import TextInput from 'components/Input/TextInput';
import Toggle from 'components/Input/Toggle';
import Modal from 'components/Modal';
import { Formik, Form, FormikContextType } from 'formik';
import * as Yup from 'yup';
import React, { useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import actions from 'state/actions';
import services from 'services';
import utils from 'utils';
import styled from 'styled-components/macro';
import Dropdown from 'components/Input/Dropdown';
import CommonButton from 'components/Buttons/CommonButton';
import { useSelector } from 'react-redux';
import selectors from 'state/selectors';
import { UserCompany } from 'models/response';
import Textarea from 'components/Input/Textarea';
import { BulkInsertBody } from 'models/request';

const ModalContainer = styled.div`
  min-width: 550px;
`;

const InputContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-bottom: 15px;
`;

const UploadContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  > * {
    margin-bottom: 30px;
  }
`;

const ToggleContainer = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 20px;
`;

const FormContainer = styled.div`
  display: flex;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 20px;
  gap: 8px;
`;

const InputLabel = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  margin-right: 15px;
`;

const ToggleText = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.XS};
  font-weight: ${({ theme }) => theme.fontWeightPalette.light};
  color: ${({ theme }) => theme.colorPalette.grey.grey9};
  margin-right: 15px;
`;

const Left = styled.div`
  margin-right: 30px;
`;

const Right = styled.div``;

const NewStore = () => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();
  const companies = useSelector(selectors.app.getCompanies);
  const allSelectedDocumentsIds = useSelector(selectors.documents.selectAllActiveDocumentsID);
  const activeDocuments = useSelector(selectors.documents.selectTableRows);
  const inputFile = useRef<HTMLInputElement>(null);
  const [isMultiple, setIsMultiple] = useState(false);
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [multiErrors, setMultiErrors] = useState<any[]>([]);
  const isOpenDocumentView = utils.documents.isOpenDocumentView();

  const getSample = async () => {
    try {
      const { data } = await services.getCsvTemplateForCreateSubjectByFile();
      utils.file.downloadFileFromBase64Source('sample', data, 'text/csv', 'csv');
    } catch (err) {
      console.error(err);
    }
  };

  const companyOptions = (data: UserCompany[]) => {
    const values = utils.input.removeDefaultValue(data, 'idCompany');
    const options = utils.input.buildOptions(values, 'name', 'idCompany');

    return options;
  };

  const getInitialCompany = () => {
    if (allSelectedDocumentsIds.length === 0) return null;
    const selectedDocuments = allSelectedDocumentsIds
      .map((protocol: number) => {
        return activeDocuments.find((e) => e.protocol === protocol);
      })
      .map((ele: any) => ele.companyName);
    if (selectedDocuments.every((ele: any) => ele === selectedDocuments[0])) {
      const companyId = companies.find((company) => company.name === selectedDocuments[0])?.idCompany;
      if (companyId === undefined) return null;
      return {
        label: selectedDocuments[0],
        value: companyId,
      };
    }
    return null;
  };

  const initialValues = {
    company: getInitialCompany(),
    store: '',
    vat: '',
    fiscal: '',
    telephone: '',
    fax: '',
    email: '',
    address: '',
  };

  const validationLogic = Yup.object({
    company: Yup.object()
      .shape({
        label: Yup.string().required(t('required')),
        value: Yup.number().required(t('required')),
      })
      .required(t('required')),
    store: Yup.string().required(t('required')),
    telephone: Yup.string()
      .matches(/^[0-9]+$/, t('Must be only digits'))
      .min(5, t('Must be at least 5 digits')),
    fax: Yup.string().min(5),
    email: Yup.string().email(t('invalid-email')),
    address: Yup.string().min(5),
  });

  const isBulkInsertBody = (data: {
    idCompany: number | null;
    base64Csv: string | Error | ArrayBuffer | null;
  }): data is BulkInsertBody => {
    return data.idCompany !== null && typeof data.base64Csv === 'string' && data.base64Csv !== '';
  };

  const handleSubmit = async (values: {
    company: { label: string; value: number } | null;
    store: string;
    vat: string;
    fiscal: string;
    telephone: string;
    fax: string;
    email: string;
    address: string;
  }) => {
    if (isMultiple) {
      if (csvFile) {
        try {
          const base = await utils.file.toBase64(csvFile);
          if (values.company === null) throw new Error('company is null');
          const apiData = {
            idCompany: values.company.value,
            base64Csv: base,
          };
          if (isBulkInsertBody(apiData)) {
            const { data } = await services.bulkInsert(apiData);
            !data.rowsInError.length && utils.app.notify('success', t('upload-success'));
            if (data.rowsInError.length) {
              setMultiErrors(data.rowsInError);
              utils.app.notify('fail', t('upload-failure'));
            }
          } else {
            utils.app.notify('fail', t('data-error'));
          }
        } catch (err) {
          console.error(err);
        }
      }
    } else {
      try {
        await services.insertSubject({
          subject: values.store,
          vatNumber: values.vat,
          fiscalCode: values.fiscal,
          tel: values.telephone,
          fax: values.fax,
          email: values.email,
          idCompany: values.company ? values.company.value : null,
          address: values.address,
          debitCredit: 0,
          validationType: 0,
          blocked: 0,
        });
        utils.app.notify('success', t('subject_added'));
        dispatch(actions.modal.closeModal());
      } catch (err) {
        console.error(err);
      }
    }
  };
  const initialTouched = {
    company: false,
    store: false,
    vat: false,
    fiscal: false,
    telephone: false,
    fax: false,
    email: false,
    address: false,
  };
  const handleClose = () => {
    dispatch(actions.modal.closeModal());
  };

  const formikRef = useRef<FormikContextType<typeof initialValues>>(null);
  return (
    <>
      <Modal.Header title={t('Add-new-store')} subtitle={t('store-subtitle')} />
      <Modal.Content>
        <ToggleContainer>
          <Toggle onChange={() => setIsMultiple(!isMultiple)} />
          <ToggleText>{t('multiple-entry')}</ToggleText>
        </ToggleContainer>
        <Formik
          // @ts-ignore
          innerRef={formikRef}
          initialValues={initialValues}
          validationSchema={!isMultiple && validationLogic}
          onSubmit={handleSubmit}
          initialTouched={initialTouched}
        >
          {({ values, errors, handleChange, setFieldValue, dirty, submitForm, touched, handleBlur }) => {
            return (
              <Form>
                <ModalContainer>
                  {!isMultiple ? (
                    <>
                      <FormContainer>
                        <Left>
                          <InputContainer>
                            <InputLabel>{t('company')}</InputLabel>
                            <Dropdown
                              name="company"
                              hasFeedback={errors.company ? true : false}
                              feedbackMessage={errors.company ? t(`${errors.company}`) : ''}
                              value={values.company}
                              onChange={(option) => setFieldValue('company', option)}
                              options={companyOptions(companies)}
                              disabled={utils.documents.isOpenDocumentView()}
                            />
                          </InputContainer>
                          <InputContainer>
                            <InputLabel>{t('store')}</InputLabel>
                            <TextInput
                              hasFeedback={touched.store && errors.store ? true : false}
                              feedbackMessage={errors.store ? t(`${errors.store}`) : ''}
                              name="store"
                              value={values.store}
                              onChange={handleChange}
                              displayText="block"
                              textPositioning="relative"
                              onBlur={handleBlur}
                            />
                          </InputContainer>
                          <InputContainer>
                            <InputLabel>{t('vat-number')}</InputLabel>
                            <TextInput
                              hasFeedback={touched.vat && errors.vat ? true : false}
                              feedbackMessage={errors.vat ? t(`${errors.vat}`) : ''}
                              name="vat"
                              value={values.vat}
                              onChange={handleChange}
                              displayText="block"
                              textPositioning="relative"
                              onBlur={handleBlur}
                            />
                          </InputContainer>
                          <InputContainer>
                            <InputLabel>{t('fiscal-code')}</InputLabel>
                            <TextInput
                              hasFeedback={touched.fiscal && errors.fiscal ? true : false}
                              feedbackMessage={errors.fiscal ? t(`${errors.fiscal}`) : ''}
                              name="fiscal"
                              value={values.fiscal}
                              onChange={handleChange}
                              displayText="block"
                              textPositioning="relative"
                              onBlur={handleBlur}
                            />
                          </InputContainer>
                        </Left>
                        <Right>
                          <InputContainer>
                            <InputLabel>{t('telephone')}</InputLabel>
                            <TextInput
                              hasFeedback={touched.telephone && errors.telephone ? true : false}
                              feedbackMessage={errors.telephone ? errors.telephone : ''}
                              name="telephone"
                              value={values.telephone}
                              onChange={handleChange}
                              displayText="block"
                              textPositioning="relative"
                              onBlur={handleBlur}
                            />
                          </InputContainer>
                          <InputContainer>
                            <InputLabel>{t('fax')}</InputLabel>
                            <TextInput
                              hasFeedback={touched.fax && errors.fax ? true : false}
                              feedbackMessage={errors.fax ? t(`${errors.fax}`) : ''}
                              name="fax"
                              value={values.fax}
                              onChange={handleChange}
                              displayText="block"
                              textPositioning="relative"
                              onBlur={handleBlur}
                            />
                          </InputContainer>
                          <InputContainer>
                            <InputLabel>{t('email')}</InputLabel>
                            <TextInput
                              hasFeedback={touched.email && errors.email ? true : false}
                              feedbackMessage={errors.email ? errors.email : ''}
                              name="email"
                              value={values.email}
                              onChange={handleChange}
                              displayText="block"
                              textPositioning="relative"
                              onBlur={handleBlur}
                            />
                          </InputContainer>
                          <InputContainer>
                            <InputLabel>{t('address')}</InputLabel>
                            <TextInput
                              hasFeedback={touched.address && errors.address ? true : false}
                              feedbackMessage={errors.address ? t(`${errors.address}`) : ''}
                              name="address"
                              value={values.address}
                              onChange={handleChange}
                              displayText="block"
                              textPositioning="relative"
                              onBlur={handleBlur}
                            />
                          </InputContainer>
                        </Right>
                      </FormContainer>
                      <ButtonContainer>
                        <CommonButton
                          action={() => {
                            submitForm();
                            handleClose();
                          }}
                          type="button"
                          scope="primary"
                          value={t('submit')}
                          disabled={!dirty || Object.keys(errors).length !== 0}
                        />
                        <CommonButton action={() => handleClose()} type="button" scope="secondary" value={'cancel'} />
                      </ButtonContainer>
                    </>
                  ) : (
                    <UploadContainer>
                      <InputContainer>
                        <InputLabel>{t('company')}</InputLabel>
                        <Dropdown
                          name="company"
                          hasFeedback={errors.company ? true : false}
                          feedbackMessage={errors.company ? t(`${errors.company}`) : ''}
                          value={values.company}
                          onChange={(option) => setFieldValue('company', option)}
                          options={companyOptions(companies)}
                          disabled={isOpenDocumentView}
                        />
                      </InputContainer>
                      <CommonButton
                        action={() => getSample()}
                        type="button"
                        scope="secondary"
                        value={t('download-formatted-sample')}
                      />
                      <>
                        <input
                          ref={inputFile}
                          style={{ display: 'none' }}
                          accept={'.csv'}
                          type="file"
                          onChange={(e: any) => setCsvFile(e.target.files[0])}
                        />
                        <CommonButton
                          action={() => inputFile?.current?.click()}
                          type="button"
                          scope="primary"
                          value={t('upload-csv')}
                        />
                      </>
                      {multiErrors.length > 0 && (
                        <>
                          <h3>{t('Upload errors')}</h3>
                          <Textarea
                            value={multiErrors.map(
                              (el) => `Row number: ${el.rowNumber}\nError Message: ${el.errorMessage} \n`,
                            )}
                          ></Textarea>
                        </>
                      )}
                    </UploadContainer>
                  )}
                </ModalContainer>
              </Form>
            );
          }}
        </Formik>
      </Modal.Content>
      <Modal.Footer
        withConfirmButton={isMultiple}
        confirmAction={async () => {
          try {
            await formikRef.current?.submitForm();
            utils.app.notify('success', t('multiple-stores-uploaded-successfully'));
          } catch (e) {
            console.error(e);
            utils.app.notify('fail', t('error-while-uploading-stores'));
          }
        }}
        withCloseButton={isMultiple}
        confirmDisabled={isMultiple && (!formikRef.current?.values.company?.value || csvFile === null)}
        confirmText={isMultiple ? t('submit-csv') : t('submit')}
      />
    </>
  );
};

export default NewStore;
