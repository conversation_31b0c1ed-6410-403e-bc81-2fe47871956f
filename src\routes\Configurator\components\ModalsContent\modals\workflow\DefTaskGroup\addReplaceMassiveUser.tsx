import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import styled from 'styled-components/macro';

import utils from 'utils';
import actions from 'state/actions';

import CommonButton from 'components/Buttons/CommonButton';
import Header from 'components/Modal/Header';
import selectors from 'state/selectors';
import services from 'services';
import { useAsync } from 'react-use';
import { Dropdown } from 'components/Input';

export const ButtonContainer = styled.div`
  text-align: center;
  button {
    margin: 10px;
  }
`;
const CustomTitle = styled.p`
  font-size: ${({ theme }) => theme.fontSizePalette.body.M};
  font-weight: bold;
`;

const Container = styled.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-bottom: 16px;
`;

const AddOrReplaceMassiveUser = () => {
  const dispatch = useDispatch();

  const t = utils.intl.useTranslator();
  const { type } = useSelector(selectors.modal.getModalProps) || {
    type: 'add',
  };
  const [replacerUser, setReplacerUser] = useState<{
    value: number;
    label: string;
  } | null>(null);
  const selectedCompanyDefinition = useSelector(selectors.configurator.getSelectedCompanyDefinition);
  // GROUP LIST
  const getListOfUserGroup = useSelector(selectors.configurator.getListOfUserForGroup);
  // GROUPUSER
  const oUserSelected = useSelector(selectors.configurator.getSelectedOUser);
  const { revalidate } = services.useGetCompanyGroups('getCompanyGroupsKey', selectedCompanyDefinition?.idCompany);
  useAsync(async () => {
    if (!selectedCompanyDefinition?.idCompany) return;
    const { data } = await services.getUsersForGroup(selectedCompanyDefinition.idCompany);
    dispatch(actions.configurator.setListOfUserForGroup(data));
  }, [selectedCompanyDefinition?.idCompany]);

  const clearState = () => {
    dispatch(actions.configurator.setListOfUserForGroup([]));
    dispatch(actions.configurator.setSelectedGroupFromCompanySelected(null));
    dispatch(actions.configurator.setSelectedOUserList(null));
  };

  const onAction = async () => {
    try {
      if (!oUserSelected?.idUser || !replacerUser?.value) return;

      const serviceAction = type === 'add' ? services.addUserMassivelyWfGroup : services.replaceUserMassivelyWfGroup;

      await serviceAction(oUserSelected?.idUser, replacerUser?.value);
      clearState();
      await revalidate();

      utils.app.notify('success', t(type === 'add' ? 'add-massive-success' : 'replace-massive-success'));
      dispatch(actions.modal.closeModal());
    } catch (error) {
      console.error('Error', error);
      await revalidate();
      dispatch(actions.modal.closeModal());
    }
  };

  return (
    <Container>
      <Header title={type === 'add' ? t('add-massive-user') : t('replace-massive-user')} />
      <Container>
        <CustomTitle>{t('selected-user')}</CustomTitle>
        <Dropdown
          options={getListOfUserGroup.map((user) => {
            return {
              value: user.idUser,
              label: user.username,
            };
          })}
          value={{
            value: oUserSelected?.idUser,
            label: oUserSelected?.username,
          }}
          disabled
        />
      </Container>
      <Container>
        <CustomTitle>{t('replacer-user')}</CustomTitle>
        <Dropdown
          options={getListOfUserGroup
            .map((user) => {
              return {
                value: user.idUser,
                label: user.username,
              };
            })
            .filter((user) => user.value !== oUserSelected?.idUser)}
          placeholder="Select an option"
          value={replacerUser}
          onChange={(value) => {
            setReplacerUser(value);
          }}
        />
      </Container>
      <ButtonContainer>
        <CommonButton
          scope="primary"
          value={type === 'add' ? t('add-massive') : t('replace-massive')}
          action={onAction}
          disabled={!replacerUser}
        />
        <CommonButton
          scope="secondary"
          value={t('close')}
          action={() => {
            dispatch(actions.modal.closeModal());
          }}
        />
      </ButtonContainer>
    </Container>
  );
};

export default AddOrReplaceMassiveUser;
