/* eslint-disable max-lines */
import { BoundingBox } from 'models/response';
import { useCallback, useEffect, useRef, useState } from 'react';
import utils from 'utils';
import * as pdfjsLib from 'pdfjs-dist';
import { useDispatch, useSelector } from 'react-redux';
import selectors from 'state/selectors';
import { useFormikContext } from 'formik';
import { ActiveBox, FormValues, Q1Value } from 'models/documents';
import services from 'services';
import actions from 'state/actions';
import { OCRError } from 'utils/helpers/errors.helpers';
import { Area } from 'models/configurator';
import { colorPalette } from 'utils/styleConstants';

window.pdfjsLib.GlobalWorkerOptions.workerSrc = `${window.location.origin}/${pdfjsLib.version}.pdf.worker.min.js`;

export type PdfBoxSizes = {
  pageNumber: number;
  id: number;
  width: number;
  height: number;
  top: number;
  left: number;
};

export const usePdfNavigation = (numPages: number) => {
  const [pageNumber, setPageNumber] = useState(1);

  const handlePrevPage = () => {
    if (pageNumber > 1) {
      setPageNumber(pageNumber - 1);
    }
  };

  const handleNextPage = () => {
    if (pageNumber < numPages) {
      setPageNumber(pageNumber + 1);
    }
  };

  const handleFirstPage = () => {
    setPageNumber(1);
  };

  const handleLastPage = () => {
    setPageNumber(numPages);
  };

  return {
    pageNumber,
    setPageNumber,
    handlePrevPage,
    handleNextPage,
    handleFirstPage,
    handleLastPage,
  };
};

export const usePdfMovements = (wrapperRef: any, pdfRef: any, pageNumber: number) => {
  const [rotation, setRotation] = useState(0);
  const [zoom, setZoom] = useState(1.5);
  const [fitMode, setFitMode] = useState<'width' | 'height' | null>('width');
  const MAX_ZOOM = 2.5;
  const MIN_ZOOM = 0.5;
  const canZoomIn = zoom < MAX_ZOOM;
  const canZoomOut = zoom > MIN_ZOOM;

  const rotateClockwise = () => {
    setRotation((prevRotation) => prevRotation + 90);
  };

  const rotateCounterClockwise = () => {
    setRotation((prevRotation) => prevRotation - 90);
  };

  const zoomIn = () => {
    if (zoom < MAX_ZOOM) {
      setZoom((prevZoom) => Math.min(prevZoom + 0.25, MAX_ZOOM));
      setFitMode(null);
    }
  };

  const zoomOut = () => {
    if (zoom > MIN_ZOOM) setZoom((prevZoom) => Math.max(prevZoom - 0.25, MIN_ZOOM));
    setFitMode(null);
  };

  const fitToWidth = async () => {
    const wrapper = wrapperRef.current;
    const pdf = pdfRef.current;
    if (wrapper && pdf) {
      const page = await pdf.getPage(pageNumber);
      const viewport = page.getViewport({ scale: 1, rotation });
      const scale = wrapper.clientWidth / viewport.width;
      setZoom(scale);
      setFitMode('width');
    }
  };

  const fitToHeight = async () => {
    const wrapper = wrapperRef.current;
    const pdf = pdfRef.current;
    if (wrapper && pdf) {
      const page = await pdf.getPage(pageNumber);
      const viewport = page.getViewport({ scale: 1, rotation });
      const scale = wrapper.clientHeight / viewport.height;
      setZoom(scale);
      setFitMode('height');
    }
  };

  useEffect(() => {
    if (fitMode === 'width') {
      fitToWidth();
    } else if (fitMode === 'height') {
      fitToHeight();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rotation]);

  return {
    rotation,
    zoom,
    rotateClockwise,
    rotateCounterClockwise,
    zoomIn,
    zoomOut,
    fitMode,
    fitToHeight,
    fitToWidth,
    canZoomIn,
    canZoomOut,
  };
};

export const usePdfDragging = (
  wrapperRef: any,
  containerRef: any,
  pageNumber: number,
  setPageNumber: (pageNumber: number) => void,
  rotation: number,
  zoom: number,
  onAreaChange?: (area: Area) => void,
) => {
  const dispatch = useDispatch();
  const [dragging, setDragging] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [scrollStart, setScrollStart] = useState({ x: 0, y: 0 });

  const isRectangleCreationStarted = useRef(false);
  const [activeMode, setActiveMode] = useState<'ocr' | 'mapping' | null>('mapping'); // Tracks active mode
  // ocr
  const [boundingBox, setBoundingBox] = useState<ActiveBox | null>(null);
  const [ocrMode, setOcrMode] = useState<'area' | 'click'>('area');
  const protocol = useSelector(selectors.documents.selectActiveDocumentID);
  const q3FocusData = useSelector(selectors.documents.selectFocussedQ3Cell);
  const q1FocusData = useSelector(selectors.documents.selectFocussedQ1Cell);
  const nameBodyQ3 = useSelector(selectors.documents.selectBodyName) || '';
  const { setFieldValue, values } = useFormikContext<{ [key: string]: Q1Value }>() ?? {};
  const activeRectangle = useSelector(selectors.documents.selectActiveBox);
  const isOcrActive = rotation === 0 && (q1FocusData.name.length > 0 || q3FocusData != null);
  // mapper
  const [areas, setAreas] = useState<PdfBoxSizes[]>([]); // Store the areas
  const areasSelector = useSelector(selectors.configurator.selectMapperAreas);
  const mapperEditRowID = useSelector(selectors.configurator.selectMapperEditRowId);
  const scaleAreaValues = utils.converter.scaleAreaValues;

  const valueQ1InputFromOCR = ({
    q1FieldName,
    content,
    boundingBox,
    pageNumber,
  }: {
    q1FieldName: string;
    content: string;
    boundingBox: BoundingBox;
    pageNumber: number;
  }) => {
    const { multi, index } = q1FocusData;
    const arrayValues = [...values[q1FieldName].content];
    arrayValues[multi && (index || index === 0) ? index : 0] = {
      ...values[q1FieldName].content[multi && (index || index === 0) ? index : 0],
      content,
      confidence: -1,
      boundingBox: {
        x1: boundingBox.x1,
        x2: boundingBox.x2,
        y1: boundingBox.y1,
        y2: boundingBox.y2,
      },
      pageNumber,
    };
    setFieldValue(q1FieldName, { ...values[q1FieldName], content: arrayValues, status: 3 } as FormValues['Q1Value']);
  };

  const performOcr = async (box: BoundingBox) => {
    if (!protocol) {
      console.warn('Protocol is not defined');
      return;
    }
    try {
      const container = containerRef.current;
      const rect = container.getBoundingClientRect();
      const pageWidth = rect.width;
      const pageHeight = rect.height;

      // Scale bounding box coordinates to percentage values
      const x1 = utils.converter.scaleValue(box.x1, pageWidth, 100).toFixed(4);
      const y1 = utils.converter.scaleValue(box.y1, pageHeight, 100).toFixed(4);
      const x2 = utils.converter.scaleValue(box.x2, pageWidth, 100).toFixed(4);
      const y2 = utils.converter.scaleValue(box.y2, pageHeight, 100).toFixed(4);

      // Sort coordinates
      const dataToSort = [
        [x1, y1],
        [x2, y2],
      ];
      const sorted = dataToSort.sort((a: any, b: any) => {
        return a[0] === b[0] ? a[1] - b[1] : a[0] - b[0];
      });

      const fieldType = q3FocusData ? q3FocusData.type : q1FocusData.fieldInputType;
      const { data } = await services.getZoneText({
        protocol,
        pageNumber,
        fieldType,
        x1: sorted[0][0],
        y1: sorted[0][1],
        x2: sorted[1][0],
        y2: sorted[1][1],
      });

      if (!data.content) {
        throw new Error('Empty or null prediction');
      }

      // if we get an invalid lucy date format dd-mm-yyy
      // if a can covert i have a valid date
      if (data.fieldType === '1') {
        utils.date.convertToObjectDate(data.content);
      }

      // Update form values
      if (q3FocusData?.columnId) {
        // Handle Q3 case
        const oldBody = values[nameBodyQ3].body;
        const newBody = oldBody?.map((el) =>
          Number(el.id) === q3FocusData.rowId
            ? {
                ...el,
                [q3FocusData.columnId]: {
                  content: data.content,
                  pageNumber: pageNumber - 1,
                  boundingBox: { x1: data.x1, y1: data.y1, x2: data.x2, y2: data.y2 },
                },
              }
            : el,
        );
        setFieldValue(nameBodyQ3, { ...values[nameBodyQ3], body: newBody });
      } else if (q1FocusData.name) {
        // Handle Q1 case
        const q1FieldName = q1FocusData.name;
        if (q1FieldName.length > 0) {
          valueQ1InputFromOCR({
            q1FieldName,
            content: data.content,
            pageNumber: pageNumber - 1,
            boundingBox: {
              x1: data.x1,
              x2: data.x2,
              y1: data.y1,
              y2: data.y2,
            },
          });
        }
      }

      // Reset focus data
      dispatch(actions.documents.setFocussedQ1({ fieldInputType: 0, name: '' }));
      dispatch(actions.documents.setQ3Cell(null));
    } catch (e) {
      console.error(e);
      utils.app.notify('warning', (e as Error).message, 3000, 'ocr-error');
    }
  };

  const getRelativeCoordinates = (e: React.MouseEvent) => {
    const rect = containerRef.current.getBoundingClientRect();
    return {
      x: e.clientX - rect.left,
      y: e.clientY - rect.top,
    };
  };

  const getCordinates = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    try {
      const rect = containerRef.current.getBoundingClientRect();
      const x = e.clientX - rect.left;
      const y = e.clientY - rect.top;

      return {
        x: utils.converter.scaleValue(x, rect.width, 100).toFixed(4),
        y: utils.converter.scaleValue(y, rect.height, 100).toFixed(4),
      };
    } catch (e) {
      console.error(e);
      return { x: '0', y: '0' };
    }
  };

  const performClickOcr = async (x: string, y: string) => {
    try {
      const q1FieldName = q1FocusData.name;
      const fieldType = q3FocusData ? q3FocusData.type : q1FocusData.fieldInputType;
      if (!protocol) {
        throw new Error('Protocol is not defined');
      }
      const { data } = await services.getZoneText({
        protocol,
        pageNumber,
        fieldType,
        x1: x,
        y1: y,
      });

      if (data.content?.length === 0 || !data.content) {
        throw new OCRError('Empty or null prediction');
      }

      // q3 case
      if (q3FocusData?.columnId) {
        const oldBody = values[nameBodyQ3].body;
        const newBody = oldBody?.map((el: any) =>
          Number(el.id) === q3FocusData.rowId
            ? {
                ...el,
                [q3FocusData.columnId]: {
                  content: data.content,
                  pageNumber: pageNumber - 1,
                  boundingBox: { x1: data.x1, y1: data.y1, x2: data.x2, y2: data.y2 },
                },
              }
            : el,
        );

        // if we get an invalid lucy date format dd-mm-yyy
        // if a can covert i have a valid date
        if (data.fieldType === '1') utils.date.convertToObjectDate(data.content);

        setFieldValue(nameBodyQ3, { ...values[nameBodyQ3], body: newBody });
      }
      // q1 case
      if (q1FieldName.length > 0) {
        // set new q1 field value
        valueQ1InputFromOCR({
          q1FieldName,
          content: data.content,
          pageNumber: pageNumber - 1,
          boundingBox: {
            x1: data.x1,
            x2: data.x2,
            y1: data.y1,
            y2: data.y2,
          },
        });
      }

      // reset states
      dispatch(actions.documents.setFocussedQ1({ fieldInputType: 0, name: '' }));
      dispatch(actions.documents.setQ3Cell(null));
    } catch (e) {
      console.error(e);
      utils.app.notify('warning', e as Error, 3000, 'ocr-error');
    }
  };

  const handleMouseDoubleClick = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (ocrMode === 'click') {
      const { x, y } = getCordinates(e);
      performClickOcr(x, y);
    }
  };

  const handleMouseDown = (e: React.MouseEvent) => {
    if (isOcrActive) {
      if (ocrMode === 'area') {
        isRectangleCreationStarted.current = true;
        const { x, y } = getRelativeCoordinates(e);
        setBoundingBox({
          x1: x,
          y1: y,
          x2: x,
          y2: y,
          pageNumber,
        });
      }
    } else if (activeMode === 'mapping') {
      setDragging(false);
    } else {
      setDragging(true);
      setDragStart({ x: e.clientX, y: e.clientY });
      if (wrapperRef.current) {
        setScrollStart({
          x: wrapperRef.current.scrollLeft,
          y: wrapperRef.current.scrollTop,
        });
      }
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (dragging && wrapperRef.current) {
      const deltaX = e.clientX - dragStart.x;
      const deltaY = e.clientY - dragStart.y;
      wrapperRef.current.scrollLeft = scrollStart.x - deltaX;
      wrapperRef.current.scrollTop = scrollStart.y - deltaY;
    }

    if (boundingBox && isRectangleCreationStarted.current) {
      const { x, y } = getRelativeCoordinates(e);
      setBoundingBox({
        x1: boundingBox.x1,
        y1: boundingBox.y1,
        x2: x,
        y2: y,
        pageNumber,
      });
    }
  };

  const handleMouseUp = () => {
    setDragging(false);
    if (boundingBox && isRectangleCreationStarted.current) {
      isRectangleCreationStarted.current = false;
      performOcr(boundingBox);
      setBoundingBox(null);
    }
    // mapper
    if (mapperEditRowID !== '') {
      const val = scaleAreaValues(
        containerRef.current.getBoundingClientRect(),
        {
          height: areas[0].height,
          width: areas[0].width,
          left: areas[0].left,
          top: areas[0].top,
        },
        pageNumber,
        areasSelector[0].id || 0,
      );
      onAreaChange?.(val);
    }
  };
  const createSuggestedBox = useCallback(() => {
    try {
      if (!activeRectangle) {
        setBoundingBox(null);
        return;
      }
      // rotation should be 0 or a multiple of 360
      if (rotation % 360 !== 0) {
        utils.app.notify('warning', 'No suggestions when PDF is rotated', 2000, 'rotated-pdf');
        setBoundingBox(null);
        dispatch(actions.documents.setActiveBox(null));
        return;
      }

      const { x1, x2, y1, y2 } = activeRectangle;

      // Check for a null box
      if (x1 === 0 && x2 === 0 && y1 === 0 && y2 === 0) {
        utils.app.notify('warning', 'Null box', 5000, 'null-box');
        return;
      }

      if (!(x2 > x1 && y2 > y1)) {
        throw new Error('Invalid suggestion box size!');
      }

      const container = containerRef.current;
      if (container) {
        const pdfHeight = container.clientHeight;
        const pdfWidth = container.clientWidth;

        const scaledX1 = utils.converter.scaleValue(x1, 1, pdfWidth);
        const scaledY1 = utils.converter.scaleValue(y1, 1, pdfHeight);
        const scaledX2 = utils.converter.scaleValue(x2, 1, pdfWidth);
        const scaledY2 = utils.converter.scaleValue(y2, 1, pdfHeight);

        setBoundingBox({
          x1: scaledX1,
          y1: scaledY1,
          x2: scaledX2,
          y2: scaledY2,
          pageNumber: activeRectangle.pageNumber + 1,
        });
        setPageNumber(activeRectangle.pageNumber + 1);
      }
    } catch (e) {
      utils.app.notify('fail', `Error: ${(e as Error).message}`);
    }
  }, [activeRectangle, rotation, containerRef, setPageNumber, dispatch]);

  const addArea = useCallback(() => {
    if (activeMode === 'mapping') {
      const container = containerRef.current;
      const height = container.clientHeight;
      const width = container.clientWidth;
      if (container && areasSelector.length > 0) {
        const x1 = utils.converter.scaleValue(areasSelector[0].x1, 1, width);
        const y1 = utils.converter.scaleValue(areasSelector[0].y1, 1, height);
        const x2 = utils.converter.scaleValue(areasSelector[0].x2, 1, width);
        const y2 = utils.converter.scaleValue(areasSelector[0].y2, 1, height);
        setAreas([
          {
            id: areasSelector[0].id || 0,
            pageNumber,
            top: y1,
            left: x1,
            width: x2 - x1,
            height: y2 - y1,
          },
        ]);
      }
    }
  }, [areasSelector, pageNumber, activeMode, containerRef]);

  useEffect(() => {
    addArea();
  }, [addArea]);

  const updateArea = (id: number, newBox: BoundingBox) => {
    setAreas((prevAreas: any[]) =>
      prevAreas.map((area) =>
        area.id === id
          ? {
              ...area,
              left: newBox.x1,
              top: newBox.y1,
              width: newBox.x2 - newBox.x1,
              height: newBox.y2 - newBox.y1,
            }
          : area,
      ),
    );
  };

  const toggleMappingMode = () => {
    setActiveMode((prevMode: 'ocr' | 'mapping' | null) => (prevMode === 'mapping' ? null : 'mapping'));
  };
  useEffect(() => {
    window.addEventListener('resize', createSuggestedBox);

    return () => {
      window.removeEventListener('resize', createSuggestedBox);
    };
  }, [createSuggestedBox]);

  useEffect(() => {
    createSuggestedBox();
  }, [createSuggestedBox, zoom, activeRectangle, rotation]);

  return {
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    boundingBox,
    activeMode,
    setActiveMode,
    dragging,
    areas,
    addArea,
    updateArea,
    toggleMappingMode,
    ocrMode,
    setOcrMode,
    handleMouseDoubleClick,
    isOcrActive,
  };
};

const useDocumentTranslator = (numPages: number) => {
  const isButtonTranslateDisabled = numPages > 22;
  const protocol = useSelector(selectors.documents.selectActiveDocumentID);
  const dispatch = useDispatch();
  const [translateActive, setTranlsateActive] = useState<boolean>(false);
  const [originalImage, setOriginalImage] = useState<string>('');
  const [translatedImage, setTranslatedImage] = useState<string>('');
  const documentImage = useSelector(selectors.documents.selectDocumentImage);
  const t = utils.intl.useTranslator();

  // Store the original image when it first loads
  useEffect(() => {
    if (documentImage && !originalImage) {
      setOriginalImage(documentImage);
    }
  }, [documentImage, originalImage]);

  const translationTooltip = isButtonTranslateDisabled
    ? {
        title: t('document-too-large'),
        color: colorPalette.grey.grey2,
      }
    : translateActive
    ? {
        title: t('document-translated'),
        color: colorPalette.turquoise.dark,
      }
    : {
        title: t('document-original'),
        color: undefined,
      };

  const translateDocument = async () => {
    try {
      if (isButtonTranslateDisabled) {
        utils.app.notify('warning', t('document-too-large'));
        return;
      }
      if (protocol) {
        if (!translateActive) {
          // If we already have the translated image, use it
          if (translatedImage) {
            dispatch(actions.documents.setDocumentImage(translatedImage));
          } else {
            // Otherwise fetch and store it
            const { data } = await services.translateDocument(protocol?.toString(), 'en');
            setTranslatedImage(data);
            dispatch(actions.documents.setDocumentImage(data));
          }
          setTranlsateActive(true);
          utils.app.notify('success', t('document-translated'));
        } else {
          dispatch(actions.documents.setDocumentImage(originalImage));
          setTranlsateActive(false);
          utils.app.notify('success', t('document-original'));
        }
      }
    } catch (e) {
      utils.app.notify('warning', e as Error);
    }
  };
  return {
    translateDocument,
    translationTooltip,
  };
};

export const usePdfRenderer = ({ base64, onAreaChange }: { base64: string; onAreaChange?: any }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const wrapperRef = useRef<HTMLDivElement>(null);
  // const [wrapperRef, { width, height }] = useMeasure();
  // console.log('width, height:', width, height);

  const contentDivRef = useRef<HTMLDivElement>(null);
  const pdfRef = useRef<any>(null);
  const renderTaskRef = useRef<any>(null);

  const [numPages, setNumPages] = useState(0);
  const { handleFirstPage, handleLastPage, handleNextPage, handlePrevPage, pageNumber, setPageNumber } =
    usePdfNavigation(numPages);
  const {
    rotateClockwise,
    rotateCounterClockwise,
    rotation,
    zoom,
    zoomIn,
    zoomOut,
    canZoomIn,
    canZoomOut,
    fitMode,
    fitToHeight,
    fitToWidth,
  } = usePdfMovements(wrapperRef, pdfRef, pageNumber);
  const {
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    boundingBox,
    dragging,
    addArea,
    areas,
    updateArea,
    activeMode,
    setActiveMode,
    toggleMappingMode,
    handleMouseDoubleClick,
    ocrMode,
    setOcrMode,
    isOcrActive,
  } = usePdfDragging(wrapperRef, containerRef, pageNumber, setPageNumber, rotation, zoom, onAreaChange);
  const { translateDocument, translationTooltip } = useDocumentTranslator(numPages);

  const renderPage = async (pageNum: number, rotate: number, zoomLevel: number) => {
    const pdf = pdfRef.current;
    if (!pdf) return;

    if (pageNum < 1 || pageNum > pdf.numPages) {
      utils.app.notify('warning', 'Invalid page number');
      return;
    }
    const page = await pdf.getPage(pageNum);
    const viewport = page.getViewport({ scale: zoomLevel, rotation: rotate });
    const canvas = canvasRef.current;
    const context = canvas?.getContext('2d');
    const contentDiv = contentDivRef.current;
    const containerDiv = containerRef.current;

    if (canvas && context && contentDiv && containerDiv) {
      canvas.height = viewport.height;
      canvas.width = viewport.width;
      contentDiv.style.width = `${viewport.width}px`;
      contentDiv.style.height = `${viewport.height}px`;
      containerDiv.style.width = `${viewport.width}px`;
      containerDiv.style.height = `${viewport.height}px`;

      if (renderTaskRef.current) {
        renderTaskRef.current.cancel();
      }
      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };
      const renderTask = page.render(renderContext);
      renderTaskRef.current = renderTask;
      try {
        await renderTask.promise;
      } catch (error) {
        if ((error as any)?.name === 'RenderingCancelledException') {
          console.warn('Render cancelled');
        } else {
          throw error;
        }
      }
    }
  };

  useEffect(() => {
    const loadPdf = async () => {
      try {
        if (!base64 && typeof base64 !== 'string') {
          console.error('Base64 data is missing.');
          return;
        }

        if (!pdfjsLib || !pdfjsLib.getDocument) {
          console.error('pdfjsLib is not loaded or getDocument function is unavailable.');
          return;
        }

        const loadingTask = pdfjsLib.getDocument({ data: atob(base64) });
        const pdf = await loadingTask.promise;

        pdfRef.current = pdf;
        setNumPages(pdf.numPages);

        await fitToWidth();
      } catch (error) {
        console.error('Error loading PDF document:', error);
      }
    };

    loadPdf();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [base64]);

  useEffect(() => {
    if (pdfRef.current) {
      renderPage(pageNumber, rotation, zoom);
    }

    // Cleanup function to cancel render task on unmount or when dependencies change
    return () => {
      if (renderTaskRef.current) {
        renderTaskRef.current.cancel();
      }
    };
  }, [pageNumber, rotation, zoom]);

  return {
    canvasRef,
    wrapperRef,
    contentDivRef,
    pageNumber,
    numPages,
    handlePrevPage,
    handleNextPage,
    rotateClockwise,
    rotateCounterClockwise,
    zoomIn,
    zoomOut,
    canZoomIn,
    canZoomOut,
    fitToWidth,
    fitToHeight,
    handleMouseDown,
    handleMouseMove,
    handleMouseUp,
    setPageNumber,
    fitMode,
    handleLastPage,
    handleFirstPage,
    dragging,
    boundingBox,
    addArea,
    areas,
    activeMode,
    setActiveMode,
    toggleMappingMode,
    updateArea,
    handleMouseDoubleClick,
    ocrMode,
    setOcrMode,
    containerRef,
    isOcrActive,
    translateDocument,
    translationTooltip,
  };
};
