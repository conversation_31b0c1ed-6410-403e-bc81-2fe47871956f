import Checkbox from 'components/Buttons/CheckboxButton';
import CommonButton from 'components/Buttons/CommonButton';
import Dropdown from 'components/Input/Dropdown';
import TextInput from 'components/Input/TextInput';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import {
  ButtonsContainer,
  CheckboxContainer,
  CheckboxLabel,
  InputContainer,
  InputLabel,
  Right,
  RightTitle,
  RightWrapper,
} from 'routes/Configurator/styles';
import actions from 'state/actions';
import selectors from 'state/selectors';
import styled from 'styled-components';
import utils from 'utils';
import CronFrequencySelector from '../components/CronFrequencySelector';
import { FormValues } from './wfReminderFormUtils';

export const ExtendedInputLabel = styled(InputLabel)`
  width: 200px;
  white-space: normal;
  overflow: hidden;
`;
export const ExtendedCheckboxLabel = styled(CheckboxLabel)`
  width: 200px;
  white-space: normal;
  overflow: hidden;
`;

interface IProps {
  values: FormValues;
  setFieldValue: Function;
  dirty: boolean;
  isValid: boolean;
  isValidating: boolean;
  submitForm: (e: any) => void;
  userList: { idUser: number; name: string }[];
  templatesList: { value: number; label: string }[];
}

const FormWfReminder = ({
  values,
  setFieldValue,
  dirty,
  isValid,
  isValidating,
  submitForm,
  userList,
  templatesList,
}: IProps) => {
  const t = utils.intl.useTranslator();
  const dispatch = useDispatch();

  const formMode = useSelector(selectors.configurator.getFormModeWfReminder);
  const selectedRow = useSelector(selectors.configurator.getSelectedWfReminderSettings);

  return (
    <RightWrapper>
      <RightTitle> {t(`${formMode}_setting`)}</RightTitle>
      <Right>
        <InputContainer>
          <ExtendedInputLabel>{t('user')}</ExtendedInputLabel>
          {formMode === 'edit' ? (
            <TextInput disabled value={selectedRow?.name} />
          ) : (
            <Dropdown
              name="user"
              value={values['user']}
              onChange={(value) => {
                setFieldValue('user', value);
              }}
              options={utils.input.buildOptions(userList ?? [], 'name', 'idUser')}
              placeholder={t('select_user')}
              width="170px"
            />
          )}
        </InputContainer>
        <CheckboxContainer>
          <ExtendedCheckboxLabel>{t('reminderSendMail')}</ExtendedCheckboxLabel>
          <Checkbox
            name="reminderSendMail"
            isActive={values['reminderSendMail'] ? true : false}
            action={(e) => setFieldValue('reminderSendMail', e)}
          />
        </CheckboxContainer>
        <InputContainer>
          <ExtendedInputLabel>{t('startDeltaReminder')}</ExtendedInputLabel>
          <TextInput
            name="startDeltaReminder"
            value={values['startDeltaReminder']}
            onChange={(e: any) => {
              const value = Number(e.target.value);
              setFieldValue('startDeltaReminder', value);
            }}
            type="number"
            min={1}
            max={Number(values['endDeltaReminder'])}
          />
        </InputContainer>
        <InputContainer>
          <ExtendedInputLabel>{t('endDeltaReminder')}</ExtendedInputLabel>
          <TextInput
            name="endDeltaReminder"
            value={values['endDeltaReminder']}
            onChange={(e: any) => {
              const value = Number(e.target.value);
              setFieldValue('endDeltaReminder', value);
            }}
            type="number"
            min={Number(values['startDeltaReminder'])}
          />
        </InputContainer>
        <InputContainer>
          <ExtendedInputLabel>{t('reminderDays')}</ExtendedInputLabel>
          <TextInput
            name="reminderDays"
            value={values['reminderDays']}
            onChange={(e: any) => {
              const value = Number(e.target.value);
              setFieldValue('reminderDays', value);
            }}
            type="number"
            min={1}
          />
        </InputContainer>
        <InputContainer>
          <ExtendedInputLabel>{t('reminderMaxDocuments')}</ExtendedInputLabel>
          <TextInput
            name="reminderMaxDocuments"
            value={values['reminderMaxDocuments']}
            onChange={(e: any) => {
              const value = Number(e.target.value);
              setFieldValue('reminderMaxDocuments', value);
            }}
            type="number"
            min={1}
          />
        </InputContainer>
        <InputContainer>
          <ExtendedInputLabel>{t('reminderMaxDocumentDetails')}</ExtendedInputLabel>
          <TextInput
            name="reminderMaxDocumentDetails"
            value={values['reminderMaxDocumentDetails']}
            onChange={(e: any) => {
              const value = Number(e.target.value);
              setFieldValue('reminderMaxDocumentDetails', value);
            }}
            type="number"
            min={1}
          />
        </InputContainer>
        <InputContainer>
          <ExtendedInputLabel>{t('idTemplateScheduledReminder')}</ExtendedInputLabel>
          <Dropdown
            name="idTemplateScheduledReminder"
            value={values['idTemplateScheduledReminder']}
            onChange={(value) => {
              setFieldValue('idTemplateScheduledReminder', value);
            }}
            options={templatesList}
            placeholder={t('select_template_scheduled_reminder')}
            width="170px"
          />
        </InputContainer>
        <CronFrequencySelector
          hour={values['hour']}
          setHour={(value: number) => setFieldValue('hour', value)}
          minute={values['minute']}
          setMinute={(value: number) => setFieldValue('minute', value)}
          allDays={values['allDays']}
          setAllDays={(value: boolean) => setFieldValue('allDays', value)}
          daysOfWeek={values['daysOfWeek']}
          setDaysOfWeek={(value: { label: string; value: number }[]) => setFieldValue('daysOfWeek', value)}
        />
        <ButtonsContainer>
          <CommonButton
            id="saveButton"
            scope="primary"
            disabled={!dirty || !isValid || isValidating}
            type="submit"
            value={t(`${formMode}_settings`)}
            action={submitForm}
          />
          <CommonButton
            id="cancelButton"
            scope="secondary"
            type="button"
            value={t('cancel')}
            action={() => {
              dispatch(actions.configurator.setFormModeWfReminder(null));
            }}
          />
        </ButtonsContainer>
      </Right>
    </RightWrapper>
  );
};
export default FormWfReminder;
