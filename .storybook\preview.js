import React, { useEffect } from 'react';
import { Reset } from 'styled-reset';
import { StylesGlobal, theme } from '../src/styles-global';
import { library } from '@fortawesome/fontawesome-svg-core';
import * as Icons from '@fortawesome/free-solid-svg-icons';
import { IntlProvider } from 'react-intl';
import translation from './translation';
import { ThemeProvider } from '../src/providers/ThemeProvider';
import { Provider } from 'react-redux';
import store from 'state/store';

// importing fontawesome icons
const iconList = Object
  .keys(Icons)
  .filter((key) => key !== 'fas' && key !== 'prefix' )
  .map((icon) => Icons[icon]);

library.add(...iconList);

const withGlobals = (Story) => {
  useEffect(() => {
    const loadScript = (src, onload) => {
      const script = document.createElement('script');
      script.src = src;
      script.onload = onload;
      document.body.appendChild(script);
    };

    loadScript('https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.6.347/pdf.min.js', () => {
      loadScript('https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.6.347/pdf.worker.min.js');
    });
  }, []);

  return <Story />;
};

export const decorators = [
  (Story) => (
    <>
      <Reset />
      <ThemeProvider>
      <StylesGlobal />
        <IntlProvider locale={navigator.language} messages={translation}>
          <Provider store={store}>
            <Story />
          </Provider>
        </IntlProvider>
      </ThemeProvider>
      <div id="notification-portal"></div>
      <div id="datepicker-portal"></div>
      <div id="react-select-portal"></div>
      <div id="multifilter-portal"></div>
    </>
  ),
  withGlobals,
];

export const parameters = {
  backgrounds: {
    default: 'light',
  },
  layout: 'centered',
  actions: { argTypesRegex: "^on[A-Z].*" },
  controls: {
    matchers: {
      color: /(background|color)$/i,
      date: /Date$/,
    },
  },
}
