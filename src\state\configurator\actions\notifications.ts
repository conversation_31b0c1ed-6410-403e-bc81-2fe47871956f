import { PayloadAction, CaseReducer } from '@reduxjs/toolkit';
import { ActiveViewNotifications, ConfiguratorState, FormTypesNotifications } from 'models/configurator';
import {
  GetTemplatesMailNotification,
  IOpenWorkflowNotifications,
  IOverdueNotifications,
  IWfReminderNotifications,
} from 'models/response';

const seTNotificationActiveView: CaseReducer<ConfiguratorState, PayloadAction<ActiveViewNotifications>> = (
  state,
  { payload },
) => {
  state.notifications.activeView = payload;
};
// OPEN WORKFLOW
const setFormModeOpenWf: CaseReducer<ConfiguratorState, PayloadAction<FormTypesNotifications>> = (
  state,
  { payload },
) => {
  state.notifications.openWf.formModeOpenWF = payload;
};

const setSelectedOpenWfSettings: CaseReducer<ConfiguratorState, PayloadAction<IOpenWorkflowNotifications | null>> = (
  state,
  { payload },
) => {
  state.notifications.openWf.selectedOpenWfSettings = payload;
};

const setListOfOpenWfSettings: CaseReducer<ConfiguratorState, PayloadAction<IOpenWorkflowNotifications[]>> = (
  state,
  { payload },
) => {
  state.notifications.openWf.listOfOpenWfSettings = payload;
};

// WF REMINDER
const setListOfWfReminderSettings: CaseReducer<ConfiguratorState, PayloadAction<IWfReminderNotifications[]>> = (
  state,
  { payload },
) => {
  state.notifications.wfReminder.listOfWfReminderSettings = payload;
};

const setSelectedWfReminderSettings: CaseReducer<ConfiguratorState, PayloadAction<IWfReminderNotifications | null>> = (
  state,
  { payload },
) => {
  state.notifications.wfReminder.selectedWfReminderSettings = payload;
};

const setFormModeWfReminder: CaseReducer<ConfiguratorState, PayloadAction<FormTypesNotifications>> = (
  state,
  { payload },
) => {
  state.notifications.wfReminder.formModeWfReminder = payload;
};

// OVERDUE
const setListOfOverdueSettings: CaseReducer<ConfiguratorState, PayloadAction<IOverdueNotifications[]>> = (
  state,
  { payload },
) => {
  state.notifications.overdue.listOfOverdueSettings = payload;
};

const setSelectedOverdueSettings: CaseReducer<ConfiguratorState, PayloadAction<IOverdueNotifications | null>> = (
  state,
  { payload },
) => {
  state.notifications.overdue.selectedOverdueSettings = payload;
};

const setFormModeOverdue: CaseReducer<ConfiguratorState, PayloadAction<FormTypesNotifications>> = (
  state,
  { payload },
) => {
  state.notifications.overdue.formModeOverdue = payload;
};

const setTemplatesNotificationList: CaseReducer<ConfiguratorState, PayloadAction<GetTemplatesMailNotification[]>> = (
  state,
  { payload },
) => {
  state.notifications.templatesList = payload;
};

export default {
  seTNotificationActiveView,
  setFormModeOpenWf,
  setSelectedOpenWfSettings,
  setListOfOpenWfSettings,
  setListOfWfReminderSettings,
  setSelectedWfReminderSettings,
  setFormModeWfReminder,
  setListOfOverdueSettings,
  setSelectedOverdueSettings,
  setFormModeOverdue,
  setTemplatesNotificationList,
};
