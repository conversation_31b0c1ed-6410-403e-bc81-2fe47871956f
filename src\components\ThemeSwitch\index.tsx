import React from 'react';
import { useTheme } from 'providers/ThemeProvider';
import styled from 'styled-components/macro';
import Toggle from 'components/Input/Toggle';

const ThemeToggleWrapper = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-right: 5px;
`;
const P = styled.p`
  margin-right: 10px;
  font-size: ${({ theme }) => theme.fontSizePalette.body.XXS};
`;

export const ThemeSwitch = () => {
  const { isDarkMode, toggleTheme } = useTheme();

  return (
    <ThemeToggleWrapper>
      <P>Light</P>
      <Toggle
        checked={isDarkMode}
        onChange={toggleTheme}
        tooltip={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
      />
      <P>Dark</P>
    </ThemeToggleWrapper>
  );
};

export default ThemeSwitch;
