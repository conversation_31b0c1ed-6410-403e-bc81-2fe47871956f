import React, { useState } from 'react';
import styled from 'styled-components/macro';
import { actionType } from 'utils/constants';

import utils from 'utils';

import CommonButton from 'components/Buttons/CommonButton';
import { TextInput } from 'components/Input';

export const NoteItem = styled.div`
  padding-left: 10px;
  border: 1px solid ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey4 : theme.colorPalette.grey.grey5};
  display: flex;
  justify-content: flex-start;
  align-items: center;
  column-gap: 8px;
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey3 : theme.colorPalette.white};
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey15 : theme.colorPalette.grey.grey9};
`;

export const AddNoteForm = styled.form`
  display: flex;
  margin-top: 20px;
`;

export const AddNoteContainer = styled.div`
  display: flex;
  flex-direction: row;
  flex-basis: 100%;
  gap: 8px;
`;

export const NoteButton = styled.button`
  padding: 10px 20px;
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.turquoise.dark : theme.colorPalette.turquoise.normal};
  color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.black : theme.colorPalette.white};
  border: none;
  cursor: pointer;
`;
const Separator = styled.div`
  width: 2px;
  height: 35px;
  background-color: ${({ theme }) =>
    theme.colorPalette.isDarkMode ? theme.colorPalette.grey.grey4 : theme.colorPalette.grey.grey5};
`;
const Group = styled.div`
  padding: 2px;
  display: flex;
  gap: 8px;
`;

export type Note = {
  id: string | number;
  text: string;
};

export type NoteProps = {
  notes: Note[];
  onUseNote: (value: string) => void;
  onDeleteNote: (value: string | number) => void;
  onAddNote: (value: string) => void;
  companyName?: string | undefined;
};
const NotesInserter = ({ notes, onUseNote, onDeleteNote, onAddNote, companyName }: NoteProps) => {
  const [newNote, setNewNote] = useState<string>('');
  const [editMode, setEditMode] = useState<boolean>(false);
  const t = utils.intl.useTranslator();

  const handleAdd = (event: React.FormEvent) => {
    event.preventDefault();
    if (newNote.trim()) {
      setNewNote('');
      onAddNote?.(newNote);
    }
  };

  return (
    <div>
      {notes?.map((note) => (
        <NoteItem key={note.id}>
          <Group>
            <CommonButton
              scope="secondary"
              value={t('delete')}
              action={() => onDeleteNote?.(note.id)}
              disabled={!utils.user.isActionByCompanyActive(actionType.DELETE_NOTE, companyName)}
            />
            <CommonButton value={t('use')} action={() => onUseNote?.(note.text)} />
          </Group>
          <Separator />
          <span>{note.text}</span>
        </NoteItem>
      ))}
      <AddNoteForm>
        {editMode && (
          <AddNoteContainer>
            <TextInput
              fullWidth
              onChange={(e: any) => setNewNote(e.target.value)}
              placeholder={t('add_new_note')}
              value={newNote}
              borderRadius="4px"
            />
            <CommonButton
              value={t('confirm')}
              action={(e) => {
                setEditMode(false);
                handleAdd?.(e);
              }}
            />
            <CommonButton
              value={t('cancel')}
              scope="secondary"
              action={() => {
                setEditMode(false);
              }}
            />
          </AddNoteContainer>
        )}
        {!editMode && (
          <CommonButton
            value={t('add_note')}
            type="submit"
            action={() => setEditMode(true)}
            disabled={!utils.user.isActionByCompanyActive(actionType.ADD_NOTE, companyName)}
          />
        )}
      </AddNoteForm>
    </div>
  );
};

export default NotesInserter;

