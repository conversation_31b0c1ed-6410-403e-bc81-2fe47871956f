import FilterList from 'components/FilterList';
import Graph from 'components/Graph';
import { Area } from 'models/response';
import React from 'react';
import { KpiElement } from './KpiElement';
import { KpiElementTable } from './KpiElementTable';
import { AccordionWrapper, MainContent, GraphWrapper, KpisArea } from './style';
import Accordion from 'components/Accordion';

interface ListViewProps {
  dateTo?: string;
  dateFrom?: string;
  gbsList?: string[];
  companies?: string[];
  channels?: string[];
  types?: string[];
  categoryName?: string;
  areas?: Area[];
  categoryId: number;
  exportReport: (reportId: number) => Promise<void>;
  drillDownProtocols: (idMetric: number, idGeoList: any) => Promise<void>;
}

const ListView: React.FC<ListViewProps> = ({
  dateTo,
  dateFrom,
  gbsList,
  companies,
  channels,
  types,
  categoryName,
  areas,
  categoryId,
  exportReport,
  drillDownProtocols,
}) => {
  return (
    <>
      {areas?.map((area) => {
        const { name, idArea, kpis, average, showSummaryDetails } = area;
        const kpisHidden = kpis?.slice(2);

        return (
          <AccordionWrapper key={idArea}>
            <Accordion
              title={name}
              mainContent={
                <MainContent>
                  {kpis?.slice(0, 2).map((kpi) => (
                    <KpiElement
                      key={kpi?.idMetric}
                      kpi={kpi}
                      categoryId={categoryId}
                      exportReport={exportReport}
                      drillDownProtocols={drillDownProtocols}
                    />
                  ))}
                </MainContent>
              }
              disabled={!average?.result && !kpisHidden?.length}
              hiddenContent={
                <>
                  <div>
                    <FilterList
                      dateTo={dateTo}
                      dateFrom={dateFrom}
                      gbsList={gbsList}
                      companies={companies}
                      channels={channels}
                      types={types}
                      categoryName={categoryName}
                      showSummaryDetails={showSummaryDetails}
                    />

                    <GraphWrapper>{average?.result && <Graph percentage={average.result} />}</GraphWrapper>
                  </div>

                  <KpisArea>
                    {kpisHidden.map((kpi) =>
                      kpi.childMetrics && kpi.childMetrics.length > 0 ? (
                        <KpiElementTable
                          key={kpi.idMetric}
                          kpi={kpi}
                          categoryId={categoryId}
                          exportReport={exportReport}
                          drillDownProtocols={drillDownProtocols}
                        />
                      ) : (
                        <KpiElement
                          key={kpi.idMetric}
                          kpi={kpi}
                          categoryId={categoryId}
                          exportReport={exportReport}
                          drillDownProtocols={drillDownProtocols}
                        />
                      ),
                    )}
                  </KpisArea>
                </>
              }
            />
          </AccordionWrapper>
        );
      })}
    </>
  );
};

export default ListView;
