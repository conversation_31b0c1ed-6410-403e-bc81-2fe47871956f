import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  PdfButton,
  InputContainer,
  NumPages,
  Center,
  Left,
  PageInput,
  PdfToolbar as Toolbar,
  VerticalBar,
  Right,
} from './newPdf.style';
import { Icon } from 'components/Input';
import { colorPalette } from 'utils/styleConstants';
import fileHelper from 'utils/helpers/file.helper';
import { OcrIcon } from './advancedPdf.style';
import iconOcr from 'images/lucy4/icon_ocr.svg';
import iconOcrActive from 'images/lucy4/icon_ocr_active.svg';
import iconOcrClick from 'images/lucy4/icon_ocr_click.svg';
import iconOcrClickActive from 'images/lucy4/icon_ocr_click_active.svg';
import iconOcrArea from 'images/lucy4/icon_ocr_area.svg';
import iconOcrAreaActive from 'images/lucy4/icon_ocr_area_active.svg';

export type PdfButtonProps = {
  showFitButtons?: boolean;
  showRotateButtons?: boolean;
  showPageNavigation?: boolean;
  showZoomButtons?: boolean;
  showDownloadButton?: boolean;
  showOcrButtons?: boolean;
  showMappingButtons?: boolean;
  showTranslateButtons?: boolean;
};
export interface PdfToolbarProps extends PdfButtonProps {
  base64: string;
  pageNumber: number;
  setPageNumber: (num: number) => void;
  numPages: number;
  onZoomIn: () => void;
  onZoomOut: () => void;
  onRotateClockwise: () => void;
  onRotateCounterClockwise: () => void;
  onFitToWidth: () => void;
  onFitToHeight: () => void;
  fitMode: 'width' | 'height' | null;
  ocrMode: boolean;
  ocrModeType: 'area' | 'click';
  setOcrModeType: (val: 'area' | 'click') => void;
  onPrevPage: () => void;
  onNextPage: () => void;
  onFirstPage: () => void;
  onLastPage: () => void;
  onToggleMappingMode: () => void;
  translateDoc?: () => void;
  translationTooltip?: {
    title?: string;
    color?: string;
  };
  canZoomIn?: boolean;
  canZoomOut?: boolean;
}

const PdfToolbar: React.FC<PdfToolbarProps> = ({
  pageNumber,
  numPages,
  onZoomIn,
  onZoomOut,
  onRotateClockwise,
  onRotateCounterClockwise,
  onFitToWidth,
  onFitToHeight,
  onPrevPage,
  onNextPage,
  onFirstPage,
  onLastPage,
  setPageNumber,
  base64,
  fitMode,
  ocrMode,
  onToggleMappingMode,
  ocrModeType,
  setOcrModeType,
  showFitButtons = true,
  showRotateButtons = true,
  showPageNavigation = true,
  showZoomButtons = true,
  showDownloadButton = true,
  showOcrButtons = true,
  showMappingButtons = true,
  showTranslateButtons = true,
  translateDoc,
  translationTooltip,
  canZoomIn,
  canZoomOut,
}) => (
  <Toolbar>
    <Left>
      {showFitButtons &&
        (fitMode === 'height' ? (
          <PdfButton style={{ color: 'grey' }} disabled={false} onClick={onFitToWidth}>
            <FontAwesomeIcon size="lg" icon="arrows-alt-h" />
          </PdfButton>
        ) : (
          <PdfButton style={{ color: 'grey' }} disabled={false} onClick={onFitToHeight}>
            <FontAwesomeIcon size="lg" icon="arrows-alt-v" />
          </PdfButton>
        ))}
    </Left>
    <Center>
      <>
        {showRotateButtons && (
          <>
            <PdfButton disabled={false} onClick={onRotateCounterClockwise}>
              <FontAwesomeIcon size="lg" icon="undo-alt" />
            </PdfButton>
            <PdfButton disabled={false} onClick={onRotateClockwise}>
              <FontAwesomeIcon size="lg" icon="redo-alt" />
            </PdfButton>
            <VerticalBar />
          </>
        )}
        {showPageNavigation && (
          <>
            <Icon icon="top-page" custom disabled={pageNumber === 1} onClick={onFirstPage} />
            <PdfButton disabled={pageNumber <= 1} onClick={onPrevPage}>
              <FontAwesomeIcon size="lg" icon="arrow-up" />
            </PdfButton>
            <InputContainer>
              <PageInput onChange={(e: any) => setPageNumber(Number(e.target.value))} value={pageNumber} />
              <NumPages>/{numPages}</NumPages>
            </InputContainer>
            <PdfButton disabled={pageNumber >= numPages} onClick={onNextPage}>
              <FontAwesomeIcon size="lg" icon="arrow-down" />
            </PdfButton>
            <Icon
              icon="top-page"
              style={{ transform: 'rotate(180deg)' }}
              custom
              disabled={pageNumber === numPages}
              onClick={onLastPage}
            />
            <VerticalBar />
          </>
        )}
        {showZoomButtons && (
          <>
            <PdfButton disabled={!canZoomOut} onClick={onZoomOut}>
              <FontAwesomeIcon size="lg" icon="search-minus" />
            </PdfButton>
            <PdfButton disabled={!canZoomIn} onClick={onZoomIn}>
              <FontAwesomeIcon size="lg" icon="search-plus" />
            </PdfButton>
          </>
        )}
      </>
    </Center>
    <Right>
      <>
        {showOcrButtons && (
          <>
            <PdfButton>
              {ocrMode ? <OcrIcon alt="ocr-icon" src={iconOcrActive} /> : <OcrIcon alt="ocr-icon" src={iconOcr} />}
            </PdfButton>
            <PdfButton onClick={() => setOcrModeType('area')}>
              {ocrModeType === 'area' ? (
                <OcrIcon alt="ocr-icon" src={iconOcrAreaActive} />
              ) : (
                <OcrIcon alt="ocr-icon" src={iconOcrArea} />
              )}
            </PdfButton>
            <PdfButton onClick={() => setOcrModeType('click')}>
              {ocrModeType === 'click' ? (
                <OcrIcon alt="ocr-icon" src={iconOcrClickActive} />
              ) : (
                <OcrIcon alt="ocr-icon" src={iconOcrClick} />
              )}
            </PdfButton>
          </>
        )}
        {showMappingButtons && (
          <PdfButton onClick={onToggleMappingMode}>
            <FontAwesomeIcon size="lg" icon="object-ungroup" />
          </PdfButton>
        )}

        {showDownloadButton && (
          <>
            <VerticalBar />
            <PdfButton style={{ color: colorPalette.grey.grey9 }} onClick={() => fileHelper.openPdfInNewTab(base64)}>
              <FontAwesomeIcon size="lg" icon="file-pdf" />
            </PdfButton>
          </>
        )}
      </>
      {showTranslateButtons && (
        <PdfButton
          style={{ color: colorPalette.grey.grey9 }}
          disabled={false}
          onClick={translateDoc}
          title={translationTooltip?.title}
        >
          <FontAwesomeIcon size="lg" icon="globe" color={translationTooltip?.color || undefined} />
        </PdfButton>
      )}
    </Right>
  </Toolbar>
);

export default PdfToolbar;
